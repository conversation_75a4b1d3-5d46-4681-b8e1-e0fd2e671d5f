/**
 * Mock Metrics Logger for Testing
 * Compatible with Bun test framework
 */

export interface CircuitBreakerEvent {
  timestamp: Date;
  service: string;
  event_type: 'opened' | 'closed' | 'half_open' | 'manual_override';
  failure_count: number;
  success_count: number;
  next_attempt?: Date;
  reason?: string;
  correlation_id?: string;
}

export interface FallbackEvent {
  timestamp: Date;
  request_id: string;
  customer_id: string;
  api_key_id: string;
  primary_service: string;
  fallback_service: string;
  fallback_reason: string;
  cost_impact_percent: number;
  processing_time_ms: number;
  success: boolean;
  error?: string;
}

export interface ServicePerformanceMetric {
  timestamp: Date;
  service: string;
  operation_type: 'health_check' | 'document_processing';
  latency_ms: number;
  success: boolean;
  error_type?: string;
  request_size_bytes?: number;
  response_size_bytes?: number;
  tokens_used?: number;
  cost_usd?: number;
}

export interface SystemHealthSnapshot {
  timestamp: Date;
  overall_status: 'healthy' | 'degraded' | 'critical';
  service_availability: Record<string, 'healthy' | 'degraded' | 'unhealthy'>;
  circuit_breaker_states: Record<string, 'closed' | 'open' | 'half_open'>;
  active_fallbacks: number;
  average_response_time_ms: number;
  success_rate_percent: number;
  cost_efficiency_score: number;
}

export interface AlertEvent {
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: 'service_health' | 'circuit_breaker' | 'cost_optimization' | 'performance';
  title: string;
  description: string;
  service?: string;
  customer_id?: string;
  metadata?: Record<string, any>;
  auto_resolved?: boolean;
  resolution_time?: Date;
}

/**
 * Mock Metrics Logger Implementation for Tests
 */
export class MetricsLogger {
  private logBuffer: Array<{
    table: string;
    data: any;
  }> = [];
  private isEnabled: boolean;

  constructor(options: {
    supabaseUrl?: string;
    supabaseKey?: string;
    enableDatabaseLogging?: boolean;
    flushIntervalMs?: number;
  } = {}) {
    this.isEnabled = options.enableDatabaseLogging ?? true;
    console.log(`Mock MetricsLogger initialized - logging ${this.isEnabled ? 'enabled' : 'disabled'}`);
  }

  logCircuitBreakerEvent(event: CircuitBreakerEvent): void {
    console.log(`🔄 Circuit Breaker [${event.service}]: ${event.event_type.toUpperCase()}`);
    this.logBuffer.push({
      table: 'circuit_breaker_events',
      data: event
    });
  }

  logFallbackEvent(event: FallbackEvent): void {
    console.log(`🔀 Fallback [${event.primary_service} → ${event.fallback_service}]: ${event.success ? '✅' : '❌'}`);
    this.logBuffer.push({
      table: 'fallback_events',
      data: event
    });
  }

  logServicePerformance(metric: ServicePerformanceMetric): void {
    if (!metric.success || metric.latency_ms > 5000) {
      console.log(`📊 Performance [${metric.service}]: ${metric.success ? '⚠️ SLOW' : '❌ FAILED'} - ${metric.latency_ms}ms`);
    }
    this.logBuffer.push({
      table: 'service_performance_metrics',
      data: metric
    });
  }

  logSystemHealth(snapshot: SystemHealthSnapshot): void {
    const healthIcon = snapshot.overall_status === 'healthy' ? '💚' : 
                      snapshot.overall_status === 'degraded' ? '🟡' : '🔴';
    console.log(`${healthIcon} System Health: ${snapshot.overall_status.toUpperCase()}`);
    this.logBuffer.push({
      table: 'system_health_snapshots',
      data: snapshot
    });
  }

  logAlert(alert: AlertEvent): void {
    const severityIcon = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      critical: '🚨'
    }[alert.severity];
    console.log(`${severityIcon} Alert [${alert.category}]: ${alert.title}`);
    this.logBuffer.push({
      table: 'alerts',
      data: alert
    });
  }

  logOperationalEvent(event: {
    timestamp: Date;
    event_type: string;
    description: string;
    operator?: string;
    service?: string;
    before_state?: any;
    after_state?: any;
    metadata?: Record<string, any>;
  }): void {
    console.log(`🔧 Operation [${event.event_type}]: ${event.description}`);
    this.logBuffer.push({
      table: 'operational_events',
      data: event
    });
  }

  async getMetricsSummary(timeRange: {
    start: Date;
    end: Date;
  }): Promise<{
    circuit_breaker_events: number;
    fallback_events: number;
    service_failures: number;
    average_response_time: number;
    success_rate: number;
    cost_savings_from_optimization: number;
    top_failing_services: Array<{ service: string; failure_count: number }>;
    alert_breakdown: Record<string, number>;
  }> {
    return {
      circuit_breaker_events: this.logBuffer.filter(log => log.table === 'circuit_breaker_events').length,
      fallback_events: this.logBuffer.filter(log => log.table === 'fallback_events').length,
      service_failures: 0,
      average_response_time: 0,
      success_rate: 100,
      cost_savings_from_optimization: 0,
      top_failing_services: [],
      alert_breakdown: {}
    };
  }

  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log(`Mock metrics logging ${enabled ? 'enabled' : 'disabled'}`);
  }

  getBufferStatus(): {
    buffer_size: number;
    is_enabled: boolean;
    database_connected: boolean;
  } {
    return {
      buffer_size: this.logBuffer.length,
      is_enabled: this.isEnabled,
      database_connected: false
    };
  }

  destroy(): void {
    this.logBuffer = [];
    console.log('Mock metrics logger destroyed');
  }
}

/**
 * Global metrics logger instance
 */
let globalMetricsLogger: MetricsLogger | null = null;

/**
 * Initialize global metrics logger
 */
export function initializeMetricsLogger(options?: {
  supabaseUrl?: string;
  supabaseKey?: string;
  enableDatabaseLogging?: boolean;
  flushIntervalMs?: number;
}): MetricsLogger {
  if (globalMetricsLogger) {
    globalMetricsLogger.destroy();
  }
  
  globalMetricsLogger = new MetricsLogger(options);
  return globalMetricsLogger;
}

/**
 * Get global metrics logger
 */
export function getMetricsLogger(): MetricsLogger | null {
  return globalMetricsLogger;
}

/**
 * Convenience functions for common logging operations
 */
export function logCircuitBreakerOpened(service: string, failureCount: number, reason?: string): void {
  globalMetricsLogger?.logCircuitBreakerEvent({
    timestamp: new Date(),
    service,
    event_type: 'opened',
    failure_count: failureCount,
    success_count: 0,
    reason
  });
}

export function logCircuitBreakerClosed(service: string, successCount: number): void {
  globalMetricsLogger?.logCircuitBreakerEvent({
    timestamp: new Date(),
    service,
    event_type: 'closed',
    failure_count: 0,
    success_count: successCount
  });
}

export function logFallbackUsed(
  requestId: string,
  customerId: string,
  apiKeyId: string,
  primary: string,
  fallback: string,
  reason: string,
  costImpact: number,
  success: boolean
): void {
  globalMetricsLogger?.logFallbackEvent({
    timestamp: new Date(),
    request_id: requestId,
    customer_id: customerId,
    api_key_id: apiKeyId,
    primary_service: primary,
    fallback_service: fallback,
    fallback_reason: reason,
    cost_impact_percent: costImpact,
    processing_time_ms: 0,
    success
  });
}

export function logServicePerformance(
  service: string,
  latency: number,
  success: boolean,
  errorType?: string
): void {
  globalMetricsLogger?.logServicePerformance({
    timestamp: new Date(),
    service,
    operation_type: 'document_processing',
    latency_ms: latency,
    success,
    error_type: errorType
  });
}