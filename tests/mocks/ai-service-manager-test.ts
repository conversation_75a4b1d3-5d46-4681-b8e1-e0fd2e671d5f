/**
 * Test-aware AI Service Manager
 * 
 * This extends the AIServiceManager to use mocked services instead of real ones
 * for comprehensive testing without external API calls.
 */

import { AIServiceManager, AIServiceConfig, ProcessingRequest } from '../../supabase/functions/ai-integration/utils/ai-service-manager.ts';
import { createMockServices, MockConfig } from './ai-services.ts';

export interface TestServiceConfigs {
  openai?: MockConfig;
  claude?: MockConfig;
  llamaparse?: MockConfig;
}

/**
 * Test AI Service Manager that uses mocks instead of real services
 */
export class TestAIServiceManager extends AIServiceManager {
  private mockServices: ReturnType<typeof createMockServices>;
  private testConfigs: TestServiceConfigs;

  constructor(
    testConfigs: TestServiceConfigs = {},
    options: {
      fallbackStrategy?: 'cost_optimized' | 'quality_optimized' | 'speed_optimized' | 'balanced';
      costOptimizationEnabled?: boolean;
      globalBudgetLimit?: number;
      healthMonitorConfig?: any;
    } = {}
  ) {
    // Convert test configs to real service configs for parent constructor
    const serviceConfigs: AIServiceConfig = {
      openai: testConfigs.openai ? { apiKey: 'test-openai-key' } : undefined,
      claude: testConfigs.claude ? { apiKey: 'test-claude-key' } : undefined,
      llamaparse: testConfigs.llamaparse ? { apiKey: 'test-llamaparse-key' } : undefined,
    };

    super(serviceConfigs, options);

    this.testConfigs = testConfigs;
    this.mockServices = createMockServices(testConfigs);
  }

  /**
   * Override processWithService to use mocks
   */
  protected async processWithService(service: string, request: ProcessingRequest): Promise<any> {
    switch (service) {
      case 'openai': {
        if (!this.testConfigs.openai) {
          throw new Error('OpenAI not configured');
        }
        return await this.mockServices.processWithOpenAI(
          request.document, 
          request.prompt, 
          this.testConfigs.openai
        );

      case 'claude': {
        if (!this.testConfigs.claude) {
          throw new Error('Claude not configured');
        }
        return await this.mockServices.processWithClaude(
          request.document, 
          request.prompt, 
          this.testConfigs.claude
        );

      case 'llamaparse': {
        if (!this.testConfigs.llamaparse || !request.file) {
          throw new Error('LlamaParse not configured or no file provided');
        }
        return await this.mockServices.processWithLlamaParse(
          request.file, 
          this.testConfigs.llamaparse
        );

      default:
        throw new Error(`Unknown service: ${service}`);
    }
  }

  /**
   * Update test configuration for a service
   */
  updateServiceConfig(service: 'openai' | 'claude' | 'llamaparse', config: MockConfig): void {
    this.testConfigs[service] = { ...this.testConfigs[service], ...config };
    this.mockServices = createMockServices(this.testConfigs);
  }

  /**
   * Make a service fail for testing
   */
  makeServiceFail(service: 'openai' | 'claude' | 'llamaparse', shouldFail: boolean = true): void {
    this.updateServiceConfig(service, { shouldFail });
  }

  /**
   * Simulate service recovery
   */
  makeServiceHealthy(service: 'openai' | 'claude' | 'llamaparse'): void {
    this.updateServiceConfig(service, { shouldFail: false });
  }

  /**
   * Get mock services for direct testing
   */
  getMockServices() {
    return this.mockServices;
  }

  /**
   * Reset all mocks
   */
  resetMocks(): void {
    Object.values(this.mockServices).forEach(mockFn => {
      if (typeof mockFn === 'function' && 'mockClear' in mockFn) {
        (mockFn as any).mockClear();
      }
    });
  }
}

/**
 * Helper to create test service manager with common configurations
 */
export const createTestServiceManager = (
  scenario: 'all_healthy' | 'openai_fails' | 'claude_fails' | 'llamaparse_fails' | 'all_fail' | 'custom',
  customConfigs: TestServiceConfigs = {},
  options: any = {}
): TestAIServiceManager => {
  
  let testConfigs: TestServiceConfigs;

  switch (scenario) {
    case 'all_healthy': {
      testConfigs = {
        openai: { shouldFail: false },
        claude: { shouldFail: false },
        llamaparse: { shouldFail: false }
      };
      break;
    }

    case 'openai_fails': {
      testConfigs = {
        openai: { shouldFail: true },
        claude: { shouldFail: false },
        llamaparse: { shouldFail: false }
      };
      break;
    }

    case 'claude_fails': {
      testConfigs = {
        openai: { shouldFail: false },
        claude: { shouldFail: true },
        llamaparse: { shouldFail: false }
      };
      break;
    }

    case 'llamaparse_fails': {
      testConfigs = {
        openai: { shouldFail: false },
        claude: { shouldFail: false },
        llamaparse: { shouldFail: true }
      };
      break;
    }

    case 'all_fail': {
      testConfigs = {
        openai: { shouldFail: true },
        claude: { shouldFail: true },
        llamaparse: { shouldFail: true }
      };
      break;
    }

    case 'custom': {
    default:
      testConfigs = customConfigs;
      break;
    }
  }

  return new TestAIServiceManager(testConfigs, {
    fallbackStrategy: 'balanced',
    costOptimizationEnabled: true,
    globalBudgetLimit: 100,
    healthMonitorConfig: {
      checkInterval: 100, // Faster for tests
      degradationThreshold: 1000,
      unhealthyThreshold: 2
    },
    ...options
  });
};