/**
 * Mock AI Services for Testing
 * 
 * This file provides complete mocking of all AI service functions
 * to prevent real API calls during testing.
 */

import { mock } from 'bun:test';

export interface MockConfig {
  shouldFail?: boolean;
  delay?: number;
  customResponse?: any;
}

// Mock response generators
const generateOpenAIResponse = (config: MockConfig = {}) => {
  if (config.shouldFail) {
    throw new Error('OpenAI service unavailable');
  }
  
  return Promise.resolve({
    success: true,
    extracted_data: config.customResponse?.extracted_data || { 
      content: 'OpenAI processed content',
      entities: ['entity1', 'entity2']
    },
    confidence: config.customResponse?.confidence || 0.95,
    usage: config.customResponse?.usage || { 
      prompt_tokens: 100, 
      completion_tokens: 50,
      total_tokens: 150
    },
    model: 'gpt-4o-mini',
    cost_usd: 0.0045
  });
};

const generateClaudeResponse = (config: MockConfig = {}) => {
  if (config.shouldFail) {
    throw new Error('Claude service unavailable');
  }
  
  return Promise.resolve({
    success: true,
    extracted_data: config.customResponse?.extracted_data || { 
      content: 'Claude processed content',
      entities: ['entity1', 'entity2']
    },
    confidence: config.customResponse?.confidence || 0.92,
    usage: config.customResponse?.usage || { 
      input_tokens: 120, 
      output_tokens: 60,
      total_tokens: 180
    },
    model: 'claude-3-haiku',
    cost_usd: 0.0027
  });
};

const generateLlamaParseResponse = (config: MockConfig = {}) => {
  if (config.shouldFail) {
    throw new Error('LlamaParse service unavailable');
  }
  
  return Promise.resolve({
    success: true,
    extracted_data: config.customResponse?.extracted_data || { 
      content: 'LlamaParse processed content',
      entities: ['entity1', 'entity2']
    },
    confidence: config.customResponse?.confidence || 0.88,
    usage: config.customResponse?.usage || { 
      pages: 1,
      processing_time: 2500
    },
    model: 'llamaparse',
    markdown: 'Extracted markdown content',
    cost_usd: 0.003
  });
};

// Create configurable mock functions
export const createMockServices = (configs: {
  openai?: MockConfig;
  claude?: MockConfig;
  llamaparse?: MockConfig;
} = {}) => {
  
  const mockProcessWithOpenAI = mock((document: string, prompt: string, config: any) => {
    const mockConfig = { ...configs.openai, ...config };
    return generateOpenAIResponse(mockConfig);
  });

  const mockProcessWithClaude = mock((document: string, prompt: string, config: any) => {
    const mockConfig = { ...configs.claude, ...config };
    return generateClaudeResponse(mockConfig);
  });

  const mockProcessWithLlamaParse = mock((file: File, config: any) => {
    const mockConfig = { ...configs.llamaparse, ...config };
    return generateLlamaParseResponse(mockConfig);
  });

  const mockCheckOpenAIHealth = mock((apiKey: string) => {
    if (configs.openai?.shouldFail) {
      return Promise.resolve({
        status: 'unhealthy',
        latency_ms: -1,
        error: 'Service unavailable'
      });
    }
    return Promise.resolve({
      status: 'healthy',
      latency_ms: 150,
      last_check: new Date().toISOString()
    });
  });

  const mockCheckClaudeHealth = mock((apiKey: string) => {
    if (configs.claude?.shouldFail) {
      return Promise.resolve({
        status: 'unhealthy',
        latency_ms: -1,
        error: 'Service unavailable'
      });
    }
    return Promise.resolve({
      status: 'healthy',
      latency_ms: 200,
      last_check: new Date().toISOString()
    });
  });

  const mockCheckLlamaParseHealth = mock((apiKey: string) => {
    if (configs.llamaparse?.shouldFail) {
      return Promise.resolve({
        status: 'unhealthy',
        latency_ms: -1,
        error: 'Service unavailable'
      });
    }
    return Promise.resolve({
      status: 'healthy',
      latency_ms: 300,
      last_check: new Date().toISOString()
    });
  });

  return {
    processWithOpenAI: mockProcessWithOpenAI,
    processWithClaude: mockProcessWithClaude,
    processWithLlamaParse: mockProcessWithLlamaParse,
    checkOpenAIHealth: mockCheckOpenAIHealth,
    checkClaudeHealth: mockCheckClaudeHealth,
    checkLlamaParseHealth: mockCheckLlamaParseHealth
  };
};

// Default mock implementations
export const {
  processWithOpenAI: mockProcessWithOpenAI,
  processWithClaude: mockProcessWithClaude,
  processWithLlamaParse: mockProcessWithLlamaParse,
  checkOpenAIHealth: mockCheckOpenAIHealth,
  checkClaudeHealth: mockCheckClaudeHealth,
  checkLlamaParseHealth: mockCheckLlamaParseHealth
} = createMockServices();

// Network intercept mock for fetch
export const createNetworkInterceptor = () => {
  const originalFetch = global.fetch;
  
  const mockFetch = mock((input: RequestInfo | URL, init?: RequestInit) => {
    const url = typeof input === 'string' ? input : input.toString();
    
    // Block all real AI service calls
    if (url.includes('api.openai.com')) {
      return Promise.reject(new Error('Network intercept: OpenAI calls blocked in test environment'));
    }
    if (url.includes('api.anthropic.com')) {
      return Promise.reject(new Error('Network intercept: Claude calls blocked in test environment'));
    }
    if (url.includes('api.cloud.llamaindex.ai')) {
      return Promise.reject(new Error('Network intercept: LlamaParse calls blocked in test environment'));
    }
    
    // Allow other network calls (like local Supabase)
    if (url.includes('localhost') || url.includes('127.0.0.1')) {
      return originalFetch(input, init);
    }
    
    // Block unknown external calls
    return Promise.reject(new Error(`Network intercept: Unexpected external call to ${url}`));
  });

  // Replace global fetch
  global.fetch = mockFetch as any;
  
  return {
    restore: () => {
      global.fetch = originalFetch;
    },
    getMockFetch: () => mockFetch
  };
};

// Service configuration helpers for tests
export const createTestServiceConfigs = (overrides: {
  openai?: Partial<MockConfig>;
  claude?: Partial<MockConfig>;
  llamaparse?: Partial<MockConfig>;
} = {}) => {
  return {
    openai: {
      apiKey: 'test-openai-key',
      shouldFail: false,
      ...overrides.openai
    },
    claude: {
      apiKey: 'test-claude-key',
      shouldFail: false,
      ...overrides.claude
    },
    llamaparse: {
      apiKey: 'test-llamaparse-key',
      shouldFail: false,
      ...overrides.llamaparse
    }
  };
};

// Reset all mocks helper
export const resetAllServiceMocks = () => {
  mockProcessWithOpenAI.mockClear();
  mockProcessWithClaude.mockClear();
  mockProcessWithLlamaParse.mockClear();
  mockCheckOpenAIHealth.mockClear();
  mockCheckClaudeHealth.mockClear();
  mockCheckLlamaParseHealth.mockClear();
};