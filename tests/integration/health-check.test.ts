import { describe, it, expect, beforeAll, _afterAll as _afterAll } from 'bun:test';

// Health check integration tests for Epic 1 Story 5
interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    database: { status: string; latency_ms: number; error?: string };
    openai: { status: string; latency_ms: number; error?: string };
    claude: { status: string; latency_ms: number; error?: string };
    llamaparse: { status: string; latency_ms: number; error?: string };
  };
  performance: {
    response_time_ms: number;
    active_connections: number;
    edge_function_cold_start: boolean;
  };
  correlation_id: string;
}

describe('Health Check Endpoint Integration Tests', () => {
  const HEALTH_ENDPOINT = 'http://127.0.0.1:14321/functions/v1/health';
  const MAX_RESPONSE_TIME = 500; // <500ms requirement

  beforeAll(async () => {
    // Test if our health endpoint is reachable
    try {
      await fetch(HEALTH_ENDPOINT);
    } catch {
      throw new Error('Health endpoint not reachable. Ensure supabase functions serve is running.');
    }
  });

  describe('Basic Health Check Requirements', () => {
    it('should respond within 500ms', async () => {
      const startTime = performance.now();

      const response = await fetch(HEALTH_ENDPOINT);
      const responseTime = performance.now() - startTime;

      expect(response.ok).toBe(true);
      expect(responseTime).toBeLessThan(MAX_RESPONSE_TIME);
    });

    it('should return correct JSON structure', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(health).toHaveProperty('status');
      expect(health).toHaveProperty('timestamp');
      expect(health).toHaveProperty('version');
      expect(health).toHaveProperty('services');
      expect(health).toHaveProperty('performance');
      expect(health).toHaveProperty('correlation_id');

      // Validate services structure
      expect(health.services).toHaveProperty('database');
      expect(health.services).toHaveProperty('openai');
      expect(health.services).toHaveProperty('claude');
      expect(health.services).toHaveProperty('llamaparse');

      // Validate performance structure
      expect(health.performance).toHaveProperty('response_time_ms');
      expect(health.performance).toHaveProperty('active_connections');
      expect(health.performance).toHaveProperty('edge_function_cold_start');
    });

    it('should include valid correlation ID', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(health.correlation_id).toMatch(/^req_\d+_[a-z0-9]+$/);
    });
  });

  describe('Database Connectivity', () => {
    it('should check database connection status', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(health.services.database).toHaveProperty('status');
      expect(health.services.database).toHaveProperty('latency_ms');
      expect(typeof health.services.database.latency_ms).toBe('number');
      expect(health.services.database.latency_ms).toBeGreaterThan(0);
    });

    it('should have database latency under 50ms when healthy', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      if (health.services.database.status === 'healthy') {
        expect(health.services.database.latency_ms).toBeLessThan(50);
      }
    });
  });

  describe('AI Service Connectivity', () => {
    it('should check OpenAI service status', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(health.services.openai).toHaveProperty('status');
      expect(health.services.openai).toHaveProperty('latency_ms');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.services.openai.status);
    });

    it('should check Claude service status', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(health.services.claude).toHaveProperty('status');
      expect(health.services.claude).toHaveProperty('latency_ms');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.services.claude.status);
    });

    it('should check LlamaParse service status', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(health.services.llamaparse).toHaveProperty('status');
      expect(health.services.llamaparse).toHaveProperty('latency_ms');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.services.llamaparse.status);
    });

    it('should timeout AI service checks after 2 seconds', async () => {
      // This test validates that slow AI services don't block the health check
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      Object.values(health.services).forEach(service => {
        if (service.status === 'unhealthy' && service.error) {
          // If unhealthy due to timeout, latency should be close to 2000ms
          if (service.error.includes('timeout')) {
            expect(service.latency_ms).toBeGreaterThan(1900);
            expect(service.latency_ms).toBeLessThan(2100);
          }
        }
      });
    });
  });

  describe('Performance Metrics', () => {
    it('should include accurate response time metrics', async () => {
      const startTime = performance.now();
      const response = await fetch(HEALTH_ENDPOINT);
      const actualResponseTime = performance.now() - startTime;
      const health: HealthResponse = await response.json();

      // Response time should be reasonably close to actual measured time
      const reportedTime = health.performance.response_time_ms;
      expect(reportedTime).toBeGreaterThan(0);
      expect(Math.abs(reportedTime - actualResponseTime)).toBeLessThan(50); // Within 50ms margin
    });

    it('should report active connections', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(typeof health.performance.active_connections).toBe('number');
      expect(health.performance.active_connections).toBeGreaterThanOrEqual(0);
    });

    it('should detect cold start vs warm function', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      expect(typeof health.performance.edge_function_cold_start).toBe('boolean');
    });
  });

  describe('Overall Status Determination', () => {
    it('should be healthy when all services are healthy', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      const allServicesHealthy = Object.values(health.services)
        .every(service => service.status === 'healthy');

      if (allServicesHealthy) {
        expect(health.status).toBe('healthy');
      }
    });

    it('should be degraded when some services are unhealthy', async () => {
      // This test will pass when the system correctly determines degraded state
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      const someServicesUnhealthy = Object.values(health.services)
        .some(service => service.status === 'unhealthy');
      const databaseHealthy = health.services.database.status === 'healthy';

      if (someServicesUnhealthy && databaseHealthy) {
        expect(['healthy', 'degraded']).toContain(health.status);
      }
    });

    it('should be unhealthy when database is down', async () => {
      // This test validates that database connectivity is critical
      const response = await fetch(HEALTH_ENDPOINT);
      const health: HealthResponse = await response.json();

      if (health.services.database.status === 'unhealthy') {
        expect(health.status).toBe('unhealthy');
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle OPTIONS request for CORS', async () => {
      const response = await fetch(HEALTH_ENDPOINT, { method: 'OPTIONS' });

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('GET');
    });

    it('should reject non-GET requests', async () => {
      const response = await fetch(HEALTH_ENDPOINT, { method: 'POST' });

      expect(response.status).toBe(405);
      const error = await response.json();
      expect(error).toHaveProperty('error');
    });
  });

  describe('Logging and Tracing', () => {
    it('should generate unique correlation IDs per request', async () => {
      const response1 = await fetch(HEALTH_ENDPOINT);
      const response2 = await fetch(HEALTH_ENDPOINT);

      const health1: HealthResponse = await response1.json();
      const health2: HealthResponse = await response2.json();

      expect(health1.correlation_id).not.toBe(health2.correlation_id);
    });

    it('should include correlation ID in response headers', async () => {
      const response = await fetch(HEALTH_ENDPOINT);
      const correlationHeader = response.headers.get('X-Correlation-ID');

      expect(correlationHeader).toMatch(/^req_\d+_[a-z0-9]+$/);
    });
  });
});