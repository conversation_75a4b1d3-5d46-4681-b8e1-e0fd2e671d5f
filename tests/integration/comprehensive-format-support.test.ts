import { describe, it, expect, beforeAll as _beforeAll, _afterAll as _afterAll } from 'bun:test';
import { 
  FileUploadValidator,
  FileValidator,
  type FileValidationResult as _FileValidationResult 
} from '../../supabase/functions/_shared/file-validation.ts';

/**
 * Integration tests for comprehensive enterprise document format support
 * Tests the complete validation pipeline for 15+ document formats across 3 tiers
 */
describe('Comprehensive Format Support - Integration Tests', () => {
  
  describe('End-to-End Format Validation Pipeline', () => {
    
    describe('Tier 1: Critical Formats - Production Ready', () => {
      const tier1Formats = [
        {
          name: 'PDF Document',
          filename: 'invoice.pdf',
          mimeType: 'application/pdf',
          header: [0x25, 0x50, 0x44, 0x46], // %PDF
          expectedStrategy: 'openai:hybrid',
          category: 'document'
        },
        {
          name: 'Microsoft Word (DOCX)',
          filename: 'contract.docx',
          mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          header: [0x50, 0x4B, 0x03, 0x04], // ZIP
          expectedStrategy: 'openai:native',
          category: 'document'
        },
        {
          name: 'Microsoft Excel (XLSX)',
          filename: 'financial_report.xlsx',
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          header: [0x50, 0x4B, 0x03, 0x04], // ZIP
          expectedStrategy: 'openai:native',
          category: 'spreadsheet'
        },
        {
          name: 'JPEG Image',
          filename: 'receipt_scan.jpg',
          mimeType: 'image/jpeg',
          header: [0xFF, 0xD8, 0xFF], 
          expectedStrategy: 'openai:ocr',
          category: 'image'
        },
        {
          name: 'PNG Image',
          filename: 'document_scan.png',
          mimeType: 'image/png',
          header: [0x89, 0x50, 0x4E, 0x47], 
          expectedStrategy: 'openai:ocr',
          category: 'image'
        },
        // NEW Tier 1 Formats
        {
          name: 'Legacy Microsoft Word (DOC)',
          filename: 'legacy_document.doc',
          mimeType: 'application/msword',
          header: [0xD0, 0xCF, 0x11, 0xE0], // OLE Compound
          expectedStrategy: 'claude:native',
          category: 'document'
        },
        {
          name: 'Legacy Microsoft Excel (XLS)',
          filename: 'legacy_spreadsheet.xls',
          mimeType: 'application/vnd.ms-excel',
          header: [0xD0, 0xCF, 0x11, 0xE0], // OLE Compound
          expectedStrategy: 'claude:native',
          category: 'spreadsheet'
        },
        {
          name: 'OpenDocument Text (ODT)',
          filename: 'international_doc.odt',
          mimeType: 'application/vnd.oasis.opendocument.text',
          header: [0x50, 0x4B, 0x03, 0x04], // ZIP
          expectedStrategy: 'openai:native',
          category: 'document'
        },
        {
          name: 'OpenDocument Spreadsheet (ODS)',
          filename: 'international_sheet.ods',
          mimeType: 'application/vnd.oasis.opendocument.spreadsheet',
          header: [0x50, 0x4B, 0x03, 0x04], // ZIP
          expectedStrategy: 'openai:native',
          category: 'spreadsheet'
        },
        {
          name: 'Rich Text Format (RTF)',
          filename: 'formatted_document.rtf',
          mimeType: 'application/rtf',
          header: [0x7B, 0x5C, 0x72, 0x74], // {\rt
          expectedStrategy: 'claude:native',
          category: 'document'
        }
      ];

      tier1Formats.forEach(format => {
        it(`should validate and process ${format.name} files end-to-end`, async () => {
          // Create file with proper header
          const header = new Uint8Array(format.header);
          const content = format.mimeType === 'application/rtf' 
            ? new TextEncoder().encode('{\\rtf1\\ansi\\deff0 Sample RTF content}')
            : header;
          
          const testFile = new File([content], format.filename, { 
            type: format.mimeType 
          });

          // Test with production key (higher limits)
          const _result = await FileUploadValidator.validateUpload(testFile, 'production');

          // Validate successful processing
          expect(result.isValid).toBe(true);
          expect(result.metadata?.type).toBe(format.mimeType);
          expect(result.metadata?.originalName).toBe(format.filename);
          expect(result.metadata?.tier).toBe(1);
          expect(result.metadata?.category).toBe(format.category);
          expect(result.metadata?.processingStrategy).toBe(format.expectedStrategy);
          expect(result.metadata?.hash).toMatch(/^[a-f0-9]{64}$/);

          // Verify format-specific metadata
          expect(result.metadata?.processing).toBeDefined();
          expect(result.metadata?.createdAt).toBeInstanceOf(Date);
        });

        it(`should enforce proper file size limits for ${format.name}`, async () => {
          const header = new Uint8Array(format.header);
          const testFile = new File([header], format.filename, { 
            type: format.mimeType 
          });

          // Get expected limit for this format
          const expectedLimit = FileValidator.getMaxFileSizeForFormat(format.mimeType, 'test');
          
          // Mock file size to just exceed the limit
          Object.defineProperty(testFile, 'size', {
            value: expectedLimit + 1024, // 1KB over limit
            writable: false
          });

          const _result = await FileUploadValidator.validateUpload(testFile, 'test');

          expect(result.isValid).toBe(false);
          expect(result.error).toContain('File too large');
          expect(result.error).toContain(format.mimeType);
        });
      });
    });

    describe('Tier 2: Business Formats - Extended Support', () => {
      const tier2Formats = [
        {
          name: 'Microsoft PowerPoint (PPTX)',
          filename: 'presentation.pptx',
          mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          header: [0x50, 0x4B, 0x03, 0x04], // ZIP
          expectedStrategy: 'openai:native',
          category: 'presentation',
          sizeMultiplier: 1.5
        },
        {
          name: 'Legacy PowerPoint (PPT)',
          filename: 'legacy_presentation.ppt',
          mimeType: 'application/vnd.ms-powerpoint',
          header: [0xD0, 0xCF, 0x11, 0xE0], // OLE
          expectedStrategy: 'claude:native',
          category: 'presentation',
          sizeMultiplier: 1.5
        },
        {
          name: 'OpenDocument Presentation (ODP)',
          filename: 'international_presentation.odp',
          mimeType: 'application/vnd.oasis.opendocument.presentation',
          header: [0x50, 0x4B, 0x03, 0x04], // ZIP
          expectedStrategy: 'openai:native',
          category: 'presentation',
          sizeMultiplier: 1.5
        },
        {
          name: 'TIFF Image (Little Endian)',
          filename: 'high_res_scan.tiff',
          mimeType: 'image/tiff',
          header: [0x49, 0x49, 0x2A, 0x00], // TIFF LE
          expectedStrategy: 'llamaparse:ocr',
          category: 'image',
          sizeMultiplier: 2.0
        },
        {
          name: 'Plain Text',
          filename: 'notes.txt',
          mimeType: 'text/plain',
          header: null, // No magic number
          expectedStrategy: 'openai:direct',
          category: 'text',
          sizeMultiplier: 0.5
        },
        {
          name: 'CSV Data',
          filename: 'data_export.csv',
          mimeType: 'text/csv',
          header: null, // No magic number
          expectedStrategy: 'claude:structured',
          category: 'text',
          sizeMultiplier: 0.5
        }
      ];

      tier2Formats.forEach(format => {
        it(`should validate and process ${format.name} files with business-tier features`, async () => {
          // Create appropriate content
          let content: Uint8Array;
          if (format.header) {
            content = new Uint8Array(format.header);
          } else if (format.mimeType === 'text/plain') {
            content = new TextEncoder().encode('Sample text content for processing');
          } else if (format.mimeType === 'text/csv') {
            content = new TextEncoder().encode('name,value,date\nJohn,123,2024-01-01\nJane,456,2024-01-02');
          } else {
            content = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
          }

          const testFile = new File([content], format.filename, { 
            type: format.mimeType 
          });

          const _result = await FileUploadValidator.validateUpload(testFile, 'production');

          expect(result.isValid).toBe(true);
          expect(result.metadata?.type).toBe(format.mimeType);
          expect(result.metadata?.tier).toBe(2);
          expect(result.metadata?.category).toBe(format.category);
          expect(result.metadata?.processingStrategy).toBe(format.expectedStrategy);
        });

        it(`should apply correct size multiplier (${format.sizeMultiplier}x) for ${format.name}`, () => {
          const testLimit = FileValidator.getMaxFileSizeForFormat(format.mimeType, 'test');
          const prodLimit = FileValidator.getMaxFileSizeForFormat(format.mimeType, 'production');

          const expectedTestLimit = Math.floor(10 * 1024 * 1024 * format.sizeMultiplier);
          const expectedProdLimit = Math.floor(50 * 1024 * 1024 * format.sizeMultiplier);

          expect(testLimit).toBe(expectedTestLimit);
          expect(prodLimit).toBe(expectedProdLimit);
        });
      });

      it('should handle TIFF big-endian variant', async () => {
        const tiffBigEndian = new Uint8Array([0x4D, 0x4D, 0x00, 0x2A]); // TIFF BE
        const tiffFile = new File([tiffBigEndian], 'scan_be.tif', { type: 'image/tiff' });

        const _result = await FileUploadValidator.validateUpload(tiffFile, 'production');
        expect(result.isValid).toBe(true);
        expect(result.metadata?.processingStrategy).toBe('llamaparse:ocr');
      });
    });

    describe('Tier 3: Specialized Formats - Future Framework', () => {
      const tier3Formats = [
        {
          name: 'Apple Pages',
          filename: 'document.pages',
          mimeType: 'application/vnd.apple.pages',
          disabled: true
        },
        {
          name: 'HTML Document',
          filename: 'webpage.html',
          mimeType: 'text/html',
          disabled: true
        },
        {
          name: 'SVG Vector Image',
          filename: 'diagram.svg',
          mimeType: 'image/svg+xml',
          disabled: true
        }
      ];

      tier3Formats.forEach(format => {
        it(`should reject ${format.name} files (disabled experimental format)`, () => {
          const testFile = new File(['content'], format.filename, { 
            type: format.mimeType 
          });

          const _result = FileValidator.validateFileType(testFile);
          
          expect(result.isValid).toBe(false);
          expect(result.error).toContain('not currently enabled');
          expect(result.error).toContain('planned for future release');
        });
      });

      it('should provide framework for enabling Tier 3 formats', () => {
        const supportInfo = FileUploadValidator.getFormatSupportInfo();
        
        expect(supportInfo.tier3.length).toBeGreaterThanOrEqual(3);
        expect(supportInfo.categoriesByTier[3]).toBeDefined();
        
        // Verify experimental formats are defined but disabled
        const pagesInfo = FileValidator.getFormatInfo('application/vnd.apple.pages');
        expect(pagesInfo).toBeDefined();
        expect(pagesInfo?.tier).toBe(3);
        expect(pagesInfo?.enabled).toBe(false);
      });
    });
  });

  describe('Security Integration Testing', () => {
    
    describe('Format-Specific Security Validation', () => {
      it('should prevent RTF object embedding exploits', async () => {
        const maliciousRtf = new TextEncoder().encode(
          '{\\rtf1\\ansi\\deff0 {\\object\\objdata 414141414141} Malicious content}'
        );
        const rtfFile = new File([maliciousRtf], 'exploit.rtf', { type: 'application/rtf' });

        const _result = await FileUploadValidator.validateUpload(rtfFile, 'production');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('potentially dangerous object embeddings');
      });

      it('should prevent CSV formula injection attacks', async () => {
        const maliciousCsv = '=cmd|"/c calc.exe"|!A1,Normal Data\n+SUM(1+1),More Data';
        const csvFile = new File([maliciousCsv], 'inject.csv', { type: 'text/csv' });

        const _result = await FileUploadValidator.validateUpload(csvFile, 'production');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('potentially dangerous formula injection');
      });

      it('should detect file extension vs MIME type mismatches', async () => {
        // File claims to be PDF but has .txt extension
        const mismatchFile = new File(['content'], 'document.txt', { type: 'application/pdf' });

        const _result = await FileUploadValidator.validateUpload(mismatchFile, 'production');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('File extension mismatch');
      });

      it('should validate TIFF structure integrity', async () => {
        const invalidTiff = new Uint8Array([0x00, 0x00, 0x00, 0x00]); // Invalid TIFF header
        const tiffFile = new File([invalidTiff], 'broken.tiff', { type: 'image/tiff' });

        const _result = await FileUploadValidator.validateUpload(tiffFile, 'production');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('Invalid TIFF file structure');
      });
    });

    describe('File Size Security Enforcement', () => {
      it('should enforce presentation-specific size limits (100MB max)', async () => {
        const pptxHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]);
        const largePptx = new File([pptxHeader], 'huge.pptx', { 
          type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
        });

        // Mock size to exceed presentation limit
        Object.defineProperty(largePptx, 'size', {
          value: 110 * 1024 * 1024, // 110MB
          writable: false
        });

        const _result = await FileUploadValidator.validateUpload(largePptx, 'production');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('Presentation file too large');
      });

      it('should enforce spreadsheet-specific size limits (75MB max)', async () => {
        const xlsxHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]);
        const largeXlsx = new File([xlsxHeader], 'massive.xlsx', { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });

        Object.defineProperty(largeXlsx, 'size', {
          value: 80 * 1024 * 1024, // 80MB
          writable: false
        });

        const _result = await FileUploadValidator.validateUpload(largeXlsx, 'production');
        
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('Spreadsheet file too large');
      });
    });
  });

  describe('Performance and Scalability Testing', () => {
    
    it('should validate multiple formats concurrently without degradation', async () => {
      const formats = [
        { content: new Uint8Array([0x25, 0x50, 0x44, 0x46]), name: 'doc.pdf', type: 'application/pdf' },
        { content: new Uint8Array([0x50, 0x4B, 0x03, 0x04]), name: 'doc.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
        { content: new Uint8Array([0xD0, 0xCF, 0x11, 0xE0]), name: 'doc.doc', type: 'application/msword' },
        { content: new Uint8Array([0x49, 0x49, 0x2A, 0x00]), name: 'scan.tiff', type: 'image/tiff' },
        { content: new TextEncoder().encode('name,value\ntest,123'), name: 'data.csv', type: 'text/csv' }
      ];

      const startTime = performance.now();
      
      const validationPromises = formats.map(format => {
        const file = new File([format.content], format.name, { type: format.type });
        return FileUploadValidator.validateUpload(file, 'production');
      });

      const results = await Promise.all(validationPromises);
      const endTime = performance.now();

      // All validations should succeed
      results.forEach(result => {
        expect(result.isValid).toBe(true);
      });

      // Performance check: should complete in under 500ms total
      const totalTime = endTime - startTime;
      expect(totalTime).toBeLessThan(500);
    });

    it('should maintain consistent validation times across different format tiers', async () => {
      const tier1File = new File([new Uint8Array([0x25, 0x50, 0x44, 0x46])], 'tier1.pdf', { 
        type: 'application/pdf' 
      });
      const tier2File = new File([new Uint8Array([0x49, 0x49, 0x2A, 0x00])], 'tier2.tiff', { 
        type: 'image/tiff' 
      });

      const tier1Start = performance.now();
      const tier1Result = await FileUploadValidator.validateUpload(tier1File, 'production');
      const tier1Time = performance.now() - tier1Start;

      const tier2Start = performance.now();
      const tier2Result = await FileUploadValidator.validateUpload(tier2File, 'production');
      const tier2Time = performance.now() - tier2Start;

      expect(tier1Result.isValid).toBe(true);
      expect(tier2Result.isValid).toBe(true);
      
      // Validation times should be comparable (within 2x factor)
      expect(Math.abs(tier1Time - tier2Time)).toBeLessThan(Math.max(tier1Time, tier2Time));
    });
  });

  describe('Format Support Reporting', () => {
    
    it('should provide accurate format support metrics', () => {
      const supportInfo = FileUploadValidator.getFormatSupportInfo();
      
      // Verify minimum format counts per requirements
      expect(supportInfo.tier1.length).toBeGreaterThanOrEqual(10); // Original 5 + 5 new
      expect(supportInfo.tier2.length).toBeGreaterThanOrEqual(6);  // 6 new business formats
      expect(supportInfo.tier3.length).toBeGreaterThanOrEqual(3);  // 3 specialized formats
      expect(supportInfo.total).toBeGreaterThanOrEqual(15);        // 15+ total enterprise formats

      // Verify category distribution
      const tier1Categories = supportInfo.categoriesByTier[1];
      expect(tier1Categories).toHaveProperty('document');
      expect(tier1Categories).toHaveProperty('image');
      expect(tier1Categories).toHaveProperty('spreadsheet');
      
      const tier2Categories = supportInfo.categoriesByTier[2];
      expect(tier2Categories).toHaveProperty('presentation');
      expect(tier2Categories).toHaveProperty('text');
    });

    it('should correctly categorize all supported formats', () => {
      const allFormats = FileValidator.getSupportedTypes();
      
      allFormats.forEach(mimeType => {
        const formatInfo = FileValidator.getFormatInfo(mimeType);
        expect(formatInfo).toBeDefined();
        expect(formatInfo?.tier).toBeGreaterThanOrEqual(1);
        expect(formatInfo?.tier).toBeLessThanOrEqual(3);
        expect(['document', 'image', 'presentation', 'text', 'spreadsheet']).toContain(formatInfo?.category);
        expect(['hybrid', 'native', 'ocr', 'direct', 'structured', 'experimental']).toContain(formatInfo?.processing);
      });
    });
  });

  describe('Processing Strategy Integration', () => {
    
    it('should assign optimal AI models for different format categories', () => {
      const strategies = [
        { format: 'application/pdf', expected: { model: 'openai', extraction: 'hybrid' } },
        { format: 'application/msword', expected: { model: 'claude', extraction: 'native' } },
        { format: 'application/vnd.oasis.opendocument.text', expected: { model: 'openai', extraction: 'native' } },
        { format: 'image/tiff', expected: { model: 'llamaparse', extraction: 'ocr' } },
        { format: 'text/csv', expected: { model: 'claude', extraction: 'structured' } }
      ];

      strategies.forEach(({ format, expected }) => {
        const strategy = FileValidator.getProcessingStrategy(format);
        expect(strategy.primaryModel).toBe(expected.model);
        expect(strategy.extraction).toBe(expected.extraction);
      });
    });

    it('should provide fallback strategy for unknown formats', () => {
      const strategy = FileValidator.getProcessingStrategy('unknown/format');
      expect(strategy.primaryModel).toBe('openai');
      expect(strategy.extraction).toBe('hybrid');
    });
  });

  describe('Backward Compatibility Verification', () => {
    
    it('should maintain full compatibility with existing API contracts', async () => {
      // Test original 5 formats still work exactly as before
      const originalFormats = [
        { content: new Uint8Array([0x25, 0x50, 0x44, 0x46]), name: 'test.pdf', type: 'application/pdf' },
        { content: new Uint8Array([0x50, 0x4B, 0x03, 0x04]), name: 'test.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
        { content: new Uint8Array([0x50, 0x4B, 0x03, 0x04]), name: 'test.xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
        { content: new Uint8Array([0xFF, 0xD8, 0xFF]), name: 'test.jpg', type: 'image/jpeg' },
        { content: new Uint8Array([0x89, 0x50, 0x4E, 0x47]), name: 'test.png', type: 'image/png' }
      ];

      for (const format of originalFormats) {
        const file = new File([format.content], format.name, { type: format.type });
        
        // Test all legacy validation methods still work
        const typeResult = FileValidator.validateFileType(file);
        expect(typeResult.isValid).toBe(true);
        
        const headerResult = await FileValidator.validateFileHeader(file);
        expect(headerResult.isValid).toBe(true);
        
        const uploadResult = await FileUploadValidator.validateUpload(file, 'production');
        expect(uploadResult.isValid).toBe(true);
      }
    });

    it('should preserve original API response structure with new fields', async () => {
      const pdfFile = new File([new Uint8Array([0x25, 0x50, 0x44, 0x46])], 'test.pdf', { 
        type: 'application/pdf' 
      });

      const _result = await FileUploadValidator.validateUpload(pdfFile, 'production');
      
      // Original fields must be present
      expect(result.isValid).toBeDefined();
      expect(result.metadata?.size).toBeDefined();
      expect(result.metadata?.type).toBeDefined();
      expect(result.metadata?.originalName).toBeDefined();
      expect(result.metadata?.hash).toBeDefined();
      
      // New fields should be added without breaking existing structure
      expect(result.metadata?.tier).toBeDefined();
      expect(result.metadata?.category).toBeDefined();
      expect(result.metadata?.processing).toBeDefined();
      expect(result.metadata?.processingStrategy).toBeDefined();
    });
  });
});

describe('Real-World Format Validation Scenarios', () => {
  
  describe('Enterprise Document Workflows', () => {
    
    it('should handle international document formats (OpenDocument)', async () => {
      const formats = [
        { name: 'German_Rechnung.odt', type: 'application/vnd.oasis.opendocument.text' },
        { name: 'Japanese_データ.ods', type: 'application/vnd.oasis.opendocument.spreadsheet' },
        { name: 'French_Présentation.odp', type: 'application/vnd.oasis.opendocument.presentation' }
      ];

      for (const format of formats) {
        const header = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // ZIP
        const file = new File([header], format.name, { type: format.type });
        
        const _result = await FileUploadValidator.validateUpload(file, 'production');
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe(format.type);
      }
    });

    it('should handle legacy enterprise formats (Microsoft Office 97-2003)', async () => {
      const legacyFormats = [
        { name: 'legacy_contract.doc', type: 'application/msword', strategy: 'claude:native' },
        { name: 'financial_data.xls', type: 'application/vnd.ms-excel', strategy: 'claude:native' },
        { name: 'quarterly_review.ppt', type: 'application/vnd.ms-powerpoint', strategy: 'claude:native' }
      ];

      for (const format of legacyFormats) {
        const oleHeader = new Uint8Array([0xD0, 0xCF, 0x11, 0xE0]); // OLE Compound
        const file = new File([oleHeader], format.name, { type: format.type });
        
        const _result = await FileUploadValidator.validateUpload(file, 'production');
        expect(result.isValid).toBe(true);
        expect(result.metadata?.processingStrategy).toBe(format.strategy);
      }
    });

    it('should handle high-resolution document scans (TIFF)', async () => {
      const tiffVariants = [
        { name: 'blueprint_scan.tiff', header: [0x49, 0x49, 0x2A, 0x00] }, // Little Endian
        { name: 'archive_doc.tif', header: [0x4D, 0x4D, 0x00, 0x2A] }     // Big Endian
      ];

      for (const variant of tiffVariants) {
        const file = new File([new Uint8Array(variant.header)], variant.name, { 
          type: 'image/tiff' 
        });
        
        const _result = await FileUploadValidator.validateUpload(file, 'production');
        expect(result.isValid).toBe(true);
        expect(result.metadata?.processingStrategy).toBe('llamaparse:ocr');
        
        // TIFF files should get 2x size multiplier
        const expectedLimit = 100 * 1024 * 1024; // 50MB * 2.0 for production
        const actualLimit = FileValidator.getMaxFileSizeForFormat('image/tiff', 'production');
        expect(actualLimit).toBe(expectedLimit);
      }
    });
  });

  describe('Data Import/Export Scenarios', () => {
    
    it('should safely process structured data files (CSV)', async () => {
      const safeDatasets = [
        'employee_id,name,department\n1,John Doe,Engineering\n2,Jane Smith,Marketing',
        'date,revenue,expenses\n2024-01-01,50000,30000\n2024-01-02,55000,32000',
        'product,quantity,price\nWidget A,100,9.99\nWidget B,50,19.99'
      ];

      for (const dataset of safeDatasets) {
        const file = new File([dataset], 'data.csv', { type: 'text/csv' });
        
        const _result = await FileUploadValidator.validateUpload(file, 'production');
        expect(result.isValid).toBe(true);
        expect(result.metadata?.processingStrategy).toBe('claude:structured');
      }
    });

    it('should process plain text documents efficiently', async () => {
      const textContent = 'Meeting Notes\n\nAttendees: John, Jane, Bob\nDate: 2024-01-15\n\nAction Items:\n1. Review Q4 budget\n2. Plan team building event\n3. Update project timeline';
      
      const file = new File([textContent], 'meeting_notes.txt', { type: 'text/plain' });
      
      const _result = await FileUploadValidator.validateUpload(file, 'production');
      expect(result.isValid).toBe(true);
      expect(result.metadata?.processingStrategy).toBe('openai:direct');
      
      // Text files should get 0.5x size multiplier
      const expectedLimit = 25 * 1024 * 1024; // 50MB * 0.5 for production
      const actualLimit = FileValidator.getMaxFileSizeForFormat('text/plain', 'production');
      expect(actualLimit).toBe(expectedLimit);
    });
  });

  describe('Document Processing Quality Validation', () => {
    
    it('should ensure consistent validation across all supported formats', async () => {
      const supportInfo = FileUploadValidator.getFormatSupportInfo();
      
      // Test a sample from each tier
      const sampleFormats = [
        ...supportInfo.tier1.slice(0, 3),
        ...supportInfo.tier2.slice(0, 3)
      ];

      let allValidationsSucceeded = true;
      const validationTimes: number[] = [];

      for (const mimeType of sampleFormats) {
        const formatInfo = FileValidator.getFormatInfo(mimeType);
        if (!formatInfo) continue;

        // Create appropriate test content
        let content: Uint8Array;
        if (formatInfo.magicNumbers) {
          content = new Uint8Array(formatInfo.magicNumbers);
        } else {
          content = new TextEncoder().encode('Sample content for testing');
        }

        const file = new File([content], `test${formatInfo.extensions[0]}`, { type: mimeType });
        
        const startTime = performance.now();
        const _result = await FileUploadValidator.validateUpload(file, 'production');
        const endTime = performance.now();
        
        validationTimes.push(endTime - startTime);
        
        if (!result.isValid) {
          allValidationsSucceeded = false;
          console.error(`Validation failed for ${mimeType}:`, result.error);
        }
      }

      expect(allValidationsSucceeded).toBe(true);
      
      // Ensure consistent performance (no validation should take more than 100ms)
      validationTimes.forEach(time => {
        expect(time).toBeLessThan(100);
      });
    });
  });
});