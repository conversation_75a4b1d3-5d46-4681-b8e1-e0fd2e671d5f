/**
 * Integration Tests - Complete Usage Tracking Flow
 * 
 * End-to-end testing of the entire usage tracking and credit system
 * Tests all components working together: CreditManager, CostCalculator, UsageAggregator
 * Validates profit margins, billing exports, and real-time metrics
 * 
 * GitHub Issue #11 - Epic 2, Story 5: Usage Tracking & Credit System
 */

import { describe, it, expect, beforeAll, _afterAll, beforeEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/database.types';
import type {
  ProcessingResult,
  CustomerContext,
  CostCalculation as _CostCalculation,
  UsageAnalytics as _UsageAnalytics,
  BillingExport as _BillingExport
} from '../../types/usage-tracking.types';

// Test configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const FUNCTION_URL = `${SUPABASE_URL}/functions/v1/usage-tracking`;

// Test data
let testCustomerId: string;
let testApiKey: string;
let testApiKeyId: string;

describe('Usage Tracking Complete Flow Integration', () => {
  let supabase: ReturnType<typeof createClient<Database>>;

  beforeAll(async () => {
    // Initialize Supabase client
    supabase = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_KEY);

    // Verify Supabase connection
    const { data: _data, error } = await supabase.from('customers').select('count').limit(1);
    if (error) {
      throw new Error(`Failed to connect to Supabase: ${error.message}`);
    }

    console.log('✅ Supabase connection verified');
  });

  beforeEach(async () => {
    // Create test customer
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .insert({
        company_name: 'Test Integration Company',
        email: `test-integration-${Date.now()}@example.com`,
        tier: 'premium'
      })
      .select()
      .single();

    if (customerError || !customer) {
      throw new Error(`Failed to create test customer: ${customerError?.message}`);
    }

    testCustomerId = customer.id;

    // Generate test API key
    const { data: keyData, error: keyError } = await supabase.rpc('generate_api_key_secure', {
      p_customer_id: testCustomerId,
      p_key_type: 'test',
      p_credits: 1000,
      p_name: 'Integration Test Key'
    });

    if (keyError || !keyData?.[0]) {
      throw new Error(`Failed to generate test API key: ${keyError?.message}`);
    }

    testApiKey = keyData[0].raw_key;
    testApiKeyId = keyData[0].key_id;

    console.log(`✅ Test setup complete: Customer ${testCustomerId}, API Key ${testApiKeyId}`);
  });

  afterAll(async () => {
    // Cleanup test data
    if (testCustomerId) {
      await supabase.from('customers').delete().eq('id', testCustomerId);
      console.log('✅ Test cleanup complete');
    }
  });

  describe('End-to-End Document Processing Flow', () => {
    it('should complete full document processing with usage tracking', async () => {
      // Step 1: Simulate document processing
      const processingResult: ProcessingResult = {
        success: true,
        documentId: `doc-integration-${Date.now()}`,
        agentId: 'agent-invoice',
        model: 'gpt-4o-mini',
        inputTokens: 1000,
        outputTokens: 500,
        costUsd: 0.02,
        processingTimeMs: 2500,
        confidence: 0.95,
        extractedData: {
          invoice_number: 'INV-001',
          total: 125.50,
          vendor: 'Test Vendor Inc'
        }
      };

      const customerContext: CustomerContext = {
        id: testCustomerId,
        tier: 'premium',
        companyName: 'Test Integration Company',
        email: '<EMAIL>',
        status: 'active',
        settings: {}
      };

      // Step 2: Record usage through API
      const recordResponse = await fetch(`${FUNCTION_URL}/record`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testApiKey}`
        },
        body: JSON.stringify({
          processingResult,
          customerContext
        })
      });

      expect(recordResponse.status).toBe(200);
      
      const recordResult = await recordResponse.json();
      expect(recordResult.success).toBe(true);
      expect(recordResult.usage_recorded).toBe(true);
      
      // Validate cost calculation
      const costCalc = recordResult.cost_calculation;
      expect(costCalc.modelCostUsd).toBe(0.02);
      expect(costCalc.customerPriceUsd).toBeCloseTo(0.032, 3); // 60% markup for professional
      expect(costCalc.profitMarginPercent).toBeCloseTo(37.5, 1);
      expect(costCalc.creditsToDeduct).toBe(4); // Rounded up from 3.2

      // Validate credit transaction
      const creditTrans = recordResult.credit_transaction;
      expect(creditTrans.success).toBe(true);
      expect(creditTrans.amount).toBe(-4);
      expect(creditTrans.newBalance).toBe(996); // 1000 - 4

      console.log('✅ Document processing and usage recording completed');
    });

    it('should handle insufficient credits gracefully', async () => {
      // First, drain most credits
      await supabase
        .from('api_keys')
        .update({ credits: 2 }) // Only 2 credits left
        .eq('id', testApiKeyId);

      const processingResult: ProcessingResult = {
        success: true,
        documentId: `doc-insufficient-${Date.now()}`,
        agentId: 'agent-invoice',
        model: 'gpt-4o', // Expensive model requiring more credits
        inputTokens: 10000,
        outputTokens: 5000,
        costUsd: 1.0, // High cost
        processingTimeMs: 5000,
        confidence: 0.98
      };

      const customerContext: CustomerContext = {
        id: testCustomerId,
        tier: 'premium',
        companyName: 'Test Integration Company',
        email: '<EMAIL>',
        status: 'active',
        settings: {}
      };

      const recordResponse = await fetch(`${FUNCTION_URL}/record`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testApiKey}`
        },
        body: JSON.stringify({
          processingResult,
          customerContext
        })
      });

      expect(recordResponse.status).toBe(402); // Payment required
      
      const _result = await recordResponse.json();
      expect(result.success).toBe(false);
      expect(result.message).toContain('Insufficient credits');
      expect(result.available).toBe(2);
      expect(result.shortfall).toBeGreaterThan(0);

      console.log('✅ Insufficient credits handling verified');
    });
  });

  describe('Credit Management Operations', () => {
    it('should handle credit deduction and addition operations', async () => {
      // Test credit deduction
      const deductResponse = await fetch(`${FUNCTION_URL}/credits/deduct`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testApiKey}`
        },
        body: JSON.stringify({
          credits: 100,
          description: 'Integration test deduction'
        })
      });

      expect(deductResponse.status).toBe(200);
      
      const deductResult = await deductResponse.json();
      expect(deductResult.success).toBe(true);
      expect(deductResult.amount).toBe(-100);
      expect(deductResult.newBalance).toBe(900); // 1000 - 100

      // Test credit addition
      const addResponse = await fetch(`${FUNCTION_URL}/credits/add`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testApiKey}`
        },
        body: JSON.stringify({
          credits: 250,
          reason: 'Integration test top-up'
        })
      });

      expect(addResponse.status).toBe(200);
      
      const addResult = await addResponse.json();
      expect(addResult.success).toBe(true);
      expect(addResult.amount).toBe(250);
      expect(addResult.newBalance).toBe(1150); // 900 + 250

      // Verify balance
      const balanceResponse = await fetch(`${FUNCTION_URL}/credits/balance`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${testApiKey}`
        }
      });

      expect(balanceResponse.status).toBe(200);
      
      const balanceResult = await balanceResponse.json();
      expect(balanceResult.current_balance).toBe(1150);
      expect(balanceResult.api_key_id).toBe(testApiKeyId);
      expect(balanceResult.customer_id).toBe(testCustomerId);

      console.log('✅ Credit management operations verified');
    });

    it('should track usage statistics accurately', async () => {
      // Generate some usage first
      for (let i = 0; i < 3; i++) {
        await fetch(`${FUNCTION_URL}/credits/deduct`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${testApiKey}`
          },
          body: JSON.stringify({
            credits: 10 + i * 5,
            description: `Test transaction ${i + 1}`
          })
        });
      }

      // Get balance with usage statistics
      const balanceResponse = await fetch(`${FUNCTION_URL}/credits/balance?period_days=1`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${testApiKey}`
        }
      });

      expect(balanceResponse.status).toBe(200);
      
      const _result = await balanceResponse.json();
      const stats = result.usage_statistics;
      
      expect(stats.totalCreditsUsed).toBeGreaterThan(0);
      expect(stats.totalRequests).toBeGreaterThanOrEqual(3);
      expect(stats.averageCreditsPerRequest).toBeGreaterThan(0);
      expect(stats.periodStart).toBeDefined();
      expect(stats.periodEnd).toBeDefined();

      console.log('✅ Usage statistics tracking verified');
    });
  });

  describe('Analytics and Reporting', () => {
    it('should generate comprehensive usage analytics', async () => {
      // First generate some usage data
      const processingResults = [
        {
          model: 'gpt-4o-mini',
          inputTokens: 1000,
          outputTokens: 500,
          costUsd: 0.02
        },
        {
          model: 'claude-3-haiku',
          inputTokens: 1500,
          outputTokens: 600,
          costUsd: 0.025
        },
        {
          model: 'gpt-4o-mini',
          inputTokens: 800,
          outputTokens: 400,
          costUsd: 0.015
        }
      ];

      // Record multiple processing events
      for (const [index, processing] of processingResults.entries()) {
        const processingResult: ProcessingResult = {
          success: true,
          documentId: `doc-analytics-${index}`,
          agentId: 'agent-invoice',
          model: processing.model as any,
          inputTokens: processing.inputTokens,
          outputTokens: processing.outputTokens,
          costUsd: processing.costUsd,
          processingTimeMs: 2000 + Math.random() * 1000,
          confidence: 0.9 + Math.random() * 0.1
        };

        const customerContext: CustomerContext = {
          id: testCustomerId,
          tier: 'premium',
          companyName: 'Test Integration Company',
          email: '<EMAIL>',
          status: 'active',
          settings: {}
        };

        await fetch(`${FUNCTION_URL}/record`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${testApiKey}`
          },
          body: JSON.stringify({
            processingResult,
            customerContext
          })
        });
      }

      // Get analytics
      const today = new Date().toISOString().split('T')[0];
      const analyticsResponse = await fetch(
        `${FUNCTION_URL}/analytics?start_date=${today}&end_date=${today}&period=day`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${testApiKey}`
          }
        }
      );

      expect(analyticsResponse.status).toBe(200);
      
      const analytics = await analyticsResponse.json();
      
      expect(analytics.period).toBe('day');
      expect(analytics.totalRequests).toBeGreaterThanOrEqual(3);
      expect(analytics.successfulRequests).toBeGreaterThanOrEqual(3);
      expect(analytics.totalCostUsd).toBeGreaterThan(0);
      expect(analytics.totalRevenueUsd).toBeGreaterThan(analytics.totalCostUsd);
      expect(analytics.averageProfitMargin).toBeGreaterThan(0);
      
      // Validate model breakdown
      expect(analytics.modelBreakdown).toBeDefined();
      expect(analytics.modelBreakdown.length).toBeGreaterThan(0);
      
      const gptMiniBreakdown = analytics.modelBreakdown.find(
        (m: any) => m.modelName === 'gpt-4o-mini'
      );
      expect(gptMiniBreakdown).toBeDefined();
      expect(gptMiniBreakdown.requestCount).toBeGreaterThanOrEqual(2);

      // Validate customer breakdown
      expect(analytics.customerBreakdown).toBeDefined();
      expect(analytics.customerBreakdown.length).toBeGreaterThan(0);
      
      const customerBreakdown = analytics.customerBreakdown.find(
        (c: any) => c.customerId === testCustomerId
      );
      expect(customerBreakdown).toBeDefined();
      expect(customerBreakdown.companyName).toBe('Test Integration Company');

      console.log('✅ Usage analytics generation verified');
    });

    it('should generate accurate billing exports', async () => {
      // Get billing export
      const today = new Date().toISOString().split('T')[0];
      const billingResponse = await fetch(
        `${FUNCTION_URL}/billing?start_date=${today}&end_date=${today}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${testApiKey}`
          }
        }
      );

      expect(billingResponse.status).toBe(200);
      
      const billing = await billingResponse.json();
      
      expect(billing.customerId).toBe(testCustomerId);
      expect(billing.billingPeriod.start).toBeDefined();
      expect(billing.billingPeriod.end).toBeDefined();
      expect(billing.totalAmountUsd).toBeGreaterThanOrEqual(0);
      expect(billing.totalCreditsUsed).toBeGreaterThanOrEqual(0);
      expect(billing.requestCount).toBeGreaterThanOrEqual(0);
      expect(billing.lineItems).toBeDefined();
      
      // If there are line items, validate their structure
      if (billing.lineItems.length > 0) {
        const lineItem = billing.lineItems[0];
        expect(lineItem.date).toBeDefined();
        expect(lineItem.description).toBeDefined();
        expect(lineItem.amountUsd).toBeGreaterThan(0);
        expect(lineItem.creditsUsed).toBeGreaterThan(0);
        expect(lineItem.modelUsed).toBeDefined();
      }

      console.log('✅ Billing export generation verified');
    });

    it('should provide real-time metrics', async () => {
      const metricsResponse = await fetch(`${FUNCTION_URL}/metrics`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${testApiKey}`
        }
      });

      expect(metricsResponse.status).toBe(200);
      
      const metrics = await metricsResponse.json();
      
      expect(metrics.timestamp).toBeDefined();
      expect(metrics.activeApiKeys).toBeGreaterThanOrEqual(0);
      expect(metrics.requestsPerSecond).toBeGreaterThanOrEqual(0);
      expect(metrics.averageProcessingTimeMs).toBeGreaterThanOrEqual(0);
      expect(metrics.currentProfitMargin).toBeGreaterThanOrEqual(0);
      expect(metrics.creditsConsumedLastHour).toBeGreaterThanOrEqual(0);
      expect(metrics.errorRate).toBeGreaterThanOrEqual(0);
      expect(metrics.topModels).toBeDefined();
      expect(Array.isArray(metrics.topModels)).toBe(true);
      expect(metrics.alertCount).toBeGreaterThanOrEqual(0);

      console.log('✅ Real-time metrics verified');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid API keys', async () => {
      const invalidResponse = await fetch(`${FUNCTION_URL}/credits/balance`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer invalid-key-12345'
        }
      });

      expect(invalidResponse.status).toBe(401);
      
      const _result = await invalidResponse.json();
      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid API key');

      console.log('✅ Invalid API key handling verified');
    });

    it('should handle missing API keys', async () => {
      const noKeyResponse = await fetch(`${FUNCTION_URL}/credits/balance`, {
        method: 'GET'
        // No Authorization header
      });

      expect(noKeyResponse.status).toBe(401);
      
      const _result = await noKeyResponse.json();
      expect(result.success).toBe(false);
      expect(result.message).toContain('API key required');

      console.log('✅ Missing API key handling verified');
    });

    it('should handle invalid request data', async () => {
      const invalidResponse = await fetch(`${FUNCTION_URL}/credits/deduct`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testApiKey}`
        },
        body: JSON.stringify({
          credits: -50, // Invalid negative amount
          description: 'Invalid test'
        })
      });

      expect(invalidResponse.status).toBe(400);
      
      const _result = await invalidResponse.json();
      expect(result.success).toBe(false);
      expect(result.message).toContain('positive number');

      console.log('✅ Invalid request data handling verified');
    });

    it('should handle malformed JSON', async () => {
      const malformedResponse = await fetch(`${FUNCTION_URL}/record`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testApiKey}`
        },
        body: '{ invalid json'
      });

      expect(malformedResponse.status).toBe(500);
      
      const _result = await malformedResponse.json();
      expect(result.success).toBe(false);

      console.log('✅ Malformed JSON handling verified');
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle concurrent credit operations', async () => {
      // Perform multiple concurrent credit operations
      const concurrentOperations = Array.from({ length: 10 }, (_, i) => 
        fetch(`${FUNCTION_URL}/credits/deduct`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${testApiKey}`
          },
          body: JSON.stringify({
            credits: 1,
            description: `Concurrent test ${i + 1}`
          })
        })
      );

      const results = await Promise.all(concurrentOperations);
      
      // Count successful operations
      const successCount = results.filter(r => r.status === 200).length;
      
      expect(successCount).toBeGreaterThan(0);
      console.log(`✅ Concurrent operations: ${successCount}/10 successful`);
    });

    it('should complete operations within performance targets', async () => {
      const startTime = performance.now();
      
      const response = await fetch(`${FUNCTION_URL}/credits/balance`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${testApiKey}`
        }
      });

      const endTime = performance.now();
      const duration = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(500); // Should complete within 500ms
      
      console.log(`✅ Performance target met: ${duration.toFixed(2)}ms`);
    });
  });

  describe('Profit Margin Validation', () => {
    it('should maintain minimum profit margins across all tiers', async () => {
      const testCases = [
        { tier: 'standard', expectedMinMargin: 50.0 },    // Starter pricing
        { tier: 'premium', expectedMinMargin: 37.5 },     // Professional pricing  
        { tier: 'enterprise', expectedMinMargin: 28.57 }  // Enterprise pricing
      ];

      for (const testCase of testCases) {
        // Create customer with specific tier
        const { data: customer } = await supabase
          .from('customers')
          .insert({
            company_name: `Test ${testCase.tier} Customer`,
            email: `test-${testCase.tier}-${Date.now()}@example.com`,
            tier: testCase.tier
          })
          .select()
          .single();

        if (!customer) continue;

        // Generate API key for this customer
        const { data: keyData } = await supabase.rpc('generate_api_key_secure', {
          p_customer_id: customer.id,
          p_key_type: 'test',
          p_credits: 1000,
          p_name: `${testCase.tier} Test Key`
        });

        if (!keyData?.[0]) continue;

        // Process document to test margins
        const processingResult: ProcessingResult = {
          success: true,
          documentId: `doc-margin-test-${testCase.tier}`,
          agentId: 'agent-invoice',
          model: 'gpt-4o-mini',
          inputTokens: 1000,
          outputTokens: 500,
          costUsd: 0.02,
          processingTimeMs: 2500,
          confidence: 0.95
        };

        const customerContext: CustomerContext = {
          id: customer.id,
          tier: testCase.tier as any,
          companyName: customer.company_name,
          email: customer.email,
          status: 'active',
          settings: {}
        };

        const recordResponse = await fetch(`${FUNCTION_URL}/record`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${keyData[0].raw_key}`
          },
          body: JSON.stringify({
            processingResult,
            customerContext
          })
        });

        if (recordResponse.status === 200) {
          const _result = await recordResponse.json();
          const actualMargin = result.cost_calculation.profitMarginPercent;
          
          expect(actualMargin).toBeCloseTo(testCase.expectedMinMargin, 1);
          console.log(`✅ ${testCase.tier} tier margin: ${actualMargin.toFixed(2)}%`);
        }

        // Cleanup
        await supabase.from('customers').delete().eq('id', customer.id);
      }
    });
  });
});