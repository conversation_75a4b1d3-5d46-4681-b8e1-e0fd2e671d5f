/**
 * Agent Cloning System Integration Tests
 * GitHub Issue #15: Agent Cloning System
 * 
 * Tests comprehensive agent cloning functionality including:
 * - Basic agent cloning
 * - Permission validation
 * - Tier limits enforcement
 * - Naming conflict resolution
 * - Bulk cloning
 * - Audit logging
 * - Parent-child relationships
 */

import { describe, it, expect, _beforeEach, _afterEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../supabase/types/database.types';

type Agent = Database['public']['Tables']['agents']['Row'];
type _Customer = Database['public']['Tables']['customers']['Row'];
type _ApiKey = Database['public']['Tables']['api_keys']['Row'];

// Test configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const TEST_API_ENDPOINT = `${SUPABASE_URL}/functions/v1`;

// Initialize Supabase client with service role for setup
const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Test data interfaces
interface TestCustomer {
  id: string;
  email: string;
  company_name: string;
  tier: 'free' | 'starter' | 'professional' | 'enterprise';
}

interface TestApiKey {
  id: string;
  key_hash: string;
  raw_key: string;
  customer_id: string;
  key_type: 'test' | 'production';
  credits: number;
}

interface CloneAgentRequest {
  source_agent_id: string;
  name?: string;
  description?: string;
  category?: string;
  customize_immediately?: boolean;
}

interface CloneAgentResponse {
  success: boolean;
  cloned_agent?: {
    id: string;
    name: string;
    parent_agent_id: string;
    customer_id: string;
    is_customizable: boolean;
    cloned_at: string;
  };
  customization_url?: string;
  error?: string;
}

interface BulkCloneRequest {
  source_agent_ids: string[];
  name_prefix?: string;
  category_override?: string;
}

interface BulkCloneResponse {
  success: boolean;
  successful_clones: Array<{
    source_agent_id: string;
    cloned_agent_id: string;
    cloned_agent_name: string;
  }>;
  failed_clones: Array<{
    agent_id: string;
    error: string;
  }>;
  total_requested: number;
  successful_count: number;
  failed_count: number;
}

// Test data storage
let testCustomers: TestCustomer[] = [];
let testApiKeys: TestApiKey[] = [];

let defaultAgents: Agent[] = [];

describe('Agent Cloning System', () => {
  
  beforeEach(async () => {
    // Clean up any existing test data
    await cleanupTestData();
    
    // Create test customers with different tiers
    testCustomers = await createTestCustomers();
    
    // Create test API keys for each customer
    testApiKeys = await createTestApiKeys();
    
    // Get default agents for cloning
    defaultAgents = await getDefaultAgents();
    
    // Verify we have at least one default agent
    expect(defaultAgents.length).toBeGreaterThan(0);
  });

  afterEach(async () => {
    // Clean up test data after each test
    await cleanupTestData();
  });

  describe('Basic Agent Cloning', () => {
    
    it('should successfully clone a default agent', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'My Custom Invoice Agent',
        description: 'Customized for my specific invoice format'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      expect(response.status).toBe(200);
      
      const result: CloneAgentResponse = await response.json();
      expect(result.success).toBe(true);
      expect(result.cloned_agent).toBeDefined();
      expect(result.cloned_agent!.name).toBe('My Custom Invoice Agent');
      expect(result.cloned_agent!.parent_agent_id).toBe(sourceAgent.id);
      expect(result.cloned_agent!.customer_id).toBe(customer.id);
      expect(result.cloned_agent!.is_customizable).toBe(true);

      // Verify agent was created in database
      const { data: clonedAgent } = await supabase
        .from('agents')
        .select('*')
        .eq('id', result.cloned_agent!.id)
        .single();

      expect(clonedAgent).toBeTruthy();
      expect(clonedAgent!.customer_id).toBe(customer.id);
      expect(clonedAgent!.parent_agent_id).toBe(sourceAgent.id);
      expect(clonedAgent!.is_default).toBe(false);
    });

    it('should inherit prompt and schema from parent agent', async () => {
      const customer = testCustomers.find(c => c.tier === 'starter')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'Inherited Agent Test'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const result: CloneAgentResponse = await response.json();
      expect(result.success).toBe(true);

      // Get full cloned agent details
      const { data: clonedAgent } = await supabase
        .from('agents')
        .select('*')
        .eq('id', result.cloned_agent!.id)
        .single();

      expect(clonedAgent!.prompt).toBe(sourceAgent.prompt);
      expect(clonedAgent!.json_schema).toEqual(sourceAgent.json_schema);
      expect(clonedAgent!.category).toBe(sourceAgent.category);
    });

    it('should set customization URL when requested', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'Immediate Customization Agent',
        customize_immediately: true
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const result: CloneAgentResponse = await response.json();
      expect(result.success).toBe(true);
      expect(result.customization_url).toBeDefined();
      expect(result.customization_url).toContain('/customize');
    });
  });

  describe('Permission Validation', () => {
    
    it('should reject cloning with invalid API key', async () => {
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'Should Fail'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': 'invalid_key_12345'
        },
        body: JSON.stringify(cloneRequest)
      });

      expect(response.status).toBe(401);
    });

    it('should reject cloning non-existent agent', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: '00000000-0000-0000-0000-000000000000',
        name: 'Should Fail'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      expect(response.status).toBe(404);
      
      const result: CloneAgentResponse = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('not found');
    });

    it('should allow cloning own custom agents', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      // First clone a default agent
      const firstCloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'First Clone'
      };

      const firstResponse = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(firstCloneRequest)
      });

      const firstResult: CloneAgentResponse = await firstResponse.json();
      expect(firstResult.success).toBe(true);

      // Now clone the cloned agent
      const secondCloneRequest: CloneAgentRequest = {
        source_agent_id: firstResult.cloned_agent!.id,
        name: 'Clone of Clone'
      };

      const secondResponse = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(secondCloneRequest)
      });

      const secondResult: CloneAgentResponse = await secondResponse.json();
      expect(secondResult.success).toBe(true);
      expect(secondResult.cloned_agent!.parent_agent_id).toBe(firstResult.cloned_agent!.id);
    });
  });

  describe('Tier Limits Enforcement', () => {
    
    it('should enforce free tier clone limits', async () => {
      const customer = testCustomers.find(c => c.tier === 'free')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      // Free tier should allow 2 clones maximum
      const clonePromises = [];
      
      for (let i = 0; i < 3; i++) {
        const cloneRequest: CloneAgentRequest = {
          source_agent_id: sourceAgent.id,
          name: `Free Tier Clone ${i + 1}`
        };

        clonePromises.push(
          fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': apiKey.raw_key
            },
            body: JSON.stringify(cloneRequest)
          })
        );
      }

      const responses = await Promise.all(clonePromises);
      const results = await Promise.all(responses.map(r => r.json()));

      // First 2 should succeed
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
      
      // Third should fail due to limit
      expect(results[2].success).toBe(false);
      expect(results[2].error).toContain('limit');
    });

    it('should allow unlimited clones for enterprise tier', async () => {
      const customer = testCustomers.find(c => c.tier === 'enterprise')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      // Enterprise should allow many clones
      const clonePromises = [];
      
      for (let i = 0; i < 5; i++) {
        const cloneRequest: CloneAgentRequest = {
          source_agent_id: sourceAgent.id,
          name: `Enterprise Clone ${i + 1}`
        };

        clonePromises.push(
          fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'apikey': apiKey.raw_key
            },
            body: JSON.stringify(cloneRequest)
          })
        );
      }

      const responses = await Promise.all(clonePromises);
      const results = await Promise.all(responses.map(r => r.json()));

      // All should succeed for enterprise
      results.forEach((result, _index) => {
        expect(result.success).toBe(true);
      });
    });
  });

  describe('Naming Conflict Resolution', () => {
    
    it('should reject duplicate names for same customer', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'Duplicate Name Test'
      };

      // First clone should succeed
      const firstResponse = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const firstResult: CloneAgentResponse = await firstResponse.json();
      expect(firstResult.success).toBe(true);

      // Second clone with same name should fail
      const secondResponse = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const secondResult: CloneAgentResponse = await secondResponse.json();
      expect(secondResult.success).toBe(false);
      expect(secondResult.error).toContain('already exists');
    });

    it('should allow same names for different customers', async () => {
      const customer1 = testCustomers.find(c => c.tier === 'professional')!;
      const customer2 = testCustomers.find(c => c.tier === 'starter')!;
      const apiKey1 = testApiKeys.find(k => k.customer_id === customer1.id)!;
      const apiKey2 = testApiKeys.find(k => k.customer_id === customer2.id)!;
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'Shared Name Test'
      };

      // First customer clones
      const firstResponse = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey1.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const firstResult: CloneAgentResponse = await firstResponse.json();
      expect(firstResult.success).toBe(true);

      // Second customer clones with same name - should succeed
      const secondResponse = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey2.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const secondResult: CloneAgentResponse = await secondResponse.json();
      expect(secondResult.success).toBe(true);
      expect(secondResult.cloned_agent!.name).toBe('Shared Name Test');
    });
  });

  describe('Bulk Cloning', () => {
    
    it('should successfully clone multiple agents', async () => {
      const customer = testCustomers.find(c => c.tier === 'enterprise')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      
      // Get multiple default agents
      const sourceAgentIds = defaultAgents.slice(0, 3).map(a => a.id);

      const bulkRequest: BulkCloneRequest = {
        source_agent_ids: sourceAgentIds,
        name_prefix: 'Bulk Test'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(bulkRequest)
      });

      expect(response.status).toBe(200);
      
      const result: BulkCloneResponse = await response.json();
      expect(result.success).toBe(true);
      expect(result.successful_count).toBe(3);
      expect(result.failed_count).toBe(0);
      expect(result.successful_clones.length).toBe(3);

      // Verify each clone was created with proper naming
      result.successful_clones.forEach((clone, _index) => {
        expect(clone.cloned_agent_name).toContain('Bulk Test');
        expect(clone.source_agent_id).toBe(sourceAgentIds[index]);
      });
    });

    it('should handle partial failures in bulk cloning', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      
      const sourceAgentIds = [
        defaultAgents[0].id,
        '00000000-0000-0000-0000-000000000000', // Invalid ID
        defaultAgents[1].id
      ];

      const bulkRequest: BulkCloneRequest = {
        source_agent_ids: sourceAgentIds,
        name_prefix: 'Partial Test'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone/bulk`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(bulkRequest)
      });

      const result: BulkCloneResponse = await response.json();
      expect(result.success).toBe(true); // Overall success even with partial failures
      expect(result.successful_count).toBe(2);
      expect(result.failed_count).toBe(1);
      expect(result.failed_clones.length).toBe(1);
      expect(result.failed_clones[0].agent_id).toBe('00000000-0000-0000-0000-000000000000');
    });
  });

  describe('Audit Logging', () => {
    
    it('should log successful clone operations', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;
      const sourceAgent = defaultAgents[0];

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: sourceAgent.id,
        name: 'Audit Log Test'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      const result: CloneAgentResponse = await response.json();
      expect(result.success).toBe(true);

      // Check audit log was created
      const { data: auditLogs } = await supabase
        .from('audit_logs')
        .select('*')
        .eq('customer_id', customer.id)
        .eq('action', 'agent_clone')
        .eq('resource_id', result.cloned_agent!.id);

      expect(auditLogs).toBeTruthy();
      expect(auditLogs!.length).toBeGreaterThan(0);
      
      const logEntry = auditLogs![0];
      expect(logEntry.success).toBe(true);
      expect(logEntry.api_key_id).toBe(apiKey.id);
      expect(logEntry.resource_type).toBe('agent');
      
      // Check metadata contains source agent info
      const metadata = logEntry.metadata as any;
      expect(metadata.source_agent_id).toBe(sourceAgent.id);
      expect(metadata.cloned_agent_id).toBe(result.cloned_agent!.id);
    });

    it('should log failed clone attempts', async () => {
      const customer = testCustomers.find(c => c.tier === 'professional')!;
      const apiKey = testApiKeys.find(k => k.customer_id === customer.id)!;

      const cloneRequest: CloneAgentRequest = {
        source_agent_id: '00000000-0000-0000-0000-000000000000',
        name: 'Failed Clone Test'
      };

      const response = await fetch(`${TEST_API_ENDPOINT}/agents/clone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': apiKey.raw_key
        },
        body: JSON.stringify(cloneRequest)
      });

      expect(response.status).toBe(404);

      // Check failed audit log was created
      const { data: auditLogs } = await supabase
        .from('audit_logs')
        .select('*')
        .eq('customer_id', customer.id)
        .eq('action', 'agent_clone')
        .eq('success', false);

      expect(auditLogs).toBeTruthy();
      expect(auditLogs!.length).toBeGreaterThan(0);
      
      const logEntry = auditLogs![0];
      expect(logEntry.success).toBe(false);
      expect(logEntry.error_message).toContain('not found');
    });
  });
});

// Helper functions

async function cleanupTestData(): Promise<void> {
  // Delete test agents (cloned ones)
  if (testCustomers.length > 0) {
    const customerIds = testCustomers.map(c => c.id);
    await supabase
      .from('agents')
      .delete()
      .in('customer_id', customerIds);
  }

  // Delete test API keys
  if (testApiKeys.length > 0) {
    const keyIds = testApiKeys.map(k => k.id);
    await supabase
      .from('api_keys')
      .delete()
      .in('id', keyIds);
  }

  // Delete test audit logs
  if (testCustomers.length > 0) {
    const customerIds = testCustomers.map(c => c.id);
    await supabase
      .from('audit_logs')
      .delete()
      .in('customer_id', customerIds);
  }

  // Delete test customers
  if (testCustomers.length > 0) {
    const customerIds = testCustomers.map(c => c.id);
    await supabase
      .from('customers')
      .delete()
      .in('id', customerIds);
  }

  // Reset arrays
  testCustomers = [];
  testApiKeys = [];
}

async function createTestCustomers(): Promise<TestCustomer[]> {
  const customers: TestCustomer[] = [
    {
      id: '10000000-0000-0000-0000-000000000001',
      email: '<EMAIL>',
      company_name: 'Free Tier Test Co',
      tier: 'free'
    },
    {
      id: '10000000-0000-0000-0000-000000000002',
      email: '<EMAIL>',
      company_name: 'Starter Tier Test Co',
      tier: 'starter'
    },
    {
      id: '10000000-0000-0000-0000-000000000003',
      email: '<EMAIL>',
      company_name: 'Professional Tier Test Co',
      tier: 'professional'
    },
    {
      id: '10000000-0000-0000-0000-000000000004',
      email: '<EMAIL>',
      company_name: 'Enterprise Tier Test Co',
      tier: 'enterprise'
    }
  ];

  await supabase.from('customers').insert(
    customers.map(c => ({
      id: c.id,
      email: c.email,
      company_name: c.company_name,
      tier: c.tier,
      status: 'active'
    }))
  );

  return customers;
}

async function createTestApiKeys(): Promise<TestApiKey[]> {
  const apiKeys: TestApiKey[] = [];

  for (const customer of testCustomers) {
    const rawKey = `skt_test_${customer.tier}_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
    const keyHash = await hashApiKey(rawKey);
    
    const apiKey: TestApiKey = {
      id: `20000000-0000-0000-0000-00000000000${testCustomers.indexOf(customer) + 1}`,
      key_hash: keyHash,
      raw_key: rawKey,
      customer_id: customer.id,
      key_type: 'test',
      credits: 1000
    };

    await supabase.from('api_keys').insert({
      id: apiKey.id,
      key_hash: apiKey.key_hash,
      key_prefix: rawKey.substring(0, 8),
      customer_id: apiKey.customer_id,
      key_type: apiKey.key_type,
      credits: apiKey.credits,
      name: `Test Key for ${customer.company_name}`,
      status: 'active',
      permissions: { clone_agents: true },
      rate_limits: { requests_per_minute: 100 }
    });

    apiKeys.push(apiKey);
  }

  return apiKeys;
}

async function getDefaultAgents(): Promise<Agent[]> {
  const { data: agents } = await supabase
    .from('agents')
    .select('*')
    .eq('is_default', true)
    .eq('status', 'active');

  return agents || [];
}

// Simple hash function for test API keys
async function hashApiKey(rawKey: string): Promise<string> {
  const encoder = new TextEncoder();
  const _data = encoder.encode(rawKey);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}