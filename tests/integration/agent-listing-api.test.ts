import { describe, it, expect, beforeAll } from 'bun:test';

// Integration tests for Agent Listing API
describe('Agent Listing API Integration', () => {
  const baseUrl = 'http://localhost:14321/functions/v1';
  const anonToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

  beforeAll(async () => {
    // Verify Supabase is running on correct port
    try {
      const healthCheck = await fetch(`${baseUrl}/../health`);
      if (!healthCheck.ok) {
        throw new Error('Supabase Edge Functions not running. Run: supabase start');
      }
    } catch {
      console.log('Health check failed, assuming Supabase is running...');
    }
  });

  describe('GET /agents - Agent Listing', () => {
    it('should list agents for authenticated customer', async () => {
      const response = await fetch(`${baseUrl}/agents`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agents).toBeDefined();
      expect(Array.isArray(data.agents)).toBe(true);
      expect(data.pagination).toBeDefined();
      expect(data.pagination.current_page).toBe(1);
      expect(data.pagination.total_count).toBeGreaterThan(0);
      expect(data.filters_applied).toBeDefined();

      // Check agent structure
      if (data.agents.length > 0) {
        const agent = data.agents[0];
        expect(agent.id).toBeDefined();
        expect(agent.name).toBeDefined();
        expect(agent.category).toBeDefined();
        expect(agent.description).toBeDefined();
        expect(typeof agent.is_default).toBe('boolean');
        expect(agent.use_cases).toBeDefined();
        expect(Array.isArray(agent.use_cases)).toBe(true);
        expect(agent.supported_formats).toBeDefined();
        expect(Array.isArray(agent.supported_formats)).toBe(true);
        expect(typeof agent.accuracy_rating).toBe('number');
        expect(typeof agent.avg_processing_time_ms).toBe('number');
        expect(agent.created_at).toBeDefined();
        expect(agent.updated_at).toBeDefined();
      }
    });

    it('should filter agents by category', async () => {
      const response = await fetch(`${baseUrl}/agents?category=invoice`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agents).toBeDefined();
      expect(data.filters_applied.category).toBe('invoice');
      
      // All returned agents should be invoice category
      data.agents.forEach((agent: any) => {
        expect(agent.category).toBe('invoice');
      });
    });

    it('should filter agents by is_default=true', async () => {
      const response = await fetch(`${baseUrl}/agents?is_default=true`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agents).toBeDefined();
      expect(data.filters_applied.is_default).toBe(true);
      
      // All returned agents should be default agents
      data.agents.forEach((agent: any) => {
        expect(agent.is_default).toBe(true);
      });
    });

    it('should paginate results correctly', async () => {
      const response = await fetch(`${baseUrl}/agents?page=1&limit=2`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agents.length).toBeLessThanOrEqual(2);
      expect(data.pagination.current_page).toBe(1);
      expect(data.pagination.page_size).toBe(2);
      expect(data.pagination.total_pages).toBeGreaterThanOrEqual(1);
    });

    it('should search agents by keyword', async () => {
      const response = await fetch(`${baseUrl}/agents?search=invoice`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agents).toBeDefined();
      expect(data.filters_applied.search).toBe('invoice');
      
      // Results should be relevant to 'invoice'
      expect(data.agents.length).toBeGreaterThan(0);
      const hasInvoiceContent = data.agents.some((agent: any) => 
        agent.name.toLowerCase().includes('invoice') ||
        agent.description.toLowerCase().includes('invoice') ||
        agent.category === 'invoice'
      );
      expect(hasInvoiceContent).toBe(true);
    });

    it('should include cache headers', async () => {
      const response = await fetch(`${baseUrl}/agents`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('cache-control')).toBe('max-age=300');
    });
  });

  describe('GET /agents/{id} - Agent Details', () => {
    it('should get detailed agent information', async () => {
      // First get the list to find an agent ID
      const listResponse = await fetch(`${baseUrl}/agents`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      const listData = await listResponse.json();
      expect(listData.agents.length).toBeGreaterThan(0);
      
      const agentId = listData.agents[0].id;

      // Now get the detailed information
      const response = await fetch(`${baseUrl}/agents/${agentId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agent).toBeDefined();
      
      const agent = data.agent;
      expect(agent.id).toBe(agentId);
      expect(agent.name).toBeDefined();
      expect(agent.category).toBeDefined();
      expect(agent.description).toBeDefined();
      expect(agent.system_prompt).toBeDefined();
      expect(agent.output_schema).toBeDefined();
      expect(typeof agent.output_schema).toBe('object');
      expect(typeof agent.is_default).toBe('boolean');
      expect(agent.use_cases).toBeDefined();
      expect(Array.isArray(agent.use_cases)).toBe(true);
      expect(agent.supported_formats).toBeDefined();
      expect(Array.isArray(agent.supported_formats)).toBe(true);
      expect(typeof agent.accuracy_rating).toBe('number');
      expect(typeof agent.avg_processing_time_ms).toBe('number');
      expect(agent.created_at).toBeDefined();
      expect(agent.updated_at).toBeDefined();
    });

    it('should return 404 for non-existent agent', async () => {
      const response = await fetch(`${baseUrl}/agents/non-existent-agent-id`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(404);
      
      const _data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('not found');
    });

    it('should include cache headers for agent details', async () => {
      // Get an agent ID first
      const listResponse = await fetch(`${baseUrl}/agents`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });
      const listData = await listResponse.json();
      const agentId = listData.agents[0].id;

      const response = await fetch(`${baseUrl}/agents/${agentId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('cache-control')).toBe('max-age=300');
    });
  });

  describe('Complex Filtering', () => {
    it('should combine multiple filters', async () => {
      const response = await fetch(`${baseUrl}/agents?category=invoice&is_default=true&limit=5`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.filters_applied.category).toBe('invoice');
      expect(data.filters_applied.is_default).toBe(true);
      expect(data.pagination.page_size).toBe(5);
      
      // All returned agents should match filters
      data.agents.forEach((agent: any) => {
        expect(agent.category).toBe('invoice');
        expect(agent.is_default).toBe(true);
      });
    });

    it('should return empty results for non-matching filters', async () => {
      const response = await fetch(`${baseUrl}/agents?category=non-existent-category`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      expect(data.agents).toBeDefined();
      expect(data.agents.length).toBe(0);
      expect(data.filters_applied.category).toBe('non-existent-category');
    });
  });

  describe('Error Handling', () => {
    it('should reject requests without authentication', async () => {
      const response = await fetch(`${baseUrl}/agents`, {
        method: 'GET'
      });

      expect(response.status).toBe(401);
    });

    it('should handle invalid pagination parameters gracefully', async () => {
      const response = await fetch(`${baseUrl}/agents?page=invalid&limit=invalid`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${anonToken}`
        }
      });

      expect(response.status).toBe(200);
      
      const _data = await response.json();
      // Should default to reasonable values
      expect(data.pagination.current_page).toBe(1);
      expect(data.pagination.page_size).toBe(10);
    });
  });
});