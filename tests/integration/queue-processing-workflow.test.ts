/**
 * Queue Processing Workflow Integration Tests
 * 
 * End-to-end tests for the complete queue processing workflow:
 * - Document upload → Queue → Processing → Results
 * - Job status tracking and real-time updates
 * - Error handling and retry scenarios
 * - Performance and timeout testing
 * - Dead letter queue functionality
 */

import { describe, it, expect, beforeAll, _afterAll, beforeEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';

// =============================================================================
// TEST CONFIGURATION
// =============================================================================

const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'test-anon-key';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-key';

// Test API endpoints
const EXTRACT_ENDPOINT = `${SUPABASE_URL}/functions/v1/extract`;
const QUEUE_PROCESSOR_ENDPOINT = `${SUPABASE_URL}/functions/v1/queue-processor`;
const JOB_STATUS_ENDPOINT = `${SUPABASE_URL}/functions/v1/job-status`;

// Test data
const TEST_API_KEY = 'skt_test123456789abcdef'; // Test key
const TEST_CUSTOMER_ID = '123e4567-e89b-12d3-a456-426614174000';

// Initialize Supabase clients
const supabaseAdmin = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const _supabaseAnon = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// =============================================================================
// TEST UTILITIES
// =============================================================================

interface TestCustomer {
  id: string;
  company_name: string;
  email: string;
  tier: string;
  status: string;
}

interface TestApiKey {
  id: string;
  customer_id: string;
  key_type: string;
  key_hash: string;
  key_prefix: string;
  name: string;
  credits: number;
  revoked: boolean;
}

class TestDataSetup {
  /**
   * Create test customer and API key for integration tests
   */
  static async setupTestCustomer(): Promise<{ customer: TestCustomer; apiKey: TestApiKey }> {
    // Create test customer
    const { data: customer, error: customerError } = await supabaseAdmin
      .from('customers')
      .insert({
        id: TEST_CUSTOMER_ID,
        company_name: 'Test Company',
        email: '<EMAIL>',
        tier: 'standard',
        status: 'active'
      })
      .select()
      .single();

    if (customerError) {
      throw new Error(`Failed to create test customer: ${customerError.message}`);
    }

    // Hash the test API key
    const { data: hashedKey, error: hashError } = await supabaseAdmin
      .rpc('hash_api_key', { raw_key: TEST_API_KEY });

    if (hashError) {
      throw new Error(`Failed to hash API key: ${hashError.message}`);
    }

    // Create test API key
    const { data: apiKey, error: apiKeyError } = await supabaseAdmin
      .from('api_keys')
      .insert({
        customer_id: TEST_CUSTOMER_ID,
        key_type: 'test',
        key_hash: hashedKey,
        key_prefix: 'skt_',
        name: 'Test API Key',
        credits: 1000,
        revoked: false
      })
      .select()
      .single();

    if (apiKeyError) {
      throw new Error(`Failed to create test API key: ${apiKeyError.message}`);
    }

    return { customer, apiKey };
  }

  /**
   * Clean up test data
   */
  static async cleanupTestData(): Promise<void> {
    // Clean up in reverse order due to foreign key constraints
    await supabaseAdmin.from('usage_logs').delete().eq('customer_id', TEST_CUSTOMER_ID);
    await supabaseAdmin.from('audit_logs').delete().eq('customer_id', TEST_CUSTOMER_ID);
    await supabaseAdmin.from('job_queue').delete().eq('customer_id', TEST_CUSTOMER_ID);
    await supabaseAdmin.from('documents').delete().eq('customer_id', TEST_CUSTOMER_ID);
    await supabaseAdmin.from('api_keys').delete().eq('customer_id', TEST_CUSTOMER_ID);
    await supabaseAdmin.from('customers').delete().eq('id', TEST_CUSTOMER_ID);
  }

  /**
   * Create test file for upload
   */
  static createTestFile(size: number = 1024, name: string = 'test-document.pdf'): File {
    const content = 'x'.repeat(size);
    const blob = new Blob([content], { type: 'application/pdf' });
    return new File([blob], name, { type: 'application/pdf' });
  }

  /**
   * Create large test file to trigger queueing
   */
  static createLargeTestFile(name: string = 'large-document.pdf'): File {
    const size = 11 * 1024 * 1024; // 11MB - triggers queueing
    return this.createTestFile(size, name);
  }
}

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

describe('Queue Processing Workflow Integration', () => {
  let testCustomer: TestCustomer;
  let _testApiKey: TestApiKey;

  beforeAll(async () => {
    console.log('Setting up test data...');
    const setup = await TestDataSetup.setupTestCustomer();
    testCustomer = setup.customer;
    _testApiKey = setup.apiKey;
    console.log(`Test customer created: ${testCustomer.id}`);
  });

  afterAll(async () => {
    console.log('Cleaning up test data...');
    await TestDataSetup.cleanupTestData();
    console.log('Test cleanup completed');
  });

  beforeEach(async () => {
    // Clean up any previous test jobs
    await supabaseAdmin.from('job_queue').delete().eq('customer_id', TEST_CUSTOMER_ID);
    await supabaseAdmin.from('documents').delete().eq('customer_id', TEST_CUSTOMER_ID);
  });

  describe('Document Upload and Queueing', () => {
    it('should queue large documents for asynchronous processing', async () => {
      const largeFile = TestDataSetup.createLargeTestFile('integration-test-large.pdf');
      const formData = new FormData();
      formData.append('document', largeFile);

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.ok).toBe(true);

      const _result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.status).toBe('queued');
      expect(result.data.jobId).toBeDefined();
      expect(result.data.estimatedCompletion).toBeDefined();
      expect(result.data.creditsUsed).toBe(1);

      // Verify job was created in database
      const { data: job } = await supabaseAdmin
        .from('job_queue')
        .select('*')
        .eq('id', result.data.jobId)
        .single();

      expect(job).toBeDefined();
      expect(job.status).toBe('queued');
      expect(job.customer_id).toBe(TEST_CUSTOMER_ID);
      expect(job.job_type).toBe('document_processing');
    });

    it('should process small documents synchronously without queueing', async () => {
      const smallFile = TestDataSetup.createTestFile(1024, 'small-document.pdf');
      const formData = new FormData();
      formData.append('document', smallFile);

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.ok).toBe(true);

      const _result = await response.json();
      expect(result.success).toBe(true);
      expect(result.data.status).toBe('completed'); // Not queued
      expect(result.data.jobId).toBeUndefined(); // No job created
      expect(result.data.extractedData).toBeDefined();
    });

    it('should handle webhook URL configuration for queued jobs', async () => {
      const largeFile = TestDataSetup.createLargeTestFile('webhook-test.pdf');
      const formData = new FormData();
      formData.append('document', largeFile);
      formData.append('options', JSON.stringify({
        webhook: 'https://example.com/webhook'
      }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.ok).toBe(true);

      const _result = await response.json();
      expect(result.data.status).toBe('queued');

      // Verify webhook URL was stored
      const { data: job } = await supabaseAdmin
        .from('job_queue')
        .select('webhook_url, job_data')
        .eq('id', result.data.jobId)
        .single();

      expect(job.webhook_url).toBe('https://example.com/webhook');
      expect(job.job_data.webhookUrl).toBe('https://example.com/webhook');
    });
  });

  describe('Job Status Tracking', () => {
    it('should provide real-time job status updates', async () => {
      // First, create a queued job
      const largeFile = TestDataSetup.createLargeTestFile('status-test.pdf');
      const formData = new FormData();
      formData.append('document', largeFile);

      const uploadResponse = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.data.jobId;

      // Check initial status
      const statusResponse = await fetch(`${JOB_STATUS_ENDPOINT}/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        }
      });

      expect(statusResponse.ok).toBe(true);

      const statusResult = await statusResponse.json();
      expect(statusResult.success).toBe(true);
      expect(statusResult.data.jobId).toBe(jobId);
      expect(statusResult.data.status).toBe('queued');
      expect(statusResult.data.progress.stage).toBe('queued');
      expect(statusResult.data.progress.estimatedCompletion).toBeDefined();
      expect(statusResult.data.metadata.correlationId).toBeDefined();
    });

    it('should list customer jobs with pagination and filtering', async () => {
      // Create multiple test jobs
      const jobIds: string[] = [];
      
      for (let i = 0; i < 3; i++) {
        const file = TestDataSetup.createLargeTestFile(`bulk-test-${i}.pdf`);
        const formData = new FormData();
        formData.append('document', file);

        const response = await fetch(EXTRACT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`
          },
          body: formData
        });

        const _result = await response.json();
        jobIds.push(result.data.jobId);
      }

      // Test job listing
      const listResponse = await fetch(`${JOB_STATUS_ENDPOINT}/jobs?limit=2&page=1`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        }
      });

      expect(listResponse.ok).toBe(true);

      const listResult = await listResponse.json();
      expect(listResult.success).toBe(true);
      expect(listResult.data.jobs).toHaveLength(2); // Limited to 2
      expect(listResult.data.pagination.total).toBe(3);
      expect(listResult.data.pagination.totalPages).toBe(2);
      expect(listResult.data.summary.totalJobs).toBe(3);
      expect(listResult.data.summary.queued).toBe(3);
    });

    it('should filter jobs by status', async () => {
      // Create a job and manually update its status for testing
      const file = TestDataSetup.createLargeTestFile('filter-test.pdf');
      const formData = new FormData();
      formData.append('document', file);

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const _result = await response.json();
      const jobId = result.data.jobId;

      // Manually update job status to 'processing' for testing
      await supabaseAdmin
        .from('job_queue')
        .update({ status: 'processing', started_at: new Date().toISOString() })
        .eq('id', jobId);

      // Test filtering by status
      const filterResponse = await fetch(`${JOB_STATUS_ENDPOINT}/jobs?status=processing`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        }
      });

      const filterResult = await filterResponse.json();
      expect(filterResult.success).toBe(true);
      expect(filterResult.data.jobs).toHaveLength(1);
      expect(filterResult.data.jobs[0].status).toBe('processing');
    });
  });

  describe('Queue Processing', () => {
    it('should process queued jobs when queue processor is invoked', async () => {
      // Create a queued job
      const file = TestDataSetup.createLargeTestFile('processor-test.pdf');
      const formData = new FormData();
      formData.append('document', file);

      const uploadResponse = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.data.jobId;

      // Manually invoke queue processor
      const processResponse = await fetch(QUEUE_PROCESSOR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'process_single',
          jobId: jobId
        })
      });

      expect(processResponse.ok).toBe(true);

      const processResult = await processResponse.json();
      expect(processResult.success).toBe(true);
      expect(processResult.processed).toBe(1);
      expect(processResult.results).toHaveLength(1);

      const jobResult = processResult.results[0];
      expect(jobResult.jobId).toBe(jobId);
      expect(jobResult.success).toBe(true);
      expect(jobResult.processingTimeMs).toBeGreaterThan(0);

      // Verify job status was updated
      const statusResponse = await fetch(`${JOB_STATUS_ENDPOINT}/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        }
      });

      const statusResult = await statusResponse.json();
      expect(statusResult.data.status).toBe('completed');
      expect(statusResult.data.result).toBeDefined();
    });

    it('should handle batch processing of multiple jobs', async () => {
      // Create multiple queued jobs
      const jobIds: string[] = [];
      
      for (let i = 0; i < 3; i++) {
        const file = TestDataSetup.createLargeTestFile(`batch-${i}.pdf`);
        const formData = new FormData();
        formData.append('document', file);

        const response = await fetch(EXTRACT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`
          },
          body: formData
        });

        const _result = await response.json();
        jobIds.push(result.data.jobId);
      }

      // Process batch
      const batchResponse = await fetch(QUEUE_PROCESSOR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'process_batch',
          batchSize: 3
        })
      });

      expect(batchResponse.ok).toBe(true);

      const batchResult = await batchResponse.json();
      expect(batchResult.success).toBe(true);
      expect(batchResult.processed).toBe(3);
      expect(batchResult.results).toHaveLength(3);
      expect(batchResult.metrics.successRate).toBe(100);
    });

    it('should handle job failures with retry logic', async () => {
      // Create a job that will fail (by setting invalid agent ID)
      const file = TestDataSetup.createLargeTestFile('failure-test.pdf');
      const formData = new FormData();
      formData.append('document', file);
      formData.append('agentId', 'invalid-agent-id');

      const uploadResponse = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.data.jobId;

      // Process the job (should fail)
      const processResponse = await fetch(QUEUE_PROCESSOR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'process_single',
          jobId: jobId
        })
      });

      const processResult = await processResponse.json();
      expect(processResult.results[0].success).toBe(false);

      // Check job status - should be failed but ready for retry
      const statusResponse = await fetch(`${JOB_STATUS_ENDPOINT}/${jobId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        }
      });

      const statusResult = await statusResponse.json();
      expect(statusResult.data.status).toBe('failed');
      expect(statusResult.data.error.retryCount).toBe(1);
      expect(statusResult.data.error.willRetry).toBe(true);
    });
  });

  describe('Queue Metrics and Monitoring', () => {
    it('should provide comprehensive queue metrics', async () => {
      // Create jobs in different states for metrics testing
      const files = [
        TestDataSetup.createLargeTestFile('metrics-1.pdf'),
        TestDataSetup.createLargeTestFile('metrics-2.pdf'),
        TestDataSetup.createLargeTestFile('metrics-3.pdf')
      ];

      for (const file of files) {
        const formData = new FormData();
        formData.append('document', file);

        await fetch(EXTRACT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`
          },
          body: formData
        });
      }

      // Get queue metrics
      const metricsResponse = await fetch(QUEUE_PROCESSOR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'metrics'
        })
      });

      expect(metricsResponse.ok).toBe(true);

      const metricsResult = await metricsResponse.json();
      expect(metricsResult.success).toBe(true);
      expect(typeof metricsResult.data.totalQueued).toBe('number');
      expect(typeof metricsResult.data.totalProcessing).toBe('number');
      expect(typeof metricsResult.data.normalQueued).toBe('number');
      expect(metricsResult.data.totalQueued).toBeGreaterThanOrEqual(3);
    });

    it('should handle health check requests', async () => {
      const healthResponse = await fetch(QUEUE_PROCESSOR_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'health_check'
        })
      });

      expect(healthResponse.ok).toBe(true);

      const healthResult = await healthResponse.json();
      expect(healthResult.success).toBe(true);
      expect(healthResult.data.status).toBe('healthy');
      expect(healthResult.data.processingNode).toBeDefined();
      expect(healthResult.data.timestamp).toBeDefined();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid API key in job status requests', async () => {
      const response = await fetch(`${JOB_STATUS_ENDPOINT}/any-job-id`, {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer invalid-key'
        }
      });

      expect(response.status).toBe(401);
    });

    it('should handle job not found scenarios', async () => {
      const response = await fetch(`${JOB_STATUS_ENDPOINT}/non-existent-job-id`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        }
      });

      expect(response.status).toBe(404);
    });

    it('should handle insufficient credits for queueing', async () => {
      // Set customer credits to 0
      await supabaseAdmin
        .from('api_keys')
        .update({ credits: 0 })
        .eq('customer_id', TEST_CUSTOMER_ID);

      const file = TestDataSetup.createLargeTestFile('no-credits.pdf');
      const formData = new FormData();
      formData.append('document', file);

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(500);
      
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('credit');

      // Restore credits for other tests
      await supabaseAdmin
        .from('api_keys')
        .update({ credits: 1000 })
        .eq('customer_id', TEST_CUSTOMER_ID);
    });

    it('should handle malformed request payloads', async () => {
      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: 'invalid json'
      });

      expect(response.status).toBe(400);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle concurrent job creation without conflicts', async () => {
      const concurrentRequests = 5;
      const promises: Promise<Response>[] = [];

      for (let i = 0; i < concurrentRequests; i++) {
        const file = TestDataSetup.createLargeTestFile(`concurrent-${i}.pdf`);
        const formData = new FormData();
        formData.append('document', file);

        const promise = fetch(EXTRACT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`
          },
          body: formData
        });

        promises.push(promise);
      }

      const responses = await Promise.all(promises);
      
      // All requests should succeed
      for (const response of responses) {
        expect(response.ok).toBe(true);
        const _result = await response.json();
        expect(result.success).toBe(true);
        expect(result.data.jobId).toBeDefined();
      }

      // Verify all jobs were created
      const { count } = await supabaseAdmin
        .from('job_queue')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', TEST_CUSTOMER_ID);

      expect(count).toBe(concurrentRequests);
    });

    it('should maintain queue order based on priority and creation time', async () => {
      // Create jobs with different priorities
      const priorities = ['low', 'normal', 'high', 'urgent'];
      const jobIds: string[] = [];

      for (const priority of priorities) {
        const file = TestDataSetup.createLargeTestFile(`priority-${priority}.pdf`);
        const formData = new FormData();
        formData.append('document', file);
        formData.append('options', JSON.stringify({ priority }));

        const response = await fetch(EXTRACT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`
          },
          body: formData
        });

        const _result = await response.json();
        jobIds.push(result.data.jobId);
      }

      // Verify jobs are ordered correctly by priority
      const { data: jobs } = await supabaseAdmin
        .from('job_queue')
        .select('id, priority')
        .eq('customer_id', TEST_CUSTOMER_ID)
        .eq('status', 'queued')
        .order('priority')
        .order('created_at');

      // Should be ordered: urgent, high, normal, low
      const expectedOrder = ['urgent', 'high', 'normal', 'low'];
      const actualOrder = jobs.map(job => job.priority);
      
      expect(actualOrder).toEqual(expectedOrder);
    });
  });
});