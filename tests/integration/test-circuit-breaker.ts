/**
 * Manual Circuit Breaker Test to Verify Implementation
 */

import { CircuitBreaker, DEFAULT_CIRCUIT_BREAKER_CONFIGS } from './supabase/functions/ai-integration/utils/circuit-breaker.ts';

console.log('🧪 Testing Circuit Breaker Implementation...\n');

// Test 1: Basic Circuit Breaker Creation
console.log('1. Testing Circuit Breaker Creation');
try {
  const circuitBreaker = new CircuitBreaker('test-service', DEFAULT_CIRCUIT_BREAKER_CONFIGS.openai);
  console.log('✅ Circuit breaker created successfully');
  console.log('   Initial state:', circuitBreaker.getState().state);
  console.log('   Service name:', circuitBreaker.getServiceName());
} catch {
  console.log('❌ Failed to create circuit breaker:', error.message);
}

// Test 2: Successful Operation
console.log('\n2. Testing Successful Operation');
try {
  const circuitBreaker = new CircuitBreaker('test-service', DEFAULT_CIRCUIT_BREAKER_CONFIGS.openai);
  
  const successfulOperation = () => Promise.resolve('success');
  const _result = await circuitBreaker.execute(successfulOperation);
  
  console.log('✅ Operation executed successfully');
  console.log('   Result:', result);
  console.log('   State after success:', circuitBreaker.getState().state);
  console.log('   Failures:', circuitBreaker.getState().failures);
} catch {
  console.log('❌ Failed to execute operation:', error.message);
}

// Test 3: Circuit Breaker Opening
console.log('\n3. Testing Circuit Breaker Opening with Failures');
try {
  const circuitBreaker = new CircuitBreaker('test-service', {
    failureThreshold: 3,
    timeout: 1000,
    resetTimeout: 5000,
    successThreshold: 2,
    degradationThreshold: 1000
  });
  
  const failingOperation = () => Promise.reject(new Error('Network timeout'));
  
  // Execute multiple failing operations
  let failures = 0;
  for (let i = 0; i < 5; i++) {
    try {
      await circuitBreaker.execute(failingOperation);
    } catch {
      failures++;
      console.log(`   Failure ${failures}: ${error.message}`);
      console.log(`   Circuit state: ${circuitBreaker.getState().state}, Failures: ${circuitBreaker.getState().failures}`);
    }
  }
  
  console.log('✅ Circuit breaker opening test completed');
  console.log('   Final state:', circuitBreaker.getState().state);
  console.log('   Total failures:', circuitBreaker.getState().failures);
  
} catch {
  console.log('❌ Circuit breaker opening test failed:', error.message);
}

// Test 4: Manual Override
console.log('\n4. Testing Manual Override');
try {
  const circuitBreaker = new CircuitBreaker('test-service', DEFAULT_CIRCUIT_BREAKER_CONFIGS.openai);
  
  console.log('   Initial state:', circuitBreaker.getState().state);
  
  circuitBreaker.forceOpen();
  console.log('   After forceOpen():', circuitBreaker.getState().state);
  
  circuitBreaker.forceClose();
  console.log('   After forceClose():', circuitBreaker.getState().state);
  
  circuitBreaker.reset();
  console.log('   After reset():', circuitBreaker.getState().state);
  
  console.log('✅ Manual override test completed');
  
} catch {
  console.log('❌ Manual override test failed:', error.message);
}

// Test 5: Metrics Collection
console.log('\n5. Testing Metrics Collection');
try {
  const circuitBreaker = new CircuitBreaker('test-service', DEFAULT_CIRCUIT_BREAKER_CONFIGS.openai);
  
  // Execute some operations
  const successfulOperation = () => Promise.resolve('success');
  await circuitBreaker.execute(successfulOperation);
  await circuitBreaker.execute(successfulOperation);
  
  const metrics = circuitBreaker.getMetrics();
  console.log('✅ Metrics collection test completed');
  console.log('   Total requests:', metrics.totalRequests);
  console.log('   Successful requests:', metrics.successfulRequests);
  console.log('   Failed requests:', metrics.failedRequests);
  console.log('   Success rate:', circuitBreaker.getSuccessRate());
  
} catch {
  console.log('❌ Metrics collection test failed:', error.message);
}

// Test 6: Status Summary
console.log('\n6. Testing Status Summary');
try {
  const circuitBreaker = new CircuitBreaker('test-service', DEFAULT_CIRCUIT_BREAKER_CONFIGS.openai);
  
  const statusSummary = circuitBreaker.getStatusSummary();
  console.log('✅ Status summary test completed');
  console.log('   Service:', statusSummary.service);
  console.log('   State:', statusSummary.state);
  console.log('   Is healthy:', statusSummary.isHealthy);
  console.log('   Success rate:', statusSummary.successRate);
  console.log('   Average response time:', statusSummary.averageResponseTime);
  
} catch {
  console.log('❌ Status summary test failed:', error.message);
}

console.log('\n🎯 Circuit Breaker Implementation Test Complete!\n');