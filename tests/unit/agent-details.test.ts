import { describe, it, expect, _beforeEach, _mock } from 'bun:test';

// Agent Details Endpoint Unit Tests
// Tests for GitHub Issue #14: Agent Storage & Retrieval
// Comprehensive agent details with versioning, performance metrics, and example outputs

interface AgentDetails {
  id: string;
  agent_id: string;
  name: string;
  description: string;
  category: string;
  version: number;
  is_default: boolean;
  customer_id: string | null;
  parent_agent_id: string | null;
  prompt: string;
  json_schema: Record<string, any>;
  settings: Record<string, any>;
  performance_metrics: Record<string, any>;
  status: 'active' | 'inactive' | 'deprecated';
  created_at: string;
  updated_at: string;
  current_version: string;
  version_history: AgentVersion[];
  performance_stats: PerformanceMetrics | null;
  example_input: any;
  example_output: any;
  supported_formats: string[];
  use_cases: string[];
  accuracy_rating: number;
  avg_processing_time_ms: number;
}

interface AgentVersion {
  id: string;
  version_number: string;
  is_current: boolean;
  changelog: string;
  created_at: string;
  deprecation_date: string | null;
}

interface PerformanceMetrics {
  id: string;
  test_document_count: number;
  accuracy_score: number;
  avg_processing_time_ms: number;
  confidence_score: number;
  test_date: string;
  test_results: Record<string, any>;
}

interface ExampleOutput {
  input_description: string;
  expected_output: Record<string, any>;
  confidence_estimate: number;
}

// Mock functions for agent details retrieval
const mockGetAgentById = mock(async (agentId: string, _customerId: string): Promise<AgentDetails | null> => {
  const mockAgent: AgentDetails = {
    id: 'agent-123',
    agent_id: 'default-invoice-v1',
    name: 'Advanced Invoice Processor',
    description: 'Production-grade invoice processor with >90% accuracy for vendor details, amounts, dates, line items, and tax information',
    category: 'invoice',
    version: 1,
    is_default: true,
    customer_id: null,
    parent_agent_id: null,
    prompt: 'You are an expert invoice data extraction system...',
    json_schema: {
      "$schema": "http://json-schema.org/draft-07/schema#",
      "type": "object",
      "required": ["vendor_name", "total_amount", "invoice_date", "currency"],
      "properties": {
        "vendor_name": { "type": "string" },
        "total_amount": { "type": "number" },
        "invoice_date": { "type": "string", "format": "date" },
        "currency": { "type": "string", "default": "USD" }
      }
    },
    settings: {
      "temperature": 0.1,
      "max_tokens": 2000
    },
    performance_metrics: {
      "target_accuracy": 0.95,
      "test_coverage": "comprehensive"
    },
    status: 'active',
    created_at: '2025-09-21T10:00:00Z',
    updated_at: '2025-09-21T10:00:00Z',
    current_version: '1.0.0',
    version_history: [
      {
        id: 'version-1',
        version_number: '1.0.0',
        is_current: true,
        changelog: 'Initial comprehensive default agent with >90% accuracy focus',
        created_at: '2025-09-21T10:00:00Z',
        deprecation_date: null
      }
    ],
    performance_stats: {
      id: 'perf-1',
      test_document_count: 50,
      accuracy_score: 0.95,
      avg_processing_time_ms: 1200,
      confidence_score: 0.92,
      test_date: '2025-09-21T10:00:00Z',
      test_results: {
        "test_environment": "production",
        "passed_threshold": true
      }
    },
    example_input: {
      description: "PDF invoice from ABC Corp",
      file_type: "application/pdf",
      estimated_pages: 1
    },
    example_output: {
      vendor_name: "ABC Corporation",
      total_amount: 1250.75,
      invoice_date: "2025-09-20",
      currency: "USD",
      invoice_number: "INV-2025-001"
    },
    supported_formats: ["pdf", "image", "scan"],
    use_cases: ["accounts payable", "expense tracking", "vendor management"],
    accuracy_rating: 0.95,
    avg_processing_time_ms: 1200
  };

  if (agentId === 'default-invoice-v1' || agentId === 'agent-123') {
    return mockAgent;
  }
  
  return null;
});

const mockGenerateExampleOutput = mock(async (agent: Partial<AgentDetails>): Promise<ExampleOutput> => {
  const examples: Record<string, ExampleOutput> = {
    'invoice': {
      input_description: "Sample invoice from ABC Corporation with line items",
      expected_output: {
        vendor_name: "ABC Corporation",
        vendor_address: "123 Business St, City, ST 12345",
        invoice_number: "INV-2025-001",
        invoice_date: "2025-09-20",
        total_amount: 1250.75,
        currency: "USD",
        line_items: [
          {
            description: "Consulting Services",
            quantity: 10,
            unit_price: 125.00,
            line_total: 1250.00
          }
        ]
      },
      confidence_estimate: 0.95
    },
    'contract': {
      input_description: "Service agreement between two companies",
      expected_output: {
        parties: ["ABC Corporation", "XYZ Services Inc"],
        contract_type: "Service Agreement",
        effective_date: "2025-01-01",
        expiration_date: "2025-12-31",
        governing_law: "State of California"
      },
      confidence_estimate: 0.92
    },
    'receipt': {
      input_description: "Restaurant receipt for business meal",
      expected_output: {
        merchant_name: "Downtown Restaurant",
        transaction_date: "2025-09-20",
        total_amount: 45.67,
        category: "meals_entertainment",
        payment_method: "credit_card"
      },
      confidence_estimate: 0.88
    }
  };

  return examples[agent.category || 'invoice'] || examples['invoice'];
});

const mockValidateAccess = mock(async (agentId: string, customerId: string): Promise<boolean> => {
  // Mock access control logic
  // Default agents are accessible to all customers
  // Custom agents are only accessible to their owners
  if (agentId.startsWith('default-')) {
    return true;
  }
  
  // For this test, assume customer-123 owns custom agents
  return customerId === 'customer-123';
});

describe('Agent Details Retrieval', () => {
  beforeEach(() => {
    mockGetAgentById.mockClear();
    mockGenerateExampleOutput.mockClear();
    mockValidateAccess.mockClear();
  });

  describe('Basic Agent Details', () => {
    it('should return complete agent details for valid agent ID', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result).not.toBeNull();
      expect(result!.agent_id).toBe('default-invoice-v1');
      expect(result!.name).toBe('Advanced Invoice Processor');
      expect(result!.category).toBe('invoice');
      expect(result!.is_default).toBe(true);
    });

    it('should return null for non-existent agent ID', async () => {
      // Arrange
      const agentId = 'non-existent-agent';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result).toBeNull();
    });

    it('should include all required fields in agent details', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result).toHaveProperty('id');
      expect(result).toHaveProperty('agent_id');
      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('category');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('is_default');
      expect(result).toHaveProperty('prompt');
      expect(result).toHaveProperty('json_schema');
      expect(result).toHaveProperty('performance_metrics');
      expect(result).toHaveProperty('version_history');
      expect(result).toHaveProperty('performance_stats');
      expect(result).toHaveProperty('example_input');
      expect(result).toHaveProperty('example_output');
    });
  });

  describe('JSON Schema Validation', () => {
    it('should include valid JSON schema structure', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.json_schema).toHaveProperty('$schema');
      expect(result!.json_schema).toHaveProperty('type');
      expect(result!.json_schema).toHaveProperty('properties');
      expect(result!.json_schema).toHaveProperty('required');
      expect(result!.json_schema.type).toBe('object');
      expect(Array.isArray(result!.json_schema.required)).toBe(true);
    });

    it('should include required fields in schema', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      const requiredFields = result!.json_schema.required;
      expect(requiredFields).toContain('vendor_name');
      expect(requiredFields).toContain('total_amount');
      expect(requiredFields).toContain('invoice_date');
      expect(requiredFields).toContain('currency');
    });

    it('should include property definitions with types', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      const properties = result!.json_schema.properties;
      expect(properties.vendor_name.type).toBe('string');
      expect(properties.total_amount.type).toBe('number');
      expect(properties.invoice_date.format).toBe('date');
      expect(properties.currency.default).toBe('USD');
    });
  });

  describe('Version History', () => {
    it('should include version history with current version marked', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.version_history).toHaveLength(1);
      expect(result!.version_history[0].version_number).toBe('1.0.0');
      expect(result!.version_history[0].is_current).toBe(true);
      expect(result!.current_version).toBe('1.0.0');
    });

    it('should include changelog information', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      const currentVersion = result!.version_history[0];
      expect(currentVersion.changelog).toContain('Initial comprehensive default agent');
      expect(currentVersion.changelog).toContain('>90% accuracy focus');
    });

    it('should handle agents with multiple versions', async () => {
      // Arrange - Mock agent with multiple versions
      const mockAgentWithVersions = {
        ...await mockGetAgentById('default-invoice-v1', 'customer-123'),
        version_history: [
          {
            id: 'version-1',
            version_number: '1.0.0',
            is_current: false,
            changelog: 'Initial version',
            created_at: '2025-09-01T10:00:00Z',
            deprecation_date: '2025-09-15T10:00:00Z'
          },
          {
            id: 'version-2',
            version_number: '1.1.0',
            is_current: true,
            changelog: 'Enhanced accuracy and performance',
            created_at: '2025-09-15T10:00:00Z',
            deprecation_date: null
          }
        ],
        current_version: '1.1.0'
      };
      
      mockGetAgentById.mockResolvedValue(mockAgentWithVersions);
      
      // Act
      const _result = await mockGetAgentById('default-invoice-v1', 'customer-123');
      
      // Assert
      expect(result!.version_history).toHaveLength(2);
      expect(result!.current_version).toBe('1.1.0');
      expect(result!.version_history[1].is_current).toBe(true);
    });
  });

  describe('Performance Metrics', () => {
    it('should include performance statistics', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.performance_stats).not.toBeNull();
      expect(result!.performance_stats!.accuracy_score).toBe(0.95);
      expect(result!.performance_stats!.avg_processing_time_ms).toBe(1200);
      expect(result!.performance_stats!.confidence_score).toBe(0.92);
      expect(result!.performance_stats!.test_document_count).toBe(50);
    });

    it('should include test results metadata', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      const testResults = result!.performance_stats!.test_results;
      expect(testResults).toHaveProperty('test_environment');
      expect(testResults).toHaveProperty('passed_threshold');
      expect(testResults.passed_threshold).toBe(true);
    });

    it('should handle agents without performance data', async () => {
      // Arrange - Mock agent without performance stats
      const mockAgentNoPerf = {
        ...await mockGetAgentById('default-invoice-v1', 'customer-123'),
        performance_stats: null,
        accuracy_rating: 0,
        avg_processing_time_ms: 0
      };
      
      mockGetAgentById.mockResolvedValue(mockAgentNoPerf);
      
      // Act
      const _result = await mockGetAgentById('default-invoice-v1', 'customer-123');
      
      // Assert
      expect(result!.performance_stats).toBeNull();
      expect(result!.accuracy_rating).toBe(0);
      expect(result!.avg_processing_time_ms).toBe(0);
    });
  });

  describe('Example Input/Output', () => {
    it('should generate appropriate example output for invoice agent', async () => {
      // Arrange
      const agent = { category: 'invoice' };
      
      // Act
      const _result = await mockGenerateExampleOutput(agent);
      
      // Assert
      expect(result.input_description).toContain('invoice');
      expect(result.expected_output).toHaveProperty('vendor_name');
      expect(result.expected_output).toHaveProperty('total_amount');
      expect(result.expected_output).toHaveProperty('invoice_date');
      expect(result.confidence_estimate).toBeGreaterThan(0.9);
    });

    it('should generate appropriate example output for contract agent', async () => {
      // Arrange
      const agent = { category: 'contract' };
      
      // Act
      const _result = await mockGenerateExampleOutput(agent);
      
      // Assert
      expect(result.input_description).toContain('agreement');
      expect(result.expected_output).toHaveProperty('parties');
      expect(result.expected_output).toHaveProperty('contract_type');
      expect(result.expected_output).toHaveProperty('effective_date');
      expect(Array.isArray(result.expected_output.parties)).toBe(true);
    });

    it('should include realistic example data', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.example_output.vendor_name).toBe('ABC Corporation');
      expect(result!.example_output.total_amount).toBe(1250.75);
      expect(result!.example_output.currency).toBe('USD');
      expect(result!.example_input.file_type).toBe('application/pdf');
    });
  });

  describe('Agent Metadata', () => {
    it('should include supported formats', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.supported_formats).toContain('pdf');
      expect(result!.supported_formats).toContain('image');
      expect(result!.supported_formats).toContain('scan');
    });

    it('should include use cases', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.use_cases).toContain('accounts payable');
      expect(result!.use_cases).toContain('expense tracking');
      expect(result!.use_cases).toContain('vendor management');
    });

    it('should include agent settings', async () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const _result = await mockGetAgentById(agentId, customerId);
      
      // Assert
      expect(result!.settings).toHaveProperty('temperature');
      expect(result!.settings).toHaveProperty('max_tokens');
      expect(result!.settings.temperature).toBe(0.1);
      expect(result!.settings.max_tokens).toBe(2000);
    });
  });
});

describe('Agent Access Control', () => {
  beforeEach(() => {
    mockValidateAccess.mockClear();
  });

  it('should allow access to default agents for any customer', async () => {
    // Arrange
    const agentId = 'default-invoice-v1';
    const customerId = 'any-customer';
    
    // Act
    const hasAccess = await mockValidateAccess(agentId, customerId);
    
    // Assert
    expect(hasAccess).toBe(true);
  });

  it('should allow access to custom agents for owner', async () => {
    // Arrange
    const agentId = 'custom-agent-123';
    const customerId = 'customer-123';
    
    // Act
    const hasAccess = await mockValidateAccess(agentId, customerId);
    
    // Assert
    expect(hasAccess).toBe(true);
  });

  it('should deny access to custom agents for non-owner', async () => {
    // Arrange
    const agentId = 'custom-agent-123';
    const customerId = 'different-customer';
    
    // Act
    const hasAccess = await mockValidateAccess(agentId, customerId);
    
    // Assert
    expect(hasAccess).toBe(false);
  });
});

describe('Error Handling', () => {
  it('should handle database connection errors gracefully', async () => {
    // Arrange
    mockGetAgentById.mockResolvedValue(null);
    
    // Act
    const _result = await mockGetAgentById('invalid-agent', 'customer-123');
    
    // Assert
    expect(result).toBeNull();
  });

  it('should handle invalid agent ID format', async () => {
    // Arrange
    const invalidAgentId = '';
    const customerId = 'customer-123';
    
    // Act
    const _result = await mockGetAgentById(invalidAgentId, customerId);
    
    // Assert
    expect(result).toBeNull();
  });

  it('should handle missing customer ID', async () => {
    // Arrange
    const agentId = 'default-invoice-v1';
    const customerId = '';
    
    // Act
    const _result = await mockGetAgentById(agentId, customerId);
    
    // Assert
    expect(result).toBeNull(); // Should return null for missing customer
  });
});

// Export for integration tests
export {
  mockGetAgentById,
  mockGenerateExampleOutput,
  mockValidateAccess
};