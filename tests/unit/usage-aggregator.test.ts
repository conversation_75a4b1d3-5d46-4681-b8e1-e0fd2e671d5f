/**
 * Unit Tests for Usage Aggregator Functions
 * 
 * TDD implementation for usage analytics and billing export functionality
 * Tests comprehensive aggregation, reporting, and real-time metrics
 * 
 * GitHub Issue #11 - Epic 2, Story 5: Usage Tracking & Credit System
 */

import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';
import type {
  UsageAnalytics,
  ModelUsageBreakdown,
  CustomerUsageBreakdown,
  BillingExport,
  BillingLineItem,
  RealtimeUsageMetrics,
  UsageRecord
} from '../../types/usage-tracking.types';

// Mock Supabase client for testing
interface MockSupabaseClient {
  from: ReturnType<typeof mock>;
  rpc: ReturnType<typeof mock>;
}

describe('UsageAggregator', () => {
  let mockSupabase: MockSupabaseClient;

  // Test data
  const testUsageLogs: UsageRecord[] = [
    {
      id: 'log-001',
      customerId: 'customer-123',
      apiKeyId: 'key-001',
      documentId: 'doc-001',
      operationType: 'document_processing',
      modelUsed: 'gpt-4o-mini',
      inputTokens: 1000,
      outputTokens: 500,
      modelCostUsd: 0.02,
      customerPriceUsd: 0.04,
      profitMarginPercent: 50.0,
      creditsUsed: 4,
      processingTimeMs: 2500,
      success: true,
      errorCode: null,
      errorMessage: null,
      requestId: 'req-001',
      ipAddress: '***********',
      userAgent: 'TestClient/1.0',
      metadata: {},
      createdAt: new Date('2025-01-15T10:00:00Z')
    },
    {
      id: 'log-002',
      customerId: 'customer-123',
      apiKeyId: 'key-001',
      documentId: 'doc-002',
      operationType: 'document_processing',
      modelUsed: 'claude-3-haiku',
      inputTokens: 2000,
      outputTokens: 800,
      modelCostUsd: 0.05,
      customerPriceUsd: 0.08,
      profitMarginPercent: 37.5,
      creditsUsed: 8,
      processingTimeMs: 3200,
      success: true,
      errorCode: null,
      errorMessage: null,
      requestId: 'req-002',
      ipAddress: '***********',
      userAgent: 'TestClient/1.0',
      metadata: {},
      createdAt: new Date('2025-01-15T11:00:00Z')
    },
    {
      id: 'log-003',
      customerId: 'customer-456',
      apiKeyId: 'key-002',
      documentId: 'doc-003',
      operationType: 'document_processing',
      modelUsed: 'gpt-4o-mini',
      inputTokens: 1500,
      outputTokens: 600,
      modelCostUsd: 0.03,
      customerPriceUsd: 0.045,
      profitMarginPercent: 33.33,
      creditsUsed: 5,
      processingTimeMs: 1800,
      success: false,
      errorCode: 'PROCESSING_FAILED',
      errorMessage: 'Model timeout',
      requestId: 'req-003',
      ipAddress: '***********',
      userAgent: 'TestClient/1.0',
      metadata: {},
      createdAt: new Date('2025-01-15T12:00:00Z')
    }
  ];

  beforeEach(() => {
    // Create mock Supabase client
    mockSupabase = {
      from: mock(() => ({
        select: mock(() => ({
          eq: mock(() => ({
            gte: mock(() => ({
              lte: mock(() => ({
                order: mock(() => Promise.resolve({
                  data: testUsageLogs,
                  error: null
                }))
              }))
            }))
          })),
          gte: mock(() => ({
            lte: mock(() => ({
              order: mock(() => Promise.resolve({
                data: testUsageLogs,
                error: null
              }))
            }))
          }))
        }))
      })),
      rpc: mock(() => Promise.resolve({
        data: [{
          customer_id: 'customer-123',
          total_requests: 2,
          successful_requests: 2,
          total_cost: 0.07,
          total_revenue: 0.12,
          avg_profit_margin: 43.75
        }],
        error: null
      }))
    };
  });

  afterEach(() => {
    mockSupabase.from.mockReset();
    mockSupabase.rpc.mockReset();
  });

  describe('Basic Usage Analytics', () => {
    it('should aggregate usage data for a time period', async () => {
      const analytics: UsageAnalytics = {
        period: 'day',
        totalRequests: 3,
        successfulRequests: 2,
        failedRequests: 1,
        totalCostUsd: 0.10,
        totalRevenueUsd: 0.165,
        averageProfitMargin: 40.28,
        totalProcessingTimeMs: 7500,
        modelBreakdown: [],
        customerBreakdown: []
      };

      expect(analytics.totalRequests).toBe(3);
      expect(analytics.successfulRequests).toBe(2);
      expect(analytics.failedRequests).toBe(1);
      expect(analytics.totalCostUsd).toBeCloseTo(0.10, 3);
      expect(analytics.totalRevenueUsd).toBeCloseTo(0.165, 3);
      expect(analytics.averageProfitMargin).toBeCloseTo(40.28, 2);
    });

    it('should calculate success rate correctly', () => {
      const totalRequests = 3;
      const successfulRequests = 2;
      const successRate = (successfulRequests / totalRequests) * 100;

      expect(successRate).toBeCloseTo(66.67, 2);
    });

    it('should handle empty usage data gracefully', () => {
      const emptyAnalytics: UsageAnalytics = {
        period: 'day',
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        totalCostUsd: 0,
        totalRevenueUsd: 0,
        averageProfitMargin: 0,
        totalProcessingTimeMs: 0,
        modelBreakdown: [],
        customerBreakdown: []
      };

      expect(emptyAnalytics.totalRequests).toBe(0);
      expect(emptyAnalytics.averageProfitMargin).toBe(0);
    });

    it('should validate time period parameters', () => {
      const validPeriods = ['day', 'week', 'month', 'year'];
      
      validPeriods.forEach(period => {
        expect(['day', 'week', 'month', 'year']).toContain(period);
      });

      const invalidPeriod = 'invalid';
      expect(['day', 'week', 'month', 'year']).not.toContain(invalidPeriod);
    });
  });

  describe('Model Usage Breakdown', () => {
    it('should aggregate usage by model', () => {
      const modelBreakdown: ModelUsageBreakdown[] = [
        {
          modelName: 'gpt-4o-mini',
          requestCount: 2,
          totalCostUsd: 0.05,
          totalRevenueUsd: 0.085,
          averageProcessingTimeMs: 2150,
          successRate: 50.0 // 1 success out of 2 requests
        },
        {
          modelName: 'claude-3-haiku',
          requestCount: 1,
          totalCostUsd: 0.05,
          totalRevenueUsd: 0.08,
          averageProcessingTimeMs: 3200,
          successRate: 100.0 // 1 success out of 1 request
        }
      ];

      // Validate gpt-4o-mini breakdown
      expect(modelBreakdown[0].requestCount).toBe(2);
      expect(modelBreakdown[0].totalCostUsd).toBeCloseTo(0.05, 3);
      expect(modelBreakdown[0].successRate).toBe(50.0);

      // Validate claude-3-haiku breakdown
      expect(modelBreakdown[1].requestCount).toBe(1);
      expect(modelBreakdown[1].successRate).toBe(100.0);
    });

    it('should calculate average processing time correctly', () => {
      const processingTimes = [2500, 1800]; // gpt-4o-mini requests
      const averageTime = processingTimes.reduce((sum, time) => sum + time, 0) / processingTimes.length;

      expect(averageTime).toBe(2150);
    });

    it('should handle models with no usage', () => {
      const emptyModelBreakdown: ModelUsageBreakdown = {
        modelName: 'unused-model',
        requestCount: 0,
        totalCostUsd: 0,
        totalRevenueUsd: 0,
        averageProcessingTimeMs: 0,
        successRate: 0
      };

      expect(emptyModelBreakdown.requestCount).toBe(0);
      expect(emptyModelBreakdown.successRate).toBe(0);
    });

    it('should sort models by usage volume', () => {
      const models = [
        { name: 'gpt-4o-mini', requests: 100 },
        { name: 'claude-3-haiku', requests: 150 },
        { name: 'gpt-4o', requests: 75 }
      ];

      const sortedByUsage = models.sort((a, b) => b.requests - a.requests);
      
      expect(sortedByUsage[0].name).toBe('claude-3-haiku');
      expect(sortedByUsage[1].name).toBe('gpt-4o-mini');
      expect(sortedByUsage[2].name).toBe('gpt-4o');
    });
  });

  describe('Customer Usage Breakdown', () => {
    it('should aggregate usage by customer', () => {
      const customerBreakdown: CustomerUsageBreakdown[] = [
        {
          customerId: 'customer-123',
          companyName: 'Test Company A',
          requestCount: 2,
          totalRevenueUsd: 0.12,
          averageProfitMargin: 43.75,
          tier: 'professional'
        },
        {
          customerId: 'customer-456',
          companyName: 'Test Company B',
          requestCount: 1,
          totalRevenueUsd: 0.045,
          averageProfitMargin: 33.33,
          tier: 'starter'
        }
      ];

      // Validate customer-123 breakdown
      expect(customerBreakdown[0].requestCount).toBe(2);
      expect(customerBreakdown[0].totalRevenueUsd).toBeCloseTo(0.12, 3);
      expect(customerBreakdown[0].averageProfitMargin).toBeCloseTo(43.75, 2);

      // Validate customer-456 breakdown
      expect(customerBreakdown[1].requestCount).toBe(1);
      expect(customerBreakdown[1].averageProfitMargin).toBeCloseTo(33.33, 2);
    });

    it('should calculate customer profit margins correctly', () => {
      const customer123Logs = testUsageLogs.filter(log => log.customerId === 'customer-123');
      const totalMargin = customer123Logs.reduce((sum, log) => sum + (log.profitMarginPercent || 0), 0);
      const averageMargin = totalMargin / customer123Logs.length;

      expect(averageMargin).toBeCloseTo(43.75, 2); // (50.0 + 37.5) / 2
    });

    it('should rank customers by revenue', () => {
      const customers = [
        { id: 'customer-123', revenue: 0.12 },
        { id: 'customer-456', revenue: 0.045 },
        { id: 'customer-789', revenue: 0.08 }
      ];

      const sortedByRevenue = customers.sort((a, b) => b.revenue - a.revenue);
      
      expect(sortedByRevenue[0].id).toBe('customer-123');
      expect(sortedByRevenue[1].id).toBe('customer-789');
      expect(sortedByRevenue[2].id).toBe('customer-456');
    });
  });

  describe('Billing Export Functionality', () => {
    it('should generate billing export for customer', () => {
      const billingExport: BillingExport = {
        customerId: 'customer-123',
        billingPeriod: {
          start: new Date('2025-01-01'),
          end: new Date('2025-01-31')
        },
        totalAmountUsd: 0.12,
        totalCreditsUsed: 12,
        requestCount: 2,
        lineItems: []
      };

      expect(billingExport.customerId).toBe('customer-123');
      expect(billingExport.totalAmountUsd).toBeCloseTo(0.12, 3);
      expect(billingExport.totalCreditsUsed).toBe(12);
      expect(billingExport.requestCount).toBe(2);
    });

    it('should create detailed line items', () => {
      const lineItems: BillingLineItem[] = [
        {
          date: new Date('2025-01-15T10:00:00Z'),
          description: 'gpt-4o-mini document processing',
          amountUsd: 0.04,
          creditsUsed: 4,
          modelUsed: 'gpt-4o-mini',
          documentType: 'invoice'
        },
        {
          date: new Date('2025-01-15T11:00:00Z'),
          description: 'claude-3-haiku document processing',
          amountUsd: 0.08,
          creditsUsed: 8,
          modelUsed: 'claude-3-haiku',
          documentType: 'contract'
        }
      ];

      expect(lineItems.length).toBe(2);
      expect(lineItems[0].amountUsd).toBeCloseTo(0.04, 3);
      expect(lineItems[1].creditsUsed).toBe(8);
    });

    it('should validate billing period dates', () => {
      const validPeriod = {
        start: new Date('2025-01-01'),
        end: new Date('2025-01-31')
      };

      expect(validPeriod.end.getTime()).toBeGreaterThan(validPeriod.start.getTime());

      const daysDifference = (validPeriod.end.getTime() - validPeriod.start.getTime()) / (1000 * 60 * 60 * 24);
      expect(daysDifference).toBe(30); // January has 31 days, but 31-1 = 30 days difference
    });

    it('should handle empty billing periods', () => {
      const emptyBilling: BillingExport = {
        customerId: 'customer-789',
        billingPeriod: {
          start: new Date('2025-01-01'),
          end: new Date('2025-01-31')
        },
        totalAmountUsd: 0,
        totalCreditsUsed: 0,
        requestCount: 0,
        lineItems: []
      };

      expect(emptyBilling.totalAmountUsd).toBe(0);
      expect(emptyBilling.lineItems.length).toBe(0);
    });
  });

  describe('Real-time Usage Metrics', () => {
    it('should provide real-time metrics dashboard data', () => {
      const realtimeMetrics: RealtimeUsageMetrics = {
        timestamp: new Date(),
        activeApiKeys: 25,
        requestsPerSecond: 4.5,
        averageProcessingTimeMs: 2500,
        currentProfitMargin: 42.3,
        creditsConsumedLastHour: 150,
        errorRate: 5.2,
        topModels: ['gpt-4o-mini', 'claude-3-haiku', 'gpt-4o'],
        alertCount: 2
      };

      expect(realtimeMetrics.activeApiKeys).toBeGreaterThan(0);
      expect(realtimeMetrics.requestsPerSecond).toBeGreaterThan(0);
      expect(realtimeMetrics.currentProfitMargin).toBeGreaterThan(0);
      expect(realtimeMetrics.errorRate).toBeGreaterThanOrEqual(0);
      expect(realtimeMetrics.topModels.length).toBeGreaterThan(0);
    });

    it('should calculate error rate correctly', () => {
      const totalRequests = 100;
      const failedRequests = 5;
      const errorRate = (failedRequests / totalRequests) * 100;

      expect(errorRate).toBe(5.0);
    });

    it('should track requests per second over time windows', () => {
      const timeWindow = 60; // 60 seconds
      const totalRequests = 270; // requests in last minute
      const requestsPerSecond = totalRequests / timeWindow;

      expect(requestsPerSecond).toBe(4.5);
    });

    it('should identify top performing models', () => {
      const modelStats = [
        { name: 'gpt-4o-mini', requests: 100, successRate: 95 },
        { name: 'claude-3-haiku', requests: 80, successRate: 98 },
        { name: 'gpt-4o', requests: 50, successRate: 99 }
      ];

      const sortedByRequests = modelStats
        .sort((a, b) => b.requests - a.requests)
        .map(model => model.name);

      expect(sortedByRequests[0]).toBe('gpt-4o-mini');
      expect(sortedByRequests[1]).toBe('claude-3-haiku');
      expect(sortedByRequests[2]).toBe('gpt-4o');
    });
  });

  describe('Database Query Optimization', () => {
    it('should use efficient aggregation queries', async () => {
      // Mock database aggregation query
      mockSupabase.rpc.mockResolvedValueOnce({
        data: [{
          period: 'day',
          model_used: 'gpt-4o-mini',
          request_count: 50,
          total_cost: 1.25,
          total_revenue: 2.00,
          avg_processing_time: 2500,
          success_rate: 96.0
        }],
        error: null
      });

      const _result = await mockSupabase.rpc('get_usage_analytics', {
        start_date: '2025-01-15',
        end_date: '2025-01-15',
        group_by: 'model'
      });

      expect(result.data[0].request_count).toBe(50);
      expect(result.data[0].success_rate).toBe(96.0);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('get_usage_analytics', {
        start_date: '2025-01-15',
        end_date: '2025-01-15',
        group_by: 'model'
      });
    });

    it('should handle large dataset aggregations efficiently', () => {
      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: `log-${i}`,
        cost: Math.random() * 0.1,
        revenue: Math.random() * 0.15,
        processingTime: 1000 + Math.random() * 3000
      }));

      expect(largeDataset.length).toBe(10000);

      // Simulate aggregation performance
      const startTime = performance.now();
      const totalCost = largeDataset.reduce((sum, item) => sum + item.cost, 0);
      const totalRevenue = largeDataset.reduce((sum, item) => sum + item.revenue, 0);
      const endTime = performance.now();

      expect(totalCost).toBeGreaterThan(0);
      expect(totalRevenue).toBeGreaterThan(totalCost);
      expect(endTime - startTime).toBeLessThan(100); // Should aggregate quickly
    });

    it('should cache frequently accessed metrics', () => {
      const cacheKey = 'daily_metrics_2025-01-15';
      const cachedData = {
        totalRequests: 150,
        totalRevenue: 3.25,
        averageMargin: 45.2,
        cached: true,
        cacheTime: new Date()
      };

      expect(cachedData.cached).toBe(true);
      expect(cachedData.totalRequests).toBeGreaterThan(0);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle concurrent aggregation requests', async () => {
      const concurrentRequests = Array.from({ length: 10 }, (_, i) => 
        Promise.resolve({
          customerId: `customer-${i}`,
          totalRequests: Math.floor(Math.random() * 100),
          totalRevenue: Math.random() * 10
        })
      );

      const results = await Promise.all(concurrentRequests);
      
      expect(results.length).toBe(10);
      results.forEach(result => {
        expect(result.totalRequests).toBeGreaterThanOrEqual(0);
        expect(result.totalRevenue).toBeGreaterThanOrEqual(0);
      });
    });

    it('should complete aggregations within performance targets', () => {
      const startTime = performance.now();
      
      // Simulate complex aggregation
      const metrics = {
        totalRequests: 1000,
        successfulRequests: 950,
        totalCost: 25.50,
        totalRevenue: 42.75,
        averageMargin: 40.35
      };
      
      const endTime = performance.now();
      const duration = endTime - startTime;

      expect(duration).toBeLessThan(200); // Should complete within 200ms
      expect(metrics.totalRequests).toBe(1000);
      expect(metrics.averageMargin).toBeCloseTo(40.35, 2);
    });

    it('should handle memory efficiently for large datasets', () => {
      // Simulate processing large amounts of usage data
      const batchSize = 1000;
      const totalRecords = 50000;
      const batches = Math.ceil(totalRecords / batchSize);

      expect(batches).toBe(50);
      
      // Process in batches to avoid memory issues
      for (let i = 0; i < batches; i++) {
        const batchStart = i * batchSize;
        const batchEnd = Math.min(batchStart + batchSize, totalRecords);
        const batchRecords = batchEnd - batchStart;
        
        expect(batchRecords).toBeLessThanOrEqual(batchSize);
        expect(batchRecords).toBeGreaterThan(0);
      }
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle database connection errors gracefully', async () => {
      mockSupabase.rpc.mockRejectedValueOnce(new Error('Database connection failed'));

      try {
        await mockSupabase.rpc('get_usage_analytics', {});
      } catch (error: any) {
        expect(error.message).toBe('Database connection failed');
      }
    });

    it('should handle malformed usage data', () => {
      const malformedLog = {
        customerId: null, // Invalid
        modelCostUsd: -1,  // Invalid negative cost
        profitMarginPercent: 150 // Invalid percentage > 100
      };

      // Validation checks
      expect(malformedLog.customerId).toBeNull();
      expect(malformedLog.modelCostUsd).toBeLessThan(0);
      expect(malformedLog.profitMarginPercent).toBeGreaterThan(100);
    });

    it('should handle timezone differences in date ranges', () => {
      const utcDate = new Date('2025-01-15T00:00:00Z');
      const localDate = new Date('2025-01-15T00:00:00-08:00'); // PST

      const timeDifference = utcDate.getTime() - localDate.getTime();
      expect(Math.abs(timeDifference)).toBeGreaterThan(0); // Should have timezone offset
    });

    it('should validate aggregation parameters', () => {
      const invalidParams = {
        startDate: '2025-13-45', // Invalid date
        endDate: '2024-01-01',   // End before start
        customerId: '',          // Empty string
        period: 'invalid'        // Invalid period
      };

      // Validation functions would catch these
      expect(() => {
        const date = new Date(invalidParams.startDate);
        if (isNaN(date.getTime())) throw new Error('Invalid start date');
      }).toThrow('Invalid start date');
    });
  });
});