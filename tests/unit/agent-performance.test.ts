/**
 * Agent Performance Tracking Test Suite
 * 
 * Tests for GitHub Issue #18: Agent Performance Tracking
 * Comprehensive test-driven development for agent performance monitoring,
 * benchmarking, pattern analysis, and alerting systems.
 */

import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/database.types';
import {
  AgentPerformanceTracker,
  AgentBenchmarker,
  CustomizationAnalyzer,
  _PerformanceAlerter
} from '../../supabase/functions/_shared/agent-performance';
import { createTestFixtures, type TestFixtures } from '../fixtures/test-fixtures';
import type {
  _AgentPerformanceMetrics,
  _BenchmarkReport,
  CustomizationInsights,
  _PerformanceAlert,
  AgentSummary
} from '../../types/agent-performance.types';

// Test infrastructure setup
const testFixtures = createTestFixtures();
let fixtures: TestFixtures;
let mockSupabase: ReturnType<typeof createClient<Database>>;

// Test setup and teardown
async function setupTest() {
  fixtures = await testFixtures.createFixtures();
  mockSupabase = testFixtures.getSupabase();
}

async function teardownTest() {
  await testFixtures.cleanup();
}

describe('AgentPerformanceTracker', () => {
  let tracker: AgentPerformanceTracker;

  beforeEach(async () => {
    await setupTest();
    tracker = new AgentPerformanceTracker(mockSupabase);
  });

  afterEach(async () => {
    await teardownTest();
  });

  describe('recordPerformance', () => {
    it('should record successful performance metrics', async () => {
      const validMetrics = testFixtures.createValidMetrics(fixtures);
      const result = await tracker.recordPerformance(validMetrics);

      expect(result.success).toBe(true);
      expect(result.metricsRecorded).toBe(true);
      expect(result.aggregationsUpdated).toBe(true);
    });

    it('should record failed performance metrics with error details', async () => {
      const failedMetrics = testFixtures.createFailedMetrics(fixtures);
      const result = await tracker.recordPerformance(failedMetrics);

      expect(result.success).toBe(true);
      expect(result.metricsRecorded).toBe(true);
      expect(result.errorTracked).toBe(true);
    });

    it('should validate required metric fields', async () => {
      const validMetrics = testFixtures.createValidMetrics(fixtures);
      const invalidMetrics = { ...validMetrics };
      delete invalidMetrics.agent_id;

      await expect(tracker.recordPerformance(invalidMetrics as any))
        .rejects.toThrow('Agent ID is required');
    });

    it('should handle database connection errors gracefully', async () => {
      const disconnectedTracker = new AgentPerformanceTracker(null as any);
      const validMetrics = testFixtures.createValidMetrics(fixtures);
      
      await expect(disconnectedTracker.recordPerformance(validMetrics))
        .rejects.toThrow('Database connection failed');
    });
  });

  describe('updateAggregatedMetrics', () => {
    it('should update daily aggregations correctly', async () => {
      const validMetrics = testFixtures.createValidMetrics(fixtures);
      const result = await tracker.recordPerformance(validMetrics);

      expect(result.aggregationsUpdated).toBe(true);
      
      // Wait for triggers to complete
      await testFixtures.waitForAggregation(200);
      
      // Verify aggregation calculations
      const aggregations = await tracker.getDailyAggregations(
        validMetrics.agent_id,
        new Date()
      );
      
      expect(aggregations.total_requests).toBeGreaterThan(0);
      expect(aggregations.avg_processing_time_ms).toBe(validMetrics.processing_time_ms);
      expect(aggregations.avg_accuracy_score).toBe(validMetrics.accuracy_score);
    });

    it('should increment counters for multiple requests', async () => {
      const validMetrics1 = testFixtures.createValidMetrics(fixtures);
      const validMetrics2 = testFixtures.createValidMetrics(fixtures, {
        processing_time_ms: 3000,
        accuracy_score: 0.90,
        correlation_id: `test-${crypto.randomUUID()}`
      });

      await tracker.recordPerformance(validMetrics1);
      await tracker.recordPerformance(validMetrics2);

      // Wait for aggregation triggers
      await testFixtures.waitForAggregation(200);

      const aggregations = await tracker.getDailyAggregations(
        validMetrics1.agent_id,
        new Date()
      );

      expect(aggregations.total_requests).toBe(2);
      expect(aggregations.avg_processing_time_ms).toBe(2750); // (2500 + 3000) / 2
      expect(aggregations.avg_accuracy_score).toBe(0.925); // (0.95 + 0.90) / 2
    });

    it('should handle different document types separately', async () => {
      const invoiceMetrics = testFixtures.createValidMetrics(fixtures, {
        document_type: 'invoice',
        correlation_id: `test-invoice-${crypto.randomUUID()}`
      });
      const contractMetrics = testFixtures.createValidMetrics(fixtures, {
        document_type: 'contract',
        correlation_id: `test-contract-${crypto.randomUUID()}`
      });

      await tracker.recordPerformance(invoiceMetrics);
      await tracker.recordPerformance(contractMetrics);

      // Wait for aggregation triggers
      await testFixtures.waitForAggregation(200);

      const invoiceAgg = await tracker.getDailyAggregations(
        invoiceMetrics.agent_id,
        new Date(),
        'invoice'
      );
      const contractAgg = await tracker.getDailyAggregations(
        contractMetrics.agent_id,
        new Date(),
        'contract'
      );

      expect(invoiceAgg.total_requests).toBe(1);
      expect(contractAgg.total_requests).toBe(1);
    });
  });

  describe('updateAgentSummary', () => {
    it('should calculate 30-day performance summary correctly', async () => {
      // Record multiple metrics over time
      const metricsData = [
        testFixtures.createValidMetrics(fixtures, { processing_time_ms: 2000, accuracy_score: 0.95, correlation_id: `test-1-${crypto.randomUUID()}` }),
        testFixtures.createValidMetrics(fixtures, { processing_time_ms: 3000, accuracy_score: 0.90, correlation_id: `test-2-${crypto.randomUUID()}` }),
        testFixtures.createValidMetrics(fixtures, { processing_time_ms: 2500, accuracy_score: 0.92, correlation_id: `test-3-${crypto.randomUUID()}` })
      ];

      for (const metrics of metricsData) {
        await tracker.recordPerformance(metrics);
      }

      // Wait for aggregation
      await testFixtures.waitForAggregation(200);

      const summary = await tracker.getAgentSummary(fixtures.customAgent.id);

      expect(summary.avgProcessingTime).toBe(2500); // (2000 + 3000 + 2500) / 3
      expect(summary.avgAccuracy).toBe(0.923); // (0.95 + 0.90 + 0.92) / 3 rounded
      expect(summary.successRate).toBe(1.0); // All successful
      expect(summary.requestCount).toBe(3);
    });

    it('should calculate success rate with failed requests', async () => {
      const successMetrics = testFixtures.createValidMetrics(fixtures, { correlation_id: `test-success-${crypto.randomUUID()}` });
      const failureMetrics = testFixtures.createFailedMetrics(fixtures, { correlation_id: `test-failure-${crypto.randomUUID()}` });
      
      await tracker.recordPerformance(successMetrics); // Success
      await tracker.recordPerformance(failureMetrics); // Failure

      // Wait for aggregation
      await testFixtures.waitForAggregation(200);

      const summary = await tracker.getAgentSummary(fixtures.customAgent.id);

      expect(summary.successRate).toBe(0.5); // 1 success out of 2 total
      expect(summary.requestCount).toBe(2);
    });

    it('should exclude metrics older than 30 days', async () => {
      // Record old metric (40 days ago)
      const oldMetrics = testFixtures.createValidMetrics(fixtures, {
        timestamp: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
        correlation_id: `test-old-${crypto.randomUUID()}`
      });
      const _recentMetrics = testFixtures.createValidMetrics(fixtures, {
        correlation_id: `test-recent-${crypto.randomUUID()}`
      });
      
      await tracker.recordPerformance(oldMetrics);
      await tracker.recordPerformance(recentMetrics); // Recent metric

      // Wait for aggregation
      await testFixtures.waitForAggregation(200);

      const summary = await tracker.getAgentSummary(fixtures.customAgent.id);

      expect(summary.requestCount).toBe(1); // Only recent metric counted
    });
  });
});

describe('AgentBenchmarker', () => {
  let benchmarker: AgentBenchmarker;

  beforeEach(async () => {
    await setupTest();
    benchmarker = new AgentBenchmarker(mockSupabase);
  });

  afterEach(async () => {
    await teardownTest();
  });

  describe('benchmarkAgentPerformance', () => {
    it('should compare custom agent against default parent', async () => {
      // Add some performance data for both agents
      const customMetrics = testFixtures.createValidMetrics(fixtures, { 
        agent_id: fixtures.customAgent.id,
        correlation_id: `test-custom-${crypto.randomUUID()}`
      });
      const defaultMetrics = testFixtures.createValidMetrics(fixtures, { 
        agent_id: fixtures.defaultAgent.id,
        correlation_id: `test-default-${crypto.randomUUID()}`
      });

      const tracker = new AgentPerformanceTracker(mockSupabase);
      await tracker.recordPerformance(customMetrics);
      await tracker.recordPerformance(defaultMetrics);
      
      // Wait for aggregation
      await testFixtures.waitForAggregation(200);

      const report = await benchmarker.benchmarkAgentPerformance(fixtures.customAgent.id, 'week');

      expect(report.agent_id).toBe(fixtures.customAgent.id);
      expect(report.parent_agent_id).toBe(fixtures.defaultAgent.id);
      expect(report.comparison).toBeDefined();
      expect(report.comparison.processing_time).toBeDefined();
      expect(report.comparison.accuracy).toBeDefined();
      expect(report.comparison.success_rate).toBeDefined();
      expect(report.comparison.cost_efficiency).toBeDefined();
    });

    it('should calculate improvement percentages correctly', async () => {
      // Create performance data where custom agent performs better
      const customMetrics = testFixtures.createValidMetrics(fixtures, { 
        agent_id: fixtures.customAgent.id,
        processing_time_ms: 2000, // Faster
        accuracy_score: 0.95, // Higher accuracy
        correlation_id: `test-custom-better-${crypto.randomUUID()}`
      });
      const defaultMetrics = testFixtures.createValidMetrics(fixtures, { 
        agent_id: fixtures.defaultAgent.id,
        processing_time_ms: 3000, // Slower
        accuracy_score: 0.85, // Lower accuracy
        correlation_id: `test-default-worse-${crypto.randomUUID()}`
      });

      const tracker = new AgentPerformanceTracker(mockSupabase);
      await tracker.recordPerformance(customMetrics);
      await tracker.recordPerformance(defaultMetrics);
      
      // Wait for aggregation
      await testFixtures.waitForAggregation(200);

      const report = await benchmarker.benchmarkAgentPerformance(fixtures.customAgent.id, 'week');

      // Verify improvement calculation logic
      expect(typeof report.comparison.processing_time.improvement_percent).toBe('number');
      expect(typeof report.comparison.accuracy.improvement_percent).toBe('number');
      
      // Positive improvement means custom agent is better
      if (report.comparison.accuracy.custom > report.comparison.accuracy.default) {
        expect(report.comparison.accuracy.improvement_percent).toBeGreaterThan(0);
      }
    });

    it('should generate appropriate recommendations', async () => {
      const customAgentId = 'custom-agent-123';
      
      const report = await benchmarker.benchmarkAgentPerformance(customAgentId, 'week');

      expect(report.recommendation).toBeDefined();
      expect(typeof report.recommendation).toBe('string');
      expect(report.recommendation.length).toBeGreaterThan(0);
    });

    it('should handle agents without performance data', async () => {
      const newAgentId = 'new-agent-no-data';
      
      const report = await benchmarker.benchmarkAgentPerformance(newAgentId, 'week');

      expect(report.comparison.processing_time.custom).toBe(0);
      expect(report.comparison.accuracy.custom).toBe(0);
      expect(report.recommendation).toContain('insufficient data');
    });

    it('should throw error for agents without parent', async () => {
      const orphanAgentId = 'orphan-agent';
      
      await expect(benchmarker.benchmarkAgentPerformance(orphanAgentId))
        .rejects.toThrow('Cannot benchmark agent without default parent');
    });
  });

  describe('calculateImprovement', () => {
    it('should calculate improvement for higher-is-better metrics', async () => {
      const improvement = benchmarker.calculateImprovement(0.8, 0.9, 'higher_better');
      expect(improvement).toBe(12.5); // (0.9 - 0.8) / 0.8 * 100
    });

    it('should calculate improvement for lower-is-better metrics', async () => {
      const improvement = benchmarker.calculateImprovement(1000, 800, 'lower_better');
      expect(improvement).toBe(20); // (1000 - 800) / 1000 * 100
    });

    it('should handle zero baseline values', async () => {
      const improvement = benchmarker.calculateImprovement(0, 50, 'lower_better');
      expect(improvement).toBe(-100); // Regression when baseline is zero
    });
  });
});

describe('CustomizationAnalyzer', () => {
  let analyzer: CustomizationAnalyzer;

  beforeEach(() => {
    analyzer = new CustomizationAnalyzer(mockSupabase);
  });

  describe('analyzeCustomizationPatterns', () => {
    it('should identify popular prompt modifications', async () => {
      const insights = await analyzer.analyzeCustomizationPatterns();

      expect(insights.total_custom_agents).toBeGreaterThan(0);
      expect(insights.patterns.prompt_modifications).toBeDefined();
      expect(insights.patterns.schema_modifications).toBeDefined();
      expect(insights.patterns.popular_additions).toBeDefined();
    });

    it('should group similar prompt modifications', async () => {
      const insights = await analyzer.analyzeCustomizationPatterns();
      
      const promptPatterns = insights.patterns.prompt_modifications;
      
      expect(Array.isArray(promptPatterns.common_additions)).toBe(true);
      expect(Array.isArray(promptPatterns.common_removals)).toBe(true);
      
      // Each pattern should have usage count
      promptPatterns.common_additions.forEach(pattern => {
        expect(pattern.usage_count).toBeGreaterThan(0);
        expect(pattern.keywords).toBeDefined();
      });
    });

    it('should analyze schema pattern changes', async () => {
      const insights = await analyzer.analyzeCustomizationPatterns();
      
      const schemaPatterns = insights.patterns.schema_modifications;
      
      expect(Array.isArray(schemaPatterns.common_fields)).toBe(true);
      expect(Array.isArray(schemaPatterns.field_type_changes)).toBe(true);
      
      schemaPatterns.common_fields.forEach(field => {
        expect(field.field_name).toBeDefined();
        expect(field.field_type).toBeDefined();
        expect(field.usage_count).toBeGreaterThan(0);
      });
    });

    it('should generate platform recommendations', async () => {
      const insights = await analyzer.analyzeCustomizationPatterns();
      
      expect(Array.isArray(insights.recommendations)).toBe(true);
      
      insights.recommendations.forEach(rec => {
        expect(rec.type).toBeDefined();
        expect(rec.priority).toMatch(/^(high|medium|low)$/);
        expect(rec.description).toBeDefined();
        expect(rec.evidence).toBeDefined();
      });
    });
  });

  describe('analyzePromptPatterns', () => {
    it('should detect common keyword additions', async () => {
      const mockAgents = [
        {
          id: '1',
          system_prompt: 'Extract invoice data with high accuracy',
          parent_agent: { system_prompt: 'Extract invoice data' },
          category: 'invoice'
        },
        {
          id: '2', 
          system_prompt: 'Extract invoice data with extreme accuracy',
          parent_agent: { system_prompt: 'Extract invoice data' },
          category: 'invoice'
        }
      ];

      const patterns = analyzer.analyzePromptPatterns(mockAgents as any);
      
      expect(patterns.common_additions.length).toBeGreaterThan(0);
      expect(patterns.common_additions[0].keywords).toContain('accuracy');
    });

    it('should categorize modifications by document type', async () => {
      const patterns = await analyzer.analyzeCustomizationPatterns();
      
      const promptPatterns = patterns.patterns.prompt_modifications;
      
      // Should group by category
      expect(promptPatterns.by_category).toBeDefined();
      expect(promptPatterns.by_category.invoice).toBeDefined();
      expect(promptPatterns.by_category.contract).toBeDefined();
    });
  });
});

describe('_PerformanceAlerter', () => {
  let alerter: _PerformanceAlerter;

  beforeEach(() => {
    alerter = new _PerformanceAlerter(mockSupabase);
  });

  describe('check_PerformanceAlerts', () => {
    it('should trigger slow processing alert', async () => {
      const slowMetrics = {
        ...mockMetrics,
        processing_time_ms: 15000 // 15 seconds - too slow
      };

      const alerts = await alerter.check_PerformanceAlerts(slowMetrics);
      
      expect(alerts.length).toBeGreaterThan(0);
      
      const slowAlert = alerts.find(a => a.type === 'slow_processing');
      expect(slowAlert).toBeDefined();
      expect(slowAlert?.severity).toBe('warning');
      expect(slowAlert?.message).toContain('10 seconds');
    });

    it('should trigger low accuracy alert', async () => {
      const lowAccuracyMetrics = {
        ...mockMetrics,
        accuracy_score: 0.75 // Below 80% threshold
      };

      const alerts = await alerter.check_PerformanceAlerts(lowAccuracyMetrics);
      
      const accuracyAlert = alerts.find(a => a.type === 'low_accuracy');
      expect(accuracyAlert).toBeDefined();
      expect(accuracyAlert?.severity).toBe('error');
      expect(accuracyAlert?.message).toContain('80%');
    });

    it('should trigger high error rate alert', async () => {
      // Simulate multiple failed requests to trigger error rate alert
      for (let i = 0; i < 5; i++) {
        await alerter.check_PerformanceAlerts({
          ...mockFailedMetrics,
          correlation_id: `corr-${i}`
        });
      }

      const alerts = await alerter.check_PerformanceAlerts(mockFailedMetrics);
      
      const errorRateAlert = alerts.find(a => a.type === 'high_error_rate');
      expect(errorRateAlert).toBeDefined();
      expect(errorRateAlert?.severity).toBe('critical');
    });

    it('should not trigger alerts for good performance', async () => {
      const goodMetrics = {
        ...mockMetrics,
        processing_time_ms: 2000, // Fast
        accuracy_score: 0.95, // High accuracy
        success: true // Successful
      };

      const alerts = await alerter.check_PerformanceAlerts(goodMetrics);
      
      expect(alerts.length).toBe(0);
    });
  });

  describe('sendAlerts', () => {
    it('should store alerts in database', async () => {
      const testAlert: _PerformanceAlert = {
        type: 'slow_processing',
        severity: 'warning',
        agent_id: 'agent-123',
        message: 'Test alert',
        timestamp: new Date()
      };

      const _result = await alerter.sendAlerts([testAlert]);
      
      expect(result.alertsStored).toBe(1);
      expect(result.notificationsSent).toBe(0); // Warning doesn't trigger immediate notification
    });

    it('should send immediate notification for critical alerts', async () => {
      const criticalAlert: _PerformanceAlert = {
        type: 'high_error_rate',
        severity: 'critical',
        agent_id: 'agent-123',
        message: 'Critical alert',
        timestamp: new Date()
      };

      const _result = await alerter.sendAlerts([criticalAlert]);
      
      expect(result.alertsStored).toBe(1);
      expect(result.notificationsSent).toBe(1); // Critical triggers immediate notification
    });
  });

  describe('calculateRecentErrorRate', () => {
    it('should calculate error rate over recent time window', async () => {
      const agentId = 'agent-123';
      
      // Record some successes and failures
      await alerter.check_PerformanceAlerts({ ...mockMetrics, agent_id: agentId });
      await alerter.check_PerformanceAlerts({ ...mockFailedMetrics, agent_id: agentId });
      
      const errorRate = await alerter.calculateRecentErrorRate(agentId);
      
      expect(errorRate).toBe(0.5); // 1 failure out of 2 total
    });

    it('should return 0 for agents with no recent activity', async () => {
      const errorRate = await alerter.calculateRecentErrorRate('nonexistent-agent');
      
      expect(errorRate).toBe(0);
    });
  });
});

describe('Integration Tests', () => {
  let tracker: AgentPerformanceTracker;
  let benchmarker: AgentBenchmarker;
  let analyzer: CustomizationAnalyzer;
  let alerter: _PerformanceAlerter;

  beforeEach(() => {
    tracker = new AgentPerformanceTracker(mockSupabase);
    benchmarker = new AgentBenchmarker(mockSupabase);
    analyzer = new CustomizationAnalyzer(mockSupabase);
    alerter = new _PerformanceAlerter(mockSupabase);
  });

  it('should handle complete performance tracking workflow', async () => {
    // 1. Record performance metrics
    const _result = await tracker.recordPerformance(mockMetrics);
    expect(result.success).toBe(true);

    // 2. Check for alerts
    const alerts = await alerter.check_PerformanceAlerts(mockMetrics);
    expect(Array.isArray(alerts)).toBe(true);

    // 3. Update aggregations
    const summary = await tracker.getAgentSummary(mockMetrics.agent_id);
    expect(summary).toBeDefined();

    // 4. Generate benchmark if custom agent
    if (mockMetrics.agent_id.includes('custom')) {
      const benchmark = await benchmarker.benchmarkAgentPerformance(mockMetrics.agent_id);
      expect(benchmark).toBeDefined();
    }
  });

  it('should maintain data consistency across components', async () => {
    // Record multiple metrics
    const metrics = [
      { ...mockMetrics, agent_id: 'agent-1' },
      { ...mockMetrics, agent_id: 'agent-2' },
      { ...mockFailedMetrics, agent_id: 'agent-1' }
    ];

    for (const metric of metrics) {
      await tracker.recordPerformance(metric);
    }

    // Verify consistent data across all components
    const summary1 = await tracker.getAgentSummary('agent-1');
    const summary2 = await tracker.getAgentSummary('agent-2');

    expect(summary1.requestCount).toBe(2); // 1 success + 1 failure
    expect(summary1.successRate).toBe(0.5);
    expect(summary2.requestCount).toBe(1); // 1 success
    expect(summary2.successRate).toBe(1.0);
  });

  it('should handle high-volume concurrent requests', async () => {
    const requests = Array.from({ length: 100 }, (_, i) => ({
      ...mockMetrics,
      correlation_id: `corr-${i}`,
      processing_time_ms: 2000 + (i * 10) // Varying processing times
    }));

    // Process all requests concurrently
    const results = await Promise.all(
      requests.map(metrics => tracker.recordPerformance(metrics))
    );

    // Verify all succeeded
    expect(results.every(r => r.success)).toBe(true);

    // Verify aggregated data is correct
    const summary = await tracker.getAgentSummary(mockMetrics.agent_id);
    expect(summary.requestCount).toBe(100);
  });
});