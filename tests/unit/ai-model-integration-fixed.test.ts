import { describe, it, expect, mock, beforeEach, afterEach } from 'bun:test';

/**
 * TDD Tests for AI Model Integration (GitHub Issue #8) - Fixed Version
 * 
 * This test suite covers:
 * - OpenAI API integration with GPT-4 for document processing
 * - Claude API integration as secondary processing option  
 * - LlamaParse API integration for complex PDF fallback
 * - API key management for all AI service providers
 * - Request/response logging for all AI service calls
 * - Error handling for AI service timeouts and rate limits
 * - Cost tracking per AI service call with model pricing
 * - Model selection based on document complexity
 * - Response format standardization across models
 */

// Mock environment variables for Bun environment
const mockEnv = {
  OPENAI_API_KEY: 'sk-test-openai-key',
  CLAUDE_API_KEY: 'sk-ant-test-claude-key',
  LLAMAPARSE_API_KEY: 'llx-test-llamaparse-key'
};

// Mock Deno environment for functions
const mockDeno = {
  env: {
    get: (key: string) => mockEnv[key as keyof typeof mockEnv]
  }
};

// Import utility functions - these would be available in the actual Edge Function
interface CostTracking {
  model: string;
  input_tokens: number;
  output_tokens: number;
  cost_usd: number;
  customer_price_usd: number;
  profit_margin_percent: number;
  timestamp: Date;
}

interface Usage {
  prompt_tokens?: number;
  completion_tokens?: number;
  input_tokens?: number;
  output_tokens?: number;
  total_tokens?: number;
  pages?: number;
}

// Utility functions that mirror the Edge Function implementation
function calculateCost(model: string, usage: Usage): CostTracking {
  let cost_usd = 0;
  
  if (model === 'gpt-4' || model === 'openai/gpt-4o') {
    const promptTokens = usage.prompt_tokens || usage.input_tokens || 0;
    const completionTokens = usage.completion_tokens || usage.output_tokens || 0;
    cost_usd = (promptTokens / 1000) * 0.03 + (completionTokens / 1000) * 0.06;
  } else if (model === 'anthropic/claude-3.5-sonnet' || model === 'claude-3-sonnet') {
    const inputTokens = usage.input_tokens || usage.prompt_tokens || 0;
    const outputTokens = usage.output_tokens || usage.completion_tokens || 0;
    cost_usd = (inputTokens / 1000) * 0.003 + (outputTokens / 1000) * 0.015;
  } else if (model === 'google/gemini-flash-1.5') {
    const totalTokens = (usage.prompt_tokens || usage.input_tokens || 0) + 
                       (usage.completion_tokens || usage.output_tokens || 0);
    cost_usd = (totalTokens / 1000000) * 0.075;
  } else if (model === 'llamaparse') {
    cost_usd = (usage.pages || 1) * 0.003;
  }

  const markup = 1.5;
  const customer_price_usd = cost_usd * markup;
  const profit_margin_percent = ((customer_price_usd - cost_usd) / customer_price_usd) * 100;

  return {
    model,
    input_tokens: usage.prompt_tokens || usage.input_tokens || 0,
    output_tokens: usage.completion_tokens || usage.output_tokens || 0,
    cost_usd: Math.round(cost_usd * 10000) / 10000,
    customer_price_usd: Math.round(customer_price_usd * 10000) / 10000,
    profit_margin_percent: Math.round(profit_margin_percent * 100) / 100,
    timestamp: new Date()
  };
}

function calculateComplexity(document: string, metadata?: any): number {
  let score = 0;
  
  score += Math.min(document.length / 1000, 3);
  
  const hasTable = /\||\t|table/i.test(document);
  const hasNumbers = /\d+[.,]\d+|\$\d+/g.test(document);
  const hasMultiColumn = document.split('\n').some(line => line.split(/\s{3,}/).length > 2);
  
  if (hasTable) score += 2;
  if (hasNumbers) score += 1;
  if (hasMultiColumn) score += 2;
  
  if (metadata?.type === 'pdf') {
    score += 2;
    if (metadata.pages && metadata.pages > 5) score += 2;
  }
  
  return Math.min(score, 10);
}

const MODEL_TIERS = {
  fast: ['google/gemini-flash-1.5', 'openai/gpt-4o-mini'],
  balanced: ['anthropic/claude-3.5-sonnet', 'openai/gpt-4o'],
  specialized: ['llamaparse']
};

function selectModel(documentType: string, complexity: number): string {
  if (documentType === 'pdf' && complexity > 8) {
    return MODEL_TIERS.specialized[0];
  } else if (complexity > 5) {
    return MODEL_TIERS.balanced[0];
  } else {
    return MODEL_TIERS.fast[0];
  }
}

describe('AI Model Integration - TDD Implementation (Fixed)', () => {
  beforeEach(() => {
    mock.restore();
  });

  afterEach(() => {
    // Clean up after each test
  });

  describe('OpenAI Integration', () => {
    it('should integrate with OpenAI GPT-4 for document processing', async () => {
      // Arrange
      const mockOpenAIResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              extracted_data: { title: "Test Document", amount: 1500.00 },
              confidence: 0.95
            })
          }
        }],
        usage: {
          prompt_tokens: 1000,
          completion_tokens: 150,
          total_tokens: 1150
        }
      };

      global.fetch = mock(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockOpenAIResponse)
      }));

      const processWithOpenAI = async (document: string, prompt: string) => {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${mockDeno.env.get('OPENAI_API_KEY')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'gpt-4',
            messages: [
              { role: 'system', content: prompt },
              { role: 'user', content: document }
            ],
            temperature: 0.1,
            response_format: { type: "json_object" }
          })
        });

        if (!response.ok) {
          throw new Error(`OpenAI API error: ${response.status}`);
        }

        const _result = await response.json();
        const extractedData = JSON.parse(result.choices[0].message.content);

        return {
          success: true,
          model: 'gpt-4',
          extracted_data: extractedData.extracted_data,
          confidence: extractedData.confidence,
          usage: result.usage
        };
      };

      // Act
      const _result = await processWithOpenAI(
        "Invoice #12345 Total: $1,500.00", 
        "Extract invoice data as JSON"
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.model).toBe('gpt-4');
      expect(result.extracted_data).toEqual({
        title: "Test Document",
        amount: 1500.00
      });
      expect(result.confidence).toBe(0.95);
      expect(result.usage.total_tokens).toBe(1150);
    });

    it('should handle OpenAI API errors gracefully', async () => {
      // Arrange
      global.fetch = mock(() => Promise.resolve({
        ok: false,
        status: 429,
        statusText: 'Rate limit exceeded'
      }));

      const processWithOpenAI = async (document: string, prompt: string) => {
        try {
          const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${mockDeno.env.get('OPENAI_API_KEY')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              model: 'gpt-4',
              messages: [
                { role: 'system', content: prompt },
                { role: 'user', content: document }
              ]
            })
          });

          if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
          }

          return { success: true };
        } catch {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      };

      // Act
      const _result = await processWithOpenAI("test document", "extract data");

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe('OpenAI API error: 429 Rate limit exceeded');
    });

    it('should track OpenAI token usage and costs', async () => {
      // Arrange
      const mockResponse = {
        choices: [{ message: { content: '{"data": "test"}' } }],
        usage: {
          prompt_tokens: 500,
          completion_tokens: 100,
          total_tokens: 600
        }
      };

      global.fetch = mock(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockResponse)
      }));

      const processWithCostTracking = async (document: string, prompt: string) => {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${mockDeno.env.get('OPENAI_API_KEY')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'gpt-4',
            messages: [
              { role: 'system', content: prompt },
              { role: 'user', content: document }
            ]
          })
        });

        const _result = await response.json();
        const cost = calculateCost('gpt-4', result.usage);

        return {
          success: true,
          usage: result.usage,
          cost_usd: cost.cost_usd
        };
      };

      // Act
      const _result = await processWithCostTracking("test doc", "extract");

      // Assert
      expect(result.success).toBe(true);
      expect(result.usage.total_tokens).toBe(600);
      expect(result.cost_usd).toBe(0.021); // (500/1000)*0.03 + (100/1000)*0.06 = 0.015 + 0.006 = 0.021
    });
  });

  describe('Claude Integration', () => {
    it('should integrate with Claude API as secondary processing option', async () => {
      // Arrange
      const mockClaudeResponse = {
        content: [{
          text: JSON.stringify({
            extracted_data: { document_type: "invoice", total: 2500.00 },
            confidence: 0.92
          })
        }],
        usage: {
          input_tokens: 800,
          output_tokens: 120
        }
      };

      global.fetch = mock(() => Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockClaudeResponse)
      }));

      const processWithClaude = async (document: string, prompt: string) => {
        const response = await fetch('https://api.anthropic.com/v1/messages', {
          method: 'POST',
          headers: {
            'x-api-key': mockDeno.env.get('CLAUDE_API_KEY') || '',
            'anthropic-version': '2023-06-01',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'claude-3-sonnet-20240229',
            max_tokens: 1000,
            messages: [{
              role: 'user',
              content: `${prompt}\n\nDocument: ${document}`
            }]
          })
        });

        if (!response.ok) {
          throw new Error(`Claude API error: ${response.status}`);
        }

        const _result = await response.json();
        const extractedData = JSON.parse(result.content[0].text);

        return {
          success: true,
          model: 'claude-3-sonnet',
          extracted_data: extractedData.extracted_data,
          confidence: extractedData.confidence,
          usage: result.usage
        };
      };

      // Act
      const _result = await processWithClaude(
        "INVOICE Total: $2,500.00",
        "Extract invoice data as JSON"
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.model).toBe('claude-3-sonnet');
      expect(result.extracted_data).toEqual({
        document_type: "invoice",
        total: 2500.00
      });
      expect(result.confidence).toBe(0.92);
      expect(result.usage.input_tokens).toBe(800);
    });

    it('should handle Claude API rate limits', async () => {
      // Arrange
      global.fetch = mock(() => Promise.resolve({
        ok: false,
        status: 429,
        json: () => Promise.resolve({
          error: { message: 'Rate limit exceeded' }
        })
      }));

      const processWithClaude = async (document: string, prompt: string) => {
        try {
          const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
              'x-api-key': mockDeno.env.get('CLAUDE_API_KEY') || '',
              'anthropic-version': '2023-06-01',
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              model: 'claude-3-sonnet-20240229',
              max_tokens: 1000,
              messages: [{ role: 'user', content: `${prompt}\n\n${document}` }]
            })
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(`Claude API error: ${response.status} - ${error.error?.message}`);
          }

          return { success: true };
        } catch {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      };

      // Act
      const _result = await processWithClaude("test", "extract");

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('Claude API error: 429');
      expect(result.error).toContain('Rate limit exceeded');
    });
  });

  describe('LlamaParse Integration', () => {
    it('should integrate with LlamaParse for complex PDF processing', async () => {
      // Arrange
      const mockLlamaParseResponse = {
        job_id: "job_123",
        status: "SUCCESS",
        result: {
          markdown: "# Invoice\nTotal: $3,500.00\nDate: 2024-01-15",
          extracted_data: {
            document_type: "invoice",
            total: 3500.00,
            date: "2024-01-15"
          }
        }
      };

      global.fetch = mock()
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ job_id: "job_123" })
        })
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve(mockLlamaParseResponse)
        });

      const processWithLlamaParse = async (pdfFile: File) => {
        // Upload PDF
        const formData = new FormData();
        formData.append('file', pdfFile);
        formData.append('result_type', 'json');

        const uploadResponse = await fetch('https://api.cloud.llamaindex.ai/api/parsing/upload', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${mockDeno.env.get('LLAMAPARSE_API_KEY')}`
          },
          body: formData
        });

        const { job_id } = await uploadResponse.json();

        // Poll for results
        const resultResponse = await fetch(`https://api.cloud.llamaindex.ai/api/parsing/job/${job_id}/result/json`, {
          headers: {
            'Authorization': `Bearer ${mockDeno.env.get('LLAMAPARSE_API_KEY')}`
          }
        });

        const _result = await resultResponse.json();

        return {
          success: true,
          model: 'llamaparse',
          extracted_data: result.result.extracted_data,
          markdown: result.result.markdown,
          job_id: result.job_id
        };
      };

      // Act
      const mockFile = new File(['mock pdf content'], 'test.pdf', { type: 'application/pdf' });
      const _result = await processWithLlamaParse(mockFile);

      // Assert
      expect(result.success).toBe(true);
      expect(result.model).toBe('llamaparse');
      expect(result.extracted_data).toEqual({
        document_type: "invoice",
        total: 3500.00,
        date: "2024-01-15"
      });
      expect(result.markdown).toContain('# Invoice');
      expect(result.job_id).toBe('job_123');
    });

    it('should handle LlamaParse processing failures', async () => {
      // Arrange
      global.fetch = mock(() => Promise.resolve({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          error: 'Invalid file format'
        })
      }));

      const processWithLlamaParse = async (pdfFile: File) => {
        try {
          const formData = new FormData();
          formData.append('file', pdfFile);

          const response = await fetch('https://api.cloud.llamaindex.ai/api/parsing/upload', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${mockDeno.env.get('LLAMAPARSE_API_KEY')}`
            },
            body: formData
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(`LlamaParse error: ${response.status} - ${error.error}`);
          }

          return { success: true };
        } catch {
          return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      };

      // Act
      const mockFile = new File(['invalid'], 'test.txt', { type: 'text/plain' });
      const _result = await processWithLlamaParse(mockFile);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toContain('LlamaParse error: 400');
      expect(result.error).toContain('Invalid file format');
    });
  });

  describe('Cost Tracking System', () => {
    it('should track costs for all AI service calls', async () => {
      // Act
      const openaiUsage = { prompt_tokens: 1000, completion_tokens: 200 };
      const claudeUsage = { input_tokens: 800, output_tokens: 150 };

      const openaiCost = calculateCost('gpt-4', openaiUsage);
      const claudeCost = calculateCost('claude-3-sonnet', claudeUsage);

      // Assert
      expect(openaiCost.cost_usd).toBe(0.042); // (1000/1000)*0.03 + (200/1000)*0.06
      expect(openaiCost.customer_price_usd).toBe(0.063); // 0.042 * 1.5
      expect(openaiCost.profit_margin_percent).toBeCloseTo(33.33, 2);

      expect(claudeCost.cost_usd).toBe(0.0046); // (800/1000)*0.003 + (150/1000)*0.015 = 0.0024 + 0.00225 = 0.00465 rounded to 0.0046
      expect(claudeCost.customer_price_usd).toBe(0.007); // 0.0046 * 1.5 = 0.0069 rounded to 0.007
      expect(claudeCost.profit_margin_percent).toBeCloseTo(33.33, 2);
    });

    it('should alert when profit margin falls below 60%', async () => {
      // Arrange
      const alerts: string[] = [];
      const alertAdmins = async (message: string) => {
        alerts.push(message);
      };

      const trackCostWithMarginCheck = async (usage: CostTracking) => {
        if (usage.profit_margin_percent < 60) {
          await alertAdmins(`Low profit margin: ${usage.profit_margin_percent.toFixed(2)}% for ${usage.model}`);
        }
      };

      // Act
      const lowMarginUsage: CostTracking = {
        model: 'gpt-4',
        input_tokens: 1000,
        output_tokens: 200,
        cost_usd: 0.042,
        customer_price_usd: 0.050, // Only 16% margin
        profit_margin_percent: 16,
        timestamp: new Date()
      };

      await trackCostWithMarginCheck(lowMarginUsage);

      // Assert
      expect(alerts).toHaveLength(1);
      expect(alerts[0]).toContain('Low profit margin: 16.00% for gpt-4');
    });
  });

  describe('Model Selection Logic', () => {
    it('should select appropriate model based on document complexity', () => {
      // Act & Assert
      expect(selectModel('text', 3)).toBe('google/gemini-flash-1.5');
      expect(selectModel('text', 7)).toBe('anthropic/claude-3.5-sonnet');
      expect(selectModel('pdf', 9)).toBe('llamaparse');
    });

    it('should calculate document complexity score', () => {
      // Act & Assert
      expect(calculateComplexity('Simple text')).toBeLessThan(2);
      expect(calculateComplexity('Invoice #123 | Item | Price\nProduct A | $100.00')).toBeGreaterThan(3);
      expect(calculateComplexity('Complex document', { type: 'pdf', pages: 10 })).toBeGreaterThan(4);
    });
  });

  describe('Response Standardization', () => {
    it('should standardize responses across all AI models', () => {
      // Arrange
      interface StandardizedResponse {
        success: boolean;
        extracted_data: Record<string, any>;
        confidence: number;
        model_used: string;
        processing_time_ms: number;
        cost_breakdown: {
          model_cost_usd: number;
          customer_price_usd: number;
          profit_margin_percent: number;
        };
      }

      const standardizeResponse = (
        rawResponse: any,
        model: string,
        startTime: number,
        costData: any
      ): StandardizedResponse => {
        return {
          success: true,
          extracted_data: rawResponse.extracted_data || {},
          confidence: rawResponse.confidence || 0.5,
          model_used: model,
          processing_time_ms: Date.now() - startTime,
          cost_breakdown: {
            model_cost_usd: costData.cost_usd,
            customer_price_usd: costData.customer_price_usd,
            profit_margin_percent: costData.profit_margin_percent
          }
        };
      };

      // Act
      const startTime = Date.now() - 1000; // 1 second ago
      const mockResponse = {
        extracted_data: { title: 'Test', amount: 100 },
        confidence: 0.9
      };
      const mockCost = {
        cost_usd: 0.01,
        customer_price_usd: 0.015,
        profit_margin_percent: 33.33
      };

      const _result = standardizeResponse(mockResponse, 'gpt-4', startTime, mockCost);

      // Assert
      expect(result.success).toBe(true);
      expect(result.extracted_data).toEqual({ title: 'Test', amount: 100 });
      expect(result.confidence).toBe(0.9);
      expect(result.model_used).toBe('gpt-4');
      expect(result.processing_time_ms).toBeGreaterThan(900);
      expect(result.cost_breakdown.model_cost_usd).toBe(0.01);
      expect(result.cost_breakdown.customer_price_usd).toBe(0.015);
      expect(result.cost_breakdown.profit_margin_percent).toBe(33.33);
    });

    it('should handle error responses consistently', () => {
      // Arrange
      const standardizeErrorResponse = (
        error: Error,
        model: string,
        startTime: number
      ): StandardizedResponse => {
        return {
          success: false,
          extracted_data: {},
          confidence: 0,
          model_used: model,
          processing_time_ms: Date.now() - startTime,
          cost_breakdown: {
            model_cost_usd: 0,
            customer_price_usd: 0,
            profit_margin_percent: 0
          },
          error: error.message
        } as any;
      };

      // Act
      const startTime = Date.now() - 500;
      const error = new Error('API rate limit exceeded');
      const _result = standardizeErrorResponse(error, 'claude-3-sonnet', startTime);

      // Assert
      expect(result.success).toBe(false);
      expect(result.extracted_data).toEqual({});
      expect(result.confidence).toBe(0);
      expect(result.model_used).toBe('claude-3-sonnet');
      expect((result as any).error).toBe('API rate limit exceeded');
    });
  });

  describe('Request/Response Logging', () => {
    it('should log all AI service requests and responses', async () => {
      // Arrange
      const logs: any[] = [];
      const logAIRequest = async (data: any) => {
        logs.push({
          type: 'ai_request',
          timestamp: new Date().toISOString(),
          ...data
        });
      };

      const processWithLogging = async (document: string, model: string) => {
        const startTime = Date.now();
        
        await logAIRequest({
          model,
          document_length: document.length,
          request_id: 'req_123'
        });

        // Simulate AI processing
        const mockResponse = { success: true, data: 'extracted' };
        
        await logAIRequest({
          model,
          response: mockResponse,
          processing_time_ms: Date.now() - startTime,
          request_id: 'req_123'
        });

        return mockResponse;
      };

      // Act
      await processWithLogging('test document', 'gpt-4');

      // Assert
      expect(logs).toHaveLength(2);
      expect(logs[0].type).toBe('ai_request');
      expect(logs[0].model).toBe('gpt-4');
      expect(logs[0].document_length).toBe(13);
      expect(logs[1].response.success).toBe(true);
    });
  });
});