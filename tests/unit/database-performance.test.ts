import { describe, it, expect, beforeAll, beforeEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';

// Test Database Performance Requirements
describe('Database Performance Requirements - Issue #2', () => {
  let supabase: any;
  let adminSupabase: any;
  
  beforeAll(() => {
    const supabaseUrl = 'http://127.0.0.1:14321';
    const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
    const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';
    
    supabase = createClient(supabaseUrl, anonKey);
    adminSupabase = createClient(supabaseUrl, serviceRoleKey);
  });

  describe('API Key Lookup Performance (<50ms)', () => {
    beforeEach(async () => {
      // Create test customers first (with UUIDs)
      const testCustomers = Array.from({ length: 10 }, (_, i) => ({
        id: `123e4567-e89b-12d3-a456-42661417400${i}`,
        company_name: `Perf Company ${i}`,
        email: `perf${i}@example.com`,
        tier: 'standard'
      }));
      await adminSupabase.from('customers').upsert(testCustomers);

      // Create test data for performance testing with proper UUIDs
      const testApiKeys = Array.from({ length: 100 }, (_, i) => ({
        id: `123e4567-e89b-12d3-a456-42661417${String(i + 100).padStart(4, '0')}`,
        customer_id: `123e4567-e89b-12d3-a456-42661417400${i % 10}`, // 10 different customers
        key_type: i % 2 === 0 ? 'test' : 'production',
        key_prefix: i % 2 === 0 ? 'skt_' : 'skp_',
        key_hash: `hash_${i.toString().padStart(3, '0')}_${'x'.repeat(57)}`, // 64 char hash
        credits: 1000 + i,
        rate_limits: { requests_per_minute: 100 }
      }));

      await adminSupabase.from('api_keys').upsert(testApiKeys);
    });

    it('should lookup API key by hash in <50ms', async () => {
      const testHash = 'hash_050_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';
      
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .from('api_keys')
        .select('*')
        .eq('key_hash', testHash)
        .single();
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`API key lookup time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(50);
        expect(data.key_hash).toBe(testHash);
      }
    });

    it('should validate API key exists efficiently', async () => {
      const testHash = 'hash_025_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';
      
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .from('api_keys')
        .select('id, customer_id, credits')
        .eq('key_hash', testHash)
        .single();
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`API key validation time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(50);
      }
    });

    it('should handle non-existent key lookup efficiently', async () => {
      const nonExistentHash = 'nonexistent_hash_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';
      
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .from('api_keys')
        .select('*')
        .eq('key_hash', nonExistentHash)
        .single();
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`Non-existent key lookup time: ${queryTime.toFixed(2)}ms`);
      
      // Should still be fast even for non-existent keys
      expect(queryTime).toBeLessThan(50);
      expect(data).toBeNull();
    });
  });

  describe('Customer Data Queries (<100ms)', () => {
    beforeEach(async () => {
      // Create test customers with proper UUIDs
      const testCustomers = Array.from({ length: 50 }, (_, i) => ({
        id: `123e4567-e89b-12d3-a456-42661417${String(i + 200).padStart(4, '0')}`,
        company_name: `Test Company ${i}`,
        email: `test${i}@example.com`,
        tier: i % 3 === 0 ? 'premium' : 'standard',
        created_at: new Date(Date.now() - i * 86400000).toISOString() // Days ago
      }));

      await adminSupabase.from('customers').upsert(testCustomers);
    });

    it('should query customer by ID in <100ms', async () => {
      const customerId = '123e4567-e89b-12d3-a456-************';
      
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .from('customers')
        .select('*')
        .eq('id', customerId)
        .single();
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`Customer query time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(100);
        expect(data.id).toBe(customerId);
      }
    });

    it('should query customers by tier efficiently', async () => {
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .from('customers')
        .select('*')
        .eq('tier', 'premium');
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`Customer tier query time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(100);
        expect(data.length).toBeGreaterThan(0);
      }
    });

    it('should query customer with related API keys efficiently', async () => {
      const customerId = '123e4567-e89b-12d3-a456-426614174210';
      
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .from('customers')
        .select(`
          *,
          api_keys (
            id,
            key_type,
            credits,
            created_at
          )
        `)
        .eq('id', customerId)
        .single();
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`Customer with API keys query time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(100);
      }
    });
  });

  describe('Usage Aggregation Performance (<200ms)', () => {
    beforeEach(async () => {
      // Create test usage logs
      const testUsageLogs = Array.from({ length: 1000 }, (_, i) => ({
        id: `usage-${i}`,
        customer_id: `customer-${i % 20}`, // 20 different customers
        api_key_id: `key-${i % 50}`, // 50 different API keys
        model_cost: 0.001 + (i % 10) * 0.001,
        customer_price: 0.002 + (i % 10) * 0.002,
        created_at: new Date(Date.now() - (i % 30) * 86400000).toISOString() // Last 30 days
      }));

      await adminSupabase.from('usage_logs').upsert(testUsageLogs);
    });

    it('should aggregate customer usage efficiently', async () => {
      const customerId = 'customer-5';
      
      const startTime = performance.now();
      
      const { data, error } = await adminSupabase
        .rpc('get_customer_usage_summary', {
          customer_id: customerId,
          start_date: new Date(Date.now() - 30 * 86400000).toISOString(),
          end_date: new Date().toISOString()
        });
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`Usage aggregation time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(200);
      }
    });

    it('should calculate monthly costs efficiently', async () => {
      const startTime = performance.now();
      
      // Use RPC call for aggregation since .group() doesn't exist in Supabase client
      const { data, error } = await adminSupabase
        .rpc('get_monthly_usage_summary', {
          days_back: 30
        });
      
      const endTime = performance.now();
      const queryTime = endTime - startTime;
      
      console.log(`Monthly cost calculation time: ${queryTime.toFixed(2)}ms`);
      
      if (!error) {
        expect(queryTime).toBeLessThan(200);
      }
    });
  });

  describe('Index Effectiveness', () => {
    it('should have proper index on api_keys.key_hash', async () => {
      // Check that the index exists and is being used
      const { data, error } = await adminSupabase
        .rpc('explain_query', {
          query: `
            SELECT * FROM api_keys 
            WHERE key_hash = 'test_hash'
          `
        });
      
      if (!error) {
        // Should use index scan, not sequential scan
        expect(data.toLowerCase()).toContain('index');
        expect(data.toLowerCase()).not.toContain('seq scan');
      }
    });

    it('should have proper index on customers.id', async () => {
      const { data, error } = await adminSupabase
        .rpc('explain_query', {
          query: `
            SELECT * FROM customers 
            WHERE id = 'test_customer_id'
          `
        });
      
      if (!error) {
        expect(data.toLowerCase()).toContain('index');
      }
    });

    it('should have proper indexes on usage_logs for aggregation', async () => {
      const { data, error } = await adminSupabase
        .rpc('explain_query', {
          query: `
            SELECT customer_id, SUM(model_cost) 
            FROM usage_logs 
            WHERE created_at >= '2025-01-01'
            GROUP BY customer_id
          `
        });
      
      if (!error) {
        // Should use efficient grouping with proper indexes
        expect(data.toLowerCase()).not.toContain('seq scan on usage_logs');
      }
    });
  });

  describe('Constraint and Foreign Key Performance', () => {
    it('should validate foreign key constraints efficiently', async () => {
      const startTime = performance.now();
      
      // Insert with valid foreign key
      const { error } = await adminSupabase
        .from('api_keys')
        .insert({
          customer_id: 'perf-customer-1',
          key_type: 'test',
          key_hash: 'test_constraint_hash_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
          credits: 100
        });
      
      const endTime = performance.now();
      const insertTime = endTime - startTime;
      
      console.log(`Foreign key validation time: ${insertTime.toFixed(2)}ms`);
      
      // Should be fast even with FK constraints
      expect(insertTime).toBeLessThan(100);
    });

    it('should handle unique constraint violations efficiently', async () => {
      const duplicateHash = 'duplicate_hash_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';
      
      // Insert first record
      await adminSupabase.from('api_keys').insert({
        customer_id: 'perf-customer-1',
        key_type: 'test',
        key_hash: duplicateHash,
        credits: 100
      });

      const startTime = performance.now();
      
      // Try to insert duplicate
      const { error } = await adminSupabase
        .from('api_keys')
        .insert({
          customer_id: 'perf-customer-2',
          key_type: 'test',
          key_hash: duplicateHash,
          credits: 200
        });
      
      const endTime = performance.now();
      const constraintTime = endTime - startTime;
      
      console.log(`Unique constraint check time: ${constraintTime.toFixed(2)}ms`);
      
      // Should fail quickly due to unique constraint
      expect(error).not.toBeNull();
      expect(constraintTime).toBeLessThan(50);
    });
  });
});