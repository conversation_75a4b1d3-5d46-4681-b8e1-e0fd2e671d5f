/**
 * Agent Cloning System Unit Tests
 * GitHub Issue #15: Agent Cloning System
 * 
 * Unit tests for core cloning logic, validation, and utilities
 */

import { describe, it, expect } from 'bun:test';

// Types for testing
interface Agent {
  id: string;
  agent_id: string;
  name: string;
  description: string | null;
  category: string;
  prompt: string;
  json_schema: object;
  version: number;
  is_default: boolean;
  customer_id: string | null;
  parent_agent_id: string | null;
  status: string;
  created_at: string;
  updated_at: string;
}

interface Customer {
  id: string;
  tier: 'free' | 'starter' | 'professional' | 'enterprise';
  status: string;
}

interface CloneValidationResult {
  isValid: boolean;
  error?: string;
  maxClones?: number;
  currentClones?: number;
}

interface CloneParams {
  sourceAgent: Agent;
  customerId: string;
  name?: string;
  description?: string;
  category?: string;
}

interface ClonedAgent extends Agent {
  is_customizable: boolean;
  clone_generation: number;
}

// Mock data
const mockDefaultAgent: Agent = {
  id: 'agent-001',
  agent_id: 'default-invoice-v1',
  name: 'Advanced Invoice Processor',
  description: 'Production-grade invoice processor',
  category: 'invoice',
  prompt: 'You are an expert invoice processor...',
  json_schema: {
    type: 'object',
    properties: {
      vendor_name: { type: 'string' },
      total_amount: { type: 'number' }
    }
  },
  version: 1,
  is_default: true,
  customer_id: null,
  parent_agent_id: null,
  status: 'active',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z'
};

const mockCustomAgent: Agent = {
  ...mockDefaultAgent,
  id: 'agent-002',
  agent_id: 'custom-invoice-001',
  name: 'My Custom Invoice Agent',
  is_default: false,
  customer_id: 'customer-001',
  parent_agent_id: 'agent-001'
};

const mockCustomers: Customer[] = [
  { id: 'customer-free', tier: 'free', status: 'active' },
  { id: 'customer-starter', tier: 'starter', status: 'active' },
  { id: 'customer-pro', tier: 'professional', status: 'active' },
  { id: 'customer-enterprise', tier: 'enterprise', status: 'active' }
];

// Clone tier limits (from implementation)
const CLONE_LIMITS = {
  free: 2,
  starter: 10,
  professional: 50,
  enterprise: -1 // Unlimited
};

describe('Agent Cloning Core Logic', () => {

  describe('Clone Validation', () => {

    it('should validate agent clonability', () => {
      const validateClonability = (agent: Agent): boolean => {
        return agent.status === 'active' && !agent.agent_id.includes('deprecated');
      };

      expect(validateClonability(mockDefaultAgent)).toBe(true);
      
      const deprecatedAgent = { ...mockDefaultAgent, agent_id: 'deprecated-agent-v1' };
      expect(validateClonability(deprecatedAgent)).toBe(false);
      
      const inactiveAgent = { ...mockDefaultAgent, status: 'inactive' };
      expect(validateClonability(inactiveAgent)).toBe(false);
    });

    it('should validate customer permissions', () => {
      const validateCustomerPermissions = (customer: Customer, sourceAgent: Agent): CloneValidationResult => {
        // Free tier can only clone default agents
        if (customer.tier === 'free' && !sourceAgent.is_default) {
          return {
            isValid: false,
            error: 'Free tier customers can only clone default agents'
          };
        }

        // Check if customer is active
        if (customer.status !== 'active') {
          return {
            isValid: false,
            error: 'Customer account is not active'
          };
        }

        return { isValid: true };
      };

      const freeCustomer = mockCustomers.find(c => c.tier === 'free')!;
      
      // Free tier cloning default agent should succeed
      expect(validateCustomerPermissions(freeCustomer, mockDefaultAgent).isValid).toBe(true);
      
      // Free tier cloning custom agent should fail
      const _result = validateCustomerPermissions(freeCustomer, mockCustomAgent);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Free tier customers can only clone default agents');
    });

    it('should validate clone limits by tier', () => {
      const validateCloneLimits = (customer: Customer, currentCloneCount: number): CloneValidationResult => {
        const maxClones = CLONE_LIMITS[customer.tier];
        
        if (maxClones === -1) {
          return { isValid: true, maxClones: -1, currentClones: currentCloneCount };
        }

        if (currentCloneCount >= maxClones) {
          return {
            isValid: false,
            error: `Clone limit reached. Maximum ${maxClones} clones allowed for ${customer.tier} tier`,
            maxClones,
            currentClones: currentCloneCount
          };
        }

        return { isValid: true, maxClones, currentClones: currentCloneCount };
      };

      const freeCustomer = mockCustomers.find(c => c.tier === 'free')!;
      const enterpriseCustomer = mockCustomers.find(c => c.tier === 'enterprise')!;

      // Free tier within limit
      expect(validateCloneLimits(freeCustomer, 1).isValid).toBe(true);
      
      // Free tier at limit
      const atLimitResult = validateCloneLimits(freeCustomer, 2);
      expect(atLimitResult.isValid).toBe(false);
      expect(atLimitResult.error).toContain('Clone limit reached');
      
      // Enterprise tier unlimited
      expect(validateCloneLimits(enterpriseCustomer, 100).isValid).toBe(true);
    });
  });

  describe('Clone Name Generation', () => {

    it('should generate unique clone names', () => {
      const generateUniqueCloneName = (baseName: string, existingNames: string[]): string => {
        let attempt = 1;
        let candidateName = `${baseName} (Custom)`;

        while (existingNames.includes(candidateName)) {
          attempt++;
          candidateName = `${baseName} (Custom ${attempt})`;
        }

        return candidateName;
      };

      const existingNames = ['My Agent (Custom)', 'My Agent (Custom 2)'];
      
      const newName = generateUniqueCloneName('My Agent', existingNames);
      expect(newName).toBe('My Agent (Custom 3)');
      
      const firstCloneName = generateUniqueCloneName('New Agent', []);
      expect(firstCloneName).toBe('New Agent (Custom)');
    });

    it('should validate name conflicts', () => {
      const checkNamingConflict = (name: string, customerId: string, existingAgents: Agent[]): boolean => {
        return existingAgents.some(agent => 
          agent.name === name && agent.customer_id === customerId
        );
      };

      const existingAgents = [mockCustomAgent];
      
      // Same name, same customer - conflict
      expect(checkNamingConflict('My Custom Invoice Agent', 'customer-001', existingAgents)).toBe(true);
      
      // Same name, different customer - no conflict
      expect(checkNamingConflict('My Custom Invoice Agent', 'customer-002', existingAgents)).toBe(false);
      
      // Different name, same customer - no conflict
      expect(checkNamingConflict('Different Agent', 'customer-001', existingAgents)).toBe(false);
    });
  });

  describe('Clone Creation Logic', () => {

    it('should create clone with inherited properties', () => {
      const createClone = (params: CloneParams): ClonedAgent => {
        const cloneId = `clone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const cloneName = params.name || `${params.sourceAgent.name} (Custom)`;
        
        return {
          id: cloneId,
          agent_id: `custom-${cloneId}`,
          name: cloneName,
          description: params.description || `Customized version of ${params.sourceAgent.name}`,
          category: params.category || params.sourceAgent.category,
          prompt: params.sourceAgent.prompt, // Inherited
          json_schema: params.sourceAgent.json_schema, // Inherited
          version: 1, // Clones start at v1
          is_default: false,
          customer_id: params.customerId,
          parent_agent_id: params.sourceAgent.id,
          status: 'active',
          is_customizable: true,
          clone_generation: (params.sourceAgent as any).clone_generation ? (params.sourceAgent as any).clone_generation + 1 : 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      };

      const cloneParams: CloneParams = {
        sourceAgent: mockDefaultAgent,
        customerId: 'customer-001',
        name: 'My Custom Clone',
        description: 'Test clone description'
      };

      const clone = createClone(cloneParams);

      expect(clone.name).toBe('My Custom Clone');
      expect(clone.description).toBe('Test clone description');
      expect(clone.customer_id).toBe('customer-001');
      expect(clone.parent_agent_id).toBe(mockDefaultAgent.id);
      expect(clone.is_default).toBe(false);
      expect(clone.is_customizable).toBe(true);
      expect(clone.prompt).toBe(mockDefaultAgent.prompt);
      expect(clone.json_schema).toEqual(mockDefaultAgent.json_schema);
      expect(clone.category).toBe(mockDefaultAgent.category);
      expect(clone.version).toBe(1);
      expect(clone.clone_generation).toBe(1);
    });

    it('should handle clone of clone (nested cloning)', () => {
      const createClone = (params: CloneParams): ClonedAgent => {
        const cloneId = `clone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const sourceCloneGeneration = (params.sourceAgent as any).clone_generation || 0;
        
        return {
          id: cloneId,
          agent_id: `custom-${cloneId}`,
          name: params.name || `${params.sourceAgent.name} (Custom)`,
          description: params.description || `Customized version of ${params.sourceAgent.name}`,
          category: params.category || params.sourceAgent.category,
          prompt: params.sourceAgent.prompt,
          json_schema: params.sourceAgent.json_schema,
          version: 1,
          is_default: false,
          customer_id: params.customerId,
          parent_agent_id: params.sourceAgent.id,
          status: 'active',
          is_customizable: true,
          clone_generation: sourceCloneGeneration + 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
      };

      // First clone from default
      const firstClone = createClone({
        sourceAgent: mockDefaultAgent,
        customerId: 'customer-001',
        name: 'First Clone'
      });

      expect(firstClone.clone_generation).toBe(1);

      // Second clone from first clone
      const secondClone = createClone({
        sourceAgent: firstClone,
        customerId: 'customer-001',
        name: 'Second Clone'
      });

      expect(secondClone.clone_generation).toBe(2);
      expect(secondClone.parent_agent_id).toBe(firstClone.id);
    });
  });

  describe('Bulk Clone Logic', () => {

    it('should handle bulk clone operations', () => {
      interface BulkCloneResult {
        successful: Array<{
          sourceAgentId: string;
          clonedAgent: ClonedAgent;
        }>;
        failed: Array<{
          sourceAgentId: string;
          error: string;
        }>;
      }

      const processBulkClone = (
        sourceAgentIds: string[],
        agents: Agent[],
        customerId: string,
        namePrefix?: string
      ): BulkCloneResult => {
        const result: BulkCloneResult = {
          successful: [],
          failed: []
        };

        sourceAgentIds.forEach(agentId => {
          const sourceAgent = agents.find(a => a.id === agentId);
          
          if (!sourceAgent) {
            result.failed.push({
              sourceAgentId: agentId,
              error: 'Agent not found'
            });
            return;
          }

          if (sourceAgent.status !== 'active') {
            result.failed.push({
              sourceAgentId: agentId,
              error: 'Agent is not active'
            });
            return;
          }

          try {
            const cloneName = namePrefix 
              ? `${namePrefix} ${sourceAgent.name}`
              : `${sourceAgent.name} (Custom)`;

            const clone: ClonedAgent = {
              id: `clone-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              agent_id: `custom-bulk-${Date.now()}`,
              name: cloneName,
              description: `Bulk cloned version of ${sourceAgent.name}`,
              category: sourceAgent.category,
              prompt: sourceAgent.prompt,
              json_schema: sourceAgent.json_schema,
              version: 1,
              is_default: false,
              customer_id: customerId,
              parent_agent_id: sourceAgent.id,
              status: 'active',
              is_customizable: true,
              clone_generation: 1,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };

            result.successful.push({
              sourceAgentId: agentId,
              clonedAgent: clone
            });

          } catch {
            result.failed.push({
              sourceAgentId: agentId,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        });

        return result;
      };

      const availableAgents = [mockDefaultAgent, mockCustomAgent];
      const sourceIds = [mockDefaultAgent.id, mockCustomAgent.id, 'non-existent-id'];

      const _result = processBulkClone(sourceIds, availableAgents, 'customer-001', 'Bulk Test');

      expect(result.successful.length).toBe(2);
      expect(result.failed.length).toBe(1);
      expect(result.failed[0].sourceAgentId).toBe('non-existent-id');
      expect(result.failed[0].error).toBe('Agent not found');
      
      result.successful.forEach(success => {
        expect(success.clonedAgent.name).toContain('Bulk Test');
        expect(success.clonedAgent.customer_id).toBe('customer-001');
      });
    });
  });

  describe('Audit Logging Logic', () => {

    it('should generate proper audit log entries', () => {
      interface AuditLogEntry {
        customer_id: string;
        api_key_id: string;
        action: string;
        resource_type: string;
        resource_id: string;
        success: boolean;
        metadata: {
          source_agent_id: string;
          cloned_agent_id: string;
          clone_timestamp: string;
          clone_name?: string;
        };
        error_message?: string;
      }

      const createCloneAuditLog = (
        customerId: string,
        apiKeyId: string,
        sourceAgentId: string,
        clonedAgentId: string,
        cloneName: string,
        success: boolean,
        errorMessage?: string
      ): AuditLogEntry => {
        return {
          customer_id: customerId,
          api_key_id: apiKeyId,
          action: 'agent_clone',
          resource_type: 'agent',
          resource_id: clonedAgentId,
          success,
          metadata: {
            source_agent_id: sourceAgentId,
            cloned_agent_id: clonedAgentId,
            clone_timestamp: new Date().toISOString(),
            clone_name: cloneName
          },
          error_message: errorMessage
        };
      };

      // Successful clone log
      const successLog = createCloneAuditLog(
        'customer-001',
        'key-001',
        'source-agent-001',
        'cloned-agent-001',
        'My Custom Agent',
        true
      );

      expect(successLog.action).toBe('agent_clone');
      expect(successLog.success).toBe(true);
      expect(successLog.metadata.source_agent_id).toBe('source-agent-001');
      expect(successLog.metadata.cloned_agent_id).toBe('cloned-agent-001');
      expect(successLog.metadata.clone_name).toBe('My Custom Agent');
      expect(successLog.error_message).toBeUndefined();

      // Failed clone log
      const failLog = createCloneAuditLog(
        'customer-001',
        'key-001',
        'source-agent-001',
        'failed-clone-001',
        'Failed Clone',
        false,
        'Agent not found'
      );

      expect(failLog.success).toBe(false);
      expect(failLog.error_message).toBe('Agent not found');
    });
  });

  describe('Error Handling', () => {

    it('should handle validation errors gracefully', () => {
      interface ValidationError extends Error {
        code: string;
        statusCode: number;
      }

      const createValidationError = (message: string, code: string, statusCode: number): ValidationError => {
        const error = new Error(message) as ValidationError;
        error.code = code;
        error.statusCode = statusCode;
        return error;
      };

      const validateCloneRequest = (sourceAgentId: string, customerId: string, name?: string): void => {
        if (!sourceAgentId) {
          throw createValidationError('Source agent ID is required', 'MISSING_SOURCE_AGENT', 400);
        }

        if (!customerId) {
          throw createValidationError('Customer ID is required', 'MISSING_CUSTOMER', 400);
        }

        if (name && name.length > 100) {
          throw createValidationError('Agent name too long (max 100 characters)', 'NAME_TOO_LONG', 400);
        }

        if (name && name.trim().length === 0) {
          throw createValidationError('Agent name cannot be empty', 'EMPTY_NAME', 400);
        }
      };

      // Valid request should not throw
      expect(() => validateCloneRequest('agent-001', 'customer-001', 'Valid Name')).not.toThrow();

      // Missing source agent should throw
      expect(() => validateCloneRequest('', 'customer-001')).toThrow('Source agent ID is required');

      // Missing customer should throw
      expect(() => validateCloneRequest('agent-001', '')).toThrow('Customer ID is required');

      // Name too long should throw
      const longName = 'a'.repeat(101);
      expect(() => validateCloneRequest('agent-001', 'customer-001', longName)).toThrow('Agent name too long');

      // Empty name should throw
      expect(() => validateCloneRequest('agent-001', 'customer-001', '   ')).toThrow('Agent name cannot be empty');
    });
  });
});

describe('Clone Utility Functions', () => {

  it('should generate agent IDs correctly', () => {
    const generateAgentId = (prefix = 'custom'): string => {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substr(2, 9);
      return `${prefix}-${timestamp}-${random}`;
    };

    const agentId = generateAgentId();
    expect(agentId).toMatch(/^custom-\d+-[a-z0-9]{9}$/);

    const customPrefix = generateAgentId('clone');
    expect(customPrefix).toMatch(/^clone-\d+-[a-z0-9]{9}$/);
  });

  it('should validate JSON schema inheritance', () => {
    const validateSchemaInheritance = (parentSchema: any, childSchema: any): boolean => {
      // Simple validation: child should have same structure as parent initially
      if (parentSchema.type !== childSchema.type) return false;
      if (parentSchema.properties && childSchema.properties) {
        const parentProps = Object.keys(parentSchema.properties);
        const childProps = Object.keys(childSchema.properties);
        return parentProps.every(key => childProps.includes(key));
      }
      return true;
    };

    const parentSchema = {
      type: 'object',
      properties: {
        vendor_name: { type: 'string' },
        total_amount: { type: 'number' }
      }
    };

    const validChildSchema = { ...parentSchema };
    expect(validateSchemaInheritance(parentSchema, validChildSchema)).toBe(true);

    const invalidChildSchema = {
      type: 'object',
      properties: {
        vendor_name: { type: 'string' }
        // Missing total_amount
      }
    };
    expect(validateSchemaInheritance(parentSchema, invalidChildSchema)).toBe(false);
  });
});