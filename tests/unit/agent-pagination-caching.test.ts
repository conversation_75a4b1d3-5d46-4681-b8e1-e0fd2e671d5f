import { describe, it, expect, _beforeEach, _mock } from 'bun:test';

// Agent Pagination & Caching Unit Tests
// Tests for GitHub Issue #14: Agent Storage & Retrieval
// Pagination logic and response caching for improved performance

interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

interface PaginationResult {
  current_page: number;
  total_pages: number;
  total_count: number;
  page_size: number;
  has_next_page: boolean;
  has_previous_page: boolean;
  next_page?: number;
  previous_page?: number;
}

interface CacheEntry {
  key: string;
  data: any;
  timestamp: number;
  ttl: number;
  expires_at: number;
}

interface CacheConfig {
  defaultTTL: number;
  maxEntries: number;
  enabled: boolean;
}

// Mock pagination utility functions
const calculatePagination = mock((page: number, limit: number, totalCount: number): PaginationResult => {
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;
  
  return {
    current_page: page,
    total_pages: totalPages,
    total_count: totalCount,
    page_size: limit,
    has_next_page: hasNextPage,
    has_previous_page: hasPreviousPage,
    next_page: hasNextPage ? page + 1 : undefined,
    previous_page: hasPreviousPage ? page - 1 : undefined
  };
});

const validatePaginationParams = mock((page?: number, limit?: number): PaginationParams => {
  const validatedPage = Math.max(1, page || 1);
  const validatedLimit = Math.min(50, Math.max(1, limit || 10));
  const offset = (validatedPage - 1) * validatedLimit;
  
  return {
    page: validatedPage,
    limit: validatedLimit,
    offset
  };
});

const calculateOffset = mock((page: number, limit: number): number => {
  return (page - 1) * limit;
});

// Mock caching functions
class MockCache {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig = {
    defaultTTL: 300000, // 5 minutes
    maxEntries: 1000,
    enabled: true
  };

  get = mock(async (key: string): Promise<any | null> => {
    if (!this.config.enabled) return null;
    
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() > entry.expires_at) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  });

  set = mock(async (key: string, data: any, ttl?: number): Promise<void> => {
    if (!this.config.enabled) return;
    
    // Evict oldest entries if at capacity
    if (this.cache.size >= this.config.maxEntries) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
    
    const actualTTL = ttl || this.config.defaultTTL;
    const entry: CacheEntry = {
      key,
      data,
      timestamp: Date.now(),
      ttl: actualTTL,
      expires_at: Date.now() + actualTTL
    };
    
    this.cache.set(key, entry);
  });

  delete = mock(async (key: string): Promise<void> => {
    this.cache.delete(key);
  });

  clear = mock(async (): Promise<void> => {
    this.cache.clear();
  });

  size(): number {
    return this.cache.size;
  }

  getStats() {
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    const activeEntries = entries.filter(e => e.expires_at > now);
    
    return {
      total_entries: this.cache.size,
      active_entries: activeEntries.length,
      expired_entries: entries.length - activeEntries.length,
      cache_enabled: this.config.enabled
    };
  }
}

// Cache key generation
const generateCacheKey = mock((prefix: string, params: Record<string, any>, customerId: string): string => {
  const keyData = {
    ...params,
    customerId
  };
  const sortedParams = Object.keys(keyData)
    .sort()
    .map(key => `${key}=${keyData[key] || 'null'}`)
    .join('&');
  
  // Create a unique hash based on the actual parameter values
  const uniqueStr = `${prefix}:${sortedParams}`;
  const _hash = Buffer.from(uniqueStr).toString('base64').substring(0, 12);
  return `${prefix}:${hash}`;
});

const generateAgentListCacheKey = mock((params: any, customerId: string): string => {
  return generateCacheKey('agents:list', params, customerId);
});

const generateAgentDetailsCacheKey = mock((agentId: string, customerId: string): string => {
  return generateCacheKey('agents:details', { agent_id: agentId }, customerId);
});

describe('Pagination Logic', () => {
  beforeEach(() => {
    calculatePagination.mockClear();
    validatePaginationParams.mockClear();
    calculateOffset.mockClear();
  });

  describe('Pagination Parameter Validation', () => {
    it('should use default values for missing parameters', () => {
      // Act
      const _result = validatePaginationParams();
      
      // Assert
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
    });

    it('should enforce minimum page number of 1', () => {
      // Act
      const _result = validatePaginationParams(0, 10);
      
      // Assert
      expect(result.page).toBe(1);
      expect(result.offset).toBe(0);
    });

    it('should enforce maximum limit of 50', () => {
      // Act
      const _result = validatePaginationParams(1, 100);
      
      // Assert
      expect(result.limit).toBe(50);
    });

    it('should enforce minimum limit of 1', () => {
      // Act
      const _result = validatePaginationParams(1, 0);

      // Assert
      expect(result.limit).toBe(10); // Default limit when 0 is passed
    });

    it('should handle negative page numbers', () => {
      // Act
      const _result = validatePaginationParams(-5, 10);
      
      // Assert
      expect(result.page).toBe(1);
      expect(result.offset).toBe(0);
    });

    it('should handle negative limit values', () => {
      // Act
      const _result = validatePaginationParams(1, -10);
      
      // Assert
      expect(result.limit).toBe(1);
    });
  });

  describe('Offset Calculation', () => {
    it('should calculate correct offset for page 1', () => {
      // Act
      const offset = calculateOffset(1, 10);
      
      // Assert
      expect(offset).toBe(0);
    });

    it('should calculate correct offset for page 2', () => {
      // Act
      const offset = calculateOffset(2, 10);
      
      // Assert
      expect(offset).toBe(10);
    });

    it('should calculate correct offset for page 5 with limit 15', () => {
      // Act
      const offset = calculateOffset(5, 15);
      
      // Assert
      expect(offset).toBe(60); // (5-1) * 15
    });

    it('should handle large page numbers', () => {
      // Act
      const offset = calculateOffset(1000, 25);
      
      // Assert
      expect(offset).toBe(24975); // (1000-1) * 25
    });
  });

  describe('Pagination Result Calculation', () => {
    it('should calculate pagination for first page', () => {
      // Arrange
      const page = 1;
      const limit = 10;
      const totalCount = 25;
      
      // Act
      const _result = calculatePagination(page, limit, totalCount);
      
      // Assert
      expect(result.current_page).toBe(1);
      expect(result.total_pages).toBe(3);
      expect(result.total_count).toBe(25);
      expect(result.page_size).toBe(10);
      expect(result.has_next_page).toBe(true);
      expect(result.has_previous_page).toBe(false);
      expect(result.next_page).toBe(2);
      expect(result.previous_page).toBeUndefined();
    });

    it('should calculate pagination for middle page', () => {
      // Arrange
      const page = 2;
      const limit = 10;
      const totalCount = 25;
      
      // Act
      const _result = calculatePagination(page, limit, totalCount);
      
      // Assert
      expect(result.current_page).toBe(2);
      expect(result.has_next_page).toBe(true);
      expect(result.has_previous_page).toBe(true);
      expect(result.next_page).toBe(3);
      expect(result.previous_page).toBe(1);
    });

    it('should calculate pagination for last page', () => {
      // Arrange
      const page = 3;
      const limit = 10;
      const totalCount = 25;
      
      // Act
      const _result = calculatePagination(page, limit, totalCount);
      
      // Assert
      expect(result.current_page).toBe(3);
      expect(result.has_next_page).toBe(false);
      expect(result.has_previous_page).toBe(true);
      expect(result.next_page).toBeUndefined();
      expect(result.previous_page).toBe(2);
    });

    it('should handle single page result', () => {
      // Arrange
      const page = 1;
      const limit = 10;
      const totalCount = 5;
      
      // Act
      const _result = calculatePagination(page, limit, totalCount);
      
      // Assert
      expect(result.total_pages).toBe(1);
      expect(result.has_next_page).toBe(false);
      expect(result.has_previous_page).toBe(false);
      expect(result.next_page).toBeUndefined();
      expect(result.previous_page).toBeUndefined();
    });

    it('should handle empty result set', () => {
      // Arrange
      const page = 1;
      const limit = 10;
      const totalCount = 0;
      
      // Act
      const _result = calculatePagination(page, limit, totalCount);
      
      // Assert
      expect(result.total_pages).toBe(0);
      expect(result.has_next_page).toBe(false);
      expect(result.has_previous_page).toBe(false);
    });

    it('should handle exact page boundary', () => {
      // Arrange
      const page = 2;
      const limit = 10;
      const totalCount = 20; // Exactly 2 pages
      
      // Act
      const _result = calculatePagination(page, limit, totalCount);
      
      // Assert
      expect(result.total_pages).toBe(2);
      expect(result.has_next_page).toBe(false);
      expect(result.has_previous_page).toBe(true);
    });
  });
});

describe('Response Caching', () => {
  let cache: MockCache;

  beforeEach(() => {
    cache = new MockCache();
  });

  describe('Basic Cache Operations', () => {
    it('should store and retrieve cached data', async () => {
      // Arrange
      const key = 'test-key';
      const _data = { test: 'data' };
      
      // Act
      await cache.set(key, data);
      const _result = await cache.get(key);
      
      // Assert
      expect(result).toEqual(data);
    });

    it('should return null for non-existent keys', async () => {
      // Arrange
      const key = 'non-existent-key';
      
      // Act
      const _result = await cache.get(key);
      
      // Assert
      expect(result).toBeNull();
    });

    it('should handle cache expiration', async () => {
      // Arrange
      const key = 'expiring-key';
      const _data = { test: 'data' };
      const shortTTL = 100; // 100ms
      
      // Act
      await cache.set(key, data, shortTTL);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const _result = await cache.get(key);
      
      // Assert
      expect(result).toBeNull();
    });

    it('should delete specific cache entries', async () => {
      // Arrange
      const key = 'delete-test';
      const _data = { test: 'data' };
      
      await cache.set(key, data);
      
      // Act
      await cache.delete(key);
      const _result = await cache.get(key);
      
      // Assert
      expect(result).toBeNull();
    });

    it('should clear all cache entries', async () => {
      // Arrange
      await cache.set('key1', { data: 1 });
      await cache.set('key2', { data: 2 });
      
      // Act
      await cache.clear();
      
      // Assert
      expect(cache.size()).toBe(0);
    });
  });

  describe('Cache Key Generation', () => {
    it('should generate consistent cache keys for same parameters', () => {
      // Arrange
      const params = { page: 1, limit: 10, category: 'invoice' };
      const customerId = 'customer-123';
      
      // Act
      const key1 = generateAgentListCacheKey(params, customerId);
      const key2 = generateAgentListCacheKey(params, customerId);
      
      // Assert
      expect(key1).toBe(key2);
    });

    it('should generate different cache keys for different parameters', () => {
      // Arrange
      const params1 = { page: 1, limit: 10, category: 'invoice' };
      const params2 = { page: 2, limit: 10, category: 'invoice' };
      const customerId = 'customer-123';
      
      // Act
      const key1 = generateAgentListCacheKey(params1, customerId);
      const key2 = generateAgentListCacheKey(params2, customerId);
      
      // Assert
      expect(key1).not.toBe(key2);
    });

    it('should generate different cache keys for different customers', () => {
      // Arrange
      const params = { page: 1, limit: 10, category: 'invoice' };
      const customerId1 = 'customer-123';
      const customerId2 = 'customer-456';
      
      // Act
      const key1 = generateAgentListCacheKey(params, customerId1);
      const key2 = generateAgentListCacheKey(params, customerId2);
      
      // Assert
      expect(key1).not.toBe(key2);
    });

    it('should generate agent details cache keys', () => {
      // Arrange
      const agentId = 'default-invoice-v1';
      const customerId = 'customer-123';
      
      // Act
      const key = generateAgentDetailsCacheKey(agentId, customerId);
      
      // Assert
      expect(key).toContain('agents:details');
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(0);
    });

    it('should handle parameter order independence', () => {
      // Arrange
      const params1 = { category: 'invoice', page: 1, limit: 10 };
      const params2 = { page: 1, limit: 10, category: 'invoice' };
      const customerId = 'customer-123';
      
      // Act
      const key1 = generateAgentListCacheKey(params1, customerId);
      const key2 = generateAgentListCacheKey(params2, customerId);
      
      // Assert
      expect(key1).toBe(key2);
    });
  });

  describe('Cache Performance', () => {
    it('should handle cache capacity limits', async () => {
      // Arrange
      const cache = new MockCache();
      const maxEntries = 1000; // Mock cache config max
      
      // Act - Add more entries than max capacity
      for (let i = 0; i < maxEntries + 100; i++) {
        await cache.set(`key-${i}`, { data: i });
      }
      
      // Assert
      expect(cache.size()).toBeLessThanOrEqual(maxEntries);
    });

    it('should provide cache statistics', () => {
      // Arrange
      const cache = new MockCache();
      
      // Act
      const stats = cache.getStats();
      
      // Assert
      expect(stats).toHaveProperty('total_entries');
      expect(stats).toHaveProperty('active_entries');
      expect(stats).toHaveProperty('expired_entries');
      expect(stats).toHaveProperty('cache_enabled');
    });

    it('should handle concurrent cache operations', async () => {
      // Arrange
      const cache = new MockCache();
      const promises: Promise<any>[] = [];
      
      // Act - Simulate concurrent operations
      for (let i = 0; i < 50; i++) {
        promises.push(cache.set(`concurrent-key-${i}`, { data: i }));
      }
      
      await Promise.all(promises);
      
      // Assert - No errors should occur
      expect(cache.size()).toBe(50);
    });
  });

  describe('Cache Integration with Pagination', () => {
    it('should cache paginated agent list results', async () => {
      // Arrange
      const params = { page: 1, limit: 10, category: 'invoice' };
      const customerId = 'customer-123';
      const mockAgentList = {
        agents: [{ id: 'agent-1', name: 'Test Agent' }],
        pagination: calculatePagination(1, 10, 1)
      };
      
      const cacheKey = generateAgentListCacheKey(params, customerId);
      
      // Act
      await cache.set(cacheKey, mockAgentList);
      const cachedResult = await cache.get(cacheKey);
      
      // Assert
      expect(cachedResult).toEqual(mockAgentList);
      expect(cachedResult.pagination.current_page).toBe(1);
    });

    it('should cache different pages separately', async () => {
      // Arrange
      const customerId = 'customer-123';
      const page1Params = { page: 1, limit: 10 };
      const page2Params = { page: 2, limit: 10 };
      
      const page1Data = { agents: ['agent1'], pagination: { current_page: 1 } };
      const page2Data = { agents: ['agent2'], pagination: { current_page: 2 } };
      
      // Act
      await cache.set(generateAgentListCacheKey(page1Params, customerId), page1Data);
      await cache.set(generateAgentListCacheKey(page2Params, customerId), page2Data);
      
      const cached1 = await cache.get(generateAgentListCacheKey(page1Params, customerId));
      const cached2 = await cache.get(generateAgentListCacheKey(page2Params, customerId));
      
      // Assert
      expect(cached1.pagination.current_page).toBe(1); // cached1 uses page1Params
      expect(cached2.pagination.current_page).toBe(2); // cached2 uses page2Params
    });

    it('should handle cache invalidation for agent updates', async () => {
      // Arrange
      const params = { page: 1, limit: 10 };
      const customerId = 'customer-123';
      const cacheKey = generateAgentListCacheKey(params, customerId);
      
      await cache.set(cacheKey, { agents: [], pagination: {} });
      
      // Act - Simulate agent update that should invalidate cache
      await cache.delete(cacheKey);
      const _result = await cache.get(cacheKey);
      
      // Assert
      expect(result).toBeNull();
    });
  });

  describe('Cache Error Handling', () => {
    it('should handle cache failures gracefully', async () => {
      // Arrange
      const mockFailingCache = {
        get: mock(async () => { throw new Error('Cache unavailable'); }),
        set: mock(async () => { throw new Error('Cache unavailable'); })
      };
      
      // Act & Assert - Should reject
      await expect(mockFailingCache.get('test')).rejects.toThrow('Cache unavailable');
      await expect(mockFailingCache.set('test', {})).rejects.toThrow('Cache unavailable');
    });

    it('should handle malformed cache data', async () => {
      // Arrange
      const cache = new MockCache();
      await cache.set('malformed', 'invalid-json-data');
      
      // Act
      const _result = await cache.get('malformed');
      
      // Assert
      expect(result).toBe('invalid-json-data'); // Should return as-is
    });
  });
});

describe('Combined Pagination and Caching', () => {
  let cache: MockCache;

  beforeEach(() => {
    cache = new MockCache();
  });

  it('should cache pagination results with correct TTL', async () => {
    // Arrange
    const params = { page: 1, limit: 10, category: 'invoice' };
    const customerId = 'customer-123';
    const totalCount = 25;
    
    const pagination = calculatePagination(params.page, params.limit, totalCount);
    const agentList = {
      agents: [],
      pagination
    };
    
    const cacheKey = generateAgentListCacheKey(params, customerId);
    
    // Act
    await cache.set(cacheKey, agentList, 300000); // 5 minutes
    const cached = await cache.get(cacheKey);
    
    // Assert
    expect(cached.pagination.total_pages).toBe(3);
    expect(cached.pagination.has_next_page).toBe(true);
  });

  it('should validate pagination before caching', () => {
    // Arrange
    const invalidPage = -1;
    const invalidLimit = 200;
    
    // Act
    const validated = validatePaginationParams(invalidPage, invalidLimit);
    
    // Assert
    expect(validated.page).toBe(1);
    expect(validated.limit).toBe(50);
  });

  it('should handle cache miss for pagination requests', async () => {
    // Arrange
    const params = { page: 1, limit: 10 };
    const customerId = 'customer-123';
    const cacheKey = generateAgentListCacheKey(params, customerId);
    
    // Act
    const _result = await cache.get(cacheKey);
    
    // Assert
    expect(result).toBeNull();
  });
});

// Export for integration tests
export {
  calculatePagination,
  validatePaginationParams,
  calculateOffset,
  MockCache,
  generateCacheKey,
  generateAgentListCacheKey,
  generateAgentDetailsCacheKey
};