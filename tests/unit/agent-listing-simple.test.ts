import { describe, it, expect, _beforeEach, _mock } from 'bun:test';

// Simplified Agent Listing API Unit Tests
// Tests for GitHub Issue #14: Agent Storage & Retrieval
// Focus on core business logic rather than complex mocking

interface Agent {
  id: string;
  agent_id: string;
  name: string;
  description: string;
  category: 'invoice' | 'contract' | 'receipt' | 'general' | 'legal';
  version: number;
  is_default: boolean;
  customer_id: string | null;
  parent_agent_id: string | null;
  created_at: string;
  updated_at: string;
}

interface AgentListResponse {
  agents: Agent[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
    page_size: number;
    has_next_page: boolean;
    has_previous_page: boolean;
    next_page?: number;
    previous_page?: number;
  };
  filters_applied: {
    category?: string | null;
    is_default?: string | null;
    search?: string | null;
  };
}

interface CustomerContext {
  customerId: string;
  keyType: 'test' | 'production';
  credits: number;
}

// Core business logic functions to test
function validatePaginationParams(page?: number, limit?: number) {
  const validatedPage = Math.max(1, page || 1);
  // Fix: properly handle 0 and negative values for limit
  const rawLimit = limit ?? 10;
  const validatedLimit = Math.min(50, Math.max(1, rawLimit));
  const offset = (validatedPage - 1) * validatedLimit;
  
  return {
    page: validatedPage,
    limit: validatedLimit,
    offset
  };
}

function calculatePagination(page: number, limit: number, totalCount: number) {
  const totalPages = Math.ceil(totalCount / limit);
  const hasNextPage = page < totalPages;
  const hasPreviousPage = page > 1;
  
  return {
    current_page: page,
    total_pages: totalPages,
    total_count: totalCount,
    page_size: limit,
    has_next_page: hasNextPage,
    has_previous_page: hasPreviousPage,
    next_page: hasNextPage ? page + 1 : undefined,
    previous_page: hasPreviousPage ? page - 1 : undefined
  };
}

function filterAgentsByCategory(agents: Agent[], category: string): Agent[] {
  return agents.filter(agent => agent.category === category);
}

function filterAgentsByDefault(agents: Agent[], isDefault: boolean): Agent[] {
  return agents.filter(agent => agent.is_default === isDefault);
}

function searchAgents(agents: Agent[], searchTerm: string): Agent[] {
  const lowerSearch = searchTerm.toLowerCase();
  return agents.filter(agent => 
    agent.name.toLowerCase().includes(lowerSearch) ||
    agent.description.toLowerCase().includes(lowerSearch)
  );
}

function applyAccessControl(agents: Agent[], customerId: string): Agent[] {
  return agents.filter(agent => 
    agent.is_default === true || agent.customer_id === customerId
  );
}

function generateCacheKey(prefix: string, params: Record<string, any>, customerId: string): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  
  // Create a more comprehensive hash that includes both params and customer
  const fullString = `${sortedParams}:customer:${customerId}`;
  
  // Use a simple hash function for better differentiation in tests
  let hash = 0;
  for (let i = 0; i < fullString.length; i++) {
    const char = fullString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return `${prefix}:${Math.abs(hash).toString(36)}`;
}

// Mock data for testing
const mockAgents: Agent[] = [
  {
    id: 'agent-1',
    agent_id: 'default-invoice-v1',
    name: 'Advanced Invoice Processor',
    description: 'Production-grade invoice processor with >90% accuracy',
    category: 'invoice',
    version: 1,
    is_default: true,
    customer_id: null,
    parent_agent_id: null,
    created_at: '2025-09-21T10:00:00Z',
    updated_at: '2025-09-21T10:00:00Z'
  },
  {
    id: 'agent-2',
    agent_id: 'default-contract-v1',
    name: 'Contract Analysis Expert',
    description: 'Advanced contract analyzer for key terms and obligations',
    category: 'contract',
    version: 1,
    is_default: true,
    customer_id: null,
    parent_agent_id: null,
    created_at: '2025-09-21T10:00:00Z',
    updated_at: '2025-09-21T10:00:00Z'
  },
  {
    id: 'agent-3',
    agent_id: 'custom-invoice-special',
    name: 'Custom Invoice Handler',
    description: 'Specialized invoice processor for construction industry',
    category: 'invoice',
    version: 1,
    is_default: false,
    customer_id: 'customer-123',
    parent_agent_id: 'agent-1',
    created_at: '2025-09-21T10:00:00Z',
    updated_at: '2025-09-21T10:00:00Z'
  },
  {
    id: 'agent-4',
    agent_id: 'other-customer-agent',
    name: 'Other Customer Agent',
    description: 'Agent belonging to different customer',
    category: 'receipt',
    version: 1,
    is_default: false,
    customer_id: 'customer-456',
    parent_agent_id: null,
    created_at: '2025-09-21T10:00:00Z',
    updated_at: '2025-09-21T10:00:00Z'
  }
];

describe('Pagination Logic', () => {
  describe('Parameter Validation', () => {
    it('should use default values for missing parameters', () => {
      const _result = validatePaginationParams();
      
      expect(result.page).toBe(1);
      expect(result.limit).toBe(10);
      expect(result.offset).toBe(0);
    });

    it('should enforce minimum page number of 1', () => {
      const _result = validatePaginationParams(0, 10);
      
      expect(result.page).toBe(1);
      expect(result.offset).toBe(0);
    });

    it('should enforce maximum limit of 50', () => {
      const _result = validatePaginationParams(1, 100);
      
      expect(result.limit).toBe(50);
    });

    it('should enforce minimum limit of 1', () => {
      const _result = validatePaginationParams(1, 0);
      
      expect(result.limit).toBe(1);
    });

    it('should calculate correct offset for page 2', () => {
      const _result = validatePaginationParams(2, 10);
      
      expect(result.offset).toBe(10);
    });
  });

  describe('Pagination Calculation', () => {
    it('should calculate pagination for first page', () => {
      const _result = calculatePagination(1, 10, 25);
      
      expect(result.current_page).toBe(1);
      expect(result.total_pages).toBe(3);
      expect(result.total_count).toBe(25);
      expect(result.page_size).toBe(10);
      expect(result.has_next_page).toBe(true);
      expect(result.has_previous_page).toBe(false);
      expect(result.next_page).toBe(2);
      expect(result.previous_page).toBeUndefined();
    });

    it('should calculate pagination for last page', () => {
      const _result = calculatePagination(3, 10, 25);
      
      expect(result.current_page).toBe(3);
      expect(result.has_next_page).toBe(false);
      expect(result.has_previous_page).toBe(true);
      expect(result.next_page).toBeUndefined();
      expect(result.previous_page).toBe(2);
    });

    it('should handle empty result set', () => {
      const _result = calculatePagination(1, 10, 0);
      
      expect(result.total_pages).toBe(0);
      expect(result.has_next_page).toBe(false);
      expect(result.has_previous_page).toBe(false);
    });
  });
});

describe('Agent Filtering Logic', () => {
  describe('Category Filtering', () => {
    it('should filter agents by invoice category', () => {
      const _result = filterAgentsByCategory(mockAgents, 'invoice');
      
      expect(result).toHaveLength(2);
      expect(result.every(agent => agent.category === 'invoice')).toBe(true);
    });

    it('should filter agents by contract category', () => {
      const _result = filterAgentsByCategory(mockAgents, 'contract');
      
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Contract Analysis Expert');
    });

    it('should return empty array for non-existent category', () => {
      const _result = filterAgentsByCategory(mockAgents, 'nonexistent' as any);
      
      expect(result).toHaveLength(0);
    });
  });

  describe('Default Agent Filtering', () => {
    it('should filter to show only default agents', () => {
      const _result = filterAgentsByDefault(mockAgents, true);
      
      expect(result).toHaveLength(2);
      expect(result.every(agent => agent.is_default === true)).toBe(true);
      expect(result.every(agent => agent.customer_id === null)).toBe(true);
    });

    it('should filter to show only custom agents', () => {
      const _result = filterAgentsByDefault(mockAgents, false);
      
      expect(result).toHaveLength(2);
      expect(result.every(agent => agent.is_default === false)).toBe(true);
      expect(result.every(agent => agent.customer_id !== null)).toBe(true);
    });
  });

  describe('Search Functionality', () => {
    it('should search by agent name', () => {
      const _result = searchAgents(mockAgents, 'Invoice');
      
      expect(result).toHaveLength(2);
      expect(result.every(agent => 
        agent.name.toLowerCase().includes('invoice')
      )).toBe(true);
    });

    it('should search by description', () => {
      const _result = searchAgents(mockAgents, 'contract');
      
      expect(result).toHaveLength(1);
      expect(result[0].description.toLowerCase()).toContain('contract');
    });

    it('should handle case-insensitive search', () => {
      const _result = searchAgents(mockAgents, 'ADVANCED');
      
      expect(result).toHaveLength(2);
    });

    it('should return empty array for non-matching search', () => {
      const _result = searchAgents(mockAgents, 'nonexistent');
      
      expect(result).toHaveLength(0);
    });
  });

  describe('Access Control', () => {
    it('should allow access to default agents for any customer', () => {
      const customerId = 'any-customer';
      const _result = applyAccessControl(mockAgents, customerId);
      
      const defaultAgents = result.filter(agent => agent.is_default);
      expect(defaultAgents).toHaveLength(2);
    });

    it('should allow access to custom agents for owner', () => {
      const customerId = 'customer-123';
      const _result = applyAccessControl(mockAgents, customerId);
      
      const customAgents = result.filter(agent => 
        !agent.is_default && agent.customer_id === customerId
      );
      expect(customAgents).toHaveLength(1);
      expect(customAgents[0].name).toBe('Custom Invoice Handler');
    });

    it('should deny access to other customers custom agents', () => {
      const customerId = 'customer-123';
      const _result = applyAccessControl(mockAgents, customerId);
      
      const otherCustomerAgents = result.filter(agent => 
        agent.customer_id === 'customer-456'
      );
      expect(otherCustomerAgents).toHaveLength(0);
    });

    it('should return both default and owned custom agents', () => {
      const customerId = 'customer-123';
      const _result = applyAccessControl(mockAgents, customerId);
      
      expect(result).toHaveLength(3); // 2 default + 1 custom
      expect(result.filter(agent => agent.is_default)).toHaveLength(2);
      expect(result.filter(agent => agent.customer_id === customerId)).toHaveLength(1);
    });
  });
});

describe('Cache Key Generation', () => {
  it('should generate consistent cache keys for same parameters', () => {
    const params = { page: 1, limit: 10, category: 'invoice' };
    const customerId = 'customer-123';
    
    const key1 = generateCacheKey('agents:list', params, customerId);
    const key2 = generateCacheKey('agents:list', params, customerId);
    
    expect(key1).toBe(key2);
  });

  it('should generate different cache keys for different parameters', () => {
    const params1 = { page: 1, limit: 10, category: 'invoice' };
    const params2 = { page: 2, limit: 10, category: 'invoice' };
    const customerId = 'customer-123';
    
    const key1 = generateCacheKey('agents:list', params1, customerId);
    const key2 = generateCacheKey('agents:list', params2, customerId);
    
    expect(key1).not.toBe(key2);
  });

  it('should generate different cache keys for different customers', () => {
    const params = { page: 1, limit: 10, category: 'invoice' };
    const customerId1 = 'customer-123';
    const customerId2 = 'customer-456';
    
    const key1 = generateCacheKey('agents:list', params, customerId1);
    const key2 = generateCacheKey('agents:list', params, customerId2);
    
    expect(key1).not.toBe(key2);
  });

  it('should handle parameter order independence', () => {
    const params1 = { category: 'invoice', page: 1, limit: 10 };
    const params2 = { page: 1, limit: 10, category: 'invoice' };
    const customerId = 'customer-123';
    
    const key1 = generateCacheKey('agents:list', params1, customerId);
    const key2 = generateCacheKey('agents:list', params2, customerId);
    
    expect(key1).toBe(key2);
  });
});

describe('Combined Filter Operations', () => {
  it('should apply category and default filters together', () => {
    let agents = mockAgents;
    
    // Apply filters in sequence
    agents = filterAgentsByCategory(agents, 'invoice');
    agents = filterAgentsByDefault(agents, true);
    
    expect(agents).toHaveLength(1);
    expect(agents[0].category).toBe('invoice');
    expect(agents[0].is_default).toBe(true);
    expect(agents[0].name).toBe('Advanced Invoice Processor');
  });

  it('should apply search and access control together', () => {
    const customerId = 'customer-123';
    let agents = mockAgents;
    
    // Apply filters in sequence
    agents = searchAgents(agents, 'invoice');
    agents = applyAccessControl(agents, customerId);
    
    expect(agents).toHaveLength(2); // Both default and custom invoice agents
    expect(agents.every(agent => 
      agent.name.toLowerCase().includes('invoice') ||
      agent.description.toLowerCase().includes('invoice')
    )).toBe(true);
  });

  it('should handle filter chain that results in no matches', () => {
    let agents = mockAgents;
    
    // Apply conflicting filters
    agents = filterAgentsByCategory(agents, 'contract');
    agents = filterAgentsByDefault(agents, false); // Contract agents are default
    
    expect(agents).toHaveLength(0);
  });
});

describe('Edge Cases', () => {
  it('should handle empty agent array', () => {
    const emptyAgents: Agent[] = [];
    
    const categoryResult = filterAgentsByCategory(emptyAgents, 'invoice');
    const searchResult = searchAgents(emptyAgents, 'test');
    const accessResult = applyAccessControl(emptyAgents, 'customer-123');
    
    expect(categoryResult).toHaveLength(0);
    expect(searchResult).toHaveLength(0);
    expect(accessResult).toHaveLength(0);
  });

  it('should handle null/undefined search terms gracefully', () => {
    const _result = searchAgents(mockAgents, '');
    
    expect(result).toHaveLength(4); // Should return all agents for empty search
  });

  it('should handle special characters in search', () => {
    const _result = searchAgents(mockAgents, 'invoice & contract');
    
    expect(Array.isArray(result)).toBe(true);
    // Search should still work even with special characters
  });
});

// Export for integration tests
export {
  validatePaginationParams,
  calculatePagination,
  filterAgentsByCategory,
  filterAgentsByDefault,
  searchAgents,
  applyAccessControl,
  generateCacheKey,
  mockAgents
};