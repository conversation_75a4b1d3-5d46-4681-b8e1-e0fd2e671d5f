import { describe, it, expect, _beforeEach, _afterEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/database.types';

const _authHeaders = {
  'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'
};

// Test types for API key management
interface ApiKeyGenerationRequest {
  customerId: string;
  keyType: 'test' | 'prod';
  credits?: number;
}

interface ApiKeyGenerationResponse {
  success: boolean;
  data?: {
    rawKey: string;
    keyId: string;
    customerId: string;
    keyType: 'test' | 'prod';
    credits: number;
    expiresAt?: string;
  };
  error?: string;
}

interface ApiKeyValidationResponse {
  success: boolean;
  data?: {
    customerId: string;
    keyType: 'test' | 'prod';
    credits: number;
    isActive: boolean;
    isExpired: boolean;
  };
  error?: string;
}

describe('API Key Management - TDD Implementation', () => {
  let supabase: ReturnType<typeof createClient<Database>>;
  const testCustomerId = 'test-customer-123';

  beforeEach(() => {
    const supabaseUrl = process.env.SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Missing Supabase environment variables');
    }

    supabase = createClient<Database>(supabaseUrl, supabaseKey);
  });

  afterEach(async () => {
    // Clean up test data
    await supabase.from('api_keys').delete().eq('customer_id', testCustomerId);
    await supabase.from('customers').delete().eq('id', testCustomerId);
  });

  describe('Key Generation', () => {
    it('should generate test key with skt_ prefix', async () => {
      // This test will fail initially - TDD approach
      const request: ApiKeyGenerationRequest = {
        customerId: testCustomerId,
        keyType: 'test',
        credits: 100
      };

      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify(request)
      });

      const result: ApiKeyGenerationResponse = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.rawKey).toMatch(/^skt_[a-zA-Z0-9]{32}$/);
      expect(result.data?.keyType).toBe('test');
      expect(result.data?.credits).toBe(100);
      expect(result.data?.customerId).toBe(testCustomerId);
      expect(result.data?.expiresAt).toBeDefined(); // Should have expiry for test keys
    });

    it('should generate production key with skp_ prefix', async () => {
      const request: ApiKeyGenerationRequest = {
        customerId: testCustomerId,
        keyType: 'prod',
        credits: 1000
      };

      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify(request)
      });

      const result: ApiKeyGenerationResponse = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.rawKey).toMatch(/^skp_[a-zA-Z0-9]{32}$/);
      expect(result.data?.keyType).toBe('prod');
      expect(result.data?.credits).toBe(1000);
      expect(result.data?.customerId).toBe(testCustomerId);
      expect(result.data?.expiresAt).toBeUndefined(); // Production keys don't expire
    });

    it('should store only hashed keys in database', async () => {
      const request: ApiKeyGenerationRequest = {
        customerId: testCustomerId,
        keyType: 'test',
        credits: 100
      };

      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify(request)
      });

      const result: ApiKeyGenerationResponse = await response.json();
      expect(result.success).toBe(true);

      // Check database doesn't contain raw key
      const { data: storedKeys } = await supabase
        .from('api_keys')
        .select('*')
        .eq('customer_id', testCustomerId);

      expect(storedKeys).toBeDefined();
      expect(storedKeys?.length).toBe(1);
      
      // Verify raw key is not stored
      expect(storedKeys?.[0].key_hash).not.toBe(result.data?.rawKey);
      expect(storedKeys?.[0].key_hash).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex
    });

    it('should enforce unique key generation', async () => {
      // Generate first key
      const request: ApiKeyGenerationRequest = {
        customerId: testCustomerId,
        keyType: 'test'
      };

      const response1 = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify(request)
      });

      const result1: ApiKeyGenerationResponse = await response1.json();
      expect(result1.success).toBe(true);

      // Generate second key
      const response2 = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify(request)
      });

      const result2: ApiKeyGenerationResponse = await response2.json();
      expect(result2.success).toBe(true);

      // Keys should be different
      expect(result1.data?.rawKey).not.toBe(result2.data?.rawKey);
    });
  });

  describe('Key Validation', () => {
    let testKey: string;
    let prodKey: string;

    beforeEach(async () => {
      // Generate test keys for validation tests
      const testResponse = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customerId: testCustomerId,
          keyType: 'test',
          credits: 100
        })
      });

      const testResult: ApiKeyGenerationResponse = await testResponse.json();
      testKey = testResult.data!.rawKey;

      const prodResponse = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customerId: testCustomerId,
          keyType: 'prod',
          credits: 1000
        })
      });

      const prodResult: ApiKeyGenerationResponse = await prodResponse.json();
      prodKey = prodResult.data!.rawKey;
    });

    it('should validate active test key', async () => {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testKey}`
        },
        body: JSON.stringify({})
      });

      const result: ApiKeyValidationResponse = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.customerId).toBe(testCustomerId);
      expect(result.data?.keyType).toBe('test');
      expect(result.data?.credits).toBe(100);
      expect(result.data?.isActive).toBe(true);
      expect(result.data?.isExpired).toBe(false);
    });

    it('should validate active production key', async () => {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${prodKey}`
        },
        body: JSON.stringify({})
      });

      const result: ApiKeyValidationResponse = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.customerId).toBe(testCustomerId);
      expect(result.data?.keyType).toBe('prod');
      expect(result.data?.credits).toBe(1000);
      expect(result.data?.isActive).toBe(true);
      expect(result.data?.isExpired).toBe(false);
    });

    it('should reject invalid key format', async () => {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer invalid-key-format'
        },
        body: JSON.stringify({})
      });

      const result: ApiKeyValidationResponse = await response.json();

      expect(response.status).toBe(401);
      expect(result.success).toBe(false);
      expect(result.error).toMatch(/invalid.*key.*format/i);
    });

    it('should reject non-existent key', async () => {
      const fakeKey = 'skt_' + 'a'.repeat(32);
      
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${fakeKey}`
        },
        body: JSON.stringify({})
      });

      const result: ApiKeyValidationResponse = await response.json();

      expect(response.status).toBe(401);
      expect(result.success).toBe(false);
      expect(result.error).toMatch(/invalid.*key/i);
    });
  });

  describe('Key Management Operations', () => {
    let testKeyId: string;

    beforeEach(async () => {
      // Generate a test key for management operations
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customerId: testCustomerId,
          keyType: 'test',
          credits: 100
        })
      });

      const result: ApiKeyGenerationResponse = await response.json();
      testKeyId = result.data!.keyId;
    });

    it('should suspend an active key', async () => {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/manage-keys`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          keyId: testKeyId,
          action: 'suspend'
        })
      });

      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('suspended');
    });

    it('should reactivate a suspended key', async () => {
      // First suspend the key
      await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/manage-keys`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          keyId: testKeyId,
          action: 'suspend'
        })
      });

      // Then reactivate it
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/manage-keys`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          keyId: testKeyId,
          action: 'activate'
        })
      });

      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('active');
    });

    it('should revoke a key permanently', async () => {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/manage-keys`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          keyId: testKeyId
        })
      });

      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data?.status).toBe('revoked');

      // Verify key is no longer usable
      const { data: revokedKey } = await supabase
        .from('api_keys')
        .select('status')
        .eq('id', testKeyId)
        .single();

      expect(revokedKey?.status).toBe('revoked');
    });
  });

  describe('Credit System', () => {
    let testKey: string;

    beforeEach(async () => {
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customerId: testCustomerId,
          keyType: 'test',
          credits: 10 // Low credits for testing
        })
      });

      const result: ApiKeyGenerationResponse = await response.json();
      testKey = result.data!.rawKey;
    });

    it('should track credit consumption', async () => {
      // Make API call that consumes credits
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testKey}`
        },
        body: JSON.stringify({ consumeCredit: true })
      });

      expect(response.status).toBe(200);

      // Check credits were deducted
      const { data: keyData } = await supabase
        .from('api_keys')
        .select('credits')
        .eq('customer_id', testCustomerId)
        .single();

      expect(keyData?.credits).toBe(9); // Should be reduced by 1
    });

    it('should reject requests when credits exhausted', async () => {
      // Exhaust all credits
      await supabase
        .from('api_keys')
        .update({ credits: 0 })
        .eq('customer_id', testCustomerId);

      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${testKey}`
        },
        body: JSON.stringify({ consumeCredit: true })
      });

      expect(response.status).toBe(402); // Payment required
      
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toMatch(/insufficient.*credits/i);
    });
  });

  describe('Retention Policy', () => {
    it('should auto-expire test keys after 7 days', async () => {
      // Create test key with past expiry date
      const pastExpiry = new Date();
      pastExpiry.setDate(pastExpiry.getDate() - 8); // 8 days ago

      const { data: expiredKey } = await supabase
        .from('api_keys')
        .insert({
          customer_id: testCustomerId,
          key_type: 'test',
          key_hash: 'test-hash-expired',
          credits: 100,
          status: 'active',
          expires_at: pastExpiry.toISOString()
        })
        .select()
        .single();

      expect(expiredKey).toBeDefined();

      // Validate expired key should fail
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/validate-api-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer skt_' + 'expired'.repeat(4) + '1234'
        },
        body: JSON.stringify({})
      });

      expect(response.status).toBe(401);
      
      const _result = await response.json();
      expect(result.error).toMatch(/expired/i);
    });

    it('should not expire production keys', async () => {
      // Production keys should not have expiry dates
      const response = await fetch(`${process.env.SUPABASE_URL}/functions/v1/admin/generate-key`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customerId: testCustomerId,
          keyType: 'prod',
          credits: 1000
        })
      });

      const result: ApiKeyGenerationResponse = await response.json();
      expect(result.success).toBe(true);
      expect(result.data?.expiresAt).toBeUndefined();

      // Verify in database
      const { data: prodKey } = await supabase
        .from('api_keys')
        .select('expires_at')
        .eq('customer_id', testCustomerId)
        .eq('key_type', 'prod')
        .single();

      expect(prodKey?.expires_at).toBeNull();
    });
  });
});