/**
 * Content Preprocessing Unit Tests
 * Tests for document content cleaning and AI model preparation
 */

import { describe, it, expect } from 'bun:test';

// Mock interfaces for preprocessing
interface Agent {
  id: string;
  name: string;
  prompt: string;
  json_schema: Record<string, unknown>;
  category: string;
}

interface ExtractedContent {
  text: string;
  pages?: number;
  metadata: Record<string, unknown>;
}

interface PreprocessingOptions {
  maxLength?: number;
  preserveFormatting?: boolean;
  removeNoise?: boolean;
  normalizeWhitespace?: boolean;
}

interface PreprocessedContent {
  processedText: string;
  originalLength: number;
  processedLength: number;
  qualityScore: number;
  preservedElements: string[];
  removedElements: string[];
}

const MAX_PROMPT_LENGTH = 16000; // Conservative limit for AI models

describe('Content Preprocessing', () => {
  describe('Text Cleaning', () => {
    it('should normalize whitespace', () => {
      const messy = 'Text    with\n\n\nexcessive    whitespace\t\tand\ttabs';
      const cleaned = normalizeWhitespace(messy);
      
      expect(cleaned).toBe('Text with excessive whitespace and tabs');
      expect(cleaned).not.toContain('  '); // No double spaces
      expect(cleaned).not.toContain('\t'); // No tabs
      expect(cleaned).not.toContain('\n\n'); // No multiple newlines
    });

    it('should remove OCR artifacts', () => {
      const ocrText = 'Th1s t3xt h4s 0CR 4rt1f4cts l1k3 numb3rs 1nst34d 0f l3tt3rs';
      const cleaned = removeOcrArtifacts(ocrText);
      
      // Should fix common OCR mistakes
      expect(cleaned).toContain('This text has');
      expect(cleaned).not.toContain('Th1s');
    });

    it('should remove non-printable characters', () => {
      const dirtyText = 'Clean text\x00with\x01non-printable\x02characters\x03';
      const cleaned = removeNonPrintable(dirtyText);
      
      expect(cleaned).toBe('Clean text with non-printable characters');
      expect(cleaned).toMatch(/^[\t\n\r\x20-\x7E]*$/); // Only printable characters
    });

    it('should preserve important formatting', () => {
      const formatted = `
        INVOICE #123
        
        Item          Qty    Price
        Product A      1     $100
        Product B      2     $50
        
        Total: $200
      `;
      
      const cleaned = preserveImportantFormatting(formatted);
      
      // Should preserve table structure and headers
      expect(cleaned).toContain('INVOICE #123');
      expect(cleaned).toContain('Item          Qty    Price');
      expect(cleaned).toContain('Total: $200');
    });
  });

  describe('Content Structure Analysis', () => {
    it('should detect document sections', () => {
      const document = `
        HEADER SECTION
        
        Introduction:
        This is the introduction.
        
        Details:
        - Point 1
        - Point 2
        
        Conclusion:
        Final thoughts.
      `;
      
      const sections = detectSections(document);
      
      expect(sections).toContain('HEADER SECTION');
      expect(sections).toContain('Introduction');
      expect(sections).toContain('Details');
      expect(sections).toContain('Conclusion');
    });

    it('should identify key value pairs', () => {
      const text = `
        Invoice Number: INV-123
        Date: 2024-01-15
        Amount: $1,000.00
        Customer: John Doe
      `;
      
      const pairs = extractKeyValuePairs(text);
      
      expect(pairs['Invoice Number']).toBe('INV-123');
      expect(pairs['Date']).toBe('2024-01-15');
      expect(pairs['Amount']).toBe('$1,000.00');
      expect(pairs['Customer']).toBe('John Doe');
    });

    it('should detect tables and lists', () => {
      const tableText = `
        Item     Qty  Price   Total
        Widget    2   $10.00  $20.00
        Gadget    1   $15.00  $15.00
      `;
      
      const listText = `
        Features:
        • Feature 1
        • Feature 2
        - Item A
        - Item B
      `;
      
      expect(hasTableStructure(tableText)).toBe(true);
      expect(hasListStructure(listText)).toBe(true);
    });
  });

  describe('Agent-Specific Preprocessing', () => {
    const invoiceAgent: Agent = {
      id: 'default-invoice',
      name: 'Invoice Processor',
      category: 'invoice',
      prompt: 'Extract invoice data...',
      json_schema: {}
    };

    const receiptAgent: Agent = {
      id: 'default-receipt',
      name: 'Receipt Processor',
      category: 'receipt',
      prompt: 'Extract receipt data...',
      json_schema: {}
    };

    it('should add agent-specific context for invoices', () => {
      const content: ExtractedContent = {
        text: 'Invoice content with vendor and amounts',
        metadata: {}
      };
      
      const processed = preprocessForAgent(content, invoiceAgent);
      
      expect(processed.processedText).toContain('Extract invoice data');
      expect(processed.processedText).toContain('Invoice content');
    });

    it('should add agent-specific context for receipts', () => {
      const content: ExtractedContent = {
        text: 'Receipt from coffee shop with total $8.37',
        metadata: {}
      };
      
      const processed = preprocessForAgent(content, receiptAgent);
      
      expect(processed.processedText).toContain('Extract receipt data');
      expect(processed.processedText).toContain('coffee shop');
    });

    it('should include relevant schema hints', () => {
      const content: ExtractedContent = {
        text: 'Document with vendor and total amount',
        metadata: {}
      };
      
      const agentWithSchema: Agent = {
        ...invoiceAgent,
        json_schema: {
          properties: {
            vendor: { type: 'string' },
            total: { type: 'number' }
          }
        }
      };
      
      const processed = preprocessForAgent(content, agentWithSchema);
      
      // Should hint at expected fields
      expect(processed.processedText).toContain('vendor');
      expect(processed.processedText).toContain('total');
    });
  });

  describe('Length Management', () => {
    it('should truncate intelligently when content is too long', () => {
      const longText = 'Very long content. '.repeat(1000); // ~20,000 characters
      const options: PreprocessingOptions = { maxLength: MAX_PROMPT_LENGTH };
      
      const processed = truncateIntelligently(longText, options);
      
      expect(processed.length).toBeLessThanOrEqual(MAX_PROMPT_LENGTH);
      expect(processed).toContain('Very long content');
      expect(processed).not.toContain('...'); // Should not end abruptly
    });

    it('should preserve important sections when truncating', () => {
      const longDocument = `
        IMPORTANT HEADER
        
        ${'Filler content. '.repeat(500)}
        
        CRITICAL SECTION:
        Total: $1,000
        
        ${'More filler. '.repeat(500)}
      `;
      
      const processed = truncateIntelligently(longDocument, { maxLength: 1000 });
      
      // Should keep important parts
      expect(processed).toContain('IMPORTANT HEADER');
      expect(processed).toContain('CRITICAL SECTION');
      expect(processed).toContain('Total: $1,000');
    });

    it('should maintain document structure in summaries', () => {
      const structured = `
        Section 1: Introduction
        Content for section 1
        
        Section 2: Details
        Content for section 2
        
        Section 3: Conclusion
        Content for section 3
      `;
      
      const summarized = summarizeForLength(structured, 200);
      
      expect(summarized).toContain('Section 1');
      expect(summarized).toContain('Section 2');
      expect(summarized).toContain('Section 3');
      expect(summarized.length).toBeLessThanOrEqual(200);
    });
  });

  describe('Quality Assessment', () => {
    it('should calculate content quality score', () => {
      const highQuality = 'Well-structured document with clear formatting and complete sentences.';
      const lowQuality = 'brk3n t3xt w1th 0CR 3rr0rs 4nd p00r f0rm4tt1ng!!!';
      
      const highScore = calculateQualityScore(highQuality);
      const lowScore = calculateQualityScore(lowQuality);
      
      expect(highScore).toBeGreaterThan(0.8);
      expect(lowScore).toBeLessThan(0.5);
      expect(highScore).toBeGreaterThan(lowScore);
    });

    it('should identify confidence indicators', () => {
      const confident = 'Clear invoice with total $1,000.00 from Acme Corp';
      const uncertain = 'unclear docment with posible amount of $1OOO';
      
      const confidentScore = getConfidenceIndicators(confident);
      const uncertainScore = getConfidenceIndicators(uncertain);
      
      expect(confidentScore.clarity).toBeGreaterThan(uncertainScore.clarity);
      expect(confidentScore.formatting).toBeGreaterThan(uncertainScore.formatting);
    });

    it('should detect missing critical information', () => {
      const complete = 'Invoice INV-123 from Vendor Corp for $500 dated 2024-01-15';
      const incomplete = 'Document with some numbers and text';
      
      const completeness = assessCompleteness(complete, 'invoice');
      const incompleteness = assessCompleteness(incomplete, 'invoice');
      
      expect(completeness.score).toBeGreaterThan(incompleteness.score);
      expect(completeness.missingFields).toHaveLength(0);
      expect(incompleteness.missingFields.length).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle empty content gracefully', () => {
      const empty = '';
      const processed = preprocessContent(empty, invoiceAgent);
      
      expect(processed.processedText).toBeDefined();
      expect(processed.qualityScore).toBe(0);
    });

    it('should handle extremely noisy content', () => {
      const noisy = '!@#$%^&*()_+{}|:"<>?[];\'.,/';
      const processed = preprocessContent(noisy, invoiceAgent);
      
      expect(processed.processedText).toBeDefined();
      expect(processed.qualityScore).toBeLessThan(0.2);
    });

    it('should handle special characters and encodings', () => {
      const special = 'Document with émojis 🎉 and spëcial chäracters';
      const processed = preprocessContent(special, invoiceAgent);
      
      expect(processed.processedText).toContain('émojis');
      expect(processed.processedText).toContain('🎉');
      expect(processed.qualityScore).toBeGreaterThan(0.5);
    });
  });
});

// Mock implementation functions for testing
function normalizeWhitespace(text: string): string {
  return text.replace(/\s+/g, ' ').trim();
}

function removeOcrArtifacts(text: string): string {
  // Simple OCR correction patterns
  return text
    .replace(/1/g, 'l')
    .replace(/0/g, 'o')
    .replace(/3/g, 'e')
    .replace(/4/g, 'a');
}

function removeNonPrintable(text: string): string {
  return text.replace(/[^\t\n\r\x20-\x7E]/g, '');
}

function preserveImportantFormatting(text: string): string {
  // Preserve structure while cleaning
  return text.replace(/\n{3,}/g, '\n\n').trim();
}

function detectSections(text: string): string[] {
  const sections = text.split(/\n\s*\n/).filter(section => section.trim());
  return sections.map(section => section.split('\n')[0].trim());
}

function extractKeyValuePairs(text: string): Record<string, string> {
  const pairs: Record<string, string> = {};
  const lines = text.split('\n');
  
  for (const line of lines) {
    const match = line.match(/^([^:]+):\s*(.+)$/);
    if (match) {
      pairs[match[1].trim()] = match[2].trim();
    }
  }
  
  return pairs;
}

function hasTableStructure(text: string): boolean {
  const lines = text.split('\n').filter(line => line.trim());
  return lines.some(line => line.split(/\s{2,}/).length > 2);
}

function hasListStructure(text: string): boolean {
  return /^[\s]*[•\-*]\s/.test(text);
}

function preprocessForAgent(content: ExtractedContent, agent: Agent): PreprocessedContent {
  const prompt = `${agent.prompt}\n\nDocument Content:\n${content.text}`;
  
  return {
    processedText: prompt,
    originalLength: content.text.length,
    processedLength: prompt.length,
    qualityScore: 0.8,
    preservedElements: [],
    removedElements: []
  };
}

function truncateIntelligently(text: string, options: PreprocessingOptions): string {
  const maxLength = options.maxLength || MAX_PROMPT_LENGTH;
  if (text.length <= maxLength) return text;
  
  // Simple truncation with sentence boundary respect
  const truncated = text.substring(0, maxLength);
  const lastSentence = truncated.lastIndexOf('.');
  
  return lastSentence > maxLength * 0.8 ? 
    truncated.substring(0, lastSentence + 1) : 
    truncated;
}

function summarizeForLength(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  
  // Extract key sections
  const sections = text.split(/\n\s*\n/);
  let summary = '';
  
  for (const section of sections) {
    const sectionHeader = section.split('\n')[0];
    if (summary.length + sectionHeader.length < maxLength) {
      summary += sectionHeader + '\n';
    }
  }
  
  return summary.trim();
}

function calculateQualityScore(text: string): number {
  const alphaRatio = (text.match(/[a-zA-Z]/g) || []).length / text.length;
  const digitRatio = (text.match(/[0-9]/g) || []).length / text.length;
  const spaceRatio = (text.match(/\s/g) || []).length / text.length;
  const specialRatio = (text.match(/[^a-zA-Z0-9\s]/g) || []).length / text.length;
  
  // Quality based on character distribution
  return Math.min(1, alphaRatio * 0.6 + digitRatio * 0.2 + spaceRatio * 0.1 + (1 - specialRatio) * 0.1);
}

function getConfidenceIndicators(text: string): { clarity: number; formatting: number } {
  const clarity = text.includes('unclear') || text.includes('posible') ? 0.3 : 0.9;
  const formatting = /\$\d+\.\d{2}/.test(text) ? 0.9 : 0.4;
  
  return { clarity, formatting };
}

function assessCompleteness(text: string, category: string): { score: number; missingFields: string[] } {
  const invoiceFields = ['invoice', 'vendor', 'amount', 'date'];
  const missing = invoiceFields.filter(field => !text.toLowerCase().includes(field));
  
  return {
    score: 1 - (missing.length / invoiceFields.length),
    missingFields: missing
  };
}

function preprocessContent(text: string, agent: Agent): PreprocessedContent {
  if (!text) {
    return {
      processedText: agent.prompt,
      originalLength: 0,
      processedLength: agent.prompt.length,
      qualityScore: 0,
      preservedElements: [],
      removedElements: []
    };
  }
  
  const cleaned = normalizeWhitespace(removeNonPrintable(text));
  const processed = `${agent.prompt}\n\nDocument:\n${cleaned}`;
  
  return {
    processedText: processed,
    originalLength: text.length,
    processedLength: processed.length,
    qualityScore: calculateQualityScore(cleaned),
    preservedElements: ['text', 'structure'],
    removedElements: ['noise', 'artifacts']
  };
}