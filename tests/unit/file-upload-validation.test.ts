import { describe, it, expect, _beforeEach } from 'bun:test';
import { 
  FileValidator, 
  ApiKeyLimitValidator, 
  FileSecurityValidator,
  FileUploadValidator,
  type FileValidationResult,
  type FileUploadLimits,
  type SupportedFileType
} from '../../supabase/functions/_shared/file-validation.ts';

describe('FileValidator - Comprehensive Enterprise Format Support', () => {
  
  describe('Tier 1: Critical Formats', () => {
    describe('Existing Tier 1 Formats', () => {
      it('should accept PDF files with correct header', async () => {
        const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
        const mockPdfFile = new File([pdfHeader], 'test.pdf', { type: 'application/pdf' });
        
        const _result = await FileValidator.validateFileHeader(mockPdfFile);
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe('application/pdf');
      });

      it('should accept DOCX files with ZIP signature', async () => {
        const docxHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // PK
        const mockDocxFile = new File([docxHeader], 'test.docx', { 
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
        });
        
        const _result = await FileValidator.validateFileHeader(mockDocxFile);
        expect(result.isValid).toBe(true);
      });

      it('should accept XLSX files with ZIP signature', async () => {
        const xlsxHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // PK
        const mockXlsxFile = new File([xlsxHeader], 'test.xlsx', { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });
        
        const _result = await FileValidator.validateFileHeader(mockXlsxFile);
        expect(result.isValid).toBe(true);
      });

      it('should accept JPEG files with correct signature', async () => {
        const jpegHeader = new Uint8Array([0xFF, 0xD8, 0xFF]);
        const mockJpegFile = new File([jpegHeader], 'test.jpg', { type: 'image/jpeg' });
        
        const _result = await FileValidator.validateFileHeader(mockJpegFile);
        expect(result.isValid).toBe(true);
      });

      it('should accept PNG files with correct signature', async () => {
        const pngHeader = new Uint8Array([0x89, 0x50, 0x4E, 0x47]);
        const mockPngFile = new File([pngHeader], 'test.png', { type: 'image/png' });
        
        const _result = await FileValidator.validateFileHeader(mockPngFile);
        expect(result.isValid).toBe(true);
      });
    });

    describe('NEW Tier 1 Formats', () => {
      it('should accept DOC files with OLE signature', async () => {
        const docHeader = new Uint8Array([0xD0, 0xCF, 0x11, 0xE0]); // OLE Compound
        const mockDocFile = new File([docHeader], 'legacy.doc', { type: 'application/msword' });
        
        const _result = await FileValidator.validateFileHeader(mockDocFile);
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe('application/msword');
      });

      it('should accept XLS files with OLE signature', async () => {
        const xlsHeader = new Uint8Array([0xD0, 0xCF, 0x11, 0xE0]); // OLE Compound
        const mockXlsFile = new File([xlsHeader], 'legacy.xls', { type: 'application/vnd.ms-excel' });
        
        const _result = await FileValidator.validateFileHeader(mockXlsFile);
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe('application/vnd.ms-excel');
      });

      it('should accept ODT files with ZIP signature', async () => {
        const odtHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // ZIP
        const mockOdtFile = new File([odtHeader], 'document.odt', { 
          type: 'application/vnd.oasis.opendocument.text' 
        });
        
        const _result = await FileValidator.validateFileHeader(mockOdtFile);
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe('application/vnd.oasis.opendocument.text');
      });

      it('should accept ODS files with ZIP signature', async () => {
        const odsHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // ZIP
        const mockOdsFile = new File([odsHeader], 'spreadsheet.ods', { 
          type: 'application/vnd.oasis.opendocument.spreadsheet' 
        });
        
        const _result = await FileValidator.validateFileHeader(mockOdsFile);
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe('application/vnd.oasis.opendocument.spreadsheet');
      });

      it('should accept RTF files with correct signature', async () => {
        const rtfHeader = new Uint8Array([0x7B, 0x5C, 0x72, 0x74]); // {\rt
        const mockRtfFile = new File([rtfHeader], 'document.rtf', { type: 'application/rtf' });
        
        const _result = await FileValidator.validateFileHeader(mockRtfFile);
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe('application/rtf');
      });
    });
  });

  describe('Tier 2: Business Formats', () => {
    it('should accept PPTX files with ZIP signature', async () => {
      const pptxHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // PK
      const mockPptxFile = new File([pptxHeader], 'presentation.pptx', { 
        type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
      });
      
      const _result = await FileValidator.validateFileHeader(mockPptxFile);
      expect(result.isValid).toBe(true);
    });

    it('should accept PPT files with OLE signature', async () => {
      const pptHeader = new Uint8Array([0xD0, 0xCF, 0x11, 0xE0]); // OLE Compound
      const mockPptFile = new File([pptHeader], 'presentation.ppt', { 
        type: 'application/vnd.ms-powerpoint' 
      });
      
      const _result = await FileValidator.validateFileHeader(mockPptFile);
      expect(result.isValid).toBe(true);
    });

    it('should accept ODP files with ZIP signature', async () => {
      const odpHeader = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // ZIP
      const mockOdpFile = new File([odpHeader], 'presentation.odp', { 
        type: 'application/vnd.oasis.opendocument.presentation' 
      });
      
      const _result = await FileValidator.validateFileHeader(mockOdpFile);
      expect(result.isValid).toBe(true);
    });

    it('should accept TIFF files with little-endian signature', async () => {
      const tiffHeader = new Uint8Array([0x49, 0x49, 0x2A, 0x00]); // TIFF LE
      const mockTiffFile = new File([tiffHeader], 'scan.tiff', { type: 'image/tiff' });
      
      const _result = await FileValidator.validateFileHeader(mockTiffFile);
      expect(result.isValid).toBe(true);
    });

    it('should accept TIFF files with big-endian signature', async () => {
      const tiffHeader = new Uint8Array([0x4D, 0x4D, 0x00, 0x2A]); // TIFF BE
      const mockTiffFile = new File([tiffHeader], 'scan.tif', { type: 'image/tiff' });
      
      const _result = await FileValidator.validateFileHeader(mockTiffFile);
      expect(result.isValid).toBe(true);
    });

    it('should accept TXT files (no magic number)', async () => {
      const mockTxtFile = new File(['Plain text content'], 'document.txt', { type: 'text/plain' });
      
      const _result = await FileValidator.validateFileHeader(mockTxtFile);
      expect(result.isValid).toBe(true);
    });

    it('should accept CSV files (no magic number)', async () => {
      const mockCsvFile = new File(['name,value\ntest,123'], 'data.csv', { type: 'text/csv' });
      
      const _result = await FileValidator.validateFileHeader(mockCsvFile);
      expect(result.isValid).toBe(true);
    });
  });

  describe('Tier 3: Specialized Formats (Disabled)', () => {
    it('should reject PAGES files (experimental, disabled)', () => {
      const mockPagesFile = new File(['content'], 'document.pages', { 
        type: 'application/vnd.apple.pages' 
      });
      
      const _result = FileValidator.validateFileType(mockPagesFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('not currently enabled');
      expect(result.error).toContain('planned for future release');
    });

    it('should reject HTML files (experimental, disabled)', () => {
      const mockHtmlFile = new File(['<html></html>'], 'document.html', { type: 'text/html' });
      
      const _result = FileValidator.validateFileType(mockHtmlFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('not currently enabled');
    });

    it('should reject SVG files (experimental, disabled)', () => {
      const mockSvgFile = new File(['<svg></svg>'], 'image.svg', { type: 'image/svg+xml' });
      
      const _result = FileValidator.validateFileType(mockSvgFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('not currently enabled');
    });
  });

  describe('Format-Specific Validation', () => {
    describe('RTF Security Validation', () => {
      it('should reject RTF files with dangerous object embeddings', async () => {
        const maliciousRtf = new TextEncoder().encode('{\\rtf1\\ansi\\deff0 {\\objdata dangerous content}}');
        const mockRtfFile = new File([maliciousRtf], 'malicious.rtf', { type: 'application/rtf' });
        
        const _result = await FileValidator.validateFormatSpecific(mockRtfFile);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('potentially dangerous object embeddings');
      });

      it('should accept safe RTF files', async () => {
        const safeRtf = new TextEncoder().encode('{\\rtf1\\ansi\\deff0 Normal text content}');
        const mockRtfFile = new File([safeRtf], 'safe.rtf', { type: 'application/rtf' });
        
        const _result = await FileValidator.validateFormatSpecific(mockRtfFile);
        expect(result.isValid).toBe(true);
      });
    });

    describe('CSV Injection Protection', () => {
      it('should reject CSV files with formula injection', async () => {
        const maliciousCsv = '=cmd|"/c calc"|!,data\n+SUM(1+1),more';
        const mockCsvFile = new File([maliciousCsv], 'malicious.csv', { type: 'text/csv' });
        
        const _result = await FileValidator.validateFormatSpecific(mockCsvFile);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('potentially dangerous formula injection');
      });

      it('should accept safe CSV files', async () => {
        const safeCsv = 'name,value,date\nJohn,123,2024-01-01\nJane,456,2024-01-02';
        const mockCsvFile = new File([safeCsv], 'safe.csv', { type: 'text/csv' });
        
        const _result = await FileValidator.validateFormatSpecific(mockCsvFile);
        expect(result.isValid).toBe(true);
      });
    });

    describe('TIFF Validation', () => {
      it('should validate TIFF structure', async () => {
        const tiffHeader = new Uint8Array([0x49, 0x49, 0x2A, 0x00]); // Valid TIFF LE
        const mockTiffFile = new File([tiffHeader], 'scan.tiff', { type: 'image/tiff' });
        
        const _result = await FileValidator.validateFormatSpecific(mockTiffFile);
        expect(result.isValid).toBe(true);
      });

      it('should reject invalid TIFF structure', async () => {
        const invalidTiff = new Uint8Array([0x00, 0x00, 0x00, 0x00]); // Invalid header
        const mockTiffFile = new File([invalidTiff], 'invalid.tiff', { type: 'image/tiff' });
        
        const _result = await FileValidator.validateFormatSpecific(mockTiffFile);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('Invalid TIFF file structure');
      });
    });

    describe('Presentation Size Limits', () => {
      it('should enforce 100MB limit for presentations', async () => {
        const largeContent = 'x'.repeat(1000);
        const largePptx = new File([largeContent], 'huge.pptx', { 
          type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation' 
        });
        
        // Mock size to exceed presentation limit
        Object.defineProperty(largePptx, 'size', {
          value: 110 * 1024 * 1024, // 110MB
          writable: false
        });
        
        const _result = await FileValidator.validateFormatSpecific(largePptx);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('Presentation file too large');
      });
    });

    describe('Spreadsheet Size Limits', () => {
      it('should enforce 75MB limit for spreadsheets', async () => {
        const largeContent = 'x'.repeat(1000);
        const largeXlsx = new File([largeContent], 'huge.xlsx', { 
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });
        
        // Mock size to exceed spreadsheet limit
        Object.defineProperty(largeXlsx, 'size', {
          value: 80 * 1024 * 1024, // 80MB
          writable: false
        });
        
        const _result = await FileValidator.validateFormatSpecific(largeXlsx);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('Spreadsheet file too large');
      });
    });
  });

  describe('Dynamic File Size Limits', () => {
    it('should apply format-specific size multipliers for test keys', () => {
      // TIFF gets 2x multiplier, so 20MB for test keys vs 10MB base
      const tiffLimit = FileValidator.getMaxFileSizeForFormat('image/tiff', 'test');
      expect(tiffLimit).toBe(20 * 1024 * 1024); // 20MB

      // PDF gets 1x multiplier, so 10MB for test keys
      const pdfLimit = FileValidator.getMaxFileSizeForFormat('application/pdf', 'test');
      expect(pdfLimit).toBe(10 * 1024 * 1024); // 10MB

      // PNG gets 1.2x multiplier, so 12MB for test keys
      const pngLimit = FileValidator.getMaxFileSizeForFormat('image/png', 'test');
      expect(pngLimit).toBe(12 * 1024 * 1024); // 12MB
    });

    it('should apply format-specific size multipliers for production keys', () => {
      // TIFF gets 2x multiplier, so 100MB for production keys vs 50MB base
      const tiffLimit = FileValidator.getMaxFileSizeForFormat('image/tiff', 'production');
      expect(tiffLimit).toBe(100 * 1024 * 1024); // 100MB

      // Text files get 0.5x multiplier, so 25MB for production keys
      const txtLimit = FileValidator.getMaxFileSizeForFormat('text/plain', 'production');
      expect(txtLimit).toBe(25 * 1024 * 1024); // 25MB
    });
  });

  describe('Processing Strategy Selection', () => {
    it('should route ODT files to OpenAI with native extraction', () => {
      const strategy = FileValidator.getProcessingStrategy('application/vnd.oasis.opendocument.text');
      expect(strategy.primaryModel).toBe('openai');
      expect(strategy.extraction).toBe('native');
    });

    it('should route legacy DOC files to Claude with native extraction', () => {
      const strategy = FileValidator.getProcessingStrategy('application/msword');
      expect(strategy.primaryModel).toBe('claude');
      expect(strategy.extraction).toBe('native');
    });

    it('should route TIFF files to LlamaParse with OCR extraction', () => {
      const strategy = FileValidator.getProcessingStrategy('image/tiff');
      expect(strategy.primaryModel).toBe('llamaparse');
      expect(strategy.extraction).toBe('ocr');
    });

    it('should route CSV files to Claude with structured extraction', () => {
      const strategy = FileValidator.getProcessingStrategy('text/csv');
      expect(strategy.primaryModel).toBe('claude');
      expect(strategy.extraction).toBe('structured');
    });

    it('should provide default strategy for unknown formats', () => {
      const strategy = FileValidator.getProcessingStrategy('unknown/format');
      expect(strategy.primaryModel).toBe('openai');
      expect(strategy.extraction).toBe('hybrid');
    });
  });

  describe('Format Information Access', () => {
    it('should return format information for supported types', () => {
      const pdfInfo = FileValidator.getFormatInfo('application/pdf');
      expect(pdfInfo).toBeDefined();
      expect(pdfInfo?.tier).toBe(1);
      expect(pdfInfo?.category).toBe('document');
      expect(pdfInfo?.processing).toBe('hybrid');
    });

    it('should return null for unsupported types', () => {
      const unknownInfo = FileValidator.getFormatInfo('unknown/type');
      expect(unknownInfo).toBeNull();
    });
  });

  describe('Tier-based Type Filtering', () => {
    it('should return only Tier 1 formats when requested', () => {
      const tier1Types = FileValidator.getSupportedTypes(1);
      expect(tier1Types).toContain('application/pdf');
      expect(tier1Types).toContain('application/msword');
      expect(tier1Types).toContain('application/vnd.oasis.opendocument.text');
      expect(tier1Types).not.toContain('image/tiff'); // Tier 2
    });

    it('should return only Tier 2 formats when requested', () => {
      const tier2Types = FileValidator.getSupportedTypes(2);
      expect(tier2Types).toContain('image/tiff');
      expect(tier2Types).toContain('text/plain');
      expect(tier2Types).toContain('text/csv');
      expect(tier2Types).not.toContain('application/pdf'); // Tier 1
    });

    it('should exclude disabled Tier 3 formats from all types', () => {
      const allTypes = FileValidator.getSupportedTypes();
      expect(allTypes).not.toContain('application/vnd.apple.pages'); // Tier 3, disabled
      expect(allTypes).not.toContain('text/html'); // Tier 3, disabled
      expect(allTypes).not.toContain('image/svg+xml'); // Tier 3, disabled
    });
  });

  describe('Metadata Extraction', () => {
    it('should extract tier and category information', () => {
      const odtFile = new File(['content'], 'document.odt', { 
        type: 'application/vnd.oasis.opendocument.text' 
      });
      
      const metadata = FileValidator.extractFileMetadata(odtFile);
      expect(metadata.tier).toBe(1);
      expect(metadata.category).toBe('document');
      expect(metadata.processing).toBe('native');
    });
  });
});

describe('Enhanced Security Validation', () => {
  describe('Format-Specific Threat Detection', () => {
    it('should detect RTF exploits', async () => {
      const maliciousRtf = new Uint8Array([0x7B, 0x5C, 0x72, 0x74, 0x66, 0x31, 0x5C, 0x6F, 0x62, 0x6A]);
      const suspiciousFile = new File([maliciousRtf], 'exploit.rtf', { type: 'application/rtf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(suspiciousFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('RTF Exploit');
    });

    it('should detect CSV formula injection patterns', async () => {
      const maliciousCsv = new Uint8Array([0x3D, 0x2B]); // =+
      const suspiciousFile = new File([maliciousCsv], 'injection.csv', { type: 'text/csv' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(suspiciousFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('CSV Formula Injection');
    });

    it('should detect file extension mismatches', async () => {
      // File claims to be PDF but has wrong extension
      const mockFile = new File(['content'], 'document.txt', { type: 'application/pdf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File extension mismatch');
    });
  });
});

describe('ApiKeyLimitValidator - Format-Aware Sizing', () => {
  describe('Format-Aware File Size Limits', () => {
    it('should apply format multipliers to test key limits', () => {
      // TIFF files get 2x multiplier
      const tiffLimit = ApiKeyLimitValidator.getMaxFileSizeForFormat('image/tiff', 'test');
      expect(tiffLimit).toBe(20 * 1024 * 1024); // 20MB (10MB * 2.0)

      // Text files get 0.5x multiplier
      const txtLimit = ApiKeyLimitValidator.getMaxFileSizeForFormat('text/plain', 'test');
      expect(txtLimit).toBe(5 * 1024 * 1024); // 5MB (10MB * 0.5)
    });

    it('should validate files against format-aware limits', () => {
      const tiffFile = new File(['content'], 'scan.tiff', { type: 'image/tiff' });
      
      // Mock size to be within TIFF limit (20MB) but over base limit (10MB)
      Object.defineProperty(tiffFile, 'size', {
        value: 15 * 1024 * 1024, // 15MB
        writable: false
      });

      const _result = ApiKeyLimitValidator.validateFileAgainstKeyLimits(tiffFile, 'test');
      expect(result.isValid).toBe(true); // Should be valid due to 2x multiplier
    });
  });
});

describe('FileUploadValidator - Complete Pipeline', () => {
  describe('Comprehensive Format Support', () => {
    const testFormats = [
      { file: 'document.odt', type: 'application/vnd.oasis.opendocument.text', tier: 1 },
      { file: 'legacy.doc', type: 'application/msword', tier: 1 },
      { file: 'presentation.pptx', type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation', tier: 2 },
      { file: 'scan.tiff', type: 'image/tiff', tier: 2 },
      { file: 'data.csv', type: 'text/csv', tier: 2 }
    ];

    testFormats.forEach(format => {
      it(`should process ${format.file} with appropriate strategy`, async () => {
        // Create appropriate header for each format
        let header: Uint8Array;
        switch (format.type) {
          case 'application/vnd.oasis.opendocument.text': {
          case 'application/vnd.openxmlformats-officedocument.presentationml.presentation': {
            header = new Uint8Array([0x50, 0x4B, 0x03, 0x04]); // ZIP
            break;
    }
          case 'application/msword': {
            header = new Uint8Array([0xD0, 0xCF, 0x11, 0xE0]); // OLE
            break;
    }
          case 'image/tiff': {
            header = new Uint8Array([0x49, 0x49, 0x2A, 0x00]); // TIFF
            break;
    }
          case 'text/csv': {
            header = new TextEncoder().encode('name,value\\ntest,123');
            break;
    }
          default:
            header = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
        }

        const testFile = new File([header], format.file, { type: format.type });
        const _result = await FileUploadValidator.validateUpload(testFile, 'production');
        
        if (!result.isValid) {
          console.error(`Validation failed for ${format.file}:`, result.error);
        }
        expect(result.isValid).toBe(true);
        expect(result.metadata?.type).toBe(format.type);
        expect(result.metadata?.tier).toBe(format.tier);
      });
    });
  });

  describe('Format Support Information', () => {
    it('should provide comprehensive format support info', () => {
      const supportInfo = FileUploadValidator.getFormatSupportInfo();
      
      expect(supportInfo.tier1.length).toBeGreaterThanOrEqual(10); // At least 10 Tier 1 formats
      expect(supportInfo.tier2.length).toBeGreaterThanOrEqual(6);  // At least 6 Tier 2 formats
      expect(supportInfo.tier3.length).toBeGreaterThanOrEqual(3);  // At least 3 Tier 3 formats
      expect(supportInfo.total).toBeGreaterThanOrEqual(15);        // At least 15 total formats
      
      // Check category organization
      expect(supportInfo.categoriesByTier[1]).toHaveProperty('document');
      expect(supportInfo.categoriesByTier[1]).toHaveProperty('image');
      expect(supportInfo.categoriesByTier[2]).toHaveProperty('presentation');
      expect(supportInfo.categoriesByTier[2]).toHaveProperty('text');
    });
  });

  describe('Processing Strategy Integration', () => {
    it('should include processing strategy in validation metadata', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const pdfFile = new File([pdfHeader], 'document.pdf', { type: 'application/pdf' });
      
      const _result = await FileUploadValidator.validateUpload(pdfFile, 'production');
      
      expect(result.isValid).toBe(true);
      expect(result.metadata?.processingStrategy).toBe('openai:hybrid');
    });
  });
});

// Legacy tests for backward compatibility
describe('Legacy File Validation (Backward Compatibility)', () => {
  describe('validateFileType', () => {
    it('should accept PDF files', () => {
      const mockPdfFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });
      
      const _result = FileValidator.validateFileType(mockPdfFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('application/pdf');
      expect(result.metadata?.originalName).toBe('test.pdf');
    });

    it('should reject unsupported file types', () => {
      const mockUnsupportedFile = new File(['content'], 'test.exe', { type: 'application/exe' });
      
      const _result = FileValidator.validateFileType(mockUnsupportedFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Unsupported file type');
    });
  });

  describe('validateFileSize', () => {
    const defaultLimits: FileUploadLimits = {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: ['application/pdf', 'image/jpeg', 'image/png']
    };

    it('should accept files within size limit', () => {
      const smallFile = new File(['small content'], 'test.pdf', { type: 'application/pdf' });
      
      const _result = FileValidator.validateFileSize(smallFile, defaultLimits);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.size).toBe(smallFile.size);
    });

    it('should reject empty files', () => {
      const emptyFile = new File([], 'empty.pdf', { type: 'application/pdf' });
      
      const _result = FileValidator.validateFileSize(emptyFile, defaultLimits);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File is empty');
    });
  });

  describe('calculateFileHash', () => {
    it('should calculate consistent hash for same file content', async () => {
      const file1 = new File(['test content'], 'file1.pdf', { type: 'application/pdf' });
      const file2 = new File(['test content'], 'file2.pdf', { type: 'application/pdf' });
      
      const hash1 = await FileValidator.calculateFileHash(file1);
      const hash2 = await FileValidator.calculateFileHash(file2);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex string
    });

    it('should produce different hashes for different content', async () => {
      const file1 = new File(['content A'], 'file1.pdf', { type: 'application/pdf' });
      const file2 = new File(['content B'], 'file2.pdf', { type: 'application/pdf' });
      
      const hash1 = await FileValidator.calculateFileHash(file1);
      const hash2 = await FileValidator.calculateFileHash(file2);
      
      expect(hash1).not.toBe(hash2);
    });
  });
});