/**
 * Queue System Unit Tests
 *
 * Tests the queue system database functions directly via Supabase client
 * - Queue metrics and health checks
 * - Job priority handling
 * - Database function integration
 * - System verification
 */

import { describe, it, expect, _beforeEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';

// =============================================================================
// TEST CONFIGURATION
// =============================================================================

const SUPABASE_URL = 'http://127.0.0.1:14321';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

let supabase: ReturnType<typeof createClient>;

// =============================================================================
// SETUP AND TEARDOWN
// =============================================================================

beforeEach(() => {
  supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
});

// =============================================================================
// QUEUE SYSTEM DATABASE TESTS
// =============================================================================

describe('Queue System Database Functions', () => {
  it('should verify cron setup correctly', async () => {
    const { data, error } = await supabase.rpc('verify_cron_setup');

    expect(error).toBeNull();
    expect(data).toBeArray();
    expect(data.length).toBeGreaterThan(0);

    // Check that pg_cron and pg_net extensions are OK
    const pgCronCheck = data.find(check => check.check_name === 'pg_cron_extension');
    const pgNetCheck = data.find(check => check.check_name === 'pg_net_extension');
    const scheduledJobsCheck = data.find(check => check.check_name === 'scheduled_jobs');

    expect(pgCronCheck?.status).toBe('OK');
    expect(pgNetCheck?.status).toBe('OK');
    expect(scheduledJobsCheck?.status).toBe('OK');
  });

  it('should get queue metrics successfully', async () => {
    const { data, error } = await supabase.rpc('get_queue_metrics');

    expect(error).toBeNull();
    expect(data).toBeArray();
    expect(data.length).toBe(1);

    const metrics = data[0];
    expect(metrics).toHaveProperty('total_queued');
    expect(metrics).toHaveProperty('total_processing');
    expect(metrics).toHaveProperty('total_failed');
    expect(metrics).toHaveProperty('total_dead_letter');
    expect(metrics).toHaveProperty('urgent_queued');
    expect(metrics).toHaveProperty('high_queued');
    expect(metrics).toHaveProperty('normal_queued');
    expect(metrics).toHaveProperty('low_queued');

    // All counts should be non-negative numbers
    expect(metrics.total_queued).toBeGreaterThanOrEqual(0);
    expect(metrics.total_processing).toBeGreaterThanOrEqual(0);
    expect(metrics.total_failed).toBeGreaterThanOrEqual(0);
    expect(metrics.total_dead_letter).toBeGreaterThanOrEqual(0);
  });

  it('should calculate customer job priority correctly', async () => {
    const testCases = [
      { tier: 'premium', expectedPriority: 'normal' },
      { tier: 'enterprise', expectedPriority: 'high' },
      { tier: 'basic', expectedPriority: 'low' }
    ];

    for (const testCase of testCases) {
      const { data, error } = await supabase.rpc('get_customer_job_priority', {
        customer_tier: testCase.tier
      });

      expect(error).toBeNull();
      expect(data).toBe(testCase.expectedPriority);
    }
  });

  it('should access job_queue table schema correctly', async () => {
    // Test that the job_queue table exists and is accessible
    const { data, error } = await supabase
      .from('job_queue')
      .select('*')
      .limit(1);

    // Should not error (table exists)
    expect(error).toBeNull();
    // Should return empty array (no jobs yet)
    expect(data).toBeArray();
    expect(data.length).toBe(0);
  });

  it('should validate job status enum values', async () => {
    // This tests that our enum values are properly defined in the database
    // by attempting to filter on each valid status
    const validStatuses = ['queued', 'processing', 'completed', 'failed', 'dead_letter'];

    for (const status of validStatuses) {
      const { error } = await supabase
        .from('job_queue')
        .select('*')
        .eq('status', status)
        .limit(1);

      // Should not error for valid enum values
      expect(error).toBeNull();
    }
  });

  it('should validate job priority enum values', async () => {
    // Test that priority enum values are properly defined
    const validPriorities = ['urgent', 'high', 'normal', 'low', 'background'];

    for (const priority of validPriorities) {
      const { error } = await supabase
        .from('job_queue')
        .select('*')
        .eq('priority', priority)
        .limit(1);

      // Should not error for valid enum values
      expect(error).toBeNull();
    }
  });
});

// =============================================================================
// QUEUE MANAGEMENT FUNCTION TESTS
// =============================================================================

describe('Queue Management Functions', () => {
  it('should have cleanup_job_queue function available', async () => {
    const { data, error } = await supabase.rpc('cleanup_job_queue');

    expect(error).toBeNull();
    expect(data).toBeArray();
    expect(data.length).toBe(1);

    const _result = data[0];
    expect(result).toHaveProperty('expired_jobs_deleted');
    expect(result).toHaveProperty('old_completed_deleted');
    expect(result).toHaveProperty('dead_letter_archived');

    // All counts should be non-negative
    expect(result.expired_jobs_deleted).toBeGreaterThanOrEqual(0);
    expect(result.old_completed_deleted).toBeGreaterThanOrEqual(0);
    expect(result.dead_letter_archived).toBeGreaterThanOrEqual(0);
  });

  it('should have reset_stuck_jobs function available', async () => {
    const { data, error } = await supabase.rpc('reset_stuck_jobs', {
      stuck_threshold_minutes: 60
    });

    expect(error).toBeNull();
    expect(typeof data).toBe('number');
    expect(data).toBeGreaterThanOrEqual(0);
  });
});

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

describe('Queue System Integration', () => {
  it('should have all required database tables', async () => {
    // Test that all queue-related tables exist by attempting to access them
    const tables = ['job_queue', 'customers', 'api_keys', 'documents', 'agents'];

    for (const table of tables) {
      const { error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      expect(error).toBeNull();
    }
  });

  it('should perform queue metrics calculation under 100ms', async () => {
    const startTime = performance.now();

    const { data, error } = await supabase.rpc('get_queue_metrics');

    const endTime = performance.now();
    const duration = endTime - startTime;

    expect(error).toBeNull();
    expect(data).toBeDefined();
    expect(duration).toBeLessThan(100); // Under 100ms performance requirement
  });

  it('should verify queue system is properly installed', async () => {
    const { data, error } = await supabase.rpc('verify_cron_setup');

    expect(error).toBeNull();
    expect(data).toBeArray();

    // Count successful checks
    const successfulChecks = data.filter(check =>
      check.status === 'OK' || check.status === 'NEEDS_UPDATE'
    );

    // Should have at least 3 successful checks (extensions and jobs)
    expect(successfulChecks.length).toBeGreaterThanOrEqual(3);
  });
});