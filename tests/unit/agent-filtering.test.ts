import { describe, it, expect, _beforeEach, _mock } from 'bun:test';

// Agent Filtering & Search Unit Tests
// Tests for GitHub Issue #14: Agent Storage & Retrieval
// Advanced filtering and search functionality with performance validation

interface AgentFilter {
  category?: string;
  is_default?: boolean;
  search?: string;
  version?: number;
  status?: 'active' | 'inactive' | 'deprecated';
  customer_id?: string;
  accuracy_threshold?: number;
  performance_min?: number;
}

interface AgentSearchResult {
  id: string;
  agent_id: string;
  name: string;
  description: string;
  category: string;
  relevance_score: number;
  use_cases?: string[];
  keywords?: string[];
}

interface AdvancedSearchOptions {
  query: string;
  categories?: string[];
  min_accuracy?: number;
  max_processing_time?: number;
  fuzzy_search?: boolean;
  include_deprecated?: boolean;
}

// Mock functions for filtering logic
const mockCategoryFilter = mock((agents: any[], category: string) => {
  return agents.filter(agent => agent.category === category);
});

const mockDefaultFilter = mock((agents: any[], isDefault: boolean) => {
  return agents.filter(agent => agent.is_default === isDefault);
});

const mockSearchFilter = mock((agents: any[], searchTerm: string) => {
  const lowerSearch = searchTerm.toLowerCase();
  return agents.filter(agent => 
    agent.name.toLowerCase().includes(lowerSearch) ||
    agent.description.toLowerCase().includes(lowerSearch) ||
    (agent.use_cases && agent.use_cases.some((useCase: string) => 
      useCase.toLowerCase().includes(lowerSearch)
    ))
  );
});

const mockPerformanceFilter = mock((agents: any[], threshold: number) => {
  return agents.filter(agent => 
    agent.accuracy_rating && agent.accuracy_rating >= threshold
  );
});

// Advanced search with relevance scoring
function calculateRelevanceScore(agent: any, searchTerm: string): number {
  const lowerSearch = searchTerm.toLowerCase();
  let score = 0;
  
  // Name match (highest weight)
  if (agent.name.toLowerCase().includes(lowerSearch)) {
    score += 100;
  }
  
  // Description match (medium weight)
  if (agent.description.toLowerCase().includes(lowerSearch)) {
    score += 50;
  }
  
  // Use cases match (medium weight)
  if (agent.use_cases) {
    const matchingUseCases = agent.use_cases.filter((useCase: string) => 
      useCase.toLowerCase().includes(lowerSearch)
    );
    score += matchingUseCases.length * 30;
  }
  
  // Category match (low weight)
  if (agent.category.toLowerCase().includes(lowerSearch)) {
    score += 20;
  }
  
  // Performance bonus for high-accuracy agents
  if (agent.accuracy_rating && agent.accuracy_rating > 0.9) {
    score += 10;
  }
  
  return score;
}

// Mock agent data for testing
const mockAgents = [
  {
    id: 'agent-1',
    agent_id: 'default-invoice-v1',
    name: 'Advanced Invoice Processor',
    description: 'Production-grade invoice processor with >90% accuracy',
    category: 'invoice',
    is_default: true,
    customer_id: null,
    accuracy_rating: 0.95,
    avg_processing_time_ms: 1200,
    use_cases: ['accounts payable', 'expense tracking'],
    status: 'active'
  },
  {
    id: 'agent-2',
    agent_id: 'default-contract-v1',
    name: 'Contract Analysis Expert',
    description: 'Advanced contract analyzer for key terms and obligations',
    category: 'contract',
    is_default: true,
    customer_id: null,
    accuracy_rating: 0.92,
    avg_processing_time_ms: 1800,
    use_cases: ['contract review', 'compliance'],
    status: 'active'
  },
  {
    id: 'agent-3',
    agent_id: 'custom-invoice-special',
    name: 'Custom Invoice Handler',
    description: 'Specialized invoice processor for construction industry',
    category: 'invoice',
    is_default: false,
    customer_id: 'customer-123',
    accuracy_rating: 0.88,
    avg_processing_time_ms: 1500,
    use_cases: ['construction invoices', 'progress billing'],
    status: 'active'
  },
  {
    id: 'agent-4',
    agent_id: 'default-receipt-v1',
    name: 'Receipt Processor',
    description: 'Basic receipt processing for expenses',
    category: 'receipt',
    is_default: true,
    customer_id: null,
    accuracy_rating: 0.85,
    avg_processing_time_ms: 800,
    use_cases: ['expense tracking'],
    status: 'deprecated'
  }
];

describe('Agent Category Filtering', () => {
  it('should filter agents by invoice category', () => {
    // Arrange
    const category = 'invoice';
    
    // Act
    const _result = mockCategoryFilter(mockAgents, category);
    
    // Assert
    expect(result).toHaveLength(2);
    expect(result.every(agent => agent.category === 'invoice')).toBe(true);
    expect(mockCategoryFilter).toHaveBeenCalledWith(mockAgents, 'invoice');
  });

  it('should filter agents by contract category', () => {
    // Arrange
    const category = 'contract';
    
    // Act
    const _result = mockCategoryFilter(mockAgents, category);
    
    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].category).toBe('contract');
    expect(result[0].name).toBe('Contract Analysis Expert');
  });

  it('should return empty array for non-existent category', () => {
    // Arrange
    const category = 'nonexistent';
    
    // Act
    const _result = mockCategoryFilter(mockAgents, category);
    
    // Assert
    expect(result).toHaveLength(0);
  });

  it('should handle case-sensitive category filtering', () => {
    // Arrange
    const category = 'INVOICE'; // Uppercase
    
    // Act
    const _result = mockCategoryFilter(mockAgents, category);
    
    // Assert
    expect(result).toHaveLength(0); // Should not match due to case sensitivity
  });
});

describe('Default Agent Filtering', () => {
  it('should filter to show only default agents', () => {
    // Arrange
    const isDefault = true;
    
    // Act
    const _result = mockDefaultFilter(mockAgents, isDefault);
    
    // Assert
    expect(result).toHaveLength(3);
    expect(result.every(agent => agent.is_default === true)).toBe(true);
    expect(result.every(agent => agent.customer_id === null)).toBe(true);
  });

  it('should filter to show only custom agents', () => {
    // Arrange
    const isDefault = false;
    
    // Act
    const _result = mockDefaultFilter(mockAgents, isDefault);
    
    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].is_default).toBe(false);
    expect(result[0].customer_id).toBe('customer-123');
    expect(result[0].name).toBe('Custom Invoice Handler');
  });
});

describe('Search Functionality', () => {
  beforeEach(() => {
    // Reset mocks before each test
    mockSearchFilter.mockClear();
  });

  it('should search by agent name', () => {
    // Arrange
    const searchTerm = 'Invoice';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(2);
    expect(result.every(agent => 
      agent.name.toLowerCase().includes('invoice')
    )).toBe(true);
  });

  it('should search by description', () => {
    // Arrange
    const searchTerm = 'contract';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].description.toLowerCase()).toContain('contract');
  });

  it('should search by use cases', () => {
    // Arrange
    const searchTerm = 'compliance';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].use_cases).toContain('compliance');
  });

  it('should handle case-insensitive search', () => {
    // Arrange
    const searchTerm = 'INVOICE';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(2);
  });

  it('should return empty array for non-matching search', () => {
    // Arrange
    const searchTerm = 'nonexistent';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(0);
  });

  it('should handle multi-word search terms', () => {
    // Arrange
    const searchTerm = 'contract analysis';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Contract Analysis Expert');
  });
});

describe('Performance-Based Filtering', () => {
  it('should filter agents by accuracy threshold', () => {
    // Arrange
    const threshold = 0.90;
    
    // Act
    const _result = mockPerformanceFilter(mockAgents, threshold);
    
    // Assert
    expect(result).toHaveLength(2);
    expect(result.every(agent => agent.accuracy_rating >= 0.90)).toBe(true);
  });

  it('should filter agents by high accuracy threshold', () => {
    // Arrange
    const threshold = 0.95;
    
    // Act
    const _result = mockPerformanceFilter(mockAgents, threshold);
    
    // Assert
    expect(result).toHaveLength(1);
    expect(result[0].accuracy_rating).toBe(0.95);
    expect(result[0].name).toBe('Advanced Invoice Processor');
  });

  it('should return empty array when no agents meet threshold', () => {
    // Arrange
    const threshold = 0.99;
    
    // Act
    const _result = mockPerformanceFilter(mockAgents, threshold);
    
    // Assert
    expect(result).toHaveLength(0);
  });
});

describe('Relevance Scoring', () => {
  it('should calculate highest score for name match', () => {
    // Arrange
    const agent = mockAgents[0]; // Advanced Invoice Processor
    const searchTerm = 'invoice';
    
    // Act
    const score = calculateRelevanceScore(agent, searchTerm);
    
    // Assert
    expect(score).toBeGreaterThan(100); // Name match + description match + performance bonus
  });

  it('should calculate medium score for description match only', () => {
    // Arrange
    const agent = mockAgents[1]; // Contract Analysis Expert
    const searchTerm = 'analysis';
    
    // Act
    const score = calculateRelevanceScore(agent, searchTerm);
    
    // Assert
    expect(score).toBeGreaterThanOrEqual(50);
    expect(score).toBeLessThan(150); // Account for name + description match + performance bonus
  });

  it('should include performance bonus for high-accuracy agents', () => {
    // Arrange
    const agent = mockAgents[0]; // 0.95 accuracy
    const searchTerm = 'processor';
    
    // Act
    const score = calculateRelevanceScore(agent, searchTerm);
    
    // Assert
    expect(score).toBeGreaterThan(50); // Should include 10-point performance bonus
  });

  it('should score use case matches appropriately', () => {
    // Arrange
    const agent = mockAgents[2]; // Custom invoice with construction use cases
    const searchTerm = 'construction';
    
    // Act
    const score = calculateRelevanceScore(agent, searchTerm);
    
    // Assert
    expect(score).toBeGreaterThanOrEqual(30); // Use case match worth 30 points
  });

  it('should return zero score for no matches', () => {
    // Arrange
    const agent = mockAgents[0];
    const searchTerm = 'unrelated';
    
    // Act
    const score = calculateRelevanceScore(agent, searchTerm);
    
    // Assert
    expect(score).toBe(10); // Only performance bonus, no text matches
  });
});

describe('Combined Filter Operations', () => {
  it('should apply category and default filters together', () => {
    // Arrange
    const agents = mockAgents;
    
    // Act
    const categoryFiltered = mockCategoryFilter(agents, 'invoice');
    const finalResult = mockDefaultFilter(categoryFiltered, true);
    
    // Assert
    expect(finalResult).toHaveLength(1);
    expect(finalResult[0].category).toBe('invoice');
    expect(finalResult[0].is_default).toBe(true);
    expect(finalResult[0].name).toBe('Advanced Invoice Processor');
  });

  it('should apply search and performance filters together', () => {
    // Arrange
    const agents = mockAgents;
    
    // Act
    const searchFiltered = mockSearchFilter(agents, 'invoice');
    const finalResult = mockPerformanceFilter(searchFiltered, 0.90);
    
    // Assert
    expect(finalResult).toHaveLength(1);
    expect(finalResult[0].name).toBe('Advanced Invoice Processor');
    expect(finalResult[0].accuracy_rating).toBeGreaterThanOrEqual(0.90);
  });

  it('should handle filter chain that results in no matches', () => {
    // Arrange
    const agents = mockAgents;
    
    // Act
    const categoryFiltered = mockCategoryFilter(agents, 'contract');
    const defaultFiltered = mockDefaultFilter(categoryFiltered, false);
    
    // Assert
    expect(defaultFiltered).toHaveLength(0);
  });
});

describe('Filter Input Validation', () => {
  it('should handle null search terms gracefully', () => {
    // Arrange
    const searchTerm = '';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(result).toHaveLength(4); // Should return all agents for empty search
  });

  it('should handle special characters in search', () => {
    // Arrange
    const searchTerm = 'invoice & contract';
    
    // Act
    const _result = mockSearchFilter(mockAgents, searchTerm);
    
    // Assert
    expect(Array.isArray(result)).toBe(true);
  });

  it('should handle negative accuracy thresholds', () => {
    // Arrange
    const threshold = -0.1;
    
    // Act
    const _result = mockPerformanceFilter(mockAgents, threshold);
    
    // Assert
    expect(result).toHaveLength(4); // All agents should pass negative threshold
  });

  it('should handle accuracy threshold above 1.0', () => {
    // Arrange
    const threshold = 1.1;
    
    // Act
    const _result = mockPerformanceFilter(mockAgents, threshold);
    
    // Assert
    expect(result).toHaveLength(0); // No agents can have accuracy > 1.0
  });
});

describe('Filter Performance', () => {
  it('should efficiently filter large agent sets', () => {
    // Arrange
    const largeAgentSet = Array(1000).fill(null).map((_, _index) => ({
      ...mockAgents[index % mockAgents.length],
      id: `agent-${index}`,
      agent_id: `agent-${index}`
    }));
    
    const startTime = performance.now();
    
    // Act
    const _result = mockCategoryFilter(largeAgentSet, 'invoice');
    
    const endTime = performance.now();
    
    // Assert
    expect(endTime - startTime).toBeLessThan(100); // Should complete in <100ms
    expect(result.length).toBeGreaterThan(0);
  });

  it('should handle complex search efficiently', () => {
    // Arrange
    const complexSearchTerm = 'advanced production grade processing';
    const startTime = performance.now();
    
    // Act
    const _result = mockSearchFilter(mockAgents, complexSearchTerm);
    
    const endTime = performance.now();
    
    // Assert
    expect(endTime - startTime).toBeLessThan(50); // Should complete in <50ms
    expect(Array.isArray(result)).toBe(true);
  });
});

// Export for use in integration tests
export {
  mockCategoryFilter,
  mockDefaultFilter,
  mockSearchFilter,
  mockPerformanceFilter,
  calculateRelevanceScore,
  mockAgents
};