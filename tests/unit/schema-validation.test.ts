import { describe, it, expect, beforeEach, beforeAll, _afterAll } from 'bun:test';
import { SchemaValidator } from '../../supabase/functions/_shared/schema-validator';
import { SchemaCompatibilityChecker } from '../../supabase/functions/_shared/schema-compatibility';
import { SchemaGenerator } from '../../supabase/functions/_shared/schema-generator';
import { SchemaCache } from '../../supabase/functions/_shared/schema-cache';
import type { JSONSchema, ValidationError, CompatibilityResult, FieldDefinition } from '../../types/schema';

// Test Data
const validInvoiceSchema: JSONSchema = {
  type: 'object',
  properties: {
    invoice_number: { type: 'string', pattern: '^INV-[0-9]+$' },
    total_amount: { type: 'number', minimum: 0 },
    currency: { type: 'string', format: 'currency' },
    due_date: { type: 'string', format: 'date' },
    line_items: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          description: { type: 'string' },
          quantity: { type: 'integer', minimum: 1 },
          unit_price: { type: 'number', minimum: 0 }
        },
        required: ['description', 'quantity', 'unit_price']
      }
    }
  },
  required: ['invoice_number', 'total_amount', 'currency'],
  additionalProperties: false
};

const validInvoiceData = {
  invoice_number: 'INV-12345',
  total_amount: 1250.00,
  currency: 'USD',
  due_date: '2024-01-31',
  line_items: [
    {
      description: 'Software License',
      quantity: 1,
      unit_price: 1250.00
    }
  ]
};

const invalidInvoiceData = {
  invoice_number: 'INVALID-FORMAT',
  total_amount: -100, // Invalid: negative amount
  currency: 'INVALID_CURRENCY', // Invalid: not 3-letter format
  extra_field: 'should be removed',
  line_items: [
    {
      description: 'Item 1',
      quantity: 0, // Invalid: below minimum
      unit_price: 100
    }
  ]
};

describe('SchemaValidator', () => {
  let validator: SchemaValidator;

  beforeEach(() => {
    validator = new SchemaValidator();
  });

  describe('Core Validation', () => {
    it('should validate correct data against schema', async () => {
      const _result = await validator.validateData(validInvoiceData, validInvoiceSchema);

      expect(result.valid).toBe(true);
      expect(result.errors).toBeUndefined();
      expect(result.data).toEqual(validInvoiceData);
    });

    it('should reject invalid data with detailed errors', async () => {
      const _result = await validator.validateData(invalidInvoiceData, validInvoiceSchema);

      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(4); // invoice_number, total_amount, currency, line_items[0].quantity
      
      const errors = result.errors!;
      expect(errors.find(e => e.field === 'invoice_number')?.message).toContain('pattern');
      expect(errors.find(e => e.field === 'total_amount')?.message).toContain('at least');
      expect(errors.find(e => e.field === 'currency')?.message).toContain('currency format');
      expect(errors.find(e => e.field === 'line_items.0.quantity')?.message).toContain('at least');
    });

    it('should remove additional properties when configured', async () => {
      const dataWithExtra = {
        ...validInvoiceData,
        extra_field: 'should be removed',
        another_extra: 123
      };

      const _result = await validator.validateData(dataWithExtra, validInvoiceSchema);

      expect(result.valid).toBe(true);
      expect(result.data).not.toHaveProperty('extra_field');
      expect(result.data).not.toHaveProperty('another_extra');
    });

    it('should apply default values from schema', async () => {
      const schemaWithDefaults: JSONSchema = {
        type: 'object',
        properties: {
          status: { type: 'string', default: 'pending' },
          priority: { type: 'integer', default: 1 }
        }
      };

      const _result = await validator.validateData({}, schemaWithDefaults);

      expect(result.valid).toBe(true);
      expect(result.data.status).toBe('pending');
      expect(result.data.priority).toBe(1);
    });

    it('should perform type coercion when enabled', async () => {
      const numericData = {
        invoice_number: 'INV-12345',
        total_amount: '1250.00', // String that should be coerced to number
        currency: 'USD'
      };

      const _result = await validator.validateData(numericData, validInvoiceSchema);

      expect(result.valid).toBe(true);
      expect(typeof result.data.total_amount).toBe('number');
      expect(result.data.total_amount).toBe(1250.00);
    });
  });

  describe('Custom Formats', () => {
    it('should validate custom currency format', async () => {
      const currencySchema: JSONSchema = {
        type: 'object',
        properties: {
          currency: { type: 'string', format: 'currency' }
        }
      };

      // Valid currencies
      const validResult = await validator.validateData({ currency: 'USD' }, currencySchema);
      expect(validResult.valid).toBe(true);

      // Invalid currencies
      const invalidResult = await validator.validateData({ currency: 'US' }, currencySchema);
      expect(invalidResult.valid).toBe(false);
      expect(invalidResult.errors![0].message).toContain('currency format');
    });

    it('should validate phone number format', async () => {
      const phoneSchema: JSONSchema = {
        type: 'object',
        properties: {
          phone: { type: 'string', format: 'phone' }
        }
      };

      const validPhones = ['+1234567890', '(*************', '+44 20 7946 0958'];
      const invalidPhones = ['123', 'not-a-phone', ''];

      for (const phone of validPhones) {
        const _result = await validator.validateData({ phone }, phoneSchema);
        expect(result.valid).toBe(true);
      }

      for (const phone of invalidPhones) {
        const _result = await validator.validateData({ phone }, phoneSchema);
        expect(result.valid).toBe(false);
      }
    });

    it('should validate confidence score format', async () => {
      const confidenceSchema: JSONSchema = {
        type: 'object',
        properties: {
          confidence: { type: 'number', format: 'confidence' }
        }
      };

      // Valid confidence scores
      const validScores = [0.0, 0.5, 1.0, 0.95];
      for (const confidence of validScores) {
        const _result = await validator.validateData({ confidence }, confidenceSchema);
        expect(result.valid).toBe(true);
      }

      // Invalid confidence scores
      const invalidScores = [-0.1, 1.1, 2.0];
      for (const confidence of invalidScores) {
        const _result = await validator.validateData({ confidence }, confidenceSchema);
        expect(result.valid).toBe(false);
      }
    });
  });

  describe('Error Formatting', () => {
    it('should provide human-readable error messages', async () => {
      const schema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          age: { type: 'integer', minimum: 0, maximum: 150 },
          email: { type: 'string', format: 'email' },
          status: { type: 'string', enum: ['active', 'inactive', 'pending'] }
        },
        required: ['name', 'email']
      };

      const invalidData = {
        age: -5,
        email: 'not-an-email',
        status: 'unknown'
      };

      const _result = await validator.validateData(invalidData, schema);

      expect(result.valid).toBe(false);
      const errors = result.errors!;

      // Check specific error messages
      expect(errors.find(e => e.field === 'name')?.message).toBe("Required field 'name' is missing");
      expect(errors.find(e => e.field === 'age')?.message).toBe("Field 'age' must be at least 0");
      expect(errors.find(e => e.field === 'email')?.message).toBe("Field 'email' must be in email format");
      expect(errors.find(e => e.field === 'status')?.message).toBe("Field 'status' must be one of: active, inactive, pending");
    });

    it('should include severity levels for errors', async () => {
      const _result = await validator.validateData(invalidInvoiceData, validInvoiceSchema);

      expect(result.valid).toBe(false);
      const errors = result.errors!;

      // All validation failures should be errors (not warnings)
      expect(errors.every(e => e.severity === 'error')).toBe(true);
    });
  });

  describe('Agent Output Validation', () => {
    it('should validate agent output with cached schema', async () => {
      // Mock database call for agent schema
      const mockAgent = {
        id: 'agent-123',
        output_schema: validInvoiceSchema
      };

      // This would typically come from database
      validator.setAgentSchema(mockAgent.id, mockAgent.output_schema);

      const _result = await validator.validateAgentOutput(mockAgent.id, validInvoiceData);

      expect(result.valid).toBe(true);
      expect(result.data).toEqual(validInvoiceData);
    });

    it('should handle missing agent schema gracefully', async () => {
      await expect(validator.validateAgentOutput('nonexistent-agent', validInvoiceData))
        .rejects.toThrow('Agent nonexistent-agent not found');
    });
  });
});

describe('SchemaCompatibilityChecker', () => {
  let checker: SchemaCompatibilityChecker;

  beforeEach(() => {
    checker = new SchemaCompatibilityChecker();
  });

  describe('Breaking Changes Detection', () => {
    it('should detect breaking change when new required field added', () => {
      const oldSchema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' }
        },
        required: ['name']
      };

      const newSchema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          email: { type: 'string' }
        },
        required: ['name', 'email'] // email is now required
      };

      const _result = checker.checkCompatibility(oldSchema, newSchema);

      expect(result.compatible).toBe(false);
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('breaking');
      expect(result.issues[0].field).toBe('email');
      expect(result.issues[0].message).toContain('New required field');
    });

    it('should detect breaking change when field type changes incompatibly', () => {
      const oldSchema: JSONSchema = {
        type: 'object',
        properties: {
          count: { type: 'string' }
        }
      };

      const newSchema: JSONSchema = {
        type: 'object',
        properties: {
          count: { type: 'number' }
        }
      };

      const _result = checker.checkCompatibility(oldSchema, newSchema);

      expect(result.compatible).toBe(false);
      expect(result.issues.some(i => i.type === 'breaking')).toBe(true);
    });

    it('should detect breaking change when enum values are removed', () => {
      const oldSchema: JSONSchema = {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['active', 'inactive', 'pending'] }
        }
      };

      const newSchema: JSONSchema = {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['active', 'inactive'] } // 'pending' removed
        }
      };

      const _result = checker.checkCompatibility(oldSchema, newSchema);

      expect(result.compatible).toBe(false);
      expect(result.issues.some(i => i.field === 'status' && i.type === 'breaking')).toBe(true);
    });
  });

  describe('Non-Breaking Changes Detection', () => {
    it('should allow optional field additions', () => {
      const oldSchema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' }
        },
        required: ['name']
      };

      const newSchema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          description: { type: 'string' } // Optional field added
        },
        required: ['name']
      };

      const _result = checker.checkCompatibility(oldSchema, newSchema);

      expect(result.compatible).toBe(true);
      expect(result.issues).toHaveLength(1);
      expect(result.issues[0].type).toBe('info');
      expect(result.issues[0].field).toBe('description');
    });

    it('should allow compatible type changes', () => {
      const oldSchema: JSONSchema = {
        type: 'object',
        properties: {
          count: { type: 'integer' }
        }
      };

      const newSchema: JSONSchema = {
        type: 'object',
        properties: {
          count: { type: 'number' } // integer -> number is compatible
        }
      };

      const _result = checker.checkCompatibility(oldSchema, newSchema);

      expect(result.compatible).toBe(true);
      const typeWarning = result.issues.find(i => i.field === 'count');
      expect(typeWarning?.type).toBe('warning');
    });

    it('should allow making required fields optional', () => {
      const oldSchema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          email: { type: 'string' }
        },
        required: ['name', 'email']
      };

      const newSchema: JSONSchema = {
        type: 'object',
        properties: {
          name: { type: 'string' },
          email: { type: 'string' }
        },
        required: ['name'] // email is now optional
      };

      const _result = checker.checkCompatibility(oldSchema, newSchema);

      expect(result.compatible).toBe(true);
      const optionalWarning = result.issues.find(i => i.field === 'email');
      expect(optionalWarning?.type).toBe('warning');
    });
  });
});

describe('SchemaGenerator', () => {
  let generator: SchemaGenerator;

  beforeEach(() => {
    generator = new SchemaGenerator();
  });

  describe('Field Definition to Schema Conversion', () => {
    it('should generate schema from simple field definitions', () => {
      const fields: FieldDefinition[] = [
        {
          name: 'title',
          type: 'string',
          required: true,
          description: 'Document title',
          maxLength: 100
        },
        {
          name: 'page_count',
          type: 'integer',
          required: false,
          description: 'Number of pages',
          minimum: 1
        }
      ];

      const schema = generator.generateFromFields(fields);

      expect(schema.type).toBe('object');
      expect(schema.required).toEqual(['title']);
      expect(schema.properties.title).toEqual({
        type: 'string',
        description: 'Document title',
        maxLength: 100
      });
      expect(schema.properties.page_count).toEqual({
        type: 'integer',
        description: 'Number of pages',
        minimum: 1
      });
    });

    it('should handle complex field types with arrays and objects', () => {
      const fields: FieldDefinition[] = [
        {
          name: 'line_items',
          type: 'array',
          required: true,
          description: 'Invoice line items',
          items: {
            name: 'item',
            type: 'object',
            properties: {
              description: { name: 'description', type: 'string', required: true },
              amount: { name: 'amount', type: 'number', required: true, minimum: 0 }
            }
          },
          minItems: 1
        }
      ];

      const schema = generator.generateFromFields(fields);

      expect(schema.properties.line_items.type).toBe('array');
      expect(schema.properties.line_items.minItems).toBe(1);
      expect(schema.properties.line_items.items.type).toBe('object');
      expect(schema.properties.line_items.items.properties.description.type).toBe('string');
      expect(schema.properties.line_items.items.properties.amount.minimum).toBe(0);
    });

    it('should handle enum fields', () => {
      const fields: FieldDefinition[] = [
        {
          name: 'status',
          type: 'string',
          required: true,
          description: 'Document status',
          enum: ['pending', 'processed', 'failed']
        }
      ];

      const schema = generator.generateFromFields(fields);

      expect(schema.properties.status.enum).toEqual(['pending', 'processed', 'failed']);
    });

    it('should set default values when provided', () => {
      const fields: FieldDefinition[] = [
        {
          name: 'priority',
          type: 'integer',
          required: false,
          description: 'Processing priority',
          default: 1
        }
      ];

      const schema = generator.generateFromFields(fields);

      expect(schema.properties.priority.default).toBe(1);
    });
  });

  describe('Schema Metadata', () => {
    it('should generate valid JSON Schema with correct metadata', () => {
      const fields: FieldDefinition[] = [
        { name: 'test', type: 'string', required: true }
      ];

      const schema = generator.generateFromFields(fields);

      expect(schema.$schema).toBe('http://json-schema.org/draft-07/schema#');
      expect(schema.additionalProperties).toBe(false);
      expect(schema.type).toBe('object');
    });
  });
});

describe('SchemaCache', () => {
  let cache: SchemaCache;

  beforeEach(() => {
    cache = new SchemaCache();
  });

  describe('Caching Behavior', () => {
    it('should cache compiled schemas', async () => {
      // Mock agent data
      const agentId = 'test-agent-123';
      const mockSchema = validInvoiceSchema;

      // Mock database response
      cache.setMockAgent(agentId, { output_schema: mockSchema, updated_at: new Date().toISOString() });

      // First call should compile and cache
      const schema1 = await cache.getSchema(agentId);
      const metrics1 = cache.getCacheMetrics();

      expect(metrics1.compilations).toBe(1);
      expect(metrics1.misses).toBe(1);
      expect(metrics1.hits).toBe(0);

      // Second call should use cache
      const schema2 = await cache.getSchema(agentId);
      const metrics2 = cache.getCacheMetrics();

      expect(schema1).toBe(schema2); // Same reference
      expect(metrics2.compilations).toBe(1); // No new compilation
      expect(metrics2.hits).toBe(1);
      expect(metrics2.hit_rate).toBe(0.5); // 1 hit out of 2 total requests
    });

    it('should handle cache cleanup when size limit exceeded', async () => {
      // Fill cache with many entries
      for (let i = 0; i < 1005; i++) {
        const agentId = `agent-${i}`;
        cache.setMockAgent(agentId, { output_schema: validInvoiceSchema, updated_at: new Date().toISOString() });
        await cache.getSchema(agentId);
      }

      const metrics = cache.getCacheMetrics();
      expect(metrics.cache_size).toBeLessThanOrEqual(1000);
    });
  });

  describe('Error Handling', () => {
    it('should throw error for non-existent agent', async () => {
      await expect(cache.getSchema('nonexistent-agent'))
        .rejects.toThrow('Agent nonexistent-agent not found');
    });
  });
});

describe('Integration Tests', () => {
  describe('Document Processing Integration', () => {
    it('should validate and format agent output correctly', async () => {
      const validator = new SchemaValidator();
      const agentId = 'invoice-agent';
      
      // Set up agent schema
      validator.setAgentSchema(agentId, validInvoiceSchema);

      // Test valid output
      const validResult = await validator.validateAndFormatOutput(agentId, validInvoiceData);
      
      expect(validResult.success).toBe(true);
      expect(validResult.validation_passed).toBe(true);
      expect(validResult.data).toEqual(validInvoiceData);

      // Test invalid output
      const invalidResult = await validator.validateAndFormatOutput(agentId, invalidInvoiceData);
      
      expect(invalidResult.success).toBe(true); // Still succeeds but with warnings
      expect(invalidResult.validation_passed).toBe(false);
      expect(invalidResult.validation_errors).toBeDefined();
      expect(invalidResult.warning).toContain('does not match expected schema');
    });

    it('should handle schema validation system errors gracefully', async () => {
      const validator = new SchemaValidator();
      
      // Invalid schema that will cause compilation error
      const invalidSchema = { type: 'invalid-type' } as JSONSchema;
      validator.setAgentSchema('broken-agent', invalidSchema);

      const _result = await validator.validateAndFormatOutput('broken-agent', validInvoiceData);
      
      expect(result.success).toBe(true);
      expect(result.validation_passed).toBe(false);
      expect(result.validation_errors![0].field).toBe('system');
      expect(result.validation_errors![0].message).toContain('Schema validation system error');
    });
  });

  describe('Performance Tests', () => {
    it('should demonstrate effective caching behavior', async () => {
      const cache = new SchemaCache();
      const agentId = 'performance-test-agent';

      // Set up schema
      cache.setMockAgent(agentId, { output_schema: validInvoiceSchema, updated_at: new Date().toISOString() });

      // First call should be a cache miss
      const schema1 = await cache.getSchema(agentId);
      expect(schema1).toBeDefined();

      let metrics = cache.getCacheMetrics();
      expect(metrics.misses).toBe(1);
      expect(metrics.hits).toBe(0);
      expect(metrics.compilations).toBe(1);

      // Second call should be a cache hit
      const schema2 = await cache.getSchema(agentId);
      expect(schema2).toBeDefined();

      metrics = cache.getCacheMetrics();
      expect(metrics.misses).toBe(1);
      expect(metrics.hits).toBe(1);
      expect(metrics.hit_rate).toBe(0.5); // 1 hit out of 2 total requests
      expect(metrics.compilations).toBe(1); // Still only 1 compilation

      // Multiple subsequent calls should all be cache hits
      for (let i = 0; i < 10; i++) {
        await cache.getSchema(agentId);
      }

      metrics = cache.getCacheMetrics();
      expect(metrics.hits).toBe(11); // 1 + 10 additional hits
      expect(metrics.misses).toBe(1); // Still only 1 miss
      expect(metrics.compilations).toBe(1); // Still only 1 compilation
      expect(metrics.hit_rate).toBeGreaterThan(0.9); // 11/12 = 0.916...
    });
  });
});