import { describe, it, expect, _beforeEach, _mock } from 'bun:test';

// Agent Listing API Unit Tests
// Tests for GitHub Issue #14: Agent Storage & Retrieval
// Following TDD approach with comprehensive test coverage

interface Agent {
  id: string;
  agent_id: string;
  name: string;
  description: string;
  category: 'invoice' | 'contract' | 'receipt' | 'general' | 'legal';
  version: number;
  is_default: boolean;
  customer_id: string | null;
  parent_agent_id: string | null;
  use_cases?: string[];
  supported_formats?: string[];
  accuracy_rating?: number;
  avg_processing_time_ms?: number;
  created_at: string;
  updated_at: string;
}

interface AgentListResponse {
  agents: Agent[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
    page_size: number;
  };
  filters_applied: {
    category?: string | null;
    is_default?: string | null;
    search?: string | null;
  };
}

interface AgentListParams {
  category?: string;
  is_default?: string;
  search?: string;
  page?: number;
  limit?: number;
}

interface CustomerContext {
  customerId: string;
  keyType: 'test' | 'production';
  credits: number;
}

// Mock Supabase client setup
const createMockSupabaseClient = () => {
  const mockRange = mock(() => ({
    data: [] as Agent[],
    error: null,
    count: 0
  }));

  const mockQuery = {
    eq: mock(() => mockQuery),
    or: mock(() => mockQuery),
    range: mockRange
  };

  const mockSelect = mock(() => mockQuery);
  
  const mockFrom = mock(() => ({
    select: mockSelect
  }));

  return {
    from: mockFrom
  };
};

// Mock validateApiKey function
const mockValidateApiKey = mock(async (apiKey: string): Promise<CustomerContext> => {
  if (!apiKey || apiKey === 'invalid-key') {
    throw new Error('Invalid API key');
  }
  
  return {
    customerId: 'customer-123',
    keyType: 'test',
    credits: 100
  };
});

// Agent listing function to test
async function listAgents(
  params: AgentListParams,
  customerContext: CustomerContext,
  supabase: any
): Promise<AgentListResponse> {
  // Apply defaults
  const page = params.page || 1;
  const limit = Math.min(params.limit || 10, 50);
  
  // Build query with access controls
  let query = supabase
    .from('agents')
    .select(`
      id, agent_id, name, description, category, version, is_default,
      customer_id, parent_agent_id, created_at, updated_at
    `)
    .or(`is_default.eq.true,customer_id.eq.${customerContext.customerId}`);

  // Apply filters
  if (params.category) {
    query = query.eq('category', params.category);
  }

  if (params.is_default !== undefined) {
    query = query.eq('is_default', params.is_default === 'true');
  }

  if (params.search) {
    query = query.or(
      `name.ilike.%${params.search}%,description.ilike.%${params.search}%`
    );
  }

  // Apply pagination
  const offset = (page - 1) * limit;
  const _result = query.range(offset, offset + limit - 1);

  if (result.error) {
    throw new Error(result.error.message);
  }

  const totalPages = Math.ceil((result.count || 0) / limit);

  return {
    agents: result.data || [],
    pagination: {
      current_page: page,
      total_pages: totalPages,
      total_count: result.count || 0,
      page_size: limit
    },
    filters_applied: {
      category: params.category || null,
      is_default: params.is_default || null,
      search: params.search || null
    }
  };
}

describe('Agent Listing API', () => {
  let mockSupabase: ReturnType<typeof createMockSupabaseClient>;
  let customerContext: CustomerContext;

  beforeEach(() => {
    mockSupabase = createMockSupabaseClient();
    customerContext = {
      customerId: 'customer-123',
      keyType: 'test',
      credits: 100
    };
  });

  describe('Basic Agent Listing', () => {
    it('should return empty list when no agents exist', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.agents).toEqual([]);
      expect(result.pagination.total_count).toBe(0);
      expect(result.pagination.current_page).toBe(1);
      expect(result.pagination.page_size).toBe(10);
    });

    it('should apply default pagination parameters', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.pagination.current_page).toBe(1);
      expect(result.pagination.page_size).toBe(10);
    });

    it('should limit page size to maximum of 50', async () => {
      // Arrange
      const params: AgentListParams = { limit: 100 };
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.pagination.page_size).toBe(50);
    });

    it('should include proper access control filter', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockSupabase.from).toHaveBeenCalledWith('agents');
      // Note: In the real implementation, access control is built into the query
    });
  });

  describe('Category Filtering', () => {
    it('should filter by invoice category', async () => {
      // Arrange
      const params: AgentListParams = { category: 'invoice' };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.eq).toHaveBeenCalledWith('category', 'invoice');
      expect(result.filters_applied.category).toBe('invoice');
    });

    it('should filter by contract category', async () => {
      // Arrange
      const params: AgentListParams = { category: 'contract' };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.eq).toHaveBeenCalledWith('category', 'contract');
      expect(result.filters_applied.category).toBe('contract');
    });

    it('should not apply category filter when not specified', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.filters_applied.category).toBe(null);
    });
  });

  describe('Default Agent Filtering', () => {
    it('should filter to show only default agents when is_default=true', async () => {
      // Arrange
      const params: AgentListParams = { is_default: 'true' };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.eq).toHaveBeenCalledWith('is_default', true);
      expect(result.filters_applied.is_default).toBe('true');
    });

    it('should filter to show only custom agents when is_default=false', async () => {
      // Arrange
      const params: AgentListParams = { is_default: 'false' };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.eq).toHaveBeenCalledWith('is_default', false);
      expect(result.filters_applied.is_default).toBe('false');
    });

    it('should not apply default filter when not specified', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.filters_applied.is_default).toBe(null);
    });
  });

  describe('Search Functionality', () => {
    it('should search across name and description fields', async () => {
      // Arrange
      const params: AgentListParams = { search: 'invoice' };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.or).toHaveBeenCalledWith(
        'name.ilike.%invoice%,description.ilike.%invoice%'
      );
      expect(result.filters_applied.search).toBe('invoice');
    });

    it('should handle search with special characters', async () => {
      // Arrange
      const params: AgentListParams = { search: 'contract & legal' };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.or).toHaveBeenCalledWith(
        'name.ilike.%contract & legal%,description.ilike.%contract & legal%'
      );
    });

    it('should not apply search filter when not specified', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.filters_applied.search).toBe(null);
    });
  });

  describe('Pagination', () => {
    it('should calculate correct offset for page 2', async () => {
      // Arrange
      const params: AgentListParams = { page: 2, limit: 10 };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.range).toHaveBeenCalledWith(10, 19); // offset 10, end 19
    });

    it('should calculate correct offset for page 3 with limit 5', async () => {
      // Arrange
      const params: AgentListParams = { page: 3, limit: 5 };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockOrReturn.range).toHaveBeenCalledWith(10, 14); // offset 10, end 14
    });

    it('should calculate total pages correctly', async () => {
      // Arrange
      const params: AgentListParams = { limit: 10 };
      // Mock 25 total records
      const mockSupabaseWithCount = {
        from: mock(() => ({
          select: mock(() => ({
            or: mock(() => ({
              range: mock(() => ({
                data: [],
                error: null,
                count: 25
              }))
            }))
          }))
        }))
      };
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabaseWithCount);
      
      // Assert
      expect(result.pagination.total_pages).toBe(3); // Math.ceil(25/10)
      expect(result.pagination.total_count).toBe(25);
    });
  });

  describe('Combined Filters', () => {
    it('should apply multiple filters simultaneously', async () => {
      // Arrange
      const params: AgentListParams = {
        category: 'invoice',
        is_default: 'true',
        search: 'advanced',
        page: 2,
        limit: 5
      };
      const mockFromReturn = mockSupabase.from();
      const mockSelectReturn = mockFromReturn.select();
      const mockOrReturn = mockSelectReturn.or();
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result.filters_applied).toEqual({
        category: 'invoice',
        is_default: 'true',
        search: 'advanced'
      });
      expect(result.pagination.current_page).toBe(2);
      expect(result.pagination.page_size).toBe(5);
    });
  });

  describe('Error Handling', () => {
    it('should throw error when database query fails', async () => {
      // Arrange
      const params: AgentListParams = {};
      const mockSupabaseWithError = {
        from: mock(() => ({
          select: mock(() => ({
            or: mock(() => ({
              range: mock(() => ({
                data: null,
                error: { message: 'Database connection failed' },
                count: 0
              }))
            }))
          }))
        }))
      };
      
      // Act & Assert
      await expect(
        listAgents(params, customerContext, mockSupabaseWithError)
      ).rejects.toThrow();
    });

    it('should handle null data gracefully', async () => {
      // Arrange
      const params: AgentListParams = {};
      const mockSupabaseWithNull = {
        from: mock(() => ({
          select: mock(() => ({
            or: mock(() => ({
              range: mock(() => ({
                data: null,
                error: null,
                count: 0
              }))
            }))
          }))
        }))
      };
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabaseWithNull);
      
      // Assert
      expect(result.agents).toEqual([]);
      expect(result.pagination.total_count).toBe(0);
    });
  });

  describe('Response Structure', () => {
    it('should include all required fields in response', async () => {
      // Arrange
      const params: AgentListParams = {};
      
      // Act
      const _result = await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(result).toHaveProperty('agents');
      expect(result).toHaveProperty('pagination');
      expect(result).toHaveProperty('filters_applied');
      
      expect(result.pagination).toHaveProperty('current_page');
      expect(result.pagination).toHaveProperty('total_pages');
      expect(result.pagination).toHaveProperty('total_count');
      expect(result.pagination).toHaveProperty('page_size');
      
      expect(result.filters_applied).toHaveProperty('category');
      expect(result.filters_applied).toHaveProperty('is_default');
      expect(result.filters_applied).toHaveProperty('search');
    });

    it('should select correct database fields', async () => {
      // Arrange
      const params: AgentListParams = {};
      const mockFromReturn = mockSupabase.from();
      
      // Act
      await listAgents(params, customerContext, mockSupabase);
      
      // Assert
      expect(mockFromReturn.select).toHaveBeenCalledWith(`
      id, agent_id, name, description, category, version, is_default,
      customer_id, parent_agent_id, created_at, updated_at
    `);
    });
  });
});

describe('API Key Validation for Agent Listing', () => {
  it('should validate API key before processing request', async () => {
    // Arrange
    const validApiKey = 'skt_test_valid_key_123';
    
    // Act
    const _result = await mockValidateApiKey(validApiKey);
    
    // Assert
    expect(result.customerId).toBe('customer-123');
    expect(result.keyType).toBe('test');
    expect(result.credits).toBe(100);
  });

  it('should throw error for invalid API key', async () => {
    // Arrange
    const invalidApiKey = 'invalid-key';
    
    // Act & Assert
    await expect(mockValidateApiKey(invalidApiKey)).rejects.toThrow();
  });

  it('should throw error for missing API key', async () => {
    // Arrange
    const missingApiKey = '';
    
    // Act & Assert
    await expect(mockValidateApiKey(missingApiKey)).rejects.toThrow();
  });
});