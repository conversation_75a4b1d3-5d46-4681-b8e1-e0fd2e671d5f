import { describe, it, expect, beforeAll, beforeEach, afterEach } from 'bun:test';
import { createClient } from '@supabase/supabase-js';

// Test Row Level Security and Customer Isolation - FIXED
describe('RLS Security and Customer Isolation - Issue #2', () => {
  let supabase: any;
  let adminSupabase: any;
  
  beforeAll(() => {
    const supabaseUrl = 'http://127.0.0.1:14321';
    const anonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';
    const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';
    
    supabase = createClient(supabaseUrl, anonKey);
    adminSupabase = createClient(supabaseUrl, serviceRoleKey);
  });

  describe('Customer Isolation Tests', () => {
    let customer1Id: string;
    let customer2Id: string;
    let apiKey1Id: string;
    let apiKey2Id: string;

    beforeEach(async () => {
      // Use unique UUIDs for each test run to avoid conflicts
      const timestamp = Date.now();
      customer1Id = `123e4567-e89b-12d3-a456-${timestamp.toString().padStart(12, '0').slice(-12)}`;
      customer2Id = `123e4567-e89b-12d3-a457-${timestamp.toString().padStart(12, '0').slice(-12)}`;
      apiKey1Id = `123e4567-e89b-12d3-a458-${timestamp.toString().padStart(12, '0').slice(-12)}`;
      apiKey2Id = `123e4567-e89b-12d3-a459-${timestamp.toString().padStart(12, '0').slice(-12)}`;

      // Clean up any existing test data first
      await adminSupabase.from('usage_logs').delete().ilike('id', '123e4567-e89b-12d3-%');
      await adminSupabase.from('documents').delete().ilike('id', '123e4567-e89b-12d3-%');
      await adminSupabase.from('api_keys').delete().ilike('id', '123e4567-e89b-12d3-%');
      await adminSupabase.from('customers').delete().ilike('id', '123e4567-e89b-12d3-%');

      // Create test customers with admin client (bypasses RLS)
      const { data: customers, error: custError } = await adminSupabase.from('customers').upsert([
        {
          id: customer1Id,
          company_name: 'Test Company 1',
          tier: 'standard',
          email: `test1-${timestamp}@example.com`
        },
        {
          id: customer2Id,
          company_name: 'Test Company 2',
          tier: 'premium',
          email: `test2-${timestamp}@example.com`
        }
      ]).select();

      expect(custError).toBeNull();
      expect(customers).toHaveLength(2);

      // Generate proper SHA-256 hashes for test API keys
      const testKey1 = `skt_test_${timestamp}_12345678901234567890`;
      const testKey2 = `skp_prod_${timestamp}_12345678901234567890`;
      
      // Hash the keys using the database function
      const { data: hash1 } = await adminSupabase.rpc('hash_api_key', { raw_key: testKey1 });
      const { data: hash2 } = await adminSupabase.rpc('hash_api_key', { raw_key: testKey2 });

      // Create test API keys with proper 64-character hashes
      const { data: keys, error: keyError } = await adminSupabase.from('api_keys').upsert([
        {
          id: apiKey1Id,
          customer_id: customer1Id,
          key_type: 'test',
          key_hash: hash1,
          key_prefix: 'skt_',
          credits: 100
        },
        {
          id: apiKey2Id,
          customer_id: customer2Id,
          key_type: 'production',
          key_hash: hash2,
          key_prefix: 'skp_',
          credits: 200
        }
      ]).select();

      expect(keyError).toBeNull();
      expect(keys).toHaveLength(2);
    });

    afterEach(async () => {
      // Clean up test data after each test
      await adminSupabase.from('usage_logs').delete().eq('customer_id', customer1Id);
      await adminSupabase.from('usage_logs').delete().eq('customer_id', customer2Id);
      await adminSupabase.from('documents').delete().eq('customer_id', customer1Id);
      await adminSupabase.from('documents').delete().eq('customer_id', customer2Id);
      await adminSupabase.from('api_keys').delete().eq('id', apiKey1Id);
      await adminSupabase.from('api_keys').delete().eq('id', apiKey2Id);
      await adminSupabase.from('customers').delete().eq('id', customer1Id);
      await adminSupabase.from('customers').delete().eq('id', customer2Id);
    });

    it('should isolate API keys by customer', async () => {
      // Admin should see our test data (bypasses RLS)
      const { data: testKeys, error: adminError } = await adminSupabase
        .from('api_keys')
        .select('*')
        .in('id', [apiKey1Id, apiKey2Id]);

      expect(adminError).toBeNull();
      expect(testKeys).toHaveLength(2); // Should see both test keys

      // Anon user should see no data without customer context
      const { data: anonKeys, error: anonError } = await supabase
        .from('api_keys')
        .select('*')
        .in('id', [apiKey1Id, apiKey2Id]);

      expect(anonError).toBeNull();
      expect(anonKeys || []).toHaveLength(0); // RLS blocks access for anon without context
    });

    it('should prevent cross-customer data access', async () => {
      // Test that anon user cannot access specific API key without context
      const { data, error } = await supabase
        .from('api_keys')
        .select('*')
        .eq('id', apiKey2Id);

      expect(error).toBeNull();
      expect(data || []).toHaveLength(0); // RLS blocks access

      // Test that admin can access (for comparison)
      const { data: adminData, error: adminError } = await adminSupabase
        .from('api_keys')
        .select('*')
        .eq('id', apiKey2Id);

      expect(adminError).toBeNull();
      expect(adminData).toHaveLength(1); // Admin bypasses RLS
    });

    it('should allow customers to see their own documents only', async () => {
      // Create test documents with unique UUID IDs
      const doc1Id = `123e4567-e89b-12d3-a460-${Date.now().toString().padStart(12, '0').slice(-12)}`;
      const doc2Id = `123e4567-e89b-12d3-a461-${Date.now().toString().padStart(12, '0').slice(-12)}`;

      const { data: docs, error: docError } = await adminSupabase.from('documents').upsert([
        {
          id: doc1Id,
          customer_id: customer1Id,
          api_key_id: apiKey1Id,
          filename: 'customer1-doc.pdf',
          original_filename: 'customer1-doc.pdf',
          file_size: 1024,
          file_type: 'application/pdf',
          file_hash: `hash1_${Date.now()}`,
          storage_path: '/path/to/doc1',
          status: 'completed'
        },
        {
          id: doc2Id,
          customer_id: customer2Id,
          api_key_id: apiKey2Id,
          filename: 'customer2-doc.pdf',
          original_filename: 'customer2-doc.pdf',
          file_size: 2048,
          file_type: 'application/pdf',
          file_hash: `hash2_${Date.now()}`,
          storage_path: '/path/to/doc2',
          status: 'completed'
        }
      ]).select();

      expect(docError).toBeNull();
      expect(docs).toHaveLength(2);

      // Admin should see our test documents (bypasses RLS)
      const { data: adminData, error: adminError } = await adminSupabase
        .from('documents')
        .select('*')
        .in('id', [doc1Id, doc2Id]);

      expect(adminError).toBeNull();
      expect(adminData).toHaveLength(2); // Admin sees both test docs

      // Anon user should see no documents without context (RLS blocks)
      const { data: anonData, error: anonError } = await supabase
        .from('documents')
        .select('*')
        .in('id', [doc1Id, doc2Id]);

      expect(anonError).toBeNull();
      expect(anonData || []).toHaveLength(0);
    });

    it('should isolate usage logs by customer', async () => {
      // Create test usage logs with unique UUID IDs
      const usage1Id = `123e4567-e89b-12d3-a462-${Date.now().toString().padStart(12, '0').slice(-12)}`;
      const usage2Id = `123e4567-e89b-12d3-a463-${Date.now().toString().padStart(12, '0').slice(-12)}`;

      const { data: logs, error: logError } = await adminSupabase.from('usage_logs').upsert([
        {
          id: usage1Id,
          customer_id: customer1Id,
          api_key_id: apiKey1Id,
          operation_type: 'document_processing',
          model_cost: 0.001,
          customer_price: 0.002
        },
        {
          id: usage2Id,
          customer_id: customer2Id,
          api_key_id: apiKey2Id,
          operation_type: 'document_processing',
          model_cost: 0.005,
          customer_price: 0.008
        }
      ]).select();

      expect(logError).toBeNull();
      expect(logs).toHaveLength(2);

      // Admin should see our test usage logs (bypasses RLS)
      const { data: adminData, error: adminError } = await adminSupabase
        .from('usage_logs')
        .select('*')
        .in('id', [usage1Id, usage2Id]);

      expect(adminError).toBeNull();
      expect(adminData).toHaveLength(2); // Admin sees both test logs

      // Anon user should see no usage logs without context (RLS blocks)
      const { data: anonData, error: anonError } = await supabase
        .from('usage_logs')
        .select('*')
        .in('id', [usage1Id, usage2Id]);

      expect(anonError).toBeNull();
      expect(anonData || []).toHaveLength(0);
    });

    it('should prevent unauthorized customer updates WITHOUT customer context', async () => {
      // First verify: anon without context should not see any customers
      const { data: anonCustomers, error: anonError } = await supabase
        .from('customers')
        .select('*')
        .in('id', [customer1Id, customer2Id]);

      expect(anonError).toBeNull();
      expect(anonCustomers || []).toHaveLength(0); // Should see nothing due to RLS

      // Set customer 1 context
      await supabase.rpc('set_current_customer', { customer_id: customer1Id });
      
      // Now customer 1 should see their own data
      const { data: customer1Data, error: c1Error } = await supabase
        .from('customers')
        .select('*')
        .eq('id', customer1Id);

      expect(c1Error).toBeNull();
      expect(customer1Data).toHaveLength(1);

      // But should NOT be able to update customer 2's data
      const { data: updateData, error: crossCustomerError } = await supabase
        .from('customers')
        .update({ company_name: 'Hacked from Customer 1' })
        .eq('id', customer2Id)
        .select();

      // This should return no updated rows (due to RLS blocking the update)
      expect(crossCustomerError).toBeNull(); // No error, but no updates
      expect(updateData || []).toHaveLength(0); // No rows updated due to RLS
    });

    it('should allow customer to access their own data with context', async () => {
      // Set customer context
      const { error: contextError } = await supabase.rpc('set_current_customer', { customer_id: customer1Id });
      expect(contextError).toBeNull();

      // Now customer 1 should see their own API keys
      const { data: customerKeys, error: keyError } = await supabase
        .from('api_keys')
        .select('*')
        .eq('customer_id', customer1Id);

      expect(keyError).toBeNull();
      expect(customerKeys).toHaveLength(1);
      expect(customerKeys[0].id).toBe(apiKey1Id);

      // But should NOT see customer 2's keys
      const { data: otherKeys, error: otherError } = await supabase
        .from('api_keys')
        .select('*')
        .eq('customer_id', customer2Id);

      expect(otherError).toBeNull();
      expect(otherKeys || []).toHaveLength(0); // RLS blocks cross-customer access
    });
  });

  describe('RLS Policy Configuration', () => {
    it('should have RLS enabled on all customer tables', async () => {
      const tables = ['customers', 'api_keys', 'agents', 'documents', 'usage_logs'];
      
      for (const table of tables) {
        const { data, error } = await adminSupabase
          .rpc('check_table_rls', { table_name: table });
        
        expect(error).toBeNull();
        expect(data).toBe(true);
      }
    });

    it('should have proper customer isolation policies', async () => {
      // Check that customer isolation policies exist
      const { data, error } = await adminSupabase
        .rpc('list_rls_policies', { schema_name: 'public' });
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      
      const customerPolicies = data.filter((policy: any) => 
        policy.policy_name.includes('customer_isolation') ||
        policy.policy_name.includes('customer_access')
      );
      
      expect(customerPolicies.length).toBeGreaterThan(0);
    });

    it('should allow admin access to all data', async () => {
      // Admin (service role) should bypass RLS and see all data
      const { data: allCustomers, error } = await adminSupabase
        .from('customers')
        .select('*');
      
      expect(error).toBeNull();
      expect(allCustomers.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Security Function Tests', () => {
    it('should have set_current_customer RLS function', async () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      const { data, error } = await supabase
        .rpc('set_current_customer', { customer_id: validUUID });
      
      // Function should exist and execute
      expect(error).toBeNull();
    });

    it('should have get_current_customer RLS function', async () => {
      // Set a customer first
      const validUUID = '123e4567-e89b-12d3-a456-************';
      await supabase.rpc('set_current_customer', { customer_id: validUUID });
      
      const { data, error } = await supabase
        .rpc('get_current_customer');
      
      expect(error).toBeNull();
      expect(data).toBe(validUUID);
    });

    it('should validate API key hashing implementation', async () => {
      const _testKey = 'skt_test_12345678901234567890123456789012';
      
      const { data, error } = await adminSupabase
        .rpc('hash_api_key', { raw_key: testKey });
      
      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(data.length).toBe(64); // SHA-256 hash length
      expect(data).not.toBe(testKey); // Should be hashed, not raw
    });
  });
});