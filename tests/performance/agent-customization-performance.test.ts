import { describe, it, expect, beforeAll } from 'bun:test';

// Performance benchmarking for Agent Customization at scale
describe('Agent Customization Performance', () => {
  const baseUrl = 'http://localhost:54321/functions/v1';
  const _testApiKey = 'skt_test_performance_key';

  beforeAll(async () => {
    // Verify Supabase is running
    const healthCheck = await fetch(`${baseUrl}/health`);
    if (!healthCheck.ok) {
      throw new Error('Supabase Edge Functions not running. Run: supabase functions serve');
    }
  });

  describe('Large-Scale Operations', () => {
    it('should handle bulk agent customizations within performance targets', async () => {
      const startTime = performance.now();
      const customizations = [];
      const concurrency = 10;
      const operationsPerBatch = 5;

      // Create multiple concurrent customization requests
      for (let batch = 0; batch < concurrency; batch++) {
        const batchPromises = [];
        
        for (let i = 0; i < operationsPerBatch; i++) {
          const agentId = `test-agent-${batch}-${i}`;
          const customization = {
            name: `Bulk Customized Agent ${batch}-${i}`,
            description: `Performance test agent ${Date.now()}`,
            system_prompt: `Extract data for performance test ${batch}-${i}. Return structured JSON with vendor, amount, date.`,
            processing_config: {
              confidence_threshold: 0.85,
              retry_attempts: 2,
              timeout_seconds: 20
            }
          };

          const promise = fetch(`${baseUrl}/agents/${agentId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'apikey': testApiKey
            },
            body: JSON.stringify(customization)
          }).then(response => ({
            batch,
            operation: i,
            status: response.status,
            time: performance.now()
          }));

          batchPromises.push(promise);
        }

        customizations.push(...batchPromises);
      }

      // Wait for all operations to complete
      const results = await Promise.all(customizations);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Performance assertions
      expect(totalTime).toBeLessThan(10000); // Should complete within 10 seconds
      expect(results.length).toBe(concurrency * operationsPerBatch);
      
      // Check success rate
      const successfulOperations = results.filter(r => r.status === 200 || r.status === 201);
      const successRate = successfulOperations.length / results.length;
      expect(successRate).toBeGreaterThanOrEqual(0.95); // 95% success rate minimum

      // Calculate average response time
      const avgResponseTime = totalTime / results.length;
      expect(avgResponseTime).toBeLessThan(500); // Average under 500ms per operation

      console.log(`Performance Metrics:
        - Total Operations: ${results.length}
        - Total Time: ${totalTime.toFixed(2)}ms
        - Average Response Time: ${avgResponseTime.toFixed(2)}ms
        - Success Rate: ${(successRate * 100).toFixed(1)}%
        - Throughput: ${(results.length / (totalTime / 1000)).toFixed(2)} ops/sec`);
    });

    it('should handle large schema validations efficiently', async () => {
      const startTime = performance.now();
      
      // Create a complex schema with many fields
      const largeSchema = {
        type: 'object',
        properties: {},
        required: []
      };

      // Generate 100 fields dynamically
      for (let i = 0; i < 100; i++) {
        largeSchema.properties[`field_${i}`] = {
          type: i % 4 === 0 ? 'string' : i % 4 === 1 ? 'number' : i % 4 === 2 ? 'boolean' : 'array',
          description: `Auto-generated field ${i} for performance testing`
        };
        
        if (i % 10 === 0) {
          largeSchema.required.push(`field_${i}`);
        }
      }

      const customization = {
        name: 'Large Schema Performance Test',
        output_schema: largeSchema,
        system_prompt: 'Extract comprehensive data with many fields for performance testing.'
      };

      const response = await fetch(`${baseUrl}/agents/test-large-schema-agent`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'apikey': testApiKey
        },
        body: JSON.stringify(customization)
      });

      const endTime = performance.now();
      const processingTime = endTime - startTime;

      expect(response.status).toBe(200);
      expect(processingTime).toBeLessThan(2000); // Should validate large schema within 2 seconds

      const _result = await response.json();
      expect(result.success).toBe(true);
      expect(result.validation_results.filter(r => r.level === 'error')).toHaveLength(0);

      console.log(`Large Schema Validation:
        - Fields: ${Object.keys(largeSchema.properties).length}
        - Required Fields: ${largeSchema.required.length}
        - Processing Time: ${processingTime.toFixed(2)}ms`);
    });

    it('should handle concurrent preview operations efficiently', async () => {
      const startTime = performance.now();
      const concurrentPreviews = 20;
      const previewPromises = [];

      for (let i = 0; i < concurrentPreviews; i++) {
        const customization = {
          system_prompt: `Concurrent preview test ${i}. Extract invoice data efficiently.`,
          output_schema: {
            type: 'object',
            properties: {
              vendor: { type: 'string' },
              total: { type: 'number' },
              date: { type: 'string' },
              test_id: { type: 'number', default: i }
            },
            required: ['vendor', 'total', 'date']
          },
          preview_mode: true
        };

        const promise = fetch(`${baseUrl}/agents/test-concurrent-preview-${i}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'apikey': testApiKey
          },
          body: JSON.stringify(customization)
        }).then(async response => ({
          id: i,
          status: response.status,
          data: response.ok ? await response.json() : null,
          time: performance.now()
        }));

        previewPromises.push(promise);
      }

      const results = await Promise.all(previewPromises);
      const endTime = performance.now();
      const totalTime = endTime - startTime;

      // Performance assertions
      expect(totalTime).toBeLessThan(15000); // Should complete within 15 seconds
      expect(results.length).toBe(concurrentPreviews);

      // Check that all previews succeeded
      const successfulPreviews = results.filter(r => r.status === 200);
      expect(successfulPreviews.length).toBe(concurrentPreviews);

      // Verify preview results structure
      for (const result of successfulPreviews) {
        if (result.data) {
          expect(result.data.success).toBe(true);
          expect(result.data.preview_results).toBeDefined();
          expect(Array.isArray(result.data.preview_results)).toBe(true);
          expect(result.data.preview_results.length).toBeGreaterThan(0);
        }
      }

      const avgResponseTime = totalTime / results.length;
      expect(avgResponseTime).toBeLessThan(1000); // Average under 1 second per preview

      console.log(`Concurrent Preview Performance:
        - Concurrent Operations: ${concurrentPreviews}
        - Total Time: ${totalTime.toFixed(2)}ms
        - Average Time: ${avgResponseTime.toFixed(2)}ms
        - Success Rate: 100%`);
    });

    it('should handle memory-intensive operations without degradation', async () => {
      const iterations = 50;
      const responseTimes = [];
      
      // Run multiple iterations to test for memory leaks/degradation
      for (let i = 0; i < iterations; i++) {
        const startTime = performance.now();
        
        const largePrompt = `
          You are an expert document processor specializing in complex data extraction.
          
          Instructions for iteration ${i}:
          ${Array(100).fill(`Extract detailed information including field_${i}_data. `).join('')}
          
          Return comprehensive JSON with all extracted data following the strict schema requirements.
          Ensure accuracy and completeness for all ${i + 1} processing requirements.
        `;

        const customization = {
          name: `Memory Test Agent ${i}`,
          system_prompt: largePrompt,
          processing_config: {
            confidence_threshold: 0.8 + (i * 0.001), // Slight variation
            retry_attempts: 3,
            timeout_seconds: 30
          }
        };

        const response = await fetch(`${baseUrl}/agents/test-memory-${i}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'apikey': testApiKey
          },
          body: JSON.stringify(customization)
        });

        const endTime = performance.now();
        const responseTime = endTime - startTime;
        responseTimes.push(responseTime);

        expect(response.status).toBe(200);
        
        // Check for memory degradation (response times shouldn't increase significantly)
        if (i > 10) {
          const recentAvg = responseTimes.slice(-10).reduce((a, b) => a + b, 0) / 10;
          const earlyAvg = responseTimes.slice(0, 10).reduce((a, b) => a + b, 0) / 10;
          const degradationRatio = recentAvg / earlyAvg;
          
          expect(degradationRatio).toBeLessThan(2.0); // Response time shouldn't double
        }
      }

      // Overall performance check
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);
      
      expect(avgResponseTime).toBeLessThan(1000); // Average under 1 second
      expect(maxResponseTime).toBeLessThan(3000); // Max under 3 seconds

      console.log(`Memory Performance Test:
        - Iterations: ${iterations}
        - Average Response Time: ${avgResponseTime.toFixed(2)}ms
        - Max Response Time: ${maxResponseTime.toFixed(2)}ms
        - Min Response Time: ${Math.min(...responseTimes).toFixed(2)}ms`);
    });
  });

  describe('Stress Testing', () => {
    it('should maintain performance under high load', async () => {
      const duration = 30000; // 30 second stress test
      const startTime = performance.now();
      const operations = [];
      let operationCount = 0;

      // Generate continuous load for the duration
      while (performance.now() - startTime < duration) {
        const batchSize = 5;
        const batchPromises = [];

        for (let i = 0; i < batchSize; i++) {
          const operationId = operationCount++;
          const customization = {
            name: `Stress Test Agent ${operationId}`,
            description: `High load test operation ${operationId}`,
            system_prompt: 'Extract data efficiently under high load conditions.',
            processing_config: {
              confidence_threshold: 0.85,
              retry_attempts: 1, // Reduced for stress test
              timeout_seconds: 15
            }
          };

          const promise = fetch(`${baseUrl}/agents/stress-test-${operationId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'apikey': testApiKey
            },
            body: JSON.stringify(customization)
          }).then(response => ({
            id: operationId,
            status: response.status,
            timestamp: performance.now()
          })).catch(error => ({
            id: operationId,
            status: 0,
            error: error.message,
            timestamp: performance.now()
          }));

          batchPromises.push(promise);
        }

        operations.push(...batchPromises);
        
        // Small delay between batches to prevent overwhelming
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const results = await Promise.all(operations);
      const endTime = performance.now();
      const totalDuration = endTime - startTime;

      // Analyze results
      const successfulOps = results.filter(r => r.status === 200 || r.status === 201);
      const failedOps = results.filter(r => r.status === 0 || r.status >= 400);
      const successRate = successfulOps.length / results.length;
      const throughput = results.length / (totalDuration / 1000);

      // Performance assertions
      expect(successRate).toBeGreaterThan(0.80); // 80% minimum success rate under stress
      expect(throughput).toBeGreaterThan(1); // At least 1 operation per second
      expect(results.length).toBeGreaterThan(50); // Should process significant load

      console.log(`Stress Test Results:
        - Duration: ${(totalDuration / 1000).toFixed(1)}s
        - Total Operations: ${results.length}
        - Success Rate: ${(successRate * 100).toFixed(1)}%
        - Failed Operations: ${failedOps.length}
        - Throughput: ${throughput.toFixed(2)} ops/sec`);
    });
  });
});