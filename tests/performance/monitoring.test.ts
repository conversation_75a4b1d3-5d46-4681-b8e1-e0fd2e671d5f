// ================================================================================
// PERFORMANCE TESTS - Monitoring and Alerting System
// ================================================================================

import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { MetricsCollector, PerformanceMonitor, PerformanceAnalytics } from '../../supabase/functions/_shared/monitoring.ts';

// Mock Supabase for testing
const mockSupabase = {
  from: (table: string) => ({
    insert: async (data: any) => ({ error: null }),
    select: (columns: string) => ({
      eq: (column: string, value: any) => ({
        single: async () => ({ data: null, error: null }),
        gte: (column: string, value: any) => ({
          lte: (column: string, value: any) => ({
            order: (column: string, options: any) => ({
              limit: (count: number) => ({ data: [], error: null })
            })
          }),
          gt: (column: string, value: any) => ({ data: [], error: null }),
          in: (column: string, values: any[]) => ({ data: [], error: null })
        }),
        order: (column: string, options: any) => ({
          limit: (count: number) => ({ data: [], error: null })
        })
      }),
      gte: (column: string, value: any) => ({
        lte: (column: string, value: any) => ({ data: [], error: null }),
        gt: (column: string, value: any) => ({ data: [], error: null })
      })
    })
  })
};

// Mock the supabase import
vi.mock('../../supabase/functions/_shared/supabase.ts', () => ({
  supabase: mockSupabase
}));

describe('Performance Monitoring System', () => {
  let metricsCollector: MetricsCollector;
  
  beforeEach(() => {
    metricsCollector = MetricsCollector.getInstance();
  });
  
  afterEach(() => {
    // Clean up any pending metrics
    metricsCollector.flush();
  });
  
  describe('MetricsCollector', () => {
    it('should collect performance metrics', async () => {
      const metrics = {
        endpoint: '/api/v1/extract',
        method: 'POST',
        status_code: 200,
        response_time_ms: 1500,
        db_query_time_ms: 200,
        ai_service_time_ms: 800,
        correlation_id: 'test-correlation-id',
        ip_address: '***********',
        user_agent: 'test-agent',
        timestamp: new Date().toISOString(),
      };
      
      await expect(metricsCollector.recordMetrics(metrics)).resolves.not.toThrow();
    });
    
    it('should batch metrics for efficient database writes', async () => {
      const metricsCount = 5;
      const baseMetrics = {
        endpoint: '/api/v1/test',
        method: 'GET',
        status_code: 200,
        response_time_ms: 100,
        timestamp: new Date().toISOString(),
      };
      
      // Record multiple metrics
      for (let i = 0; i < metricsCount; i++) {
        await metricsCollector.recordMetrics({
          ...baseMetrics,
          correlation_id: `test-${i}`,
        });
      }
      
      // Check that metrics are buffered
      expect(metricsCollector.getBufferSize()).toBe(metricsCount);
      
      // Flush should clear the buffer
      await metricsCollector.flush();
      expect(metricsCollector.getBufferSize()).toBe(0);
    });
    
    it('should handle metrics recording errors gracefully', async () => {
      const invalidMetrics = {
        endpoint: null as any,
        method: undefined as any,
        status_code: 'invalid' as any,
        response_time_ms: -1,
        timestamp: 'invalid-date',
      };
      
      // Should not throw even with invalid data
      await expect(metricsCollector.recordMetrics(invalidMetrics)).resolves.not.toThrow();
    });
    
    it('should auto-flush when batch size is reached', async () => {
      const batchSize = 50; // Default batch size
      const baseMetrics = {
        endpoint: '/api/v1/auto-flush',
        method: 'POST',
        status_code: 200,
        response_time_ms: 100,
        timestamp: new Date().toISOString(),
      };
      
      // Record exactly batch size metrics
      for (let i = 0; i < batchSize; i++) {
        await metricsCollector.recordMetrics({
          ...baseMetrics,
          correlation_id: `auto-flush-${i}`,
        });
      }
      
      // Buffer should be flushed automatically
      expect(metricsCollector.getBufferSize()).toBe(0);
    });
  });
  
  describe('PerformanceMonitor', () => {
    beforeEach(async () => {
      await PerformanceMonitor.initialize();
    });
    
    it('should initialize without errors', async () => {
      await expect(PerformanceMonitor.initialize()).resolves.not.toThrow();
    });
    
    it('should load alert thresholds from configuration', async () => {
      // Mock successful threshold loading
      const mockThresholds = [
        {
          id: 'test-1',
          metric_name: 'API Response Time',
          threshold_type: 'response_time',
          threshold_value: 5000,
          duration_minutes: 5,
          severity: 'high',
          enabled: true,
        },
      ];
      
      // This would normally load from database
      expect(mockThresholds.length).toBeGreaterThan(0);
    });
  });
  
  describe('PerformanceAnalytics', () => {
    it('should generate performance summary', async () => {
      const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
      const endDate = new Date().toISOString();
      
      const summary = await PerformanceAnalytics.getPerformanceSummary(
        startDate,
        endDate,
        'test-customer-id'
      );
      
      expect(summary).toHaveProperty('period');
      expect(summary).toHaveProperty('total_requests');
      expect(summary).toHaveProperty('avg_response_time_ms');
      expect(summary).toHaveProperty('error_rate');
    });
    
    it('should detect performance anomalies', async () => {
      const anomalies = await PerformanceAnalytics.detectAnomalies(24);
      
      expect(Array.isArray(anomalies)).toBe(true);
      // Anomalies array could be empty if no issues detected
    });
    
    it('should calculate percentiles correctly', () => {
      // Test the private method through public interface
      const testData = [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000];
      
      // This tests the percentile calculation indirectly through summary
      const summary = {
        data: testData.map((time, index) => ({
          response_time_ms: time,
          status_code: 200,
          endpoint: '/test',
          method: 'GET',
          created_at: new Date().toISOString(),
        }))
      };
      
      // P95 of [100...1000] should be around 950
      expect(testData[Math.ceil(0.95 * testData.length) - 1]).toBe(950);
    });
  });
  
  describe('Alert System', () => {
    it('should trigger alerts for response time violations', async () => {
      const mockMetrics = Array.from({ length: 10 }, (_, i) => ({
        endpoint: '/api/v1/slow',
        method: 'POST',
        status_code: 200,
        response_time_ms: 6000, // Above 5000ms threshold
        created_at: new Date(Date.now() - i * 60000).toISOString(), // Spread over time
      }));
      
      // Mock database response
      mockSupabase.from = () => ({
        select: () => ({
          gte: () => ({
            gt: () => ({ data: mockMetrics, error: null })
          })
        })
      });
      
      // This would trigger an alert in a real scenario
      expect(mockMetrics.every(m => m.response_time_ms > 5000)).toBe(true);
    });
    
    it('should trigger alerts for high error rates', async () => {
      const totalRequests = 100;
      const errorRequests = 15; // 15% error rate
      
      const mockMetrics = [
        ...Array.from({ length: totalRequests - errorRequests }, (_, i) => ({
          status_code: 200,
          endpoint: '/api/v1/test',
          created_at: new Date().toISOString(),
        })),
        ...Array.from({ length: errorRequests }, (_, i) => ({
          status_code: 500,
          endpoint: '/api/v1/test',
          created_at: new Date().toISOString(),
        })),
      ];
      
      const errorRate = errorRequests / totalRequests;
      expect(errorRate).toBeGreaterThan(0.05); // Above 5% threshold
    });
    
    it('should track security events for alerting', async () => {
      const mockSecurityEvents = [
        {
          event_type: 'prompt_injection_attempt',
          severity: 'high',
          ip_address: '***********00',
          created_at: new Date().toISOString(),
        },
        {
          event_type: 'brute_force_attempt',
          severity: 'critical',
          ip_address: '*********',
          created_at: new Date().toISOString(),
        },
      ];
      
      const highSeverityEvents = mockSecurityEvents.filter(
        event => event.severity === 'high' || event.severity === 'critical'
      );
      
      expect(highSeverityEvents.length).toBe(2);
    });
  });
  
  describe('Performance Middleware', () => {
    it('should measure request processing time', async () => {
      const mockRequest = new Request('http://localhost/test', {
        method: 'GET',
        headers: {
          'x-correlation-id': 'test-correlation',
          'user-agent': 'test-agent',
        },
      });
      
      const mockHandler = async (req: Request) => {
        // Simulate processing time
        await new Promise(resolve => setTimeout(resolve, 100));
        return new Response('OK', { status: 200 });
      };
      
      // This would use the actual middleware in integration
      const startTime = Date.now();
      await mockHandler(mockRequest);
      const endTime = Date.now();
      
      const processingTime = endTime - startTime;
      expect(processingTime).toBeGreaterThanOrEqual(100);
      expect(processingTime).toBeLessThan(200); // Should be close to 100ms
    });
    
    it('should handle errors in request processing', async () => {
      const mockRequest = new Request('http://localhost/error', {
        method: 'POST',
      });
      
      const errorHandler = async (req: Request) => {
        throw new Error('Simulated processing error');
      };
      
      // Should not throw when using middleware
      await expect(async () => {
        try {
          await errorHandler(mockRequest);
        } catch (error) {
          // Middleware would catch this and return 500 response
          expect(error.message).toBe('Simulated processing error');
        }
      }).not.toThrow();
    });
    
    it('should add performance headers to responses', () => {
      const mockResponse = new Response('Test response', {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      // Simulate adding performance headers
      const headers = new Headers(mockResponse.headers);
      headers.set('X-Response-Time', '150ms');
      headers.set('X-Correlation-ID', 'test-correlation-id');
      
      const enhancedResponse = new Response(mockResponse.body, {
        status: mockResponse.status,
        headers,
      });
      
      expect(enhancedResponse.headers.get('X-Response-Time')).toBe('150ms');
      expect(enhancedResponse.headers.get('X-Correlation-ID')).toBe('test-correlation-id');
    });
  });
  
  describe('Load Testing Scenarios', () => {
    it('should handle concurrent metrics collection', async () => {
      const concurrentRequests = 50;
      const baseMetrics = {
        endpoint: '/api/v1/concurrent',
        method: 'POST',
        status_code: 200,
        response_time_ms: 100,
        timestamp: new Date().toISOString(),
      };
      
      const startTime = Date.now();
      
      const promises = Array.from({ length: concurrentRequests }, (_, i) =>
        metricsCollector.recordMetrics({
          ...baseMetrics,
          correlation_id: `concurrent-${i}`,
        })
      );
      
      await Promise.all(promises);
      const endTime = Date.now();
      
      const processingTime = endTime - startTime;
      
      // Should handle concurrent requests efficiently
      expect(processingTime).toBeLessThan(1000); // Less than 1 second
      
      // All metrics should be recorded
      const bufferSize = metricsCollector.getBufferSize();
      expect(bufferSize).toBeGreaterThanOrEqual(0); // May auto-flush
    });
    
    it('should maintain performance under sustained load', async () => {
      const sustainedRequests = 200;
      const batchSize = 10;
      const baseMetrics = {
        endpoint: '/api/v1/sustained',
        method: 'GET',
        status_code: 200,
        response_time_ms: 50,
        timestamp: new Date().toISOString(),
      };
      
      const startTime = Date.now();
      
      // Process in batches to simulate sustained load
      for (let batch = 0; batch < sustainedRequests / batchSize; batch++) {
        const batchPromises = Array.from({ length: batchSize }, (_, i) =>
          metricsCollector.recordMetrics({
            ...baseMetrics,
            correlation_id: `sustained-${batch}-${i}`,
          })
        );
        
        await Promise.all(batchPromises);
        
        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      const endTime = Date.now();
      const totalTime = endTime - startTime;
      const avgTimePerRequest = totalTime / sustainedRequests;
      
      // Should maintain good performance
      expect(avgTimePerRequest).toBeLessThan(10); // Less than 10ms per request
      expect(totalTime).toBeLessThan(5000); // Total under 5 seconds
    });
    
    it('should handle memory usage efficiently', async () => {
      const memoryTestRequests = 1000;
      const baseMetrics = {
        endpoint: '/api/v1/memory',
        method: 'POST',
        status_code: 200,
        response_time_ms: 75,
        timestamp: new Date().toISOString(),
      };
      
      // Record initial memory usage (if available)
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Generate large number of metrics
      for (let i = 0; i < memoryTestRequests; i++) {
        await metricsCollector.recordMetrics({
          ...baseMetrics,
          correlation_id: `memory-test-${i}`,
          ip_address: `192.168.1.${(i % 255) + 1}`,
          user_agent: `test-agent-${i}`,
        });
        
        // Force flush periodically to prevent excessive memory usage
        if (i % 100 === 0) {
          await metricsCollector.flush();
        }
      }
      
      // Final flush
      await metricsCollector.flush();
      
      // Check final memory usage (if available)
      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      if (initialMemory > 0 && finalMemory > 0) {
        const memoryGrowth = finalMemory - initialMemory;
        // Memory growth should be reasonable (less than 50MB)
        expect(memoryGrowth).toBeLessThan(50 * 1024 * 1024);
      }
      
      // Buffer should be empty after flush
      expect(metricsCollector.getBufferSize()).toBe(0);
    });
  });
  
  describe('Edge Cases and Error Handling', () => {
    it('should handle missing correlation IDs gracefully', async () => {
      const metricsWithoutCorrelation = {
        endpoint: '/api/v1/no-correlation',
        method: 'GET',
        status_code: 200,
        response_time_ms: 100,
        timestamp: new Date().toISOString(),
        // correlation_id is missing
      };
      
      await expect(
        metricsCollector.recordMetrics(metricsWithoutCorrelation)
      ).resolves.not.toThrow();
    });
    
    it('should handle negative response times', async () => {
      const invalidMetrics = {
        endpoint: '/api/v1/invalid',
        method: 'POST',
        status_code: 200,
        response_time_ms: -100, // Invalid negative time
        timestamp: new Date().toISOString(),
      };
      
      await expect(
        metricsCollector.recordMetrics(invalidMetrics)
      ).resolves.not.toThrow();
    });
    
    it('should handle extremely large response times', async () => {
      const extremeMetrics = {
        endpoint: '/api/v1/extreme',
        method: 'POST',
        status_code: 200,
        response_time_ms: 999999999, // Very large time
        timestamp: new Date().toISOString(),
      };
      
      await expect(
        metricsCollector.recordMetrics(extremeMetrics)
      ).resolves.not.toThrow();
    });
    
    it('should handle database connection failures', async () => {
      // Mock database error
      const originalSupabase = mockSupabase.from;
      mockSupabase.from = () => ({
        insert: async () => ({ error: { message: 'Connection failed' } })
      });
      
      const metrics = {
        endpoint: '/api/v1/db-error',
        method: 'GET',
        status_code: 200,
        response_time_ms: 100,
        timestamp: new Date().toISOString(),
      };
      
      // Should not throw even with database errors
      await expect(metricsCollector.recordMetrics(metrics)).resolves.not.toThrow();
      
      // Restore original mock
      mockSupabase.from = originalSupabase;
    });
  });
});