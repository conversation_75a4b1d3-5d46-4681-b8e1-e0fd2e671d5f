/**
 * Story 4.1: Customer Management - TDD Implementation
 * Tests comprehensive customer lifecycle management capabilities
 */

import { describe, it, expect, beforeEach, _afterEach } from 'bun:test';
import { 
  setupTestIsolation, 
  createTestAdminClient, 
  makeAdminRequest,
  assertAdminApiResponse,
  AdminAssertions,
  PerformanceTimer,
  TransactionTestHelper,
  TEST_CONFIG
} from './admin-test-setup';
import { CustomerFixtures } from './admin-fixtures';
import { MockSupabaseClient, resetAllMocks } from './admin-mocks';

// Test isolation setup
setupTestIsolation();

describe('Story 4.1: Customer Management', () => {
  const timer = new PerformanceTimer();
  const _transactionHelper = new TransactionTestHelper();
  let testClient: ReturnType<typeof createTestAdminClient>;

  beforeEach(() => {
    testClient = createTestAdminClient();
    resetAllMocks();
  });

  describe('4.1.1 Customer CRUD Operations', () => {
    it('should create customer with proper validation', async () => {
      timer.start();
      
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );

      timer.assertMaxDuration(500); // Performance requirement
      expect(response.status).toBe(201);
      
      const _data = await response.json();
      assertAdminApiResponse(data);
      AdminAssertions.validateCustomer(data.data);
      
      // Verify database state
      const { data: dbCustomer } = await testClient
        .from('customers')
        .select('*')
        .eq('customer_id', CustomerFixtures.validCustomer.customer_id)
        .single();
      
      expect(dbCustomer).toBeTruthy();
      expect(dbCustomer.tier).toBe('professional');
      expect(dbCustomer.status).toBe('trial'); // Default status
      expect(dbCustomer.tier_settings).toEqual(CustomerFixtures.validCustomer.tier_settings);
    });

    it('should reject customer creation with invalid data', async () => {
      const invalidCustomer = CustomerFixtures.invalidCustomers.missingName;
      
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        invalidCustomer
      );

      expect(response.status).toBe(400);
      
      const _data = await response.json();
      assertAdminApiResponse(data);
      expect(data.success).toBe(false);
      expect(data.error).toContain('name');
    });

    it('should prevent duplicate customer_id creation', async () => {
      // Create first customer
      await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );

      // Attempt to create duplicate
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );

      expect(response.status).toBe(409);
      const _data = await response.json();
      expect(data.error).toContain('already exists');
    });

    it('should list customers with pagination and filtering', async () => {
      // Create test customers
      const customers = [
        CustomerFixtures.validCustomer,
        CustomerFixtures.enterpriseCustomer,
        CustomerFixtures.starterCustomer
      ];

      for (const customer of customers) {
        await makeAdminRequest(
          TEST_CONFIG.endpoints.adminCustomers,
          'POST',
          customer
        );
      }

      // Test pagination
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}?limit=2&offset=0&tier=professional`
      );

      expect(response.status).toBe(200);
      const _data = await response.json();
      assertAdminApiResponse(data);
      
      expect(Array.isArray(data.data.customers)).toBe(true);
      expect(data.data.customers.length).toBeLessThanOrEqual(2);
      expect(data.data.pagination).toBeDefined();
      expect(data.data.pagination.total).toBeGreaterThanOrEqual(1);
    });

    it('should get customer details with full information', async () => {
      // Create customer
      const createResponse = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.enterpriseCustomer
      );
      const createdCustomer = (await createResponse.json()).data;

      // Get customer details
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${createdCustomer.id}`
      );

      expect(response.status).toBe(200);
      const _data = await response.json();
      assertAdminApiResponse(data);
      AdminAssertions.validateCustomer(data.data);
      
      // Should include tier settings and usage stats
      expect(data.data.tier_settings).toBeDefined();
      expect(data.data.usage_stats).toBeDefined();
      expect(data.data.api_keys_count).toBeDefined();
    });

    it('should update customer information', async () => {
      // Create customer
      const createResponse = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );
      const createdCustomer = (await createResponse.json()).data;

      // Update customer
      const updateData = {
        name: 'Updated Company Name',
        tier: 'enterprise',
        tier_settings: {
          max_api_keys: 50,
          default_credit_limit: 5000,
          rate_limit_multiplier: 3
        }
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${createdCustomer.id}`,
        'PUT',
        updateData
      );

      expect(response.status).toBe(200);
      const _data = await response.json();
      assertAdminApiResponse(data);
      
      expect(data.data.name).toBe(updateData.name);
      expect(data.data.tier).toBe(updateData.tier);
      expect(data.data.tier_settings).toEqual(updateData.tier_settings);
    });

    it('should soft delete customer with data retention', async () => {
      // Create customer
      const createResponse = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );
      const createdCustomer = (await createResponse.json()).data;

      // Delete customer
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${createdCustomer.id}`,
        'DELETE'
      );

      expect(response.status).toBe(200);
      const _data = await response.json();
      assertAdminApiResponse(data);

      // Verify soft delete - should still exist but marked as deleted
      const { data: dbCustomer } = await testClient
        .from('customers')
        .select('*')
        .eq('id', createdCustomer.id)
        .single();

      expect(dbCustomer).toBeTruthy();
      expect(dbCustomer.status).toBe('cancelled');
      expect(dbCustomer.deleted_at).toBeTruthy();
    });
  });

  describe('4.1.2 Customer Status Management', () => {
    let testCustomer: any;

    beforeEach(async () => {
      const createResponse = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );
      testCustomer = (await createResponse.json()).data;
    });

    it('should transition customer from trial to active', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${testCustomer.id}/status`,
        'PUT',
        { status: 'active', reason: 'Payment verified' }
      );

      expect(response.status).toBe(200);
      const _data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.status).toBe('active');
    });

    it('should suspend customer with reason', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${testCustomer.id}/status`,
        'PUT',
        { status: 'suspended', reason: 'Payment failure' }
      );

      expect(response.status).toBe(200);
      const _data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.status).toBe('suspended');
      expect(data.data.suspension_reason).toBe('Payment failure');
    });

    it('should validate status transitions', async () => {
      // Cannot go directly from trial to enterprise without proper validation
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${testCustomer.id}/status`,
        'PUT',
        { status: 'enterprise' }
      );

      expect(response.status).toBe(400);
      const _data = await response.json();
      expect(data.error).toContain('invalid status transition');
    });
  });

  describe('4.1.3 Audit Trail Implementation', () => {
    it('should log all customer operations', async () => {
      const correlationId = `test-audit-${Date.now()}`;
      
      // Create customer with correlation ID
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer,
        { 'x-correlation-id': correlationId }
      );

      expect(response.status).toBe(201);
      const createdCustomer = (await response.json()).data;

      // Verify audit log entry
      const { data: auditLogs } = await testClient
        .from('audit_logs')
        .select('*')
        .eq('correlation_id', correlationId);

      expect(auditLogs).toBeTruthy();
      expect(auditLogs.length).toBeGreaterThan(0);
      
      const createLog = auditLogs.find(log => log.action === 'create');
      expect(createLog).toBeTruthy();
      expect(createLog.resource_type).toBe('customer');
      expect(createLog.resource_id).toBe(createdCustomer.id);
    });

    it('should track admin user for all operations', async () => {
      const adminUserId = 'test-admin-user';
      
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer,
        { 'x-admin-user-id': adminUserId }
      );

      expect(response.status).toBe(201);

      // Verify admin user tracking
      const { data: auditLogs } = await testClient
        .from('audit_logs')
        .select('*')
        .eq('actor_id', adminUserId);

      expect(auditLogs.length).toBeGreaterThan(0);
    });
  });

  describe('Security and Validation', () => {
    it('should require admin authentication', async () => {
      const response = await fetch(TEST_CONFIG.endpoints.adminCustomers, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(CustomerFixtures.validCustomer)
      });

      expect(response.status).toBe(401);
    });

    it('should validate admin permissions', async () => {
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer,
        { 'x-admin-role': 'read-only' }
      );

      expect(response.status).toBe(403);
    });

    it('should prevent SQL injection in search parameters', async () => {
      const maliciousQuery = "'; DROP TABLE customers; --";
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}?search=${encodeURIComponent(maliciousQuery)}`
      );

      // Should handle safely without error
      expect(response.status).toBe(200);
    });
  });

  describe('Performance Requirements', () => {
    it('should handle customer listing under 500ms', async () => {
      timer.start();
      
      const response = await makeAdminRequest(TEST_CONFIG.endpoints.adminCustomers);
      
      timer.assertMaxDuration(500);
      expect(response.status).toBe(200);
    });

    it('should handle bulk customer operations efficiently', async () => {
      // Create multiple customers
      const customers = Array.from({ length: 10 }, (_, i) => ({
        ...CustomerFixtures.validCustomer,
        customer_id: `test-bulk-${i}-${Date.now()}`,
        email: `bulk-${i}-${Date.now()}@example.com`
      }));

      timer.start();
      
      const promises = customers.map(customer =>
        makeAdminRequest(TEST_CONFIG.endpoints.adminCustomers, 'POST', customer)
      );
      
      await Promise.all(promises);
      
      timer.assertMaxDuration(2000); // Should handle 10 creates in under 2 seconds
    });
  });

  describe('Data Integrity', () => {
    it('should maintain referential integrity on customer updates', async () => {
      // This will be tested with actual API keys and usage data
      const createResponse = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        CustomerFixtures.validCustomer
      );
      const customer = (await createResponse.json()).data;

      // Update customer tier
      const updateResponse = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCustomers}/${customer.id}`,
        'PUT',
        { tier: 'enterprise' }
      );

      expect(updateResponse.status).toBe(200);
      
      // Verify all related records are still accessible
      const { data: relatedData } = await testClient
        .from('api_keys')
        .select('*')
        .eq('customer_id', customer.id);

      // Should maintain referential integrity
      expect(Array.isArray(relatedData)).toBe(true);
    });

    it('should rollback transactions on failure', async () => {
      await transactionHelper.testRollback(async () => {
        // Simulate an operation that should fail
        await makeAdminRequest(
          TEST_CONFIG.endpoints.adminCustomers,
          'POST',
          { ...CustomerFixtures.validCustomer, tier: 'invalid-tier' }
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection failures gracefully', async () => {
      // This would typically involve mocking database failures
      // For now, we'll test with invalid data that should be handled gracefully
      
      const response = await makeAdminRequest(
        TEST_CONFIG.endpoints.adminCustomers,
        'POST',
        { invalid: 'data' }
      );

      expect(response.status).toBe(400);
      const _data = await response.json();
      assertAdminApiResponse(data);
      expect(data.error).toBeDefined();
    });

    it('should return appropriate error codes for different scenarios', async () => {
      // Test various error scenarios
      const scenarios = [
        { data: {}, expectedStatus: 400, description: 'missing required fields' },
        { data: CustomerFixtures.invalidCustomers.invalidEmail, expectedStatus: 400, description: 'invalid email' },
        { data: CustomerFixtures.invalidCustomers.invalidTier, expectedStatus: 400, description: 'invalid tier' }
      ];

      for (const scenario of scenarios) {
        const response = await makeAdminRequest(
          TEST_CONFIG.endpoints.adminCustomers,
          'POST',
          scenario.data
        );

        expect(response.status).toBe(scenario.expectedStatus);
      }
    });
  });
});