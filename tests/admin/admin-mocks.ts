/**
 * Admin Test Mocks
 * Provides mock implementations for testing Epic 4 admin functionality
 */

/**
 * Mock Supabase client for admin operations
 */
export class MockSupabaseClient {
  private mockData: Map<string, any[]> = new Map();
  private mockRpcResults: Map<string, any> = new Map();

  constructor() {
    this.setupDefaultMockData();
  }

  /**
   * Setup default mock data for tables
   */
  private setupDefaultMockData(): void {
    this.mockData.set('customers', []);
    this.mockData.set('api_keys', []);
    this.mockData.set('usage_logs', []);
    this.mockData.set('audit_logs', []);
  }

  /**
   * Mock the from() method
   */
  from(table: string) {
    return new MockQueryBuilder(table, this.mockData, this.mockRpcResults);
  }

  /**
   * Mock RPC calls
   */
  rpc(functionName: string, params?: any) {
    const key = `${functionName}_${JSON.stringify(params || {})}`;
    const _result = this.mockRpcResults.get(key);
    
    return Promise.resolve({
      data: result,
      error: result ? null : new Error(`Mock RPC not configured: ${functionName}`)
    });
  }

  /**
   * Configure mock RPC result
   */
  mockRpc(functionName: string, params: any, result: any): void {
    const key = `${functionName}_${JSON.stringify(params)}`;
    this.mockRpcResults.set(key, result);
  }

  /**
   * Add mock data to a table
   */
  addMockData(table: string, data: any[]): void {
    this.mockData.set(table, [...(this.mockData.get(table) || []), ...data]);
  }

  /**
   * Clear mock data for a table
   */
  clearMockData(table: string): void {
    this.mockData.set(table, []);
  }

  /**
   * Get current mock data for a table
   */
  getMockData(table: string): any[] {
    return this.mockData.get(table) || [];
  }
}

/**
 * Mock query builder for Supabase operations
 */
export class MockQueryBuilder {
  private filters: Array<{ column: string; operator: string; value: any }> = [];
  private selectColumns: string = '*';
  private insertData: any[] = [];
  private updateData: any = {};
  private orderBy: { column: string; ascending: boolean } | null = null;
  private limitValue: number | null = null;

  constructor(
    private table: string,
    private mockData: Map<string, any[]>,
    private mockRpcResults: Map<string, any>
  ) {}

  /**
   * Mock select operation
   */
  select(columns: string = '*', options?: any) {
    this.selectColumns = columns;
    return this;
  }

  /**
   * Mock insert operation
   */
  insert(data: any | any[]) {
    this.insertData = Array.isArray(data) ? data : [data];
    return this;
  }

  /**
   * Mock update operation
   */
  update(data: any) {
    this.updateData = data;
    return this;
  }

  /**
   * Mock delete operation
   */
  delete() {
    // Mark this as a delete operation
    this.updateData = { __delete: true };
    return this;
  }

  /**
   * Mock eq filter
   */
  eq(column: string, value: any) {
    this.filters.push({ column, operator: 'eq', value });
    return this;
  }

  /**
   * Mock neq filter
   */
  neq(column: string, value: any) {
    this.filters.push({ column, operator: 'neq', value });
    return this;
  }

  /**
   * Mock like filter
   */
  like(column: string, pattern: string) {
    this.filters.push({ column, operator: 'like', value: pattern });
    return this;
  }

  /**
   * Mock in filter
   */
  in(column: string, values: any[]) {
    this.filters.push({ column, operator: 'in', value: values });
    return this;
  }

  /**
   * Mock gte filter (greater than or equal)
   */
  gte(column: string, value: any) {
    this.filters.push({ column, operator: 'gte', value });
    return this;
  }

  /**
   * Mock lte filter (less than or equal)
   */
  lte(column: string, value: any) {
    this.filters.push({ column, operator: 'lte', value });
    return this;
  }

  /**
   * Mock order operation
   */
  order(column: string, options?: { ascending?: boolean }) {
    this.orderBy = { column, ascending: options?.ascending ?? true };
    return this;
  }

  /**
   * Mock limit operation
   */
  limit(count: number) {
    this.limitValue = count;
    return this;
  }

  /**
   * Mock single operation
   */
  single() {
    return this.then(result => {
      if (result.error) return result;
      return {
        data: result.data?.[0] || null,
        error: result.data?.length === 0 ? new Error('No rows found') : null
      };
    });
  }

  /**
   * Execute the mock query
   */
  then(callback?: (result: { data: any; error: any }) => any): Promise<any> {
    const _result = this.executeQuery();
    if (callback) {
      return Promise.resolve(callback(result));
    }
    return Promise.resolve(result);
  }

  /**
   * Execute the actual mock query logic
   */
  private executeQuery(): { data: any; error: any } {
    try {
      const tableData = this.mockData.get(this.table) || [];

      // Handle insert operations
      if (this.insertData.length > 0) {
        const newData = this.insertData.map(item => ({
          id: `mock-id-${Date.now()}-${Math.random()}`,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          ...item
        }));
        
        this.mockData.set(this.table, [...tableData, ...newData]);
        return { data: newData, error: null };
      }

      // Handle update operations
      if (Object.keys(this.updateData).length > 0) {
        if (this.updateData.__delete) {
          // Delete operation
          const filteredData = tableData.filter(row => !this.matchesFilters(row));
          this.mockData.set(this.table, filteredData);
          return { data: null, error: null };
        } else {
          // Update operation
          const updatedData = tableData.map(row => {
            if (this.matchesFilters(row)) {
              return {
                ...row,
                ...this.updateData,
                updated_at: new Date().toISOString()
              };
            }
            return row;
          });
          this.mockData.set(this.table, updatedData);
          return { data: updatedData.filter(row => this.matchesFilters(row)), error: null };
        }
      }

      // Handle select operations
      let filteredData = tableData.filter(row => this.matchesFilters(row));

      // Apply ordering
      if (this.orderBy) {
        filteredData.sort((a, b) => {
          const aVal = a[this.orderBy!.column];
          const bVal = b[this.orderBy!.column];
          const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
          return this.orderBy!.ascending ? comparison : -comparison;
        });
      }

      // Apply limit
      if (this.limitValue) {
        filteredData = filteredData.slice(0, this.limitValue);
      }

      return { data: filteredData, error: null };

    } catch {
      return { data: null, error };
    }
  }

  /**
   * Check if a row matches the current filters
   */
  private matchesFilters(row: any): boolean {
    return this.filters.every(filter => {
      const value = row[filter.column];
      
      switch (filter.operator) {
        case 'eq': {
          return value === filter.value;
        case 'neq': {
          return value !== filter.value;
        case 'like': {
          return String(value).includes(filter.value.replace('%', ''));
        case 'in': {
          return filter.value.includes(value);
        case 'gte': {
          return value >= filter.value;
        case 'lte': {
          return value <= filter.value;
        default:
          return true;
      }
    });
  }
}

/**
 * Mock external API responses
 */
export class MockExternalApis {
  private static responses: Map<string, any> = new Map();

  /**
   * Configure mock response for a URL
   */
  static mockResponse(url: string, response: any): void {
    this.responses.set(url, response);
  }

  /**
   * Get mock response for a URL
   */
  static getResponse(url: string): any {
    return this.responses.get(url);
  }

  /**
   * Clear all mock responses
   */
  static clearResponses(): void {
    this.responses.clear();
  }

  /**
   * Mock fetch implementation
   */
  static async mockFetch(url: string, options?: RequestInit): Promise<Response> {
    const mockResponse = this.getResponse(url);
    
    if (!mockResponse) {
      throw new Error(`No mock response configured for: ${url}`);
    }

    return new Response(JSON.stringify(mockResponse), {
      status: mockResponse.status || 200,
      statusText: mockResponse.statusText || 'OK',
      headers: mockResponse.headers || { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Mock rate limiter
 */
export class MockRateLimiter {
  private static limits: Map<string, { count: number; resetTime: number }> = new Map();

  /**
   * Mock rate limit check
   */
  static checkLimit(keyId: string, limit: number, windowMs: number): {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
  } {
    const now = Date.now();
    const key = `${keyId}_${Math.floor(now / windowMs)}`;
    
    const current = this.limits.get(key) || { count: 0, resetTime: now + windowMs };
    
    if (current.count >= limit) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: new Date(current.resetTime)
      };
    }

    current.count++;
    this.limits.set(key, current);

    return {
      allowed: true,
      remaining: limit - current.count,
      resetTime: new Date(current.resetTime)
    };
  }

  /**
   * Reset rate limits (for testing)
   */
  static reset(): void {
    this.limits.clear();
  }
}

/**
 * Mock credit processor
 */
export class MockCreditProcessor {
  private static balance: Map<string, number> = new Map();

  /**
   * Set mock balance for customer
   */
  static setBalance(customerId: string, balance: number): void {
    this.balance.set(customerId, balance);
  }

  /**
   * Get mock balance for customer
   */
  static getBalance(customerId: string): number {
    return this.balance.get(customerId) || 0;
  }

  /**
   * Mock credit deduction
   */
  static deductCredits(customerId: string, amount: number): {
    success: boolean;
    remaining: number;
    error?: string;
  } {
    const current = this.getBalance(customerId);
    
    if (current < amount) {
      return {
        success: false,
        remaining: current,
        error: 'Insufficient credits'
      };
    }

    const newBalance = current - amount;
    this.setBalance(customerId, newBalance);

    return {
      success: true,
      remaining: newBalance
    };
  }

  /**
   * Reset all balances (for testing)
   */
  static reset(): void {
    this.balance.clear();
  }
}

/**
 * Mock audit logger
 */
export class MockAuditLogger {
  private static logs: Array<{
    customerId?: string;
    action: string;
    details: any;
    timestamp: string;
  }> = [];

  /**
   * Mock log audit event
   */
  static logEvent(customerId: string | undefined, action: string, details: any): void {
    this.logs.push({
      customerId,
      action,
      details,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Get all logged events
   */
  static getLogs(): typeof MockAuditLogger.logs {
    return [...this.logs];
  }

  /**
   * Get logs for specific customer
   */
  static getLogsForCustomer(customerId: string): typeof MockAuditLogger.logs {
    return this.logs.filter(log => log.customerId === customerId);
  }

  /**
   * Clear all logs (for testing)
   */
  static clearLogs(): void {
    this.logs.length = 0;
  }
}

/**
 * Test helper to reset all mocks
 */
export function resetAllMocks(): void {
  MockRateLimiter.reset();
  MockCreditProcessor.reset();
  MockAuditLogger.clearLogs();
  MockExternalApis.clearResponses();
}