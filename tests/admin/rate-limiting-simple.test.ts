/**
 * Rate Limiting Simple Integration Tests
 * Epic 4 Story 4.4: Basic validation of rate limiting system
 * Focuses on database functions and core logic without Edge Function dependency
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import {
  createTestAdminClient,
  setupTestIsolation
} from './admin-test-setup';
import {
  CustomerFixtures,
  createTestCustomer
} from './admin-fixtures';

// Test isolation
setupTestIsolation();

describe('Rate Limiting System - Core Functionality', () => {
  const adminClient = createTestAdminClient();

  describe('Database Schema and Functions', () => {
    let testCustomer: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
    });

    it('should create rate limit configurations', async () => {
      const rateLimitData = {
        customer_id: testCustomer.id,
        limit_type: 'per_minute',
        limit_value: 60,
        burst_allowance: 10,
        algorithm_type: 'sliding_window',
        reset_at: new Date(Date.now() + 60000).toISOString()
      };

      const { data: rateLimit, error } = await adminClient
        .from('rate_limits')
        .insert(rateLimitData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(rateLimit).toBeDefined();
      expect(rateLimit.customer_id).toBe(testCustomer.id);
      expect(rateLimit.limit_type).toBe('per_minute');
      expect(rateLimit.limit_value).toBe(60);
      expect(rateLimit.burst_allowance).toBe(10);
      expect(rateLimit.algorithm_type).toBe('sliding_window');
    });

    it('should create rate limit violations log', async () => {
      // First create a rate limit
      const { data: rateLimit } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          reset_at: new Date(Date.now() + 60000).toISOString()
        })
        .select()
        .single();

      // Then create a violation
      const violationData = {
        rate_limit_id: rateLimit.id,
        customer_id: testCustomer.id,
        endpoint_called: '/extract',
        method: 'POST',
        attempted_requests: 1,
        limit_exceeded: 5,
        response_code: 429,
        retry_after_seconds: 60
      };

      const { data: violation, error } = await adminClient
        .from('rate_limit_violations')
        .insert(violationData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(violation).toBeDefined();
      expect(violation.rate_limit_id).toBe(rateLimit.id);
      expect(violation.endpoint_called).toBe('/extract');
      expect(violation.response_code).toBe(429);
    });

    it('should create whitelist entries', async () => {
      const whitelistData = {
        customer_id: testCustomer.id,
        whitelist_type: 'emergency',
        bypass_all_limits: true,
        valid_from: new Date().toISOString(),
        valid_until: new Date(Date.now() + 3600000).toISOString(),
        requested_by: 'test-admin',
        approval_reason: 'Emergency access for critical customer'
      };

      const { data: whitelist, error } = await adminClient
        .from('rate_limit_whitelist')
        .insert(whitelistData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(whitelist).toBeDefined();
      expect(whitelist.customer_id).toBe(testCustomer.id);
      expect(whitelist.whitelist_type).toBe('emergency');
      expect(whitelist.bypass_all_limits).toBe(true);
    });

    it('should test rate limit check function', async () => {
      // Create a rate limit
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          burst_allowance: 2,
          reset_at: new Date(Date.now() + 60000).toISOString()
        });

      // Test the rate limit check function
      const { data: result, error } = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      expect(error).toBeNull();
      expect(result).toBeDefined();
      expect(typeof result.allowed).toBe('boolean');
    });

    it('should test rate limit window reset function', async () => {
      // Create a rate limit
      const { data: rateLimit } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          current_usage: 3,
          burst_usage: 1,
          reset_at: new Date(Date.now() + 60000).toISOString()
        })
        .select()
        .single();

      // Reset the window
      const { error } = await adminClient.rpc('reset_rate_limit_window', {
        limit_id: rateLimit.id
      });

      expect(error).toBeNull();

      // Verify reset worked
      const { data: updatedLimit } = await adminClient
        .from('rate_limits')
        .select('current_usage, burst_usage')
        .eq('id', rateLimit.id)
        .single();

      expect(updatedLimit.current_usage).toBe(0);
      expect(updatedLimit.burst_usage).toBe(0);
    });

    it('should support multiple algorithm types', async () => {
      const algorithms = ['sliding_window', 'token_bucket', 'leaky_bucket', 'fixed_window'];
      
      for (const algorithm of algorithms) {
        const { data: rateLimit, error } = await adminClient
          .from('rate_limits')
          .insert({
            customer_id: testCustomer.id,
            limit_type: 'per_hour',
            limit_value: 100,
            algorithm_type: algorithm,
            reset_at: new Date(Date.now() + 3600000).toISOString()
          })
          .select()
          .single();

        expect(error).toBeNull();
        expect(rateLimit.algorithm_type).toBe(algorithm);
      }
    });

    it('should support geographic and endpoint restrictions', async () => {
      const { data: rateLimit, error } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 30,
          geographic_regions: ['us-east', 'eu-west'],
          endpoint_patterns: ['/extract', '/agents/*'],
          reset_at: new Date(Date.now() + 60000).toISOString()
        })
        .select()
        .single();

      expect(error).toBeNull();
      expect(rateLimit.geographic_regions).toEqual(['us-east', 'eu-west']);
      expect(rateLimit.endpoint_patterns).toEqual(['/extract', '/agents/*']);
    });

    it('should support priority levels', async () => {
      const { data: rateLimit, error } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 100,
          priority_level: 5,
          reset_at: new Date(Date.now() + 60000).toISOString()
        })
        .select()
        .single();

      expect(error).toBeNull();
      expect(rateLimit.priority_level).toBe(5);
    });

    it('should validate constraint checks', async () => {
      // Test that we can't have both customer_id and api_key_id
      const invalidData = {
        customer_id: testCustomer.id,
        api_key_id: testCustomer.id, // This should fail
        limit_type: 'per_minute',
        limit_value: 60,
        reset_at: new Date(Date.now() + 60000).toISOString()
      };

      const { error } = await adminClient
        .from('rate_limits')
        .insert(invalidData);

      expect(error).toBeDefined();
      expect(error.message).toContain('rate_limit_scope_check');
    });

    it('should handle rate limit analytics table', async () => {
      const analyticsData = {
        customer_id: testCustomer.id,
        window_start: new Date(Date.now() - 3600000).toISOString(),
        window_end: new Date().toISOString(),
        window_type: 'hour',
        total_requests: 150,
        blocked_requests: 5,
        burst_requests: 10,
        limit_utilization_percent: 75.5
      };

      const { data: analytics, error } = await adminClient
        .from('rate_limit_analytics')
        .insert(analyticsData)
        .select()
        .single();

      expect(error).toBeNull();
      expect(analytics).toBeDefined();
      expect(analytics.total_requests).toBe(150);
      expect(analytics.blocked_requests).toBe(5);
      expect(analytics.window_type).toBe('hour');
    });
  });

  describe('Rate Limiting Business Logic', () => {
    let testCustomer: any;
    let rateLimit: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, {
        ...CustomerFixtures.validCustomer,
        customer_id: `test-customer-logic-${Date.now()}`
      });

      // Create a test rate limit
      const { data } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 3,
          burst_allowance: 1,
          algorithm_type: 'sliding_window',
          reset_at: new Date(Date.now() + 60000).toISOString()
        })
        .select()
        .single();
      
      rateLimit = data;
    });

    it('should allow requests within limits', async () => {
      const { data: result } = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 2
      });

      expect(result.allowed).toBe(true);
    });

    it('should handle burst allowance correctly', async () => {
      // Use up normal limit
      await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 3
      });

      // Use burst allowance
      const { data: burstResult } = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      expect(burstResult.allowed).toBe(true);

      // Exceed burst allowance
      const { data: exceedResult } = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      expect(exceedResult.allowed).toBe(false);
    });

    it('should respect whitelist entries', async () => {
      // Create whitelist entry
      await adminClient
        .from('rate_limit_whitelist')
        .insert({
          customer_id: testCustomer.id,
          whitelist_type: 'emergency',
          bypass_all_limits: true,
          valid_from: new Date().toISOString(),
          valid_until: new Date(Date.now() + 3600000).toISOString(),
          requested_by: 'test-admin',
          approval_reason: 'Emergency test'
        });

      // Should bypass limits
      const { data: result } = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 100 // Way over limit
      });

      expect(result.allowed).toBe(true);
      expect(result.whitelisted).toBe(true);
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle multiple concurrent rate limit checks', async () => {
      const testCustomer = await createTestCustomer(adminClient, {
        ...CustomerFixtures.validCustomer,
        customer_id: `test-customer-perf-${Date.now()}`
      });

      // Create rate limit
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 100,
          reset_at: new Date(Date.now() + 60000).toISOString()
        });

      // Perform concurrent checks
      const promises = Array(10).fill(null).map(() =>
        adminClient.rpc('check_rate_limit', {
          p_customer_id: testCustomer.id,
          p_endpoint: '/extract',
          p_requests_to_add: 1
        })
      );

      const results = await Promise.all(promises);
      
      // All should succeed (within limit)
      results.forEach(({ data }) => {
        expect(data.allowed).toBe(true);
      });
    });

    it('should maintain data integrity under concurrent access', async () => {
      const testCustomer = await createTestCustomer(adminClient, {
        ...CustomerFixtures.validCustomer,
        customer_id: `test-customer-integrity-${Date.now()}`
      });

      // Create restrictive rate limit
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          reset_at: new Date(Date.now() + 60000).toISOString()
        });

      // Try to exceed limit concurrently
      const promises = Array(10).fill(null).map(() =>
        adminClient.rpc('check_rate_limit', {
          p_customer_id: testCustomer.id,
          p_endpoint: '/extract',
          p_requests_to_add: 1
        })
      );

      const results = await Promise.all(promises);
      
      // Count allowed vs blocked
      const allowedCount = results.filter(({ data }) => data.allowed).length;
      const blockedCount = results.filter(({ data }) => !data.allowed).length;
      
      // Should respect the limit of 5
      expect(allowedCount).toBeLessThanOrEqual(5);
      expect(blockedCount).toBeGreaterThanOrEqual(5);
    });
  });
});