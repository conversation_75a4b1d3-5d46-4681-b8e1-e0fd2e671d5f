/**
 * Admin Test Setup Utilities
 * Provides comprehensive testing infrastructure for Epic 4 admin functionality
 */

import { createClient } from '@supabase/supabase-js';
import { beforeEach, afterEach } from 'bun:test';
import type { Database } from '../../types/database.types';

// Test configuration
export const TEST_CONFIG = {
  supabase: {
    url: 'http://127.0.0.1:14321',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0',
    serviceRoleKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
  },
  endpoints: {
    base: 'http://127.0.0.1:14321/functions/v1',
    adminCustomers: 'http://127.0.0.1:14321/functions/v1/admin-customers',
    adminApiKeys: 'http://127.0.0.1:14321/functions/v1/admin-api-keys',
    adminCredits: 'http://127.0.0.1:14321/functions/v1/admin-credits',
    adminRateLimits: 'http://127.0.0.1:14321/functions/v1/admin-rate-limits',
    adminAnalytics: 'http://127.0.0.1:14321/functions/v1/admin-analytics',
    adminSupport: 'http://127.0.0.1:14321/functions/v1/admin-support'
  }
};

/**
 * Test database client with admin privileges
 */
export function createTestAdminClient() {
  return createClient<Database>(
    TEST_CONFIG.supabase.url,
    TEST_CONFIG.supabase.serviceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

/**
 * Test database client with regular privileges
 */
export function createTestClient() {
  return createClient<Database>(
    TEST_CONFIG.supabase.url,
    TEST_CONFIG.supabase.anonKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );
}

/**
 * Clean test data before each test
 */
export async function cleanTestData() {
  const client = createTestAdminClient();
  
  // Clean in reverse dependency order
  await client.from('audit_logs').delete().like('customer_id', 'test-%');
  await client.from('usage_logs').delete().like('customer_id', 'test-%');
  await client.from('extraction_results').delete().like('customer_id', 'test-%');
  await client.from('documents').delete().like('customer_id', 'test-%');
  await client.from('api_keys').delete().like('customer_id', 'test-%');
  await client.from('agents').delete().like('customer_id', 'test-%');
  await client.from('customers').delete().like('customer_id', 'test-%');
}

/**
 * Generate test correlation ID for tracing
 */
export function generateTestCorrelationId(): string {
  return `test-${Date.now()}-${Math.random().toString(36).substring(7)}`;
}

/**
 * Wait for database consistency
 */
export async function waitForDbConsistency(ms: number = 100): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Assert response structure for admin APIs
 */
export function assertAdminApiResponse(response: any): void {
  if (!response) {
    throw new Error('Response is null or undefined');
  }
  
  if (typeof response !== 'object') {
    throw new Error('Response is not an object');
  }
  
  if (!('success' in response)) {
    throw new Error('Response missing success field');
  }
  
  if (!('timestamp' in response)) {
    throw new Error('Response missing timestamp field');
  }
  
  if (response.success && !('data' in response)) {
    throw new Error('Successful response missing data field');
  }
  
  if (!response.success && !('error' in response)) {
    throw new Error('Failed response missing error field');
  }
}

/**
 * Create HTTP request with proper headers
 */
export async function makeAdminRequest(
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body?: any,
  headers: Record<string, string> = {}
): Promise<Response> {
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${TEST_CONFIG.supabase.serviceRoleKey}`,
    'x-correlation-id': generateTestCorrelationId(),
    ...headers
  };

  const requestInit: RequestInit = {
    method,
    headers: requestHeaders
  };

  if (body && method !== 'GET') {
    requestInit.body = JSON.stringify(body);
  }

  return fetch(endpoint, requestInit);
}

/**
 * Assertion helpers for admin functionality
 */
export const AdminAssertions = {
  /**
   * Assert customer object structure
   */
  validateCustomer(customer: any): void {
    if (!customer || typeof customer !== 'object') {
      throw new Error('Customer must be an object');
    }
    
    const requiredFields = ['id', 'customer_id', 'name', 'email', 'tier', 'status', 'created_at'];
    for (const field of requiredFields) {
      if (!(field in customer)) {
        throw new Error(`Customer missing required field: ${field}`);
      }
    }
    
    if (!customer.tier_settings || typeof customer.tier_settings !== 'object') {
      throw new Error('Customer must have tier_settings object');
    }
  },

  /**
   * Assert API key object structure
   */
  validateApiKey(apiKey: any): void {
    if (!apiKey || typeof apiKey !== 'object') {
      throw new Error('API key must be an object');
    }
    
    const requiredFields = ['id', 'customer_id', 'key_prefix', 'key_type', 'name', 'is_active'];
    for (const field of requiredFields) {
      if (!(field in apiKey)) {
        throw new Error(`API key missing required field: ${field}`);
      }
    }
    
    if (!['test', 'production'].includes(apiKey.key_type)) {
      throw new Error('API key type must be test or production');
    }
  },

  /**
   * Assert usage analytics structure
   */
  validateUsageAnalytics(analytics: any): void {
    if (!analytics || typeof analytics !== 'object') {
      throw new Error('Analytics must be an object');
    }
    
    const requiredFields = ['total_requests', 'total_credits', 'date_range'];
    for (const field of requiredFields) {
      if (!(field in analytics)) {
        throw new Error(`Analytics missing required field: ${field}`);
      }
    }
  }
};

/**
 * Test isolation setup for each test
 */
export function setupTestIsolation() {
  beforeEach(async () => {
    await cleanTestData();
  });

  afterEach(async () => {
    await cleanTestData();
  });
}

/**
 * Performance measurement utilities
 */
export class PerformanceTimer {
  private startTime: number = 0;
  
  start(): void {
    this.startTime = performance.now();
  }
  
  stop(): number {
    return performance.now() - this.startTime;
  }
  
  assertMaxDuration(maxMs: number): void {
    const duration = this.stop();
    if (duration > maxMs) {
      throw new Error(`Operation took ${duration}ms, expected max ${maxMs}ms`);
    }
  }
}

/**
 * Database transaction test helpers
 */
export class TransactionTestHelper {
  private client = createTestAdminClient();
  
  /**
   * Test that operations are properly rolled back on error
   */
  async testRollback(operation: () => Promise<void>): Promise<void> {
    const beforeCount = await this.getTableRowCount('customers');
    
    try {
      await operation();
      throw new Error('Expected operation to fail');
    } catch {
      // Expected failure
    }
    
    const afterCount = await this.getTableRowCount('customers');
    if (beforeCount !== afterCount) {
      throw new Error('Transaction was not properly rolled back');
    }
  }
  
  private async getTableRowCount(table: string): Promise<number> {
    const { count } = await this.client
      .from(table)
      .select('*', { count: 'exact', head: true });
    return count || 0;
  }
}

/**
 * Mock external dependencies
 */
export const MockServices = {
  /**
   * Mock Supabase RPC calls
   */
  mockRpcCall(functionName: string, expectedResult: any): void {
    // Implementation would depend on test framework capabilities
    console.log(`Mocking RPC call: ${functionName} -> ${JSON.stringify(expectedResult)}`);
  },

  /**
   * Mock external API calls (for credit processing, etc.)
   */
  mockExternalApi(url: string, response: any): void {
    // Implementation would depend on test framework capabilities
    console.log(`Mocking external API: ${url} -> ${JSON.stringify(response)}`);
  }
};