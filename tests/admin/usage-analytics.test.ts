import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { adminTestSetup, adminTestTeardown } from './admin-test-setup';
import { createTestCustomer, createTestApiKey, createUsageData } from './admin-fixtures';
import type { Database } from '../../types/database.types';

describe('Admin Usage Analytics Dashboard', () => {
  let supabase: any;
  let testCustomerId: string;
  let testApiKeyId: string;
  const baseUrl = 'http://127.0.0.1:54321/functions/v1';

  beforeEach(async () => {
    supabase = await adminTestSetup();
    
    // Create test customer with usage data
    const customer = await createTestCustomer(supabase, {
      company_name: 'Analytics Test Corp',
      tier: 'enterprise'
    });
    testCustomerId = customer.id;

    // Create test API key
    const apiKey = await createTestApiKey(supabase, testCustomerId, {
      name: 'Analytics Test Key',
      key_type: 'production',
      credits: 10000
    });
    testApiKeyId = apiKey.id;

    // Generate usage data for analytics
    await createUsageData(supabase, {
      customer_id: testCustomerId,
      api_key_id: testApiKeyId,
      days: 30,
      requests_per_day: 100
    });
  });

  afterEach(async () => {
    await adminTestTeardown(supabase);
  });

  describe('GET /admin-analytics/usage', () => {
    it('should retrieve usage analytics for date range', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          },
          group_by: 'day'
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('usage_metrics');
      expect(data.data.usage_metrics).toBeInstanceOf(Array);
      expect(data.data.usage_metrics.length).toBeGreaterThan(0);
      
      // Verify metric structure
      const firstMetric = data.data.usage_metrics[0];
      expect(firstMetric).toHaveProperty('date');
      expect(firstMetric).toHaveProperty('total_requests');
      expect(firstMetric).toHaveProperty('successful_requests');
      expect(firstMetric).toHaveProperty('failed_requests');
      expect(firstMetric).toHaveProperty('credits_consumed');
      expect(firstMetric).toHaveProperty('documents_processed');
    });

    it('should filter usage analytics by customer', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customer_id: testCustomerId,
          date_range: {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          },
          group_by: 'day'
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.usage_metrics).toBeInstanceOf(Array);
      expect(data.data.customer_id).toBe(testCustomerId);
    });

    it('should aggregate usage by week', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          },
          group_by: 'week'
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.usage_metrics).toBeInstanceOf(Array);
      expect(data.data.usage_metrics.length).toBeLessThanOrEqual(5); // Max 5 weeks in 30 days
    });

    it('should aggregate usage by month', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          },
          group_by: 'month'
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.usage_metrics).toBeInstanceOf(Array);
      expect(data.data.usage_metrics.length).toBeLessThanOrEqual(4); // Max 4 months in 90 days
    });

    it('should return 400 for invalid date range', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date().toISOString(),
            end: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // End before start
          },
          group_by: 'day'
        })
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid date range');
    });
  });

  describe('GET /admin-analytics/revenue', () => {
    it('should retrieve revenue analytics', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/revenue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          }
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('revenue_metrics');
      expect(data.data.revenue_metrics).toHaveProperty('total_revenue');
      expect(data.data.revenue_metrics).toHaveProperty('total_cost');
      expect(data.data.revenue_metrics).toHaveProperty('gross_profit');
      expect(data.data.revenue_metrics).toHaveProperty('profit_margin');
      expect(data.data.revenue_metrics).toHaveProperty('revenue_by_customer');
    });

    it('should calculate profit margins correctly', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/revenue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customer_id: testCustomerId,
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          }
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      const metrics = data.data.revenue_metrics;
      
      // Verify profit margin calculation
      const expectedProfit = metrics.total_revenue - metrics.total_cost;
      const expectedMargin = (expectedProfit / metrics.total_revenue) * 100;
      
      expect(metrics.gross_profit).toBeCloseTo(expectedProfit, 2);
      expect(metrics.profit_margin).toBeCloseTo(expectedMargin, 2);
      expect(metrics.profit_margin).toBeGreaterThanOrEqual(60); // Target 60%+ margin
    });

    it('should break down costs by AI service', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/revenue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          },
          include_cost_breakdown: true
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.cost_breakdown).toHaveProperty('openai_costs');
      expect(data.data.cost_breakdown).toHaveProperty('claude_costs');
      expect(data.data.cost_breakdown).toHaveProperty('llamaparse_costs');
      expect(data.data.cost_breakdown).toHaveProperty('infrastructure_costs');
    });
  });

  describe('GET /admin-analytics/performance', () => {
    it('should retrieve performance metrics', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/performance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metrics: ['response_time', 'error_rate', 'uptime']
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.performance).toHaveProperty('avg_response_time_ms');
      expect(data.data.performance).toHaveProperty('p50_response_time_ms');
      expect(data.data.performance).toHaveProperty('p95_response_time_ms');
      expect(data.data.performance).toHaveProperty('p99_response_time_ms');
      expect(data.data.performance).toHaveProperty('error_rate');
      expect(data.data.performance).toHaveProperty('uptime_percentage');
    });

    it('should track AI service performance', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/performance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metrics: ['ai_service_health']
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.ai_services).toBeInstanceOf(Array);
      
      const openaiService = data.data.ai_services.find((s: any) => s.name === 'openai');
      expect(openaiService).toBeDefined();
      expect(openaiService).toHaveProperty('availability');
      expect(openaiService).toHaveProperty('avg_response_time_ms');
      expect(openaiService).toHaveProperty('error_count');
      expect(openaiService).toHaveProperty('fallback_triggered_count');
    });

    it('should return performance percentiles', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/performance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metrics: ['response_time'],
          percentiles: [50, 75, 90, 95, 99]
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.performance).toHaveProperty('p50_response_time_ms');
      expect(data.data.performance).toHaveProperty('p75_response_time_ms');
      expect(data.data.performance).toHaveProperty('p90_response_time_ms');
      expect(data.data.performance).toHaveProperty('p95_response_time_ms');
      expect(data.data.performance).toHaveProperty('p99_response_time_ms');
      
      // Verify percentiles are in ascending order
      expect(data.data.performance.p50_response_time_ms).toBeLessThanOrEqual(
        data.data.performance.p75_response_time_ms
      );
      expect(data.data.performance.p95_response_time_ms).toBeLessThanOrEqual(
        data.data.performance.p99_response_time_ms
      );
    });
  });

  describe('GET /admin-analytics/customers/top', () => {
    it('should retrieve top customers by usage', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/customers/top`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metric: 'usage',
          limit: 10,
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          }
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.top_customers).toBeInstanceOf(Array);
      expect(data.data.top_customers.length).toBeLessThanOrEqual(10);
      
      if (data.data.top_customers.length > 0) {
        const topCustomer = data.data.top_customers[0];
        expect(topCustomer).toHaveProperty('customer_id');
        expect(topCustomer).toHaveProperty('company_name');
        expect(topCustomer).toHaveProperty('total_requests');
        expect(topCustomer).toHaveProperty('credits_consumed');
        expect(topCustomer).toHaveProperty('revenue_generated');
      }
    });

    it('should retrieve top customers by revenue', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/customers/top`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metric: 'revenue',
          limit: 5
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.top_customers).toBeInstanceOf(Array);
      expect(data.data.top_customers.length).toBeLessThanOrEqual(5);
      
      // Verify customers are sorted by revenue
      for (let i = 0; i < data.data.top_customers.length - 1; i++) {
        expect(data.data.top_customers[i].revenue_generated).toBeGreaterThanOrEqual(
          data.data.top_customers[i + 1].revenue_generated
        );
      }
    });
  });

  describe('GET /admin-analytics/dashboard', () => {
    it('should retrieve real-time dashboard metrics', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('active_customers');
      expect(data.data).toHaveProperty('total_api_calls_today');
      expect(data.data).toHaveProperty('revenue_today');
      expect(data.data).toHaveProperty('profit_margin');
      expect(data.data).toHaveProperty('ai_service_health');
      expect(data.data).toHaveProperty('top_customers');
      expect(data.data).toHaveProperty('last_updated');
    });

    it('should include AI service health in dashboard', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.data.ai_service_health).toBeInstanceOf(Array);
      expect(data.data.ai_service_health).toContainEqual(
        expect.objectContaining({
          name: 'openai',
          status: expect.any(String),
          availability: expect.any(Boolean)
        })
      );
    });

    it('should cache dashboard metrics', async () => {
      // First request
      const response1 = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      });
      const data1 = await response1.json();
      
      // Second request immediately after
      const response2 = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      });
      const data2 = await response2.json();
      
      // Should return same cached data
      expect(data1.data.last_updated).toBe(data2.data.last_updated);
    });
  });

  describe('GET /admin-analytics/behavior', () => {
    it('should analyze customer behavior patterns', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/behavior`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          customer_id: testCustomerId
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.behavior).toHaveProperty('usage_pattern');
      expect(data.data.behavior).toHaveProperty('peak_usage_hour');
      expect(data.data.behavior).toHaveProperty('peak_usage_day');
      expect(data.data.behavior).toHaveProperty('days_active_last_30');
      expect(data.data.behavior).toHaveProperty('churn_risk_score');
      expect(data.data.behavior).toHaveProperty('lifetime_value');
    });

    it('should identify at-risk customers', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/behavior`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          filter: 'at_risk',
          churn_threshold: 70
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.at_risk_customers).toBeInstanceOf(Array);
      
      // All returned customers should have high churn risk
      for (const customer of data.data.at_risk_customers) {
        expect(customer.churn_risk_score).toBeGreaterThanOrEqual(70);
      }
    });
  });

  describe('GET /admin-analytics/export', () => {
    it('should export analytics data as CSV', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          format: 'csv',
          report_type: 'usage',
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          }
        })
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toBe('text/csv');
      expect(response.headers.get('Content-Disposition')).toContain('attachment');
      
      const csvContent = await response.text();
      expect(csvContent).toContain('customer_id');
      expect(csvContent).toContain('total_requests');
      expect(csvContent).toContain('credits_consumed');
    });

    it('should export analytics data as JSON', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          format: 'json',
          report_type: 'revenue',
          date_range: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          }
        })
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('Content-Type')).toBe('application/json');
      
      const data = await response.json();
      expect(data).toHaveProperty('report_type', 'revenue');
      expect(data).toHaveProperty('date_range');
      expect(data).toHaveProperty('data');
      expect(data.data).toBeInstanceOf(Array);
    });
  });

  describe('GET /admin-analytics/forecast', () => {
    it('should provide usage forecasting', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/forecast`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metric: 'usage',
          forecast_days: 30
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.forecast).toHaveProperty('predicted_usage');
      expect(data.data.forecast).toHaveProperty('confidence_interval');
      expect(data.data.forecast).toHaveProperty('trend');
      expect(data.data.forecast.predicted_usage).toBeInstanceOf(Array);
      expect(data.data.forecast.predicted_usage.length).toBe(30);
    });

    it('should provide revenue forecasting', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/forecast`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          metric: 'revenue',
          forecast_days: 90,
          customer_id: testCustomerId
        })
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      
      expect(data.success).toBe(true);
      expect(data.data.forecast).toHaveProperty('predicted_revenue');
      expect(data.data.forecast).toHaveProperty('growth_rate');
      expect(data.data.forecast).toHaveProperty('seasonality_detected');
    });
  });

  describe('Audit Logging', () => {
    it('should log all analytics access', async () => {
      const response = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'X-Correlation-ID': 'test-analytics-audit'
        }
      });

      expect(response.status).toBe(200);

      // Verify audit log was created
      const { data: auditLogs } = await supabase
        .from('admin_audit_logs')
        .select('*')
        .eq('correlation_id', 'test-analytics-audit')
        .single();

      expect(auditLogs).toBeDefined();
      expect(auditLogs.action).toBe('analytics.dashboard.view');
      expect(auditLogs.resource_type).toBe('analytics');
    });
  });

  describe('Performance Requirements', () => {
    it('should return analytics within 500ms', async () => {
      const startTime = Date.now();
      
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            end: new Date().toISOString()
          },
          group_by: 'day'
        })
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(500);
    });

    it('should handle large date ranges efficiently', async () => {
      const startTime = Date.now();
      
      const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        },
        body: JSON.stringify({
          date_range: {
            start: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
            end: new Date().toISOString()
          },
          group_by: 'month'
        })
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(1000); // 1 second for large queries
    });
  });
});