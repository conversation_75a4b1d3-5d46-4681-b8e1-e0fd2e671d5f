/**
 * Simple Customer Management Test - TDD Green Phase
 * Basic test to verify our Epic 4.1 implementation works
 */

import { describe, it, expect } from 'bun:test';

describe('Story 4.1: Customer Management - Simple Integration Test', () => {
  const adminEndpoint = 'http://127.0.0.1:14321/functions/v1/admin-customers';
  const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

  it('should handle admin authentication', async () => {
    // Test without auth header - should fail
    const response = await fetch(adminEndpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        customer_id: 'test-customer-1',
        name: 'Test Company',
        tier: 'professional'
      })
    });

    expect(response.status).toBe(401);
    
    const _data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain('Unauthorized');
  });

  it('should create customer with valid admin token', async () => {
    const customerData = {
      customer_id: `test-customer-${Date.now()}`,
      name: 'Test Company Ltd',
      email: '<EMAIL>',
      tier: 'professional'
    };

    const response = await fetch(adminEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceRoleKey}`,
        'x-correlation-id': 'test-correlation-123'
      },
      body: JSON.stringify(customerData)
    });

    // Should succeed with proper auth
    expect(response.status).toBe(201);
    
    const _data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.customer_id).toBe(customerData.customer_id);
    expect(data.data.name).toBe(customerData.name);
    expect(data.data.tier).toBe(customerData.tier);
  });

  it('should reject invalid customer data', async () => {
    const invalidCustomer = {
      // Missing required name field
      customer_id: `test-invalid-${Date.now()}`,
      tier: 'professional'
    };

    const response = await fetch(adminEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceRoleKey}`
      },
      body: JSON.stringify(invalidCustomer)
    });

    expect(response.status).toBe(400);
    
    const _data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain('name');
  });

  it('should list customers', async () => {
    const response = await fetch(adminEndpoint, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${serviceRoleKey}`
      }
    });

    expect(response.status).toBe(200);
    
    const _data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.customers).toBeDefined();
    expect(Array.isArray(data.data.customers)).toBe(true);
    expect(data.data.pagination).toBeDefined();
  });
});