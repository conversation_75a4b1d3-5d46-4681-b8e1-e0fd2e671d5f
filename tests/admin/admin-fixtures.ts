/**
 * Admin Test Fixtures
 * Provides reusable test data for Epic 4 admin functionality testing
 */

import { generateTestCorrelationId } from './admin-test-setup';
import { createHash } from 'crypto';

/**
 * Test customer data templates
 */
export const CustomerFixtures = {
  /**
   * Valid customer creation request
   */
  validCustomer: {
    customer_id: `test-customer-${Date.now()}-${Math.random().toString(36).substring(7)}`,
    name: 'Test Company Ltd',
    email: `test-${Date.now()}-${Math.random().toString(36).substring(7)}@example.com`,
    tier: 'professional' as const,
    tier_settings: {
      max_api_keys: 10,
      default_credit_limit: 1000,
      rate_limit_multiplier: 2
    }
  },

  /**
   * Enterprise customer template
   */
  enterpriseCustomer: {
    customer_id: `test-enterprise-${Date.now()}`,
    name: 'Enterprise Corp',
    email: `enterprise-${Date.now()}@bigcorp.com`,
    tier: 'enterprise' as const,
    tier_settings: {
      max_api_keys: 100,
      default_credit_limit: 10000,
      rate_limit_multiplier: 5
    }
  },

  /**
   * Starter tier customer
   */
  starterCustomer: {
    customer_id: `test-starter-${Date.now()}`,
    name: 'Startup Inc',
    email: `startup-${Date.now()}@startup.com`,
    tier: 'starter' as const,
    tier_settings: {
      max_api_keys: 3,
      default_credit_limit: 100,
      rate_limit_multiplier: 1
    }
  },

  /**
   * Invalid customer data for validation testing
   */
  invalidCustomers: {
    missingName: {
      customer_id: `test-invalid-${Date.now()}`,
      email: `invalid-${Date.now()}@example.com`,
      tier: 'professional' as const
      // Missing name field
    },
    invalidEmail: {
      customer_id: `test-invalid-${Date.now()}`,
      name: 'Invalid Customer',
      email: 'not-an-email',
      tier: 'professional' as const
    },
    invalidTier: {
      customer_id: `test-invalid-${Date.now()}`,
      name: 'Invalid Customer',
      email: `invalid-${Date.now()}@example.com`,
      tier: 'invalid-tier' as any
    }
  }
};

/**
 * Test API key data templates
 */
export const ApiKeyFixtures = {
  /**
   * Valid test API key
   */
  validTestKey: {
    name: 'Test Development Key',
    description: 'Key for testing purposes',
    key_type: 'test' as const,
    credits_allocated: 500,
    scope_restrictions: {
      allowed_endpoints: ['extract', 'agents'],
      max_file_size: 10485760 // 10MB
    }
  },

  /**
   * Valid production API key
   */
  validProdKey: {
    name: 'Production API Key',
    description: 'Production environment key',
    key_type: 'production' as const,
    credits_allocated: 1000,
    expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
    scope_restrictions: {
      allowed_endpoints: ['extract', 'agents', 'analytics'],
      allowed_agents: ['default-invoice-extractor'],
      max_file_size: 52428800 // 50MB
    }
  },

  /**
   * Enterprise API key with extended permissions
   */
  enterpriseKey: {
    name: 'Enterprise Master Key',
    description: 'Full access enterprise key',
    key_type: 'production' as const,
    credits_allocated: 10000,
    scope_restrictions: {
      // No restrictions for enterprise
    }
  },

  /**
   * API key updates for testing
   */
  keyUpdates: {
    suspend: {
      suspended: true,
      suspension_reason: 'Suspicious activity detected'
    },
    reactivate: {
      suspended: false,
      suspension_reason: null
    },
    updateScope: {
      scope_restrictions: {
        allowed_endpoints: ['extract'],
        max_file_size: 5242880 // 5MB
      }
    },
    extendExpiry: {
      expires_at: new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000).toISOString() // 2 years
    }
  }
};

/**
 * Credit transaction fixtures
 */
export const CreditFixtures = {
  /**
   * Credit purchase transaction
   */
  creditPurchase: {
    amount: 1000,
    transaction_type: 'purchase' as const,
    payment_reference: `stripe_pi_${Date.now()}`,
    admin_notes: 'Monthly credit purchase'
  },

  /**
   * Credit refund transaction
   */
  creditRefund: {
    amount: 500,
    transaction_type: 'refund' as const,
    payment_reference: `refund_${Date.now()}`,
    admin_notes: 'Customer requested refund for unused credits'
  },

  /**
   * Manual credit adjustment
   */
  creditAdjustment: {
    amount: 100,
    transaction_type: 'adjustment' as const,
    admin_notes: 'Compensation for service downtime'
  },

  /**
   * Bulk credit operations
   */
  bulkOperations: {
    massRefund: {
      customer_ids: [] as string[], // Will be populated with test customer IDs
      amount: 250,
      transaction_type: 'refund' as const,
      admin_notes: 'Bulk refund for service incident'
    }
  }
};

/**
 * Rate limiting fixtures
 */
export const RateLimitFixtures = {
  /**
   * Standard rate limits
   */
  standardLimits: {
    per_minute: 60,
    per_hour: 1000,
    per_day: 10000,
    burst_allowance: 10
  },

  /**
   * Enterprise rate limits
   */
  enterpriseLimits: {
    per_minute: 300,
    per_hour: 10000,
    per_day: 100000,
    burst_allowance: 50
  },

  /**
   * Custom rate limit configurations
   */
  customLimits: {
    restrictive: {
      per_minute: 10,
      per_hour: 100,
      per_day: 1000,
      burst_allowance: 2
    },
    generous: {
      per_minute: 1000,
      per_hour: 50000,
      per_day: 500000,
      burst_allowance: 100
    }
  }
};

/**
 * Analytics query fixtures
 */
export const AnalyticsFixtures = {
  /**
   * Date range queries
   */
  dateRanges: {
    lastWeek: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    },
    lastMonth: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    },
    lastQuarter: {
      start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
      end: new Date().toISOString()
    }
  },

  /**
   * Analytics query parameters
   */
  queryParams: {
    usageByCustomer: {
      group_by: 'customer' as const,
      include_costs: true,
      include_performance: true
    },
    usageByAgent: {
      group_by: 'agent' as const,
      include_accuracy: true
    },
    revenueAnalysis: {
      group_by: 'day' as const,
      include_profit_margin: true,
      include_model_costs: true
    }
  }
};

/**
 * Usage log fixtures for testing analytics
 */
export const UsageLogFixtures = {
  /**
   * Generate sample usage logs
   */
  generateUsageLogs(customerId: string, apiKeyId: string, count: number = 10) {
    const logs = [];
    const now = Date.now();
    
    for (let i = 0; i < count; i++) {
      logs.push({
        customer_id: customerId,
        api_key_id: apiKeyId,
        endpoint: '/extract',
        method: 'POST',
        credits_used: Math.floor(Math.random() * 10) + 1,
        model_cost_usd: Math.random() * 0.01, // $0.00 - $0.01
        customer_price_usd: Math.random() * 0.02, // $0.00 - $0.02
        processing_time_ms: Math.floor(Math.random() * 5000) + 500,
        model_used: ['openai/gpt-4o', 'claude-3-sonnet', 'llamaparse'][Math.floor(Math.random() * 3)],
        input_tokens: Math.floor(Math.random() * 1000) + 100,
        output_tokens: Math.floor(Math.random() * 500) + 50,
        status_code: 200,
        success: true,
        created_at: new Date(now - i * 60000).toISOString() // Space logs 1 minute apart
      });
    }
    
    return logs;
  }
};

/**
 * Support ticket fixtures
 */
export const SupportFixtures = {
  /**
   * Customer activity timeline events
   */
  timelineEvents: {
    apiKeyCreated: {
      event_type: 'api_key_created',
      resource_type: 'api_key',
      action: 'create',
      details: {
        key_type: 'test',
        name: 'Development Key'
      }
    },
    documentProcessed: {
      event_type: 'document_processed',
      resource_type: 'document',
      action: 'process',
      details: {
        agent_id: 'default-invoice-extractor',
        processing_time_ms: 2500,
        credits_used: 5
      }
    },
    errorOccurred: {
      event_type: 'processing_error',
      resource_type: 'document',
      action: 'process',
      details: {
        error_code: 'INVALID_FILE_FORMAT',
        error_message: 'Unsupported file type: .xyz',
        agent_id: 'default-receipt-extractor'
      },
      risk_level: 'medium'
    },
    customerSuspended: {
      event_type: 'customer_suspended',
      resource_type: 'customer',
      action: 'suspend',
      details: {
        suspension_reason: 'Payment failure',
        previous_status: 'active'
      },
      risk_level: 'high'
    },
    creditRefund: {
      event_type: 'credit_refund',
      resource_type: 'credit_transaction',
      action: 'refund',
      details: {
        amount: 500,
        reason: 'Service downtime compensation',
        payment_reference: 'stripe_re_123456'
      },
      risk_level: 'low'
    }
  },

  /**
   * Error aggregation data
   */
  errorPatterns: {
    fileFormatErrors: {
      error_code: 'INVALID_FILE_FORMAT',
      frequency: 'high',
      impact: 'medium',
      suggested_resolution: 'Add file format validation'
    },
    rateLimitErrors: {
      error_code: 'RATE_LIMIT_EXCEEDED',
      frequency: 'medium',
      impact: 'high',
      suggested_resolution: 'Increase rate limits or implement queuing'
    },
    authenticationErrors: {
      error_code: 'INVALID_API_KEY',
      frequency: 'low',
      impact: 'high',
      suggested_resolution: 'Check API key validity and permissions'
    },
    processingTimeouts: {
      error_code: 'PROCESSING_TIMEOUT',
      frequency: 'medium',
      impact: 'high',
      suggested_resolution: 'Optimize document processing pipeline'
    }
  },

  /**
   * Customer impersonation requests
   */
  impersonationRequests: {
    validRequest: {
      customer_id: '', // Will be set in tests
      reason: 'Reproduce reported extraction issue',
      duration_minutes: 30,
      admin_notes: 'Customer reports invoice extraction returning incorrect totals'
    },
    extendedRequest: {
      customer_id: '', // Will be set in tests
      reason: 'Complex integration debugging',
      duration_minutes: 120,
      admin_notes: 'Multi-step debugging session for enterprise customer'
    }
  },

  /**
   * Notification templates
   */
  notificationTemplates: {
    creditLowBalance: {
      type: 'credit_alert',
      subject: 'Low Credit Balance Alert',
      message: 'Your account has less than 100 credits remaining. Please add credits to continue processing.',
      priority: 'medium'
    },
    serviceDowntime: {
      type: 'service_alert',
      subject: 'Scheduled Maintenance Notification',
      message: 'We will be performing scheduled maintenance on {{date}} from {{start_time}} to {{end_time}}.',
      priority: 'high'
    },
    processingIssue: {
      type: 'issue_resolution',
      subject: 'Processing Issue Resolved',
      message: 'The processing issue you reported has been resolved. Please try your request again.',
      priority: 'medium'
    }
  }
};

/**
 * Admin user fixtures
 */
export const AdminFixtures = {
  /**
   * Test admin user
   */
  testAdmin: {
    id: 'test-admin-user',
    email: '<EMAIL>',
    role: 'admin',
    permissions: ['customer_management', 'api_key_management', 'analytics_access', 'support_tools']
  },

  /**
   * Super admin user
   */
  superAdmin: {
    id: 'super-admin-user',
    email: '<EMAIL>',
    role: 'super_admin',
    permissions: ['*'] // All permissions
  }
};

/**
 * Generate correlation IDs for test tracing
 */
/**
 * Helper functions for creating test data
 */

/**
 * Create a test customer in the database
 */
export async function createTestCustomer(
  client: any,
  overrides: Partial<typeof CustomerFixtures.validCustomer> = {}
): Promise<any> {
  // Generate unique customer data for each call
  const uniqueCustomerData = {
    customer_id: `test-customer-${Date.now()}-${Math.random().toString(36).substring(7)}`,
    name: 'Test Company Ltd',
    email: `test-${Date.now()}-${Math.random().toString(36).substring(7)}@example.com`,
    tier: 'professional' as const,
    credits_available: 1000,
    credits_used: 0,
    status: 'active' as const
  };
  const customerData = { ...uniqueCustomerData, ...overrides };
  
  const { data, error } = await client
    .from('customers')
    .insert([customerData])
    .select()
    .single();
  
  if (error) {
    throw new Error(`Failed to create test customer: ${error.message}`);
  }
  
  return data;
}

/**
 * Create a test API key in the database
 */
export async function createTestApiKey(
  client: any,
  customerId: string,
  overrides: Partial<typeof ApiKeyFixtures.validTestKey> = {}
): Promise<any> {
  const keyData = { 
    ...ApiKeyFixtures.validTestKey, 
    ...overrides,
    customer_id: customerId
  };
  
  // Generate a test key hash and prefix
  const keyPrefix = keyData.key_type === 'production' ? 'skp_' : 'skt_';
  const keyId = `${keyPrefix}${Math.random().toString(36).substring(2, 15)}`;
  
  const apiKeyRecord = {
    customer_id: customerId,
    name: keyData.name,
    description: keyData.description || '',
    key_type: keyData.key_type,
    key_prefix: keyPrefix,
    key_hash: createHash('sha256').update(keyId).digest('hex'),
    is_active: true,
    scope_restrictions: keyData.scope_restrictions || {},
    expires_at: keyData.expires_at || null,
    credits_allocated: keyData.credits_allocated || 1000
  };
  
  const { data, error } = await client
    .from('api_keys')
    .insert([apiKeyRecord])
    .select()
    .single();
  
  if (error) {
    throw new Error(`Failed to create test API key: ${error.message}`);
  }
  
  return { ...data, key: keyId }; // Include the actual key for testing
}

/**
 * Create test usage logs for analytics testing
 */
export async function createTestUsageLogs(
  client: any,
  customerId: string,
  apiKeyId: string,
  count: number = 5
): Promise<any[]> {
  const logs = UsageLogFixtures.generateUsageLogs(customerId, apiKeyId, count);
  
  const { data, error } = await client
    .from('usage_logs')
    .insert(logs)
    .select();
  
  if (error) {
    throw new Error(`Failed to create test usage logs: ${error.message}`);
  }
  
  return data;
}

/**
 * Generate correlation IDs for test tracing
 */
/**
 * Create usage data for analytics testing
 */
export async function createUsageData(
  client: any,
  params: {
    customer_id: string;
    api_key_id: string;
    days: number;
    requests_per_day: number;
  }
): Promise<void> {
  const now = new Date();
  const logs = [];

  for (let day = 0; day < params.days; day++) {
    for (let request = 0; request < params.requests_per_day; request++) {
      const timestamp = new Date(now.getTime() - (day * 24 + request) * 60 * 60 * 1000);
      const isSuccess = Math.random() > 0.05; // 95% success rate
      
      logs.push({
        customer_id: params.customer_id,
        api_key_id: params.api_key_id,
        endpoint: ['/extract', '/agents', '/analytics'][Math.floor(Math.random() * 3)],
        method: 'POST',
        status: isSuccess ? 'success' : 'error',
        status_code: isSuccess ? 200 : [400, 429, 500][Math.floor(Math.random() * 3)],
        credits_used: isSuccess ? Math.floor(Math.random() * 10) + 1 : 0,
        model_cost: isSuccess ? Math.random() * 0.01 : 0,
        customer_price: isSuccess ? Math.random() * 0.02 : 0,
        processing_time_ms: Math.floor(Math.random() * 3000) + 500,
        model_used: ['gpt-4o', 'claude-3-sonnet', 'llamaparse'][Math.floor(Math.random() * 3)],
        input_tokens: isSuccess ? Math.floor(Math.random() * 1000) + 100 : 0,
        output_tokens: isSuccess ? Math.floor(Math.random() * 500) + 50 : 0,
        document_id: isSuccess ? `doc_${day}_${request}` : null,
        metadata: {
          pages: Math.floor(Math.random() * 10) + 1,
          file_type: ['pdf', 'png', 'docx'][Math.floor(Math.random() * 3)]
        },
        created_at: timestamp.toISOString(),
        updated_at: timestamp.toISOString()
      });
    }
  }

  // Insert in batches to avoid overwhelming the database
  const batchSize = 100;
  for (let i = 0; i < logs.length; i += batchSize) {
    const batch = logs.slice(i, i + batchSize);
    const { error } = await client
      .from('usage_logs')
      .insert(batch);
    
    if (error) {
      console.error('Failed to insert usage logs batch:', error);
      throw error;
    }
  }

  // Also create aggregated hourly data for better performance
  const hourlyData: { [key: string]: any } = {};
  
  for (const log of logs) {
    const hourBucket = new Date(log.created_at);
    hourBucket.setMinutes(0, 0, 0);
    const hourKey = hourBucket.toISOString();
    
    if (!hourlyData[hourKey]) {
      hourlyData[hourKey] = {
        customer_id: params.customer_id,
        api_key_id: params.api_key_id,
        hour_bucket: hourKey,
        total_requests: 0,
        successful_requests: 0,
        failed_requests: 0,
        credits_consumed: 0,
        total_cost: 0,
        total_revenue: 0,
        avg_processing_time_ms: 0,
        min_processing_time_ms: 999999,
        max_processing_time_ms: 0,
        documents_processed: 0,
        total_pages: 0,
        total_tokens_input: 0,
        total_tokens_output: 0,
        openai_calls: 0,
        claude_calls: 0,
        llamaparse_calls: 0,
        validation_errors: 0,
        processing_errors: 0,
        rate_limit_errors: 0,
        processing_times: []
      };
    }
    
    const hour = hourlyData[hourKey];
    hour.total_requests++;
    
    if (log.status === 'success') {
      hour.successful_requests++;
      hour.documents_processed++;
      hour.total_pages += log.metadata.pages;
    } else {
      hour.failed_requests++;
      if (log.status_code === 400) hour.validation_errors++;
      if (log.status_code === 429) hour.rate_limit_errors++;
      if (log.status_code === 500) hour.processing_errors++;
    }
    
    hour.credits_consumed += log.credits_used;
    hour.total_cost += log.model_cost;
    hour.total_revenue += log.customer_price;
    hour.total_tokens_input += log.input_tokens;
    hour.total_tokens_output += log.output_tokens;
    
    if (log.model_used?.includes('gpt')) hour.openai_calls++;
    if (log.model_used?.includes('claude')) hour.claude_calls++;
    if (log.model_used === 'llamaparse') hour.llamaparse_calls++;
    
    hour.processing_times.push(log.processing_time_ms);
    hour.min_processing_time_ms = Math.min(hour.min_processing_time_ms, log.processing_time_ms);
    hour.max_processing_time_ms = Math.max(hour.max_processing_time_ms, log.processing_time_ms);
  }

  // Calculate averages and insert hourly data
  for (const hourKey in hourlyData) {
    const hour = hourlyData[hourKey];
    hour.avg_processing_time_ms = Math.round(
      hour.processing_times.reduce((sum: number, t: number) => sum + t, 0) / hour.processing_times.length
    );
    delete hour.processing_times; // Remove temporary array
    
    const { error } = await client
      .from('usage_analytics_hourly')
      .insert([hour]);
    
    if (error && !error.message.includes('duplicate key')) {
      console.error('Failed to insert hourly analytics:', error);
    }
  }

  // Create daily rollups
  const dailyData: { [key: string]: any } = {};
  
  for (const hourKey in hourlyData) {
    const hour = hourlyData[hourKey];
    const dateBucket = new Date(hour.hour_bucket);
    dateBucket.setHours(0, 0, 0, 0);
    const dateKey = dateBucket.toISOString().split('T')[0];
    
    if (!dailyData[dateKey]) {
      dailyData[dateKey] = {
        customer_id: params.customer_id,
        date_bucket: dateKey,
        total_requests: 0,
        successful_requests: 0,
        failed_requests: 0,
        credits_consumed: 0,
        total_cost: 0,
        total_revenue: 0,
        documents_processed: 0,
        total_pages: 0,
        total_tokens_input: 0,
        total_tokens_output: 0,
        active_api_keys: 1,
        avg_processing_time_ms: 0,
        processing_times: []
      };
    }
    
    const day = dailyData[dateKey];
    day.total_requests += hour.total_requests;
    day.successful_requests += hour.successful_requests;
    day.failed_requests += hour.failed_requests;
    day.credits_consumed += hour.credits_consumed;
    day.total_cost += hour.total_cost;
    day.total_revenue += hour.total_revenue;
    day.documents_processed += hour.documents_processed;
    day.total_pages += hour.total_pages;
    day.total_tokens_input += hour.total_tokens_input;
    day.total_tokens_output += hour.total_tokens_output;
    day.processing_times.push(hour.avg_processing_time_ms);
  }

  // Insert daily data
  for (const dateKey in dailyData) {
    const day = dailyData[dateKey];
    day.avg_processing_time_ms = Math.round(
      day.processing_times.reduce((sum: number, t: number) => sum + t, 0) / day.processing_times.length
    );
    delete day.processing_times;
    
    const { error } = await client
      .from('usage_analytics_daily')
      .insert([day]);
    
    if (error && !error.message.includes('duplicate key')) {
      console.error('Failed to insert daily analytics:', error);
    }
  }
}

/**
 * Create test audit logs for timeline testing
 */
export async function createTestAuditLogs(
  client: any,
  customerId: string,
  count: number = 10
): Promise<any[]> {
  const logs = [];
  const now = Date.now();

  const eventTypes = [
    'api_key_created',
    'document_processed',
    'processing_error',
    'customer_updated',
    'credit_transaction'
  ];

  for (let i = 0; i < count; i++) {
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    const correlationId = generateTestCorrelationId();

    logs.push({
      customer_id: customerId,
      event_type: eventType,
      resource_type: eventType.includes('api_key') ? 'api_key' :
                    eventType.includes('document') ? 'document' :
                    eventType.includes('customer') ? 'customer' : 'transaction',
      resource_id: `resource_${i}`,
      action: eventType.includes('created') ? 'create' :
              eventType.includes('processed') ? 'process' :
              eventType.includes('error') ? 'error' :
              eventType.includes('updated') ? 'update' : 'transaction',
      actor_type: 'api_key',
      details: {
        correlation_id: correlationId,
        ...(eventType === 'processing_error' ? {
          error_code: ['INVALID_FILE_FORMAT', 'RATE_LIMIT_EXCEEDED', 'PROCESSING_TIMEOUT'][Math.floor(Math.random() * 3)],
          error_message: 'Test error message'
        } : {}),
        ...(eventType === 'document_processed' ? {
          agent_id: 'default-invoice-extractor',
          processing_time_ms: Math.floor(Math.random() * 5000) + 500,
          credits_used: Math.floor(Math.random() * 10) + 1
        } : {})
      },
      risk_level: eventType.includes('error') ? 'medium' : 'low',
      created_at: new Date(now - i * 60000).toISOString() // Space logs 1 minute apart
    });
  }

  const { data, error } = await client
    .from('audit_logs')
    .insert(logs)
    .select();

  if (error) {
    throw new Error(`Failed to create test audit logs: ${error.message}`);
  }

  return data;
}

/**
 * Create test error logs for error aggregation testing
 */
export async function createTestErrorLogs(
  client: any,
  customerId: string,
  errorCount: number = 5
): Promise<any[]> {
  const errorCodes = ['INVALID_FILE_FORMAT', 'RATE_LIMIT_EXCEEDED', 'PROCESSING_TIMEOUT', 'INVALID_API_KEY'];
  const logs = [];

  for (let i = 0; i < errorCount; i++) {
    const errorCode = errorCodes[Math.floor(Math.random() * errorCodes.length)];
    const correlationId = generateTestCorrelationId();

    logs.push({
      customer_id: customerId,
      event_type: 'processing_error',
      resource_type: 'document',
      resource_id: `error_doc_${i}`,
      action: 'process',
      actor_type: 'api_key',
      details: {
        error_code: errorCode,
        error_message: `Test error: ${errorCode}`,
        correlation_id: correlationId,
        stack_trace: 'Test stack trace'
      },
      risk_level: errorCode === 'INVALID_API_KEY' ? 'high' : 'medium',
      created_at: new Date(Date.now() - i * 3600000).toISOString() // Space errors 1 hour apart
    });
  }

  const { data, error } = await client
    .from('audit_logs')
    .insert(logs)
    .select();

  if (error) {
    throw new Error(`Failed to create test error logs: ${error.message}`);
  }

  return data;
}

export function generateTestData(dataType: string): { correlationId: string; timestamp: string } {
  return {
    correlationId: generateTestCorrelationId(),
    timestamp: new Date().toISOString()
  };
}