/**
 * Rate Limiting Tests
 * Epic 4 Story 4.4: Comprehensive test suite for advanced rate limiting system
 * Following TDD methodology with established admin patterns
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import {
  createTestAdminClient,
  makeAdminRequest,
  assertAdminApiResponse,
  PerformanceTimer,
  TransactionTestHelper,
  TEST_CONFIG,
  setupTestIsolation
} from './admin-test-setup';
import {
  CustomerFixtures,
  ApiKeyFixtures,
  RateLimitFixtures,
  createTestCustomer,
  createTestApiKey
} from './admin-fixtures';

// Test isolation
setupTestIsolation();

describe('Advanced Rate Limiting System', () => {
  const adminClient = createTestAdminClient();
  const timer = new PerformanceTimer();
  const _transactionHelper = new TransactionTestHelper();

  describe('Rate Limit Configuration Management', () => {
    let testCustomer: any;
    let testApiKey: any;

    beforeEach(async () => {
      // Create test customer and API key
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
      testApiKey = await createTestApiKey(adminClient, testCustomer.id, ApiKeyFixtures.validTestKey);
    });

    it('should create customer-level rate limits', async () => {
      timer.start();
      
      const rateLimitData = {
        customer_id: testCustomer.id,
        limit_type: 'per_minute',
        limit_value: 60,
        burst_allowance: 10,
        algorithm_type: 'sliding_window'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits`,
        'POST',
        rateLimitData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.success).toBe(true);
      expect(result.data.id).toBeDefined();
      expect(result.data.customer_id).toBe(testCustomer.id);
      expect(result.data.limit_type).toBe('per_minute');
      expect(result.data.limit_value).toBe(60);
      expect(result.data.burst_allowance).toBe(10);

      timer.assertMaxDuration(500);
    });

    it('should create API key-level rate limits', async () => {
      const rateLimitData = {
        api_key_id: testApiKey.id,
        limit_type: 'per_hour',
        limit_value: 1000,
        burst_allowance: 50,
        algorithm_type: 'token_bucket',
        endpoint_patterns: ['/extract', '/agents/*']
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits`,
        'POST',
        rateLimitData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.api_key_id).toBe(testApiKey.id);
      expect(result.data.endpoint_patterns).toEqual(['/extract', '/agents/*']);
      expect(result.data.algorithm_type).toBe('token_bucket');
    });

    it('should validate rate limit configuration', async () => {
      const invalidData = {
        customer_id: testCustomer.id,
        limit_type: 'invalid_type',
        limit_value: -1,
        burst_allowance: -5
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits`,
        'POST',
        invalidData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('validation');
    });

    it('should prevent conflicting rate limit scopes', async () => {
      const conflictData = {
        customer_id: testCustomer.id,
        api_key_id: testApiKey.id, // Both customer and API key - should fail
        limit_type: 'per_minute',
        limit_value: 60
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits`,
        'POST',
        conflictData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('scope');
    });

    it('should update existing rate limits', async () => {
      // Create initial rate limit
      const { data: rateLimit } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 60,
          burst_allowance: 10
        })
        .select()
        .single();

      const updateData = {
        limit_value: 120,
        burst_allowance: 20,
        algorithm_type: 'leaky_bucket'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/${rateLimit.id}`,
        'PUT',
        updateData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.limit_value).toBe(120);
      expect(result.data.burst_allowance).toBe(20);
      expect(result.data.algorithm_type).toBe('leaky_bucket');
    });

    it('should delete rate limits', async () => {
      // Create rate limit to delete
      const { data: rateLimit } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_day',
          limit_value: 10000
        })
        .select()
        .single();

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/${rateLimit.id}`,
        'DELETE'
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.success).toBe(true);

      // Verify deletion
      const { data: deletedLimit } = await adminClient
        .from('rate_limits')
        .select()
        .eq('id', rateLimit.id)
        .single();

      expect(deletedLimit).toBeNull();
    });
  });

  describe('Rate Limit Enforcement Engine', () => {
    let testCustomer: any;
    let testApiKey: any;
    let customerRateLimit: any;
    let apiKeyRateLimit: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
      testApiKey = await createTestApiKey(adminClient, testCustomer.id, ApiKeyFixtures.validTestKey);

      // Create customer-level rate limit
      const { data: customerLimit } = await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          burst_allowance: 2,
          algorithm_type: 'sliding_window'
        })
        .select()
        .single();
      customerRateLimit = customerLimit;

      // Create API key-level rate limit
      const { data: keyLimit } = await adminClient
        .from('rate_limits')
        .insert({
          api_key_id: testApiKey.id,
          limit_type: 'per_hour',
          limit_value: 100,
          burst_allowance: 10,
          algorithm_type: 'token_bucket'
        })
        .select()
        .single();
      apiKeyRateLimit = keyLimit;
    });

    it('should allow requests within rate limits', async () => {
      const checkData = {
        customer_id: testCustomer.id,
        endpoint: '/extract',
        requests: 3
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/check`,
        'POST',
        checkData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.allowed).toBe(true);
      expect(result.data.remaining).toBeGreaterThan(0);
    });

    it('should block requests exceeding rate limits', async () => {
      // First, consume the available requests
      for (let i = 0; i < 5; i++) {
        await adminClient.rpc('check_rate_limit', {
          p_customer_id: testCustomer.id,
          p_endpoint: '/extract',
          p_requests_to_add: 1
        });
      }

      // Now try to exceed the limit
      const checkData = {
        customer_id: testCustomer.id,
        endpoint: '/extract',
        requests: 3 // This should exceed limit + burst
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/check`,
        'POST',
        checkData
      );

      expect(response.status).toBe(429);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.allowed).toBe(false);
      expect(result.data.retry_after).toBeGreaterThan(0);
    });

    it('should handle burst allowance correctly', async () => {
      // Consume normal limit
      await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 5
      });

      // Use burst allowance
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 2
      });

      expect(result.allowed).toBe(true);

      // Now should be blocked
      const blockedResult = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      expect(blockedResult.allowed).toBe(false);
    });

    it('should respect endpoint patterns', async () => {
      // Update rate limit with endpoint restrictions
      await adminClient
        .from('rate_limits')
        .update({
          endpoint_patterns: ['/extract', '/agents/*']
        })
        .eq('id', apiKeyRateLimit.id);

      // Should apply to matching endpoint
      const extractCheck = await adminClient.rpc('check_rate_limit', {
        p_api_key_id: testApiKey.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });
      expect(extractCheck.allowed).toBe(true);

      // Should not apply to non-matching endpoint
      const analyticsCheck = await adminClient.rpc('check_rate_limit', {
        p_api_key_id: testApiKey.id,
        p_endpoint: '/analytics',
        p_requests_to_add: 150 // Would exceed limit if applied
      });
      expect(analyticsCheck.allowed).toBe(true);
    });

    it('should reset rate limit windows correctly', async () => {
      // Consume all requests
      await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 7 // 5 + 2 burst
      });

      // Should be blocked
      const blockedResult = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });
      expect(blockedResult.allowed).toBe(false);

      // Manually reset window for testing
      await adminClient.rpc('reset_rate_limit_window', {
        limit_id: customerRateLimit.id
      });

      // Should be allowed again
      const allowedResult = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });
      expect(allowedResult.allowed).toBe(true);
    });
  });

  describe('Rate Limit Whitelist Management', () => {
    let testCustomer: any;
    let testApiKey: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.enterpriseCustomer);
      testApiKey = await createTestApiKey(adminClient, testCustomer.id, ApiKeyFixtures.enterpriseKey);

      // Create restrictive rate limit
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 1,
          burst_allowance: 0
        });
    });

    it('should create emergency whitelist entries', async () => {
      const whitelistData = {
        customer_id: testCustomer.id,
        whitelist_type: 'emergency',
        bypass_all_limits: true,
        valid_until: new Date(Date.now() + 3600000).toISOString(), // 1 hour
        approval_reason: 'Emergency processing for critical customer',
        requested_by: 'admin-user-id'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/whitelist`,
        'POST',
        whitelistData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.whitelist_type).toBe('emergency');
      expect(result.data.bypass_all_limits).toBe(true);
    });

    it('should bypass rate limits for whitelisted customers', async () => {
      // Create whitelist entry
      await adminClient
        .from('rate_limit_whitelist')
        .insert({
          customer_id: testCustomer.id,
          whitelist_type: 'enterprise',
          bypass_all_limits: true,
          valid_from: new Date().toISOString(),
          valid_until: new Date(Date.now() + 3600000).toISOString(),
          requested_by: 'admin-user-id',
          approved_by: 'super-admin-id',
          approval_reason: 'Enterprise tier unlimited access'
        });

      // Should bypass even restrictive limits
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 100 // Way over the limit
      });

      expect(result.allowed).toBe(true);
      expect(result.whitelisted).toBe(true);
      expect(result.whitelist_type).toBe('enterprise');
    });

    it('should respect endpoint-specific whitelist', async () => {
      // Create endpoint-specific whitelist
      await adminClient
        .from('rate_limit_whitelist')
        .insert({
          customer_id: testCustomer.id,
          whitelist_type: 'testing',
          bypass_all_limits: false,
          specific_endpoints: ['/extract'],
          valid_from: new Date().toISOString(),
          valid_until: new Date(Date.now() + 3600000).toISOString(),
          requested_by: 'admin-user-id',
          approval_reason: 'Testing endpoint optimization'
        });

      // Should bypass for whitelisted endpoint
      const extractResult = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 100
      });
      expect(extractResult.allowed).toBe(true);

      // Should not bypass for non-whitelisted endpoint
      const agentsResult = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/agents',
        p_requests_to_add: 2
      });
      expect(agentsResult.allowed).toBe(false);
    });

    it('should expire whitelist entries automatically', async () => {
      // Create expired whitelist
      await adminClient
        .from('rate_limit_whitelist')
        .insert({
          customer_id: testCustomer.id,
          whitelist_type: 'testing',
          bypass_all_limits: true,
          valid_from: new Date(Date.now() - 7200000).toISOString(), // 2 hours ago
          valid_until: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
          requested_by: 'admin-user-id',
          approval_reason: 'Expired test whitelist'
        });

      // Should not bypass with expired whitelist
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 2
      });

      expect(result.allowed).toBe(false);
      expect(result.whitelisted).toBeUndefined();
    });

    it('should deactivate whitelist entries', async () => {
      // Create active whitelist
      const { data: whitelist } = await adminClient
        .from('rate_limit_whitelist')
        .insert({
          customer_id: testCustomer.id,
          whitelist_type: 'emergency',
          bypass_all_limits: true,
          valid_from: new Date().toISOString(),
          valid_until: new Date(Date.now() + 3600000).toISOString(),
          requested_by: 'admin-user-id',
          approval_reason: 'Emergency access'
        })
        .select()
        .single();

      // Deactivate whitelist
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/whitelist/${whitelist.id}`,
        'DELETE'
      );

      expect(response.status).toBe(200);

      // Should not bypass after deactivation
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 2
      });

      expect(result.allowed).toBe(false);
    });
  });

  describe('Rate Limit Violations and Analytics', () => {
    let testCustomer: any;
    let testApiKey: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
      testApiKey = await createTestApiKey(adminClient, testCustomer.id, ApiKeyFixtures.validTestKey);

      // Create restrictive rate limit for violation testing
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 2,
          burst_allowance: 1
        });
    });

    it('should log rate limit violations', async () => {
      // Exceed the rate limit to trigger violation
      await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 3 // Exceeds limit + burst
      });

      // Try another request to trigger violation log
      await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      // Check violation was logged
      const { data: violations } = await adminClient
        .from('rate_limit_violations')
        .select('*')
        .eq('customer_id', testCustomer.id);

      expect(violations).toBeDefined();
      expect(violations.length).toBeGreaterThan(0);
      expect(violations[0].endpoint_called).toBe('/extract');
      expect(violations[0].response_code).toBe(429);
    });

    it('should retrieve violation analytics', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/violations?customer_id=${testCustomer.id}&period=last_24h`
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.violations).toBeDefined();
      expect(Array.isArray(result.data.violations)).toBe(true);
    });

    it('should generate rate limit usage analytics', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/analytics?customer_id=${testCustomer.id}&window_type=hour&limit=24`
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.analytics).toBeDefined();
      expect(result.data.analytics.total_requests).toBeDefined();
      expect(result.data.analytics.blocked_requests).toBeDefined();
      expect(result.data.analytics.limit_utilization_percent).toBeDefined();
    });

    it('should provide rate limit dashboard data', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/dashboard?period=last_7d`
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.summary).toBeDefined();
      expect(result.data.top_violators).toBeDefined();
      expect(result.data.trending_patterns).toBeDefined();
    });
  });

  describe('Multi-Algorithm Rate Limiting', () => {
    let testCustomer: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
    });

    it('should support sliding window algorithm', async () => {
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          algorithm_type: 'sliding_window'
        });

      // Test sliding window behavior
      // This would require more sophisticated timing tests
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 3
      });

      expect(result.allowed).toBe(true);
    });

    it('should support token bucket algorithm', async () => {
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          burst_allowance: 3,
          algorithm_type: 'token_bucket'
        });

      // Test token bucket behavior
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 8 // Should use burst capacity
      });

      expect(result.allowed).toBe(true);
    });

    it('should support leaky bucket algorithm', async () => {
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          algorithm_type: 'leaky_bucket'
        });

      // Test leaky bucket behavior
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      expect(result.allowed).toBe(true);
    });

    it('should support fixed window algorithm', async () => {
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 5,
          algorithm_type: 'fixed_window'
        });

      // Test fixed window behavior
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: testCustomer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 3
      });

      expect(result.allowed).toBe(true);
    });
  });

  describe('Geographic Rate Limiting', () => {
    let testCustomer: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
    });

    it('should apply region-specific rate limits', async () => {
      // Create region-specific rate limits
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 10,
          geographic_regions: ['us-east', 'us-west']
        });

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/check`,
        'POST',
        {
          customer_id: testCustomer.id,
          endpoint: '/extract',
          requests: 5,
          region: 'us-east'
        }
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.allowed).toBe(true);
    });

    it('should ignore rate limits for non-matching regions', async () => {
      await adminClient
        .from('rate_limits')
        .insert({
          customer_id: testCustomer.id,
          limit_type: 'per_minute',
          limit_value: 1, // Very restrictive
          geographic_regions: ['eu-west']
        });

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/check`,
        'POST',
        {
          customer_id: testCustomer.id,
          endpoint: '/extract',
          requests: 10,
          region: 'us-east' // Different region
        }
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.allowed).toBe(true); // Should be allowed since region doesn't match
    });
  });

  describe('Performance and Scalability', () => {
    let testCustomers: any[];

    beforeEach(async () => {
      // Create multiple test customers for load testing
      testCustomers = [];
      for (let i = 0; i < 5; i++) {
        const customer = await createTestCustomer(adminClient, {
          ...CustomerFixtures.validCustomer,
          customer_id: `test-customer-perf-${i}-${Date.now()}`
        });
        testCustomers.push(customer);
      }
    });

    it('should handle high-volume rate limit checks efficiently', async () => {
      timer.start();

      // Create rate limits for all customers
      for (const customer of testCustomers) {
        await adminClient
          .from('rate_limits')
          .insert({
            customer_id: customer.id,
            limit_type: 'per_minute',
            limit_value: 100
          });
      }

      // Perform concurrent rate limit checks
      const promises = testCustomers.map(customer =>
        adminClient.rpc('check_rate_limit', {
          p_customer_id: customer.id,
          p_endpoint: '/extract',
          p_requests_to_add: 1
        })
      );

      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result.allowed).toBe(true);
      });

      timer.assertMaxDuration(1000); // Should complete in under 1 second
    });

    it('should maintain performance with complex rate limit configurations', async () => {
      timer.start();

      const customer = testCustomers[0];

      // Create multiple overlapping rate limits
      const rateLimits = [
        { limit_type: 'per_minute', limit_value: 60 },
        { limit_type: 'per_hour', limit_value: 1000 },
        { limit_type: 'per_day', limit_value: 10000 }
      ];

      for (const limit of rateLimits) {
        await adminClient
          .from('rate_limits')
          .insert({
            customer_id: customer.id,
            ...limit
          });
      }

      // Test rate limit checking performance
      const result = await adminClient.rpc('check_rate_limit', {
        p_customer_id: customer.id,
        p_endpoint: '/extract',
        p_requests_to_add: 1
      });

      expect(result.allowed).toBe(true);

      timer.assertMaxDuration(500); // Should be fast even with multiple limits
    });
  });

  describe('Admin Operations Integration', () => {
    let testCustomer: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
    });

    it('should integrate with audit logging', async () => {
      const rateLimitData = {
        customer_id: testCustomer.id,
        limit_type: 'per_minute',
        limit_value: 60
      };

      // Set admin user for audit trail
      await adminClient.rpc('set_admin_context', {
        admin_user_id: 'test-admin-user'
      });

      await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits`,
        'POST',
        rateLimitData
      );

      // Check audit log was created
      const { data: auditLogs } = await adminClient
        .from('audit_logs')
        .select('*')
        .eq('resource_type', 'rate_limit')
        .eq('customer_id', testCustomer.id);

      expect(auditLogs.length).toBeGreaterThan(0);
      expect(auditLogs[0].action).toBe('INSERT');
    });

    it('should support bulk rate limit operations', async () => {
      const bulkData = {
        customer_ids: [testCustomer.id],
        rate_limit_config: {
          limit_type: 'per_hour',
          limit_value: 1000,
          burst_allowance: 100
        }
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/bulk`,
        'POST',
        bulkData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.created_count).toBe(1);
      expect(result.data.failed_count).toBe(0);
    });

    it('should provide comprehensive rate limit status', async () => {
      // Create various rate limits
      await adminClient
        .from('rate_limits')
        .insert([
          {
            customer_id: testCustomer.id,
            limit_type: 'per_minute',
            limit_value: 60,
            current_usage: 45
          },
          {
            customer_id: testCustomer.id,
            limit_type: 'per_hour',
            limit_value: 1000,
            current_usage: 750
          }
        ]);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.base}/admin-rate-limits/status?customer_id=${testCustomer.id}`
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      expect(result.data.rate_limits).toBeDefined();
      expect(result.data.rate_limits.length).toBe(2);
      expect(result.data.overall_utilization).toBeDefined();
      expect(result.data.next_reset_times).toBeDefined();
    });
  });
});