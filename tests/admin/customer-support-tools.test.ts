/**
 * Customer Support Tools Tests (Story 4.6)
 * Tests for comprehensive customer support and troubleshooting tools
 */

import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { 
  createTestAdminClient,
  makeAdminRequest,
  TEST_CONFIG,
  setupTestIsolation,
  assertAdminApiResponse,
  generateTestCorrelationId
} from './admin-test-setup';
import { 
  CustomerFixtures,
  SupportFixtures,
  createTestCustomer,
  createTestApiKey,
  createTestAuditLogs,
  createTestErrorLogs,
  createTestUsageLogs
} from './admin-fixtures';

describe('Story 4.6: Customer Support Tools', () => {
  setupTestIsolation();
  
  let testCustomer: any;
  let testApiKey: any;
  let adminClient: any;

  beforeEach(async () => {
    adminClient = createTestAdminClient();
    
    // Create test customer and API key
    testCustomer = await createTestCustomer(adminClient);
    testApiKey = await createTestApiKey(adminClient, testCustomer.id);
  });

  describe('4.6.1 Customer Activity Timeline', () => {
    it('should retrieve comprehensive customer timeline', async () => {
      // Create test audit logs and usage logs
      await createTestAuditLogs(adminClient, testCustomer.id, 5);
      await createTestUsageLogs(adminClient, testCustomer.id, testApiKey.id, 3);
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/timeline`,
        'GET'
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.success).toBe(true);
      expect(data.data.timeline).toBeDefined();
      expect(Array.isArray(data.data.timeline)).toBe(true);
      expect(data.data.timeline.length).toBeGreaterThan(0);
      
      // Verify timeline structure
      const timelineEntry = data.data.timeline[0];
      expect(timelineEntry).toHaveProperty('created_at');
      expect(timelineEntry).toHaveProperty('action');
      expect(timelineEntry).toHaveProperty('event_type');
      expect(timelineEntry).toHaveProperty('details');
    });

    it('should limit timeline results with pagination', async () => {
      // Create many audit logs
      await createTestAuditLogs(adminClient, testCustomer.id, 50);
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/timeline?limit=10&offset=0`,
        'GET'
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.data.timeline.length).toBeLessThanOrEqual(10);
      expect(data.data.pagination).toBeDefined();
      expect(data.data.pagination.total).toBeGreaterThan(10);
    });

    it('should filter timeline by event type', async () => {
      await createTestAuditLogs(adminClient, testCustomer.id, 10);
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/timeline?event_type=processing_error`,
        'GET'
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      if (data.data.timeline.length > 0) {
        data.data.timeline.forEach((entry: any) => {
          expect(entry.event_type).toBe('processing_error');
        });
      }
    });

    it('should handle non-existent customer gracefully', async () => {
      const fakeCustomerId = 'non-existent-customer-id';
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${fakeCustomerId}/timeline`,
        'GET'
      );
      
      expect(response.status).toBe(404);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Customer not found');
    });
  });

  describe('4.6.2 Error Aggregation and Analysis', () => {
    it('should aggregate customer errors by type', async () => {
      // Create test error logs
      await createTestErrorLogs(adminClient, testCustomer.id, 10);
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/errors`,
        'GET'
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.errors).toBeDefined();
      expect(Array.isArray(data.data.errors)).toBe(true);
      
      if (data.data.errors.length > 0) {
        const errorSummary = data.data.errors[0];
        expect(errorSummary).toHaveProperty('error_code');
        expect(errorSummary).toHaveProperty('error_count');
        expect(errorSummary).toHaveProperty('first_occurrence');
        expect(errorSummary).toHaveProperty('last_occurrence');
      }
    });

    it('should filter errors by date range', async () => {
      await createTestErrorLogs(adminClient, testCustomer.id, 5);
      
      const startDate = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // 24 hours ago
      const endDate = new Date().toISOString();
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/errors?start_date=${startDate}&end_date=${endDate}`,
        'GET'
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.data.errors).toBeDefined();
    });

    it('should provide detailed error information by correlation ID', async () => {
      const errorLogs = await createTestErrorLogs(adminClient, testCustomer.id, 1);
      const correlationId = errorLogs[0].details.correlation_id;
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/errors/${correlationId}`,
        'GET'
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.error_details).toBeDefined();
      expect(data.data.error_details.correlation_id).toBe(correlationId);
      expect(data.data.error_details.details).toBeDefined();
    });
  });

  describe('4.6.3 Customer Impersonation', () => {
    it('should create safe impersonation session', async () => {
      const impersonationRequest = {
        ...SupportFixtures.impersonationRequests.validRequest,
        customer_id: testCustomer.id
      };
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/impersonate`,
        'POST',
        impersonationRequest
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.impersonation_token).toBeDefined();
      expect(data.data.expires_at).toBeDefined();
      expect(data.data.session_id).toBeDefined();
      
      // Verify audit log was created
      const { data: auditLogs } = await adminClient
        .from('audit_logs')
        .select('*')
        .eq('customer_id', testCustomer.id)
        .eq('event_type', 'admin_impersonation_started')
        .order('created_at', { ascending: false })
        .limit(1);
      
      expect(auditLogs).toBeDefined();
      expect(auditLogs.length).toBe(1);
      expect(auditLogs[0].details.reason).toBe(impersonationRequest.reason);
    });

    it('should validate impersonation duration limits', async () => {
      const invalidRequest = {
        ...SupportFixtures.impersonationRequests.validRequest,
        customer_id: testCustomer.id,
        duration_minutes: 300 // Exceeds maximum allowed
      };
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/impersonate`,
        'POST',
        invalidRequest
      );
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('duration exceeds maximum');
    });

    it('should require valid reason for impersonation', async () => {
      const invalidRequest = {
        customer_id: testCustomer.id,
        duration_minutes: 30
        // Missing reason
      };
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/impersonate`,
        'POST',
        invalidRequest
      );
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Reason is required');
    });
  });

  describe('4.6.4 Customer Notifications', () => {
    it('should send notification to customer', async () => {
      const notification = {
        type: 'service_alert',
        subject: 'Test Notification',
        message: 'This is a test notification for customer support.',
        priority: 'medium'
      };
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/notifications/${testCustomer.id}`,
        'POST',
        notification
      );
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.notification_id).toBeDefined();
      expect(data.data.sent_at).toBeDefined();
    });

    it('should validate notification template', async () => {
      const invalidNotification = {
        type: 'invalid_type',
        subject: 'Test',
        message: 'Test message'
      };
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/notifications/${testCustomer.id}`,
        'POST',
        invalidNotification
      );
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid notification type');
    });
  });

  describe('4.6.5 Automated Issue Detection', () => {
    it('should detect high error rates', async () => {
      // Create many error logs to trigger detection
      await createTestErrorLogs(adminClient, testCustomer.id, 20);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/issues/detect`,
        'POST'
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.issues_detected).toBeDefined();
      expect(Array.isArray(data.data.issues_detected)).toBe(true);

      if (data.data.issues_detected.length > 0) {
        const issue = data.data.issues_detected[0];
        expect(issue).toHaveProperty('issue_type');
        expect(issue).toHaveProperty('severity');
        expect(issue).toHaveProperty('description');
        expect(issue).toHaveProperty('suggested_actions');
      }
    });

    it('should detect processing performance degradation', async () => {
      // Create usage logs with slow processing times
      const slowLogs = [];
      for (let i = 0; i < 10; i++) {
        slowLogs.push({
          customer_id: testCustomer.id,
          api_key_id: testApiKey.id,
          endpoint: '/extract',
          method: 'POST',
          processing_time_ms: 15000 + Math.random() * 5000, // 15-20 seconds (slow)
          status: 'success',
          credits_used: 5,
          created_at: new Date(Date.now() - i * 60000).toISOString()
        });
      }

      await adminClient.from('usage_logs').insert(slowLogs);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/issues/detect`,
        'POST'
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      const performanceIssues = data.data.issues_detected.filter(
        (issue: any) => issue.issue_type === 'performance_degradation'
      );

      expect(performanceIssues.length).toBeGreaterThan(0);
    });

    it('should detect credit exhaustion risk', async () => {
      // Update customer to have very low credits
      await adminClient
        .from('customers')
        .update({ credits_available: 5 })
        .eq('id', testCustomer.id);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/issues/detect`,
        'POST'
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      const creditIssues = data.data.issues_detected.filter(
        (issue: any) => issue.issue_type === 'credit_exhaustion_risk'
      );

      expect(creditIssues.length).toBeGreaterThan(0);
    });
  });

  describe('4.6.6 Communication Templates', () => {
    it('should list available notification templates', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/templates`,
        'GET'
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.templates).toBeDefined();
      expect(Array.isArray(data.data.templates)).toBe(true);

      if (data.data.templates.length > 0) {
        const template = data.data.templates[0];
        expect(template).toHaveProperty('type');
        expect(template).toHaveProperty('subject');
        expect(template).toHaveProperty('message');
        expect(template).toHaveProperty('priority');
      }
    });

    it('should render template with customer data', async () => {
      const templateData = {
        template_type: 'credit_alert',
        customer_data: {
          customer_name: testCustomer.name,
          credits_remaining: 50,
          tier: testCustomer.tier
        }
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/templates/render`,
        'POST',
        templateData
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.rendered_message).toBeDefined();
      expect(data.data.rendered_subject).toBeDefined();
      expect(data.data.rendered_message).toContain(testCustomer.name);
    });
  });

  describe('4.6.7 Root Cause Analysis', () => {
    it('should analyze processing failures with context', async () => {
      const correlationId = generateTestCorrelationId();

      // Create related logs for the same correlation ID
      await adminClient.from('audit_logs').insert([
        {
          customer_id: testCustomer.id,
          event_type: 'document_upload',
          resource_type: 'document',
          action: 'upload',
          details: { correlation_id: correlationId, file_type: 'pdf', file_size: 5242880 },
          created_at: new Date(Date.now() - 5000).toISOString()
        },
        {
          customer_id: testCustomer.id,
          event_type: 'processing_error',
          resource_type: 'document',
          action: 'process',
          details: {
            correlation_id: correlationId,
            error_code: 'PROCESSING_TIMEOUT',
            error_message: 'Document processing timed out after 30 seconds'
          },
          risk_level: 'high',
          created_at: new Date().toISOString()
        }
      ]);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/errors/${correlationId}/analyze`,
        'POST'
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.root_cause_analysis).toBeDefined();
      expect(data.data.root_cause_analysis.correlation_id).toBe(correlationId);
      expect(data.data.root_cause_analysis.timeline).toBeDefined();
      expect(data.data.root_cause_analysis.suggested_resolution).toBeDefined();
    });

    it('should identify patterns in recurring errors', async () => {
      const errorCode = 'INVALID_FILE_FORMAT';

      // Create multiple similar errors
      for (let i = 0; i < 5; i++) {
        await adminClient.from('audit_logs').insert({
          customer_id: testCustomer.id,
          event_type: 'processing_error',
          resource_type: 'document',
          action: 'process',
          details: {
            error_code: errorCode,
            error_message: 'Unsupported file format',
            file_type: 'xyz'
          },
          risk_level: 'medium',
          created_at: new Date(Date.now() - i * 3600000).toISOString()
        });
      }

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminSupport}/customers/${testCustomer.id}/errors/patterns`,
        'GET'
      );

      expect(response.status).toBe(200);

      const data = await response.json();
      assertAdminApiResponse(data);
      expect(data.data.error_patterns).toBeDefined();

      const pattern = data.data.error_patterns.find(
        (p: any) => p.error_code === errorCode
      );
      expect(pattern).toBeDefined();
      expect(pattern.frequency).toBeGreaterThan(1);
      expect(pattern.suggested_resolution).toBeDefined();
    });
  });
});
