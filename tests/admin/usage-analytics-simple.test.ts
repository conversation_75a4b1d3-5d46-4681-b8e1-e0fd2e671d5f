import { describe, it, expect, beforeAll, afterAll } from 'bun:test';
import { createClient } from '@supabase/supabase-js';

/**
 * Simple integration test for Usage Analytics Dashboard
 * Tests the complete flow of the analytics system
 */
describe('Usage Analytics Dashboard - Integration Test', () => {
  let supabase: any;
  let testCustomerId: string;
  let testApiKeyId: string;
  const baseUrl = 'http://127.0.0.1:14321/functions/v1';

  beforeAll(async () => {
    // Initialize Supabase client
    const supabaseUrl = process.env.SUPABASE_URL || 'http://127.0.0.1:14321';
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    
    supabase = createClient(supabaseUrl, supabaseKey);

    // Create test customer
    const customerId = `test-customer-${Date.now()}`;
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .insert({
        customer_id: customerId,
        name: 'Analytics Integration Test',
        company_name: 'Analytics Integration Test',
        email: '<EMAIL>',
        tier: 'enterprise',
        status: 'active'
      })
      .select()
      .single();

    if (customerError) throw customerError;
    testCustomerId = customer.id;

    // Create test API key
    const { data: apiKey, error: keyError } = await supabase
      .from('api_keys')
      .insert({
        customer_id: testCustomerId,
        name: 'Analytics Test Key',
        key_type: 'production',
        key_prefix: 'skp_',
        key_hash: 'test_hash_' + Date.now(),
        credits_allocated: 10000,
        is_active: true
      })
      .select()
      .single();

    if (keyError) throw keyError;
    testApiKeyId = apiKey.id;

    // Insert some test usage data
    const usageData = [];
    const now = new Date();
    
    for (let i = 0; i < 10; i++) {
      usageData.push({
        customer_id: testCustomerId,
        api_key_id: testApiKeyId,
        endpoint: '/extract',
        method: 'POST',
        success: true,
        status_code: 200,
        credits_used: 5,
        model_cost_usd: 0.01,
        customer_price_usd: 0.02,
        processing_time_ms: 1500,
        model_used: 'gpt-4o',
        input_tokens: 500,
        output_tokens: 200,
        created_at: new Date(now.getTime() - i * 60 * 60 * 1000).toISOString()
      });
    }

    const { error: usageError } = await supabase
      .from('usage_logs')
      .insert(usageData);

    if (usageError) throw usageError;
  });

  afterAll(async () => {
    // Cleanup test data
    if (testCustomerId) {
      await supabase
        .from('customers')
        .delete()
        .eq('id', testCustomerId);
    }
  });

  it('should fetch dashboard metrics successfully', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      }
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data).toHaveProperty('active_customers');
    expect(data.data).toHaveProperty('total_api_calls_today');
    expect(data.data).toHaveProperty('revenue_today');
    expect(data.data).toHaveProperty('profit_margin');
    expect(data.data).toHaveProperty('ai_service_health');
    expect(data.data.ai_service_health).toBeInstanceOf(Array);
  });

  it('should retrieve usage analytics for the test customer', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customer_id: testCustomerId,
        date_range: {
          start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        },
        group_by: 'day'
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.customer_id).toBe(testCustomerId);
    expect(data.data.usage_metrics).toBeInstanceOf(Array);
  });

  it('should calculate revenue analytics correctly', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/revenue`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customer_id: testCustomerId,
        date_range: {
          start: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        },
        include_cost_breakdown: true
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data.revenue_metrics).toBeDefined();
    expect(data.data.revenue_metrics).toHaveProperty('total_revenue');
    expect(data.data.revenue_metrics).toHaveProperty('total_cost');
    expect(data.data.revenue_metrics).toHaveProperty('gross_profit');
    expect(data.data.revenue_metrics).toHaveProperty('profit_margin');
    
    // Verify profit margin is positive
    expect(data.data.revenue_metrics.profit_margin).toBeGreaterThanOrEqual(0);
  });

  it('should export analytics data as JSON', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        format: 'json',
        report_type: 'usage',
        date_range: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        },
        customer_id: testCustomerId
      })
    });

    expect(response.status).toBe(200);
    expect(response.headers.get('Content-Type')).toBe('application/json');
    
    const data = await response.json();
    expect(data).toHaveProperty('report_type', 'usage');
    expect(data).toHaveProperty('date_range');
    expect(data).toHaveProperty('data');
    expect(data.data).toBeInstanceOf(Array);
  });

  it('should export analytics data as CSV', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        format: 'csv',
        report_type: 'usage',
        date_range: {
          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        }
      })
    });

    expect(response.status).toBe(200);
    expect(response.headers.get('Content-Type')).toBe('text/csv');
    expect(response.headers.get('Content-Disposition')).toContain('attachment');
    
    const csvContent = await response.text();
    expect(csvContent).toContain('Date');
    expect(csvContent).toContain('Customer ID');
    expect(csvContent).toContain('Total Requests');
  });

  it('should retrieve top customers', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/customers/top`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        metric: 'usage',
        limit: 5
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data.top_customers).toBeInstanceOf(Array);
    expect(data.data.top_customers.length).toBeLessThanOrEqual(5);
  });

  it('should analyze customer behavior', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/behavior`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customer_id: testCustomerId
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data.behavior).toBeDefined();
    expect(data.data.behavior).toHaveProperty('usage_pattern');
    expect(data.data.behavior).toHaveProperty('churn_risk_score');
  });

  it('should provide usage forecasting', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/forecast`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        metric: 'usage',
        forecast_days: 7,
        customer_id: testCustomerId
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data.forecast).toBeDefined();
    expect(data.data.forecast).toHaveProperty('trend');
    expect(data.data.forecast).toHaveProperty('confidence_interval');
  });

  it('should handle invalid date ranges gracefully', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/usage`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        date_range: {
          start: new Date().toISOString(),
          end: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() // End before start
        },
        group_by: 'day'
      })
    });

    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain('Invalid date range');
  });

  it('should enforce authorization', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/dashboard`, {
      method: 'GET',
      headers: {
        'Authorization': 'Bearer invalid_token'
      }
    });

    expect(response.status).toBe(401);
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toContain('Invalid admin token');
  });

  it('should track performance metrics', async () => {
    const response = await fetch(`${baseUrl}/admin-analytics/performance`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        metrics: ['response_time', 'error_rate', 'uptime']
      })
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    
    expect(data.success).toBe(true);
    expect(data.data.performance).toBeDefined();
    expect(data.data.performance).toHaveProperty('avg_response_time_ms');
    expect(data.data.performance).toHaveProperty('error_rate');
    expect(data.data.performance).toHaveProperty('uptime_percentage');
  });
});