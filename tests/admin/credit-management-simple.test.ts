/**
 * Credit Management Simple Integration Tests
 * Epic 4 Story 4.3: Basic integration tests for quick validation
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import {
  createTestAdminClient,
  makeAdminRequest,
  TEST_CONFIG,
  setupTestIsolation
} from './admin-test-setup';
import {
  CustomerFixtures,
  createTestCustomer
} from './admin-fixtures';

// Test isolation
setupTestIsolation();

describe('Credit Management - Simple Integration', () => {
  const adminClient = createTestAdminClient();
  let testCustomer: any;

  beforeEach(async () => {
    testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
    
    // Set initial balance
    await adminClient
      .from('customers')
      .update({ credits_available: 500 })
      .eq('id', testCustomer.id);
  });

  it('should process complete credit purchase flow', async () => {
    // Purchase credits
    const response = await makeAdminRequest(
      `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
      'POST',
      {
        amount: 1000,
        payment_reference: 'stripe_pi_simple_test',
        admin_notes: 'Simple integration test purchase'
      }
    );

    expect(response.status).toBe(200);
    
    const result = await response.json();
    expect(result.success).toBe(true);
    expect(result.data.balance_after).toBe(1500); // 500 + 1000

    // Verify in database
    const { data: customer } = await adminClient
      .from('customers')
      .select('credits_available')
      .eq('id', testCustomer.id)
      .single();

    expect(customer.credits_available).toBe(1500);
  });

  it('should retrieve credit history', async () => {
    // Create a transaction first
    await adminClient.from('credit_transactions').insert({
      customer_id: testCustomer.id,
      transaction_type: 'purchase',
      amount: 500,
      balance_before: 0,
      balance_after: 500,
      payment_reference: 'test_ref'
    });

    const response = await makeAdminRequest(
      `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/history`,
      'GET'
    );

    expect(response.status).toBe(200);
    
    const result = await response.json();
    expect(result.success).toBe(true);
    expect(result.data.transactions.length).toBeGreaterThan(0);
  });

  it('should handle insufficient balance gracefully', async () => {
    const response = await makeAdminRequest(
      `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/refund`,
      'POST',
      {
        amount: 1000, // More than available (500)
        admin_notes: 'Test insufficient balance'
      }
    );

    expect(response.status).toBe(400);
    
    const result = await response.json();
    expect(result.success).toBe(false);
    expect(result.error.message).toContain('Insufficient credits');
  });

  it('should create and check credit alerts', async () => {
    // Create alert
    const alertResponse = await makeAdminRequest(
      `${TEST_CONFIG.endpoints.adminCredits}/alerts`,
      'POST',
      {
        customer_id: testCustomer.id,
        alert_type: 'low_balance',
        threshold_value: 100
      }
    );

    expect(alertResponse.status).toBe(201);

    // Set balance below threshold
    await adminClient
      .from('customers')
      .update({ credits_available: 50 })
      .eq('id', testCustomer.id);

    // Check alerts
    const checkResponse = await makeAdminRequest(
      `${TEST_CONFIG.endpoints.adminCredits}/alerts/check`,
      'POST',
      { customer_id: testCustomer.id }
    );

    expect(checkResponse.status).toBe(200);
    
    const result = await checkResponse.json();
    expect(result.data.alerts_triggered.length).toBe(1);
  });

  it('should handle authentication properly', async () => {
    // Request without auth header
    const response = await fetch(
      `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ amount: 100 })
      }
    );

    expect(response.status).toBe(401);
  });
});