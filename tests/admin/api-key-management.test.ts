/**
 * Epic 4.2: Advanced API Key Operations - Comprehensive Test Suite
 * TDD Implementation for admin-api-keys Edge Function
 */

import { describe, it, expect, _beforeEach, _afterEach } from 'bun:test';
import { 
  TEST_CONFIG, 
  createTestAdminClient, 
  cleanTestData, 
  makeAdminRequest,
  assertAdminApiResponse,
  _AdminAssertions,
  PerformanceTimer,
  _generateTestCorrelationId
} from './admin-test-setup';
import { createTestCustomer, createTestApiKey } from './admin-fixtures';

// Test endpoint
const API_KEYS_ENDPOINT = TEST_CONFIG.endpoints.adminApiKeys;

describe('Epic 4.2: Advanced API Key Operations', () => {
  const client = createTestAdminClient();
  
  beforeEach(async () => {
    await cleanTestData();
  });

  afterEach(async () => {
    await cleanTestData();
  });

  describe('API Key Creation with Enhanced Features', () => {
    it('should create API key with custom expiration date', async () => {
      const timer = new PerformanceTimer();
      timer.start();

      // Create test customer first
      const customer = await createTestCustomer(client, {
        tier: 'professional'
      });

      // Create API key with expiration
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 30);

      const apiKeyData = {
        customer_id: customer.customer_id,
        name: 'Test Expiring Key',
        key_type: 'test',
        expires_at: expiresAt.toISOString(),
        scope_restrictions: {
          allowed_endpoints: ['/document-processing', '/agent-management'],
          max_file_size: 10485760 // 10MB
        }
      };

      const response = await makeAdminRequest(API_KEYS_ENDPOINT, 'POST', apiKeyData);
      const _result = await response.json();

      // Assertions
      expect(response.status).toBe(201);
      assertAdminApiResponse(result);
      expect(result.success).toBe(true);
      expect(result.data.key).toBeDefined();
      expect(result.data.key_prefix).toMatch(/^skt_/);
      expect(result.data.expires_at).toBeDefined();
      expect(result.data.scope_restrictions).toEqual(apiKeyData.scope_restrictions);

      timer.assertMaxDuration(500);
    });

    it('should create API key with scope restrictions', async () => {
      const customer = await createTestCustomer(client, {
        tier: 'enterprise'
      });

      const scopeRestrictions = {
        allowed_endpoints: ['/document-processing'],
        allowed_agents: ['agent-001', 'agent-002'],
        max_file_size: 52428800 // 50MB
      };

      const apiKeyData = {
        customer_id: customer.customer_id,
        name: 'Scoped API Key',
        key_type: 'production',
        scope_restrictions: scopeRestrictions
      };

      const response = await makeAdminRequest(API_KEYS_ENDPOINT, 'POST', apiKeyData);
      const _result = await response.json();

      expect(response.status).toBe(201);
      expect(result.data.scope_restrictions).toEqual(scopeRestrictions);
      expect(result.data.key_prefix).toMatch(/^skp_/);
    });

    it('should reject invalid scope restrictions', async () => {
      const customer = await createTestCustomer(client);

      const invalidApiKeyData = {
        customer_id: customer.customer_id,
        name: 'Invalid Scope Key',
        key_type: 'test',
        scope_restrictions: {
          allowed_endpoints: ['invalid-endpoint'],
          max_file_size: -1 // Invalid negative size
        }
      };

      const response = await makeAdminRequest(API_KEYS_ENDPOINT, 'POST', invalidApiKeyData);
      const _result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('scope restrictions');
    });
  });

  describe('API Key Suspension Operations', () => {
    it('should suspend API key with reason', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id, {
        name: 'Test Suspend Key'
      });

      const suspensionData = {
        reason: 'Security violation detected'
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/suspend`, 
        'PUT', 
        suspensionData
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      assertAdminApiResponse(result);
      expect(result.data.suspended_at).toBeDefined();
      expect(result.data.suspension_reason).toBe(suspensionData.reason);
      expect(result.data.is_active).toBe(false);
    });

    it('should reactivate suspended API key', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id);

      // First suspend the key
      await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/suspend`, 
        'PUT', 
        { reason: 'Test suspension' }
      );

      // Then reactivate
      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/activate`, 
        'PUT'
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.suspended_at).toBeNull();
      expect(result.data.suspension_reason).toBeNull();
      expect(result.data.is_active).toBe(true);
    });

    it('should not allow activation of expired key', async () => {
      const customer = await createTestCustomer(client);
      
      // Create expired key
      const expiredDate = new Date();
      expiredDate.setDate(expiredDate.getDate() - 1);
      
      const apiKey = await createTestApiKey(client, customer.customer_id, {
        expires_at: expiredDate.toISOString()
      });

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/activate`, 
        'PUT'
      );
      const _result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('expired');
    });
  });

  describe('Scope Restriction Updates', () => {
    it('should update scope restrictions for existing key', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id, {
        scope_restrictions: {
          allowed_endpoints: ['/document-processing']
        }
      });

      const newScopeRestrictions = {
        allowed_endpoints: ['/document-processing', '/agent-management'],
        allowed_agents: ['agent-001'],
        max_file_size: 20971520 // 20MB
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/scope`, 
        'PUT', 
        { scope_restrictions: newScopeRestrictions }
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.scope_restrictions).toEqual(newScopeRestrictions);
    });

    it('should clear scope restrictions when set to empty object', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id, {
        scope_restrictions: {
          allowed_endpoints: ['/document-processing'],
          max_file_size: 10485760
        }
      });

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/scope`, 
        'PUT', 
        { scope_restrictions: {} }
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.scope_restrictions).toEqual({});
    });
  });

  describe('API Key Rotation', () => {
    it('should rotate API key with transition period', async () => {
      const customer = await createTestCustomer(client);
      const originalKey = await createTestApiKey(client, customer.customer_id);

      const rotationData = {
        transition_period_hours: 24,
        preserve_settings: true
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${originalKey.id}/rotate`, 
        'POST', 
        rotationData
      );
      const _result = await response.json();

      expect(response.status).toBe(201);
      expect(result.data.new_key).toBeDefined();
      expect(result.data.new_key.key_prefix).toMatch(/^skt_/);
      expect(result.data.transition_expires_at).toBeDefined();
      expect(result.data.old_key_id).toBe(originalKey.id);
    });

    it('should immediately deactivate old key when no transition period', async () => {
      const customer = await createTestCustomer(client);
      const originalKey = await createTestApiKey(client, customer.customer_id);

      const rotationData = {
        transition_period_hours: 0,
        preserve_settings: false
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${originalKey.id}/rotate`, 
        'POST', 
        rotationData
      );
      const _result = await response.json();

      expect(response.status).toBe(201);
      expect(result.data.new_key).toBeDefined();
      expect(result.data.transition_expires_at).toBeNull();
      
      // Verify old key is immediately deactivated
      const { data: oldKey } = await client
        .from('api_keys')
        .select('is_active')
        .eq('id', originalKey.id)
        .single();
      
      expect(oldKey.is_active).toBe(false);
    });
  });

  describe('Bulk Operations', () => {
    it('should perform bulk suspension of multiple keys', async () => {
      const customer = await createTestCustomer(client);
      
      // Create multiple API keys
      const apiKey1 = await createTestApiKey(client, customer.customer_id, { name: 'Bulk Key 1' });
      const apiKey2 = await createTestApiKey(client, customer.customer_id, { name: 'Bulk Key 2' });
      const apiKey3 = await createTestApiKey(client, customer.customer_id, { name: 'Bulk Key 3' });

      const bulkData = {
        operation: 'suspend',
        key_ids: [apiKey1.id, apiKey2.id, apiKey3.id],
        parameters: {
          reason: 'Bulk security audit'
        }
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/bulk`, 
        'POST', 
        bulkData
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.total).toBe(3);
      expect(result.data.success).toHaveLength(3);
      expect(result.data.failed).toHaveLength(0);
    });

    it('should handle partial failures in bulk operations', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id);

      const bulkData = {
        operation: 'suspend',
        key_ids: [apiKey.id, 'non-existent-key-id'],
        parameters: {
          reason: 'Bulk test with failure'
        }
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/bulk`, 
        'POST', 
        bulkData
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.total).toBe(2);
      expect(result.data.success).toHaveLength(1);
      expect(result.data.failed).toHaveLength(1);
      expect(result.data.success[0]).toBe(apiKey.id);
    });

    it('should perform bulk scope restriction updates', async () => {
      const customer = await createTestCustomer(client);
      
      const apiKey1 = await createTestApiKey(client, customer.customer_id);
      const apiKey2 = await createTestApiKey(client, customer.customer_id);

      const newScopeRestrictions = {
        allowed_endpoints: ['/document-processing'],
        max_file_size: 5242880 // 5MB
      };

      const bulkData = {
        operation: 'update_scope',
        key_ids: [apiKey1.id, apiKey2.id],
        parameters: {
          scope_restrictions: newScopeRestrictions
        }
      };

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/bulk`, 
        'POST', 
        bulkData
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.success).toHaveLength(2);
      
      // Verify scope restrictions were updated
      const { data: updatedKeys } = await client
        .from('api_keys')
        .select('scope_restrictions')
        .in('id', [apiKey1.id, apiKey2.id]);
      
      updatedKeys.forEach(key => {
        expect(key.scope_restrictions).toEqual(newScopeRestrictions);
      });
    });
  });

  describe('Usage Analytics', () => {
    it('should retrieve detailed usage analytics for API key', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id);

      // Create some usage logs for the key
      await client.from('usage_logs').insert([
        {
          id: crypto.randomUUID(),
          customer_id: customer.customer_id,
          api_key_id: apiKey.id,
          endpoint: '/document-processing',
          credits_used: 10,
          processing_time_ms: 1500,
          created_at: new Date().toISOString()
        }
      ]);

      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/usage?days=7`
      );
      const _result = await response.json();

      expect(response.status).toBe(200);
      expect(result.data.total_requests).toBeDefined();
      expect(result.data.total_credits).toBeDefined();
      expect(result.data.avg_processing_time).toBeDefined();
      expect(result.data.usage_by_endpoint).toBeDefined();
    });
  });

  describe('Security and Validation', () => {
    it('should reject requests without admin authorization', async () => {
      const response = await fetch(API_KEYS_ENDPOINT, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      expect(response.status).toBe(401);
    });

    it('should validate API key ID format in endpoints', async () => {
      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/invalid-uuid-format/suspend`, 
        'PUT',
        { reason: 'Test' }
      );
      const _result = await response.json();

      expect(response.status).toBe(400);
      expect(result.error.message).toContain('Invalid API key ID');
    });

    it('should log all administrative actions in audit logs', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id);

      await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/suspend`, 
        'PUT',
        { reason: 'Audit test' }
      );

      // Check audit log was created
      const { data: auditLogs } = await client
        .from('audit_logs')
        .select('*')
        .eq('resource_type', 'api_key')
        .eq('resource_id', apiKey.id)
        .eq('action', 'suspend');

      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0].changes).toEqual({ reason: 'Audit test' });
    });
  });

  describe('Performance Requirements', () => {
    it('should handle API key operations within 500ms', async () => {
      const timer = new PerformanceTimer();
      const customer = await createTestCustomer(client);
      
      timer.start();
      const apiKey = await createTestApiKey(client, customer.customer_id);
      
      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/suspend`, 
        'PUT',
        { reason: 'Performance test' }
      );

      expect(response.status).toBe(200);
      timer.assertMaxDuration(500);
    });

    it('should handle bulk operations efficiently', async () => {
      const timer = new PerformanceTimer();
      const customer = await createTestCustomer(client);
      
      // Create 10 API keys
      const keyIds = [];
      for (let i = 0; i < 10; i++) {
        const key = await createTestApiKey(client, customer.customer_id, { 
          name: `Bulk Test Key ${i}` 
        });
        keyIds.push(key.id);
      }

      timer.start();
      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/bulk`, 
        'POST',
        {
          operation: 'suspend',
          key_ids: keyIds,
          parameters: { reason: 'Bulk performance test' }
        }
      );

      expect(response.status).toBe(200);
      timer.assertMaxDuration(1000); // Allow slightly more time for bulk operations
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle duplicate scope restriction updates gracefully', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id, {
        scope_restrictions: { allowed_endpoints: ['/test'] }
      });

      // Update with same restrictions
      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/scope`, 
        'PUT',
        { scope_restrictions: { allowed_endpoints: ['/test'] } }
      );

      expect(response.status).toBe(200);
      expect(response.json()).resolves.toMatchObject({
        success: true,
        data: expect.objectContaining({
          scope_restrictions: { allowed_endpoints: ['/test'] }
        })
      });
    });

    it('should prevent suspension of already suspended keys', async () => {
      const customer = await createTestCustomer(client);
      const apiKey = await createTestApiKey(client, customer.customer_id);

      // First suspension
      await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/suspend`, 
        'PUT',
        { reason: 'First suspension' }
      );

      // Second suspension attempt
      const response = await makeAdminRequest(
        `${API_KEYS_ENDPOINT}/${apiKey.id}/suspend`, 
        'PUT',
        { reason: 'Second suspension' }
      );
      const _result = await response.json();

      expect(response.status).toBe(400);
      expect(result.error.message).toContain('already suspended');
    });
  });
});