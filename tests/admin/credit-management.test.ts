/**
 * Credit Management Tests
 * Epic 4 Story 4.3: Comprehensive test suite for credit transaction engine
 * Following TDD methodology with established admin patterns
 */

import { describe, it, expect, beforeEach } from 'bun:test';
import {
  createTestAdminClient,
  makeAdminRequest,
  assertAdminApiResponse,
  PerformanceTimer,
  TransactionTestHelper,
  TEST_CONFIG,
  setupTestIsolation
} from './admin-test-setup';
import {
  CustomerFixtures,
  CreditFixtures,
  createTestCustomer
} from './admin-fixtures';

// Test isolation
setupTestIsolation();

describe('Credit Management System', () => {
  const adminClient = createTestAdminClient();
  const timer = new PerformanceTimer();
  const _transactionHelper = new TransactionTestHelper();

  describe('Credit Transaction Engine', () => {
    let testCustomer: any;

    beforeEach(async () => {
      // Create test customer with initial credits
      testCustomer = await createTestCustomer(adminClient, {
        ...CustomerFixtures.validCustomer,
        tier_settings: {
          max_api_keys: 10,
          default_credit_limit: 1000,
          rate_limit_multiplier: 2
        }
      });

      // Set initial credit balance
      await adminClient
        .from('customers')
        .update({ credits_available: 500 })
        .eq('id', testCustomer.id);
    });

    it('should create credit purchase transaction successfully', async () => {
      timer.start();

      const creditData = {
        ...CreditFixtures.creditPurchase,
        customer_id: testCustomer.id
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
        'POST',
        creditData
      );

      timer.assertMaxDuration(500);

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.transaction_type).toBe('purchase');
      expect(result.data.amount).toBe(creditData.amount);
      expect(result.data.balance_after).toBe(500 + creditData.amount); // 500 initial + 1000 purchase
      expect(result.data.payment_reference).toBe(creditData.payment_reference);

      // Verify customer balance updated
      const { data: updatedCustomer } = await adminClient
        .from('customers')
        .select('credits_available, credits_purchased')
        .eq('id', testCustomer.id)
        .single();

      expect(updatedCustomer.credits_available).toBe(1500);
      expect(updatedCustomer.credits_purchased).toBe(creditData.amount);
    });

    it('should create credit refund transaction successfully', async () => {
      const refundData = {
        ...CreditFixtures.creditRefund,
        customer_id: testCustomer.id
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/refund`,
        'POST',
        refundData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.transaction_type).toBe('refund');
      expect(result.data.amount).toBe(refundData.amount);
      expect(result.data.balance_after).toBe(500 - refundData.amount); // 500 initial - 500 refund = 0
    });

    it('should create manual credit adjustment successfully', async () => {
      const adjustmentData = {
        ...CreditFixtures.creditAdjustment,
        customer_id: testCustomer.id
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/adjust`,
        'POST',
        adjustmentData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.transaction_type).toBe('adjustment');
      expect(result.data.amount).toBe(adjustmentData.amount);
      expect(result.data.balance_after).toBe(500 + adjustmentData.amount);
    });

    it('should reject deduction when insufficient balance', async () => {
      const deductionData = {
        transaction_type: 'deduction',
        amount: 1000, // More than available (500)
        admin_notes: 'Test insufficient balance'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/deduct`,
        'POST',
        deductionData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('Insufficient credits');
      expect(result.error.message).toContain('Required: 1000, Available: 500');
    });

    it('should validate required fields for credit transactions', async () => {
      const invalidData = {
        // Missing amount and transaction_type
        admin_notes: 'Invalid transaction'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
        'POST',
        invalidData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('Missing required fields');
    });

    it('should create audit log entry for credit transactions', async () => {
      const creditData = {
        ...CreditFixtures.creditPurchase,
        customer_id: testCustomer.id
      };

      await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
        'POST',
        creditData
      );

      // Verify audit log created
      const { data: auditLogs } = await adminClient
        .from('audit_logs')
        .select('*')
        .eq('customer_id', testCustomer.id)
        .eq('event_type', 'credit_transaction')
        .eq('action', 'purchase');

      expect(auditLogs).toBeDefined();
      expect(auditLogs.length).toBe(1);
      expect(auditLogs[0].resource_type).toBe('credit_transaction');
      expect(auditLogs[0].details.amount).toBe(creditData.amount);
    });

    it('should handle concurrent credit transactions safely', async () => {
      const transaction1 = CreditFixtures.creditPurchase;
      const transaction2 = { ...CreditFixtures.creditRefund, amount: 200 };

      // Execute concurrent transactions
      const [response1, response2] = await Promise.all([
        makeAdminRequest(
          `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
          'POST',
          { ...transaction1, customer_id: testCustomer.id }
        ),
        makeAdminRequest(
          `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/refund`,
          'POST',
          { ...transaction2, customer_id: testCustomer.id }
        )
      ]);

      expect(response1.status).toBe(200);
      expect(response2.status).toBe(200);

      // Final balance should be: 500 (initial) + 1000 (purchase) - 200 (refund) = 1300
      const { data: finalCustomer } = await adminClient
        .from('customers')
        .select('credits_available')
        .eq('id', testCustomer.id)
        .single();

      expect(finalCustomer.credits_available).toBe(1300);
    });
  });

  describe('Credit History and Analytics', () => {
    let testCustomer: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
      
      // Create some test transactions
      await adminClient.from('credit_transactions').insert([
        {
          customer_id: testCustomer.id,
          transaction_type: 'purchase',
          amount: 1000,
          balance_before: 0,
          balance_after: 1000,
          payment_reference: 'stripe_pi_test1',
          admin_notes: 'Initial purchase'
        },
        {
          customer_id: testCustomer.id,
          transaction_type: 'deduction',
          amount: 150,
          balance_before: 1000,
          balance_after: 850,
          admin_notes: 'API usage'
        },
        {
          customer_id: testCustomer.id,
          transaction_type: 'refund',
          amount: 50,
          balance_before: 850,
          balance_after: 800,
          admin_notes: 'Partial refund'
        }
      ]);
    });

    it('should retrieve customer credit history', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/history`,
        'GET'
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.transactions).toBeDefined();
      expect(result.data.transactions.length).toBe(3);
      expect(result.data.summary.total_purchased).toBe(1000);
      expect(result.data.summary.total_used).toBe(150);
      expect(result.data.summary.total_refunded).toBe(50);
      expect(result.data.summary.current_balance).toBe(800);
    });

    it('should filter credit history by date range', async () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/history?start_date=${yesterday.toISOString()}&limit=10`,
        'GET'
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.transactions.length).toBeGreaterThan(0);
      expect(result.data.date_range.start).toBeDefined();
      expect(result.data.date_range.end).toBeDefined();
    });

    it('should filter credit history by transaction type', async () => {
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/history?transaction_type=purchase`,
        'GET'
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.transactions.every((t: any) => t.transaction_type === 'purchase')).toBe(true);
    });

    it('should return 404 for non-existent customer', async () => {
      const fakeCustomerId = '00000000-0000-0000-0000-000000000000';
      
      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${fakeCustomerId}/history`,
        'GET'
      );

      expect(response.status).toBe(404);
      
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('Customer not found');
    });
  });

  describe('Credit Alerts System', () => {
    let testCustomer: any;

    beforeEach(async () => {
      testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
    });

    it('should configure low balance alerts for customer', async () => {
      const alertConfig = {
        alert_type: 'low_balance',
        threshold_value: 100,
        notification_channels: {
          email: true,
          webhook: true
        }
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/alerts`,
        'POST',
        {
          customer_id: testCustomer.id,
          ...alertConfig
        }
      );

      expect(response.status).toBe(201);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.alert_type).toBe('low_balance');
      expect(result.data.threshold_value).toBe(100);
      expect(result.data.is_enabled).toBe(true);
    });

    it('should update existing alert configuration', async () => {
      // First create an alert
      const { data: alert } = await adminClient
        .from('credit_alerts')
        .insert({
          customer_id: testCustomer.id,
          alert_type: 'low_balance',
          threshold_value: 50,
          is_enabled: true
        })
        .select()
        .single();

      const updateData = {
        threshold_value: 200,
        is_enabled: false
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/alerts/${alert.id}`,
        'PUT',
        updateData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.threshold_value).toBe(200);
      expect(result.data.is_enabled).toBe(false);
    });

    it('should list customer alert configurations', async () => {
      // Create test alerts
      await adminClient.from('credit_alerts').insert([
        {
          customer_id: testCustomer.id,
          alert_type: 'low_balance',
          threshold_value: 100,
          is_enabled: true
        },
        {
          customer_id: testCustomer.id,
          alert_type: 'zero_balance',
          is_enabled: true
        }
      ]);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/alerts`,
        'GET'
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.alerts.length).toBe(2);
      expect(result.data.alerts.some((a: any) => a.alert_type === 'low_balance')).toBe(true);
      expect(result.data.alerts.some((a: any) => a.alert_type === 'zero_balance')).toBe(true);
    });

    it('should trigger alert when balance threshold reached', async () => {
      // Set up low balance alert
      await adminClient.from('credit_alerts').insert({
        customer_id: testCustomer.id,
        alert_type: 'low_balance',
        threshold_value: 100,
        is_enabled: true
      });

      // Set customer balance to trigger alert
      await adminClient
        .from('customers')
        .update({ credits_available: 50 })
        .eq('id', testCustomer.id);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/alerts/check`,
        'POST',
        { customer_id: testCustomer.id }
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.alerts_triggered.length).toBe(1);
      expect(result.data.alerts_triggered[0].alert_type).toBe('low_balance');
      expect(result.data.alerts_triggered[0].current_balance).toBe(50);
    });
  });

  describe('Bulk Credit Operations', () => {
    let testCustomers: any[];

    beforeEach(async () => {
      // Create multiple test customers
      testCustomers = await Promise.all([
        createTestCustomer(adminClient, { ...CustomerFixtures.validCustomer, customer_id: 'test-bulk-1' }),
        createTestCustomer(adminClient, { ...CustomerFixtures.validCustomer, customer_id: 'test-bulk-2' }),
        createTestCustomer(adminClient, { ...CustomerFixtures.validCustomer, customer_id: 'test-bulk-3' })
      ]);

      // Set initial balances
      for (const customer of testCustomers) {
        await adminClient
          .from('customers')
          .update({ credits_available: 500 })
          .eq('id', customer.id);
      }
    });

    it('should perform bulk credit refund operation', async () => {
      const bulkData = {
        operation: 'refund',
        customer_ids: testCustomers.map(c => c.id),
        amount: 250,
        admin_notes: 'Bulk refund for service incident'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/bulk`,
        'POST',
        bulkData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.operation).toBe('refund');
      expect(result.data.total_customers).toBe(3);
      expect(result.data.success_count).toBe(3);
      expect(result.data.error_count).toBe(0);

      // Verify all customers received refund
      for (const customer of testCustomers) {
        const { data: updatedCustomer } = await adminClient
          .from('customers')
          .select('credits_available')
          .eq('id', customer.id)
          .single();

        expect(updatedCustomer.credits_available).toBe(250); // 500 - 250 refund
      }
    });

    it('should handle bulk operation with some failures', async () => {
      // Create scenario where one customer has insufficient balance
      await adminClient
        .from('customers')
        .update({ credits_available: 100 }) // Insufficient for 250 refund
        .eq('id', testCustomers[0].id);

      const bulkData = {
        operation: 'refund',
        customer_ids: testCustomers.map(c => c.id),
        amount: 250,
        admin_notes: 'Mixed success bulk operation'
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/bulk`,
        'POST',
        bulkData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.success_count).toBe(2);
      expect(result.data.error_count).toBe(1);
      expect(result.data.results.some((r: any) => !r.success)).toBe(true);
    });

    it('should validate bulk operation parameters', async () => {
      const invalidBulkData = {
        operation: 'invalid_operation',
        customer_ids: testCustomers.map(c => c.id),
        amount: 250
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/bulk`,
        'POST',
        invalidBulkData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error.message).toContain('Invalid operation type');
    });

    it('should enforce bulk operation limits', async () => {
      // Create too many customer IDs (>100)
      const tooManyIds = Array(101).fill(testCustomers[0].id);

      const bulkData = {
        operation: 'adjustment',
        customer_ids: tooManyIds,
        amount: 100
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/bulk`,
        'POST',
        bulkData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.error.message).toContain('Maximum 100 customers allowed');
    });
  });

  describe('Credit Pool Management (Enterprise)', () => {
    let enterpriseCustomer: any;

    beforeEach(async () => {
      enterpriseCustomer = await createTestCustomer(adminClient, CustomerFixtures.enterpriseCustomer);
    });

    it('should create enterprise credit pool', async () => {
      const poolData = {
        pool_name: 'Development Team Pool',
        total_credits: 10000,
        expires_at: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString()
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${enterpriseCustomer.id}/pools`,
        'POST',
        poolData
      );

      expect(response.status).toBe(201);
      
      const result = await response.json();
      assertAdminApiResponse(result);
      
      expect(result.success).toBe(true);
      expect(result.data.pool_name).toBe(poolData.pool_name);
      expect(result.data.total_credits).toBe(poolData.total_credits);
      expect(result.data.allocated_credits).toBe(0);
      expect(result.data.available_credits).toBe(poolData.total_credits);
    });

    it('should allocate credits from pool to API key', async () => {
      // Create pool first
      const { data: pool } = await adminClient
        .from('credit_pools')
        .insert({
          customer_id: enterpriseCustomer.id,
          pool_name: 'Test Pool',
          total_credits: 5000
        })
        .select()
        .single();

      // Create API key
      const _testApiKey = await createTestApiKey(adminClient, enterpriseCustomer.id);

      const allocationData = {
        api_key_id: testApiKey.id,
        amount: 1000
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/pools/${pool.id}/allocate`,
        'POST',
        allocationData
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.allocated_amount).toBe(1000);
      expect(result.data.pool_remaining).toBe(4000);

      // Verify API key updated
      const { data: updatedKey } = await adminClient
        .from('api_keys')
        .select('credit_pool_id, credits_allocated')
        .eq('id', testApiKey.id)
        .single();

      expect(updatedKey.credit_pool_id).toBe(pool.id);
      expect(updatedKey.credits_allocated).toBe(1000);
    });

    it('should prevent over-allocation from pool', async () => {
      const { data: pool } = await adminClient
        .from('credit_pools')
        .insert({
          customer_id: enterpriseCustomer.id,
          pool_name: 'Small Pool',
          total_credits: 100
        })
        .select()
        .single();

      const _testApiKey = await createTestApiKey(adminClient, enterpriseCustomer.id);

      const allocationData = {
        api_key_id: testApiKey.id,
        amount: 150 // More than pool total
      };

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/pools/${pool.id}/allocate`,
        'POST',
        allocationData
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.error.message).toContain('Insufficient credits in pool');
    });

    it('should list customer credit pools', async () => {
      // Create multiple pools
      await adminClient.from('credit_pools').insert([
        {
          customer_id: enterpriseCustomer.id,
          pool_name: 'Pool A',
          total_credits: 1000,
          allocated_credits: 300
        },
        {
          customer_id: enterpriseCustomer.id,
          pool_name: 'Pool B',
          total_credits: 2000,
          allocated_credits: 0
        }
      ]);

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${enterpriseCustomer.id}/pools`,
        'GET'
      );

      expect(response.status).toBe(200);
      
      const result = await response.json();
      expect(result.data.pools.length).toBe(2);
      expect(result.data.total_credits).toBe(3000);
      expect(result.data.total_allocated).toBe(300);
      expect(result.data.total_available).toBe(2700);
    });
  });

  describe('Performance and Error Handling', () => {
    it('should handle invalid customer IDs gracefully', async () => {
      const invalidId = 'not-a-uuid';

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${invalidId}/purchase`,
        'POST',
        CreditFixtures.creditPurchase
      );

      expect(response.status).toBe(400);
      
      const result = await response.json();
      expect(result.error.message).toContain('Invalid customer ID format');
    });

    it('should meet performance requirements for credit transactions', async () => {
      const testCustomer = await createTestCustomer(adminClient, CustomerFixtures.validCustomer);
      
      timer.start();

      const response = await makeAdminRequest(
        `${TEST_CONFIG.endpoints.adminCredits}/${testCustomer.id}/purchase`,
        'POST',
        CreditFixtures.creditPurchase
      );

      timer.assertMaxDuration(500); // Must complete within 500ms

      expect(response.status).toBe(200);
    });

    it('should handle database transaction rollback on errors', async () => {
      await transactionHelper.testRollback(async () => {
        // This should fail and rollback
        await makeAdminRequest(
          `${TEST_CONFIG.endpoints.adminCredits}/invalid-endpoint`,
          'POST',
          {}
        );
      });
    });

    it('should validate authentication headers', async () => {
      const response = await fetch(
        `${TEST_CONFIG.endpoints.adminCredits}/test-customer/purchase`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
            // Missing Authorization header
          },
          body: JSON.stringify(CreditFixtures.creditPurchase)
        }
      );

      expect(response.status).toBe(401);
      
      const result = await response.json();
      expect(result.error.message).toContain('Missing or invalid authorization');
    });

    it('should handle CORS preflight requests', async () => {
      const response = await fetch(TEST_CONFIG.endpoints.adminCredits, {
        method: 'OPTIONS'
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
    });
  });
});