// ================================================================================
// SECURITY TESTS - Encryption and Data Protection
// ================================================================================

import { describe, it, expect, beforeAll } from 'bun:test';
import { DataEncryption, PIIProtection, KeyManagement, SecureConfig } from '../../supabase/functions/_shared/encryption.ts';

describe('Encryption and Data Protection', () => {
  beforeAll(async () => {
    // Set up test environment with encryption key
    Deno.env.set('ENCRYPTION_MASTER_KEY', DataEncryption.generateMasterKey());
    Deno.env.set('API_KEY_SALT', 'test-salt-for-security-testing');
    Deno.env.set('JWT_SECRET', 'test-jwt-secret-minimum-32-characters-long');
    Deno.env.set('SUPABASE_SERVICE_ROLE_KEY', 'test-service-role-key');
    
    await DataEncryption.initialize();
    await SecureConfig.initialize();
  });
  
  describe('DataEncryption', () => {
    it('should encrypt and decrypt data correctly', async () => {
      const testData = 'Sensitive customer information';
      
      const encrypted = await DataEncryption.encrypt(testData);
      const decrypted = await DataEncryption.decrypt(encrypted);
      
      expect(decrypted).toBe(testData);
      expect(encrypted).not.toBe(testData);
      expect(encrypted.length).toBeGreaterThan(testData.length);
    });
    
    it('should produce different ciphertexts for same plaintext', async () => {
      const testData = 'Same plaintext data';
      
      const encrypted1 = await DataEncryption.encrypt(testData);
      const encrypted2 = await DataEncryption.encrypt(testData);
      
      expect(encrypted1).not.toBe(encrypted2); // Different due to random IV
      
      const decrypted1 = await DataEncryption.decrypt(encrypted1);
      const decrypted2 = await DataEncryption.decrypt(encrypted2);
      
      expect(decrypted1).toBe(testData);
      expect(decrypted2).toBe(testData);
    });
    
    it('should handle empty and special characters', async () => {
      const testCases = [
        '',
        ' ',
        '\n\t\r',
        '🔒💾🛡️',
        'Special chars: !@#$%^&*()_+-=[]{}|;:,.<>?',
        'Unicode: αβγδε ñáéíóú 中文 🚀',
      ];
      
      for (const testData of testCases) {
        const encrypted = await DataEncryption.encrypt(testData);
        const decrypted = await DataEncryption.decrypt(encrypted);
        
        expect(decrypted).toBe(testData);
      }
    });
    
    it('should handle large data efficiently', async () => {
      const largeData = 'x'.repeat(100000); // 100KB
      const startTime = Date.now();
      
      const encrypted = await DataEncryption.encrypt(largeData);
      const decrypted = await DataEncryption.decrypt(encrypted);
      
      const endTime = Date.now();
      
      expect(decrypted).toBe(largeData);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete in less than 1 second
    });
    
    it('should fail gracefully with invalid encrypted data', async () => {
      const invalidData = [
        'invalid-base64-data',
        'bm90LXZhbGlkLWVuY3J5cHRlZC1kYXRh', // Valid base64 but not encrypted
        '', // Empty string
      ];
      
      for (const invalid of invalidData) {
        await expect(DataEncryption.decrypt(invalid)).rejects.toThrow();
      }
    });
    
    it('should generate cryptographically secure master keys', () => {
      const key1 = DataEncryption.generateMasterKey();
      const key2 = DataEncryption.generateMasterKey();
      
      expect(key1).not.toBe(key2);
      expect(key1.length).toBeGreaterThan(40); // Base64 encoded 32 bytes
      expect(key2.length).toBeGreaterThan(40);
      
      // Should be valid base64
      expect(() => atob(key1)).not.toThrow();
      expect(() => atob(key2)).not.toThrow();
    });
    
    it('should create and verify hashes correctly', async () => {
      const testData = 'password123';
      
      const hash1 = await DataEncryption.hash(testData);
      const hash2 = await DataEncryption.hash(testData);
      
      // Different hashes due to random salt
      expect(hash1).not.toBe(hash2);
      
      // Both should verify correctly
      expect(await DataEncryption.verifyHash(testData, hash1)).toBe(true);
      expect(await DataEncryption.verifyHash(testData, hash2)).toBe(true);
      
      // Wrong data should not verify
      expect(await DataEncryption.verifyHash('wrongdata', hash1)).toBe(false);
    });
    
    it('should use custom salt for hashing', async () => {
      const testData = 'test';
      const customSalt = 'custom-salt';
      
      const hash1 = await DataEncryption.hash(testData, customSalt);
      const hash2 = await DataEncryption.hash(testData, customSalt);
      
      // Same salt should produce same hash
      expect(hash1).toBe(hash2);
      
      expect(await DataEncryption.verifyHash(testData, hash1)).toBe(true);
    });
  });
  
  describe('PIIProtection', () => {
    it('should detect email addresses', () => {
      const texts = [
        'Contact: <EMAIL>',
        '<NAME_EMAIL>',
        'Email: <EMAIL>',
      ];
      
      for (const text of texts) {
        expect(PIIProtection.containsPII(text)).toBe(true);
        
        const pii = PIIProtection.extractPII(text);
        expect(pii.some(item => item.type === 'email')).toBe(true);
        
        const masked = PIIProtection.maskPII(text);
        expect(masked).not.toContain('@example.com');
        expect(masked).toContain('*');
      }
    });
    
    it('should detect phone numbers', () => {
      const texts = [
        'Call: (*************',
        'Phone: ************',
        'Mobile: ******-123-4567',
        'Contact: 5551234567',
      ];
      
      for (const text of texts) {
        expect(PIIProtection.containsPII(text)).toBe(true);
        
        const pii = PIIProtection.extractPII(text);
        expect(pii.some(item => item.type === 'phone')).toBe(true);
        
        const masked = PIIProtection.maskPII(text);
        expect(masked).toContain('*');
      }
    });
    
    it('should detect SSN patterns', () => {
      const texts = [
        'SSN: ***********',
        'Social Security: ***********',
      ];
      
      for (const text of texts) {
        expect(PIIProtection.containsPII(text)).toBe(true);
        
        const pii = PIIProtection.extractPII(text);
        expect(pii.some(item => item.type === 'ssn')).toBe(true);
        
        const masked = PIIProtection.maskPII(text);
        expect(masked).not.toContain('***********');
        expect(masked).toContain('*');
      }
    });
    
    it('should detect credit card numbers', () => {
      const texts = [
        'Card: 4532 1234 5678 9012',
        'CC: 4532-1234-5678-9012',
        'Payment: ****************',
      ];
      
      for (const text of texts) {
        expect(PIIProtection.containsPII(text)).toBe(true);
        
        const pii = PIIProtection.extractPII(text);
        expect(pii.some(item => item.type === 'credit_card')).toBe(true);
        
        const masked = PIIProtection.maskPII(text);
        expect(masked).toContain('*');
        expect(masked).not.toContain('1234 5678');
      }
    });
    
    it('should detect IP addresses', () => {
      const texts = [
        'Server: ***********',
        'IP: ********',
        'Connect to ************',
      ];
      
      for (const text of texts) {
        expect(PIIProtection.containsPII(text)).toBe(true);
        
        const pii = PIIProtection.extractPII(text);
        expect(pii.some(item => item.type === 'ip_address')).toBe(true);
        
        const masked = PIIProtection.maskPII(text);
        expect(masked).toContain('*');
      }
    });
    
    it('should not detect false positives', () => {
      const safePhrases = [
        'Please process this invoice',
        'Extract the total amount',
        'Document contains line items',
        'Company address and contact info',
        'Product code: ABC-123-XYZ',
      ];
      
      for (const phrase of safePhrases) {
        expect(PIIProtection.containsPII(phrase)).toBe(false);
        
        const pii = PIIProtection.extractPII(phrase);
        expect(pii).toEqual([]);
        
        const masked = PIIProtection.maskPII(phrase);
        expect(masked).toBe(phrase); // Should be unchanged
      }
    });
    
    it('should properly mask PII preserving readability', () => {
      const text = 'Contact <NAME_EMAIL> or (*************';
      const masked = PIIProtection.maskPII(text);
      
      // Should preserve some characters for readability
      expect(masked).toContain('jo');
      expect(masked).toContain('.com');
      expect(masked).toContain('(5');
      expect(masked).toContain('67');
      
      // Should contain masking characters
      expect(masked).toContain('*');
      
      // Should not contain full sensitive data
      expect(masked).not.toContain('john.doe@example');
      expect(masked).not.toContain('555) 123-4');
    });
  });
  
  describe('KeyManagement', () => {
    it('should generate valid API keys', () => {
      const testKey = KeyManagement.generateApiKey('skt', 'test-customer-id');
      
      expect(testKey.key).toMatch(/^skt_live_[a-zA-Z0-9]{32}$/);
      expect(testKey.hash).toBeTruthy();
      expect(testKey.metadata.key_type).toBe('skt');
      expect(testKey.metadata.customer_id).toBe('test-customer-id');
      expect(testKey.metadata.version).toBe('1');
    });
    
    it('should generate different keys for same customer', () => {
      const key1 = KeyManagement.generateApiKey('skt', 'customer-1');
      const key2 = KeyManagement.generateApiKey('skt', 'customer-1');
      
      expect(key1.key).not.toBe(key2.key);
      expect(key1.hash).not.toBe(key2.hash);
    });
    
    it('should generate different key types correctly', () => {
      const testKey = KeyManagement.generateApiKey('skp', 'prod-customer');
      const prodKey = KeyManagement.generateApiKey('skp', 'prod-customer');
      
      expect(testKey.key).toMatch(/^skt_live_/);
      expect(prodKey.key).toMatch(/^skp_live_/);
    });
    
    it('should validate API key formats correctly', () => {
      const validKeys = [
        'skt_live_abcd1234efgh5678ijkl9012mnop3456',
        'skp_live_ABCD1234EFGH5678IJKL9012MNOP3456',
        'skt_live_abcd1234EFGH5678ijkl9012MNOP3456',
      ];
      
      const invalidKeys = [
        'sk_live_short',
        'skt_test_abcd1234efgh5678ijkl9012mnop3456',
        'invalid_key_format',
        'skt_live_',
        'skt_live_abc!@#$%^&*()123456789012345678',
      ];
      
      for (const key of validKeys) {
        expect(KeyManagement.validateApiKeyFormat(key)).toBe(true);
      }
      
      for (const key of invalidKeys) {
        expect(KeyManagement.validateApiKeyFormat(key)).toBe(false);
      }
    });
    
    it('should extract key types correctly', () => {
      expect(KeyManagement.extractKeyType('skt_live_12345678901234567890123456789012')).toBe('test');
      expect(KeyManagement.extractKeyType('skp_live_12345678901234567890123456789012')).toBe('production');
      expect(KeyManagement.extractKeyType('invalid_key')).toBe(null);
    });
    
    it('should detect when key rotation is needed', () => {
      const oldDate = new Date(Date.now() - 100 * 24 * 60 * 60 * 1000); // 100 days ago
      const recentDate = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000); // 10 days ago
      
      expect(KeyManagement.isKeyRotationNeeded(oldDate)).toBe(true);
      expect(KeyManagement.isKeyRotationNeeded(recentDate)).toBe(false);
    });
    
    it('should hash API keys consistently', () => {
      const testKey = 'skt_live_test12345678901234567890123456';
      
      const hash1 = KeyManagement.hashApiKey(testKey);
      const hash2 = KeyManagement.hashApiKey(testKey);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toBeTruthy();
      expect(typeof hash1).toBe('string');
    });
  });
  
  describe('SecureConfig', () => {
    it('should initialize with required environment variables', async () => {
      expect(() => SecureConfig.get('ENCRYPTION_MASTER_KEY')).not.toThrow();
      expect(SecureConfig.get('ENCRYPTION_MASTER_KEY')).toBeTruthy();
    });
    
    it('should detect production environment correctly', () => {
      const originalEnv = Deno.env.get('ENVIRONMENT');
      
      Deno.env.set('ENVIRONMENT', 'production');
      expect(SecureConfig.isProduction()).toBe(true);
      
      Deno.env.set('ENVIRONMENT', 'development');
      expect(SecureConfig.isProduction()).toBe(false);
      
      // Restore original environment
      if (originalEnv) {
        Deno.env.set('ENVIRONMENT', originalEnv);
      } else {
        Deno.env.delete('ENVIRONMENT');
      }
    });
    
    it('should provide appropriate security headers', () => {
      const headers = SecureConfig.getSecurityHeaders();
      
      expect(headers['X-Content-Type-Options']).toBe('nosniff');
      expect(headers['X-Frame-Options']).toBe('DENY');
      expect(headers['X-XSS-Protection']).toBe('1; mode=block');
      expect(headers['Referrer-Policy']).toBe('strict-origin-when-cross-origin');
      expect(headers['Content-Security-Policy']).toContain("default-src 'self'");
    });
    
    it('should throw error for non-existent config keys', () => {
      expect(() => {
        SecureConfig.get('NON_EXISTENT_KEY');
      }).not.toThrow(); // Should return undefined, not throw
      
      expect(SecureConfig.get('NON_EXISTENT_KEY')).toBeUndefined();
    });
  });
  
  describe('Integration Tests', () => {
    it('should handle end-to-end encryption workflow', async () => {
      const sensitiveData = {
        customerEmail: '<EMAIL>',
        creditCard: '4532-1234-5678-9012',
        ssn: '***********',
        notes: 'Confidential customer information',
      };
      
      // Convert to string for encryption
      const dataString = JSON.stringify(sensitiveData);
      
      // Encrypt the data
      const encrypted = await DataEncryption.encrypt(dataString);
      
      // Verify it's actually encrypted
      expect(encrypted).not.toContain('<EMAIL>');
      expect(encrypted).not.toContain('4532-1234-5678-9012');
      
      // Decrypt and verify
      const decrypted = await DataEncryption.decrypt(encrypted);
      const parsedData = JSON.parse(decrypted);
      
      expect(parsedData.customerEmail).toBe(sensitiveData.customerEmail);
      expect(parsedData.creditCard).toBe(sensitiveData.creditCard);
      expect(parsedData.ssn).toBe(sensitiveData.ssn);
    });
    
    it('should handle PII masking for logging', () => {
      const logData = {
        user: '<EMAIL>',
        action: 'document_upload',
        ip: '*************',
        phone: '(*************',
        document: 'invoice_with_ssn_***********.pdf',
      };
      
      const logString = JSON.stringify(logData);
      const maskedLog = PIIProtection.maskPII(logString);
      
      // Should not contain full sensitive information
      expect(maskedLog).not.toContain('<EMAIL>');
      expect(maskedLog).not.toContain('*************');
      expect(maskedLog).not.toContain('(*************');
      expect(maskedLog).not.toContain('***********');
      
      // Should still be parseable JSON
      expect(() => JSON.parse(maskedLog.replace(/\*/g, 'x'))).not.toThrow();
    });
    
    it('should maintain performance under concurrent load', async () => {
      const concurrentOperations = 100;
      const testData = 'Performance test data';
      
      const startTime = Date.now();
      
      const promises = Array(concurrentOperations).fill(null).map(async () => {
        const encrypted = await DataEncryption.encrypt(testData);
        const decrypted = await DataEncryption.decrypt(encrypted);
        return decrypted === testData;
      });
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      // All operations should succeed
      expect(results.every(result => result === true)).toBe(true);
      
      // Should complete in reasonable time (less than 5 seconds)
      expect(endTime - startTime).toBeLessThan(5000);
      
      // Average time per operation should be reasonable
      const avgTime = (endTime - startTime) / concurrentOperations;
      expect(avgTime).toBeLessThan(50); // Less than 50ms per operation
    });
  });
});