/**
 * Test Fixtures for Agent Performance Tracking
 * 
 * Provides reusable test data with proper database relationships
 * and cleanup utilities for isolated test runs
 */

import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/database.types';
import type { AgentPerformanceMetrics } from '../../types/agent-performance.types';

export interface TestFixtures {
  customer: {
    id: string;
    email: string;
    name: string;
  };
  defaultAgent: {
    id: string;
    name: string;
    category: string;
    customer_id: null;
    is_default: true;
  };
  customAgent: {
    id: string;
    name: string;
    category: string;
    customer_id: string;
    cloned_from: string;
    is_default: false;
  };
  document: {
    id: string;
    customer_id: string;
    filename: string;
    file_type: string;
  };
}

export class TestFixtureManager {
  private supabase: ReturnType<typeof createClient<Database>>;
  private createdFixtures: string[] = [];

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient<Database>(supabaseUrl, supabaseKey);
  }

  /**
   * Create complete test fixtures with proper relationships
   */
  async createFixtures(): Promise<TestFixtures> {
    try {
      // Create customer
      const customer = {
        id: crypto.randomUUID(),
        customer_id: `test-${crypto.randomUUID()}`,
        email: '<EMAIL>',
        name: 'Test Customer',
        tier: 'starter' as const,
        status: 'active' as const,
        credits_available: 100,
        credits_used: 0,
        billing_cycle: 'monthly' as const,
        created_at: new Date().toISOString()
      };

      const { error: customerError } = await this.supabase
        .from('customers')
        .insert(customer);

      if (customerError) {
        throw new Error(`Failed to create test customer: ${customerError.message}`);
      }

      this.createdFixtures.push(`customers:${customer.id}`);

      // Create default agent
      const defaultAgent = {
        id: crypto.randomUUID(),
        agent_id: `default-test-${crypto.randomUUID()}`,
        name: 'Test Default Invoice Agent',
        category: 'invoice',
        description: 'Test default agent for invoice processing',
        system_prompt: 'Extract invoice data accurately',
        prompt: 'Extract invoice data accurately',
        output_schema: {
          type: 'object',
          properties: {
            invoice_number: { type: 'string' },
            total_amount: { type: 'number' }
          }
        },
        json_schema: {
          type: 'object',
          properties: {
            invoice_number: { type: 'string' },
            total_amount: { type: 'number' }
          }
        },
        processing_config: {},
        customer_id: null,
        is_default: true,
        is_customizable: true,
        status: 'active' as const,
        created_at: new Date().toISOString()
      };

      const { error: defaultAgentError } = await this.supabase
        .from('agents')
        .insert(defaultAgent);

      if (defaultAgentError) {
        throw new Error(`Failed to create test default agent: ${defaultAgentError.message}`);
      }

      this.createdFixtures.push(`agents:${defaultAgent.id}`);

      // Create custom agent (cloned from default)
      const customAgent = {
        id: crypto.randomUUID(),
        agent_id: `custom-test-${crypto.randomUUID()}`,
        name: 'Test Custom Invoice Agent',
        category: 'invoice',
        description: 'Test custom agent cloned from default',
        system_prompt: 'Extract invoice data with high accuracy and include confidence scores',
        prompt: 'Extract invoice data with high accuracy and include confidence scores',
        output_schema: {
          type: 'object',
          properties: {
            invoice_number: { type: 'string' },
            total_amount: { type: 'number' },
            confidence: { type: 'number' }
          }
        },
        json_schema: {
          type: 'object',
          properties: {
            invoice_number: { type: 'string' },
            total_amount: { type: 'number' },
            confidence: { type: 'number' }
          }
        },
        processing_config: {},
        customer_id: customer.id,
        cloned_from: defaultAgent.id,
        is_default: false,
        is_customizable: true,
        status: 'active' as const,
        created_at: new Date().toISOString()
      };

      const { error: customAgentError } = await this.supabase
        .from('agents')
        .insert(customAgent);

      if (customAgentError) {
        throw new Error(`Failed to create test custom agent: ${customAgentError.message}`);
      }

      this.createdFixtures.push(`agents:${customAgent.id}`);

      // Create test document
      const document = {
        id: crypto.randomUUID(),
        customer_id: customer.id,
        document_hash: crypto.randomUUID(),
        original_filename: 'test-invoice.pdf',
        file_size: 1024,
        mime_type: 'application/pdf',
        agent_id: defaultAgent.id,
        processing_time_ms: 2500,
        storage_path: '/tmp/test-invoice.pdf',
        content_preview: 'Invoice from Test Company...',
        status: 'processed' as const,
        created_at: new Date().toISOString()
      };

      const { error: documentError } = await this.supabase
        .from('documents')
        .insert(document);

      if (documentError) {
        throw new Error(`Failed to create test document: ${documentError.message}`);
      }

      this.createdFixtures.push(`documents:${document.id}`);

      return {
        customer: {
          id: customer.id,
          email: customer.email,
          name: customer.name
        },
        defaultAgent: {
          id: defaultAgent.id,
          name: defaultAgent.name,
          category: defaultAgent.category,
          customer_id: null,
          is_default: true
        },
        customAgent: {
          id: customAgent.id,
          name: customAgent.name,
          category: customAgent.category,
          customer_id: customAgent.customer_id!,
          cloned_from: customAgent.cloned_from!,
          is_default: false
        },
        document: {
          id: document.id,
          customer_id: document.customer_id,
          filename: document.original_filename!,
          file_type: document.mime_type!
        }
      };

    } catch (error) {
      // If creation fails, clean up any partial fixtures
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Create performance metrics with valid references
   */
  createValidMetrics(fixtures: TestFixtures, overrides: Partial<AgentPerformanceMetrics> = {}): AgentPerformanceMetrics {
    return {
      agent_id: fixtures.customAgent.id,
      customer_id: fixtures.customer.id,
      document_type: 'invoice',
      processing_time_ms: 2500,
      accuracy_score: 0.95,
      confidence_score: 0.88,
      success: true,
      model_used: 'openai/gpt-4o',
      cost_usd: 0.025,
      timestamp: new Date(),
      correlation_id: `test-${crypto.randomUUID()}`,
      ...overrides
    };
  }

  /**
   * Create failed metrics for error testing
   */
  createFailedMetrics(fixtures: TestFixtures, overrides: Partial<AgentPerformanceMetrics> = {}): AgentPerformanceMetrics {
    return this.createValidMetrics(fixtures, {
      success: false,
      error_type: 'timeout',
      processing_time_ms: 15000,
      accuracy_score: 0.0,
      confidence_score: 0.0,
      ...overrides
    });
  }

  /**
   * Clean up all created fixtures
   */
  async cleanup(): Promise<void> {
    const errors: string[] = [];

    for (const fixture of this.createdFixtures.reverse()) {
      try {
        const [table, id] = fixture.split(':');
        const { error } = await this.supabase
          .from(table as any)
          .delete()
          .eq('id', id);

        if (error) {
          errors.push(`Failed to delete ${table}:${id} - ${error.message}`);
        }
      } catch (error) {
        errors.push(`Error cleaning up ${fixture}: ${error}`);
      }
    }

    this.createdFixtures = [];

    if (errors.length > 0) {
      console.warn('Test cleanup warnings:', errors);
    }
  }

  /**
   * Wait for database triggers to complete
   */
  async waitForAggregation(delayMs: number = 100): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, delayMs));
  }

  /**
   * Get Supabase client for direct queries in tests
   */
  getSupabase() {
    return this.supabase;
  }
}

/**
 * Factory for creating test fixture manager
 */
export function createTestFixtures(): TestFixtureManager {
  return new TestFixtureManager(
    'http://127.0.0.1:14321',
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU'
  );
}