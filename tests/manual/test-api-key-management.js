#!/usr/bin/env node

/**
 * Manual Integration Tests for API Key Management
 * 
 * This script provides interactive testing for the API key management system.
 * Run with: npm run test:manual:keys
 */

const crypto = require('crypto');

// Test configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
const SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

class ApiKeyTester {
  constructor() {
    this.testCustomerId = `test-customer-${Date.now()}`;
    this.generatedKeys = [];
  }

  async runAllTests() {
    console.log('🚀 Starting API Key Management Integration Tests');
    console.log('─'.repeat(60));
    
    try {
      await this.testKeyGeneration();
      await this.testKeyValidation();
      await this.testKeyManagement();
      await this.testCreditSystem();
      await this.testRetentionPolicy();
      
      console.log('\n✅ All tests completed successfully!');
    } catch (error) {
      console.error('\n❌ Test suite failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }

  async testKeyGeneration() {
    console.log('\n📋 Testing Key Generation...');
    
    // Test 1: Generate test key
    console.log('  🔑 Generating test key (skt_)...');
    const testKeyResponse = await this.generateKey('test', 100);
    
    if (!testKeyResponse.success) {
      throw new Error(`Test key generation failed: ${testKeyResponse.error}`);
    }
    
    const testKey = testKeyResponse.data.rawKey;
    this.generatedKeys.push(testKey);
    
    console.log(`    ✅ Generated: ${testKey.substring(0, 15)}...`);
    console.log(`    ✅ Key ID: ${testKeyResponse.data.keyId}`);
    console.log(`    ✅ Credits: ${testKeyResponse.data.credits}`);
    console.log(`    ✅ Expires: ${testKeyResponse.data.expiresAt}`);
    
    // Validate format
    if (!testKey.match(/^skt_[a-zA-Z0-9]{32}$/)) {
      throw new Error(`Invalid test key format: ${testKey}`);
    }
    
    // Test 2: Generate production key
    console.log('  🔑 Generating production key (skp_)...');
    const prodKeyResponse = await this.generateKey('prod', 1000);
    
    if (!prodKeyResponse.success) {
      throw new Error(`Production key generation failed: ${prodKeyResponse.error}`);
    }
    
    const prodKey = prodKeyResponse.data.rawKey;
    this.generatedKeys.push(prodKey);
    
    console.log(`    ✅ Generated: ${prodKey.substring(0, 15)}...`);
    console.log(`    ✅ Key ID: ${prodKeyResponse.data.keyId}`);
    console.log(`    ✅ Credits: ${prodKeyResponse.data.credits}`);
    console.log(`    ✅ No expiry: ${!prodKeyResponse.data.expiresAt ? 'Correct' : 'ERROR'}`);
    
    // Validate format
    if (!prodKey.match(/^skp_[a-zA-Z0-9]{32}$/)) {
      throw new Error(`Invalid production key format: ${prodKey}`);
    }
    
    // Test 3: Uniqueness check
    console.log('  🔄 Testing key uniqueness...');
    const anotherTestKey = await this.generateKey('test', 50);
    
    if (!anotherTestKey.success) {
      throw new Error(`Second test key generation failed: ${anotherTestKey.error}`);
    }
    
    if (testKey === anotherTestKey.data.rawKey) {
      throw new Error('Generated keys are not unique!');
    }
    
    this.generatedKeys.push(anotherTestKey.data.rawKey);
    console.log('    ✅ Key uniqueness verified');
    
    console.log('  ✅ Key generation tests passed');
  }

  async testKeyValidation() {
    console.log('\n🔍 Testing Key Validation...');
    
    if (this.generatedKeys.length === 0) {
      throw new Error('No keys available for validation testing');
    }
    
    const testKey = this.generatedKeys[0];
    
    // Test 1: Valid key validation
    console.log('  ✅ Testing valid key validation...');
    const validResponse = await this.validateKey(testKey);
    
    if (!validResponse.success) {
      throw new Error(`Valid key validation failed: ${validResponse.error}`);
    }
    
    console.log(`    ✅ Customer ID: ${validResponse.data.customerId}`);
    console.log(`    ✅ Key Type: ${validResponse.data.keyType}`);
    console.log(`    ✅ Credits: ${validResponse.data.credits}`);
    console.log(`    ✅ Active: ${validResponse.data.isActive}`);
    console.log(`    ✅ Not Expired: ${!validResponse.data.isExpired}`);
    
    // Test 2: Invalid key format
    console.log('  ❌ Testing invalid key format...');
    const invalidResponse = await this.validateKey('invalid-key-format');
    
    if (invalidResponse.success) {
      throw new Error('Invalid key format should have been rejected');
    }
    
    console.log(`    ✅ Correctly rejected: ${invalidResponse.error}`);
    
    // Test 3: Non-existent key
    console.log('  ❌ Testing non-existent key...');
    const fakeKey = 'skt_' + 'a'.repeat(32);
    const fakeResponse = await this.validateKey(fakeKey);
    
    if (fakeResponse.success) {
      throw new Error('Non-existent key should have been rejected');
    }
    
    console.log(`    ✅ Correctly rejected: ${fakeResponse.error}`);
    
    console.log('  ✅ Key validation tests passed');
  }

  async testKeyManagement() {
    console.log('\n⚙️ Testing Key Management...');
    
    if (this.generatedKeys.length === 0) {
      throw new Error('No keys available for management testing');
    }
    
    // Get a key ID for testing
    const testKey = this.generatedKeys[0];
    const keyId = await this.getKeyId(testKey);
    
    // Test 1: Suspend key
    console.log('  ⏸️ Testing key suspension...');
    const suspendResponse = await this.manageKey(keyId, 'suspend');
    
    if (!suspendResponse.success) {
      throw new Error(`Key suspension failed: ${suspendResponse.error}`);
    }
    
    console.log(`    ✅ Key suspended: ${suspendResponse.data.status}`);
    
    // Verify suspended key fails validation
    const suspendedValidation = await this.validateKey(testKey);
    if (suspendedValidation.success) {
      throw new Error('Suspended key should not validate');
    }
    
    console.log('    ✅ Suspended key correctly rejected');
    
    // Test 2: Reactivate key
    console.log('  ▶️ Testing key reactivation...');
    const activateResponse = await this.manageKey(keyId, 'activate');
    
    if (!activateResponse.success) {
      throw new Error(`Key reactivation failed: ${activateResponse.error}`);
    }
    
    console.log(`    ✅ Key reactivated: ${activateResponse.data.status}`);
    
    // Verify reactivated key works
    const reactivatedValidation = await this.validateKey(testKey);
    if (!reactivatedValidation.success) {
      throw new Error('Reactivated key should validate');
    }
    
    console.log('    ✅ Reactivated key correctly accepted');
    
    console.log('  ✅ Key management tests passed');
  }

  async testCreditSystem() {
    console.log('\n💳 Testing Credit System...');
    
    // Generate a key with low credits for testing
    console.log('  🔑 Generating key with low credits...');
    const lowCreditKey = await this.generateKey('test', 2);
    
    if (!lowCreditKey.success) {
      throw new Error(`Low credit key generation failed: ${lowCreditKey.error}`);
    }
    
    const creditKey = lowCreditKey.data.rawKey;
    this.generatedKeys.push(creditKey);
    
    console.log(`    ✅ Generated key with ${lowCreditKey.data.credits} credits`);
    
    // Test 1: Credit consumption
    console.log('  📉 Testing credit consumption...');
    const consumeResponse = await this.validateKey(creditKey, true);
    
    if (!consumeResponse.success) {
      throw new Error(`Credit consumption test failed: ${consumeResponse.error}`);
    }
    
    console.log('    ✅ Credit consumed successfully');
    
    // Test 2: Credit exhaustion
    console.log('  🚫 Testing credit exhaustion...');
    
    // Consume remaining credits
    await this.validateKey(creditKey, true); // Should work
    
    // This should fail due to insufficient credits
    const exhaustedResponse = await this.validateKey(creditKey, true);
    
    if (exhaustedResponse.success) {
      console.warn('    ⚠️ Credit exhaustion test may not be implemented yet');
    } else {
      console.log(`    ✅ Correctly rejected: ${exhaustedResponse.error}`);
    }
    
    console.log('  ✅ Credit system tests completed');
  }

  async testRetentionPolicy() {
    console.log('\n📅 Testing Retention Policy...');
    
    // Test 1: Test key has expiry
    console.log('  ⏰ Verifying test key expiry...');
    const testKeyResponse = await this.generateKey('test', 100);
    
    if (!testKeyResponse.success) {
      throw new Error(`Test key generation failed: ${testKeyResponse.error}`);
    }
    
    if (!testKeyResponse.data.expiresAt) {
      throw new Error('Test key should have expiry date');
    }
    
    const expiryDate = new Date(testKeyResponse.data.expiresAt);
    const now = new Date();
    const daysDiff = Math.ceil((expiryDate - now) / (1000 * 60 * 60 * 24));
    
    console.log(`    ✅ Test key expires in ${daysDiff} days`);
    
    if (daysDiff < 6 || daysDiff > 8) {
      throw new Error(`Test key expiry should be ~7 days, got ${daysDiff} days`);
    }
    
    this.generatedKeys.push(testKeyResponse.data.rawKey);
    
    // Test 2: Production key has no expiry
    console.log('  ♾️ Verifying production key no expiry...');
    const prodKeyResponse = await this.generateKey('prod', 1000);
    
    if (!prodKeyResponse.success) {
      throw new Error(`Production key generation failed: ${prodKeyResponse.error}`);
    }
    
    if (prodKeyResponse.data.expiresAt) {
      throw new Error('Production key should not have expiry date');
    }
    
    console.log('    ✅ Production key has no expiry');
    
    this.generatedKeys.push(prodKeyResponse.data.rawKey);
    
    console.log('  ✅ Retention policy tests passed');
  }

  // Helper methods
  async generateKey(keyType, credits) {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/admin/generate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customerId: this.testCustomerId,
        keyType,
        credits
      })
    });

    return await response.json();
  }

  async validateKey(key, consumeCredit = false) {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${key}`
      },
      body: JSON.stringify({ consumeCredit })
    });

    return await response.json();
  }

  async manageKey(keyId, action) {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/admin/manage-keys`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({ keyId, action })
    });

    return await response.json();
  }

  async getKeyId(rawKey) {
    // This would typically hash the key and look it up
    // For testing, we'll use a mock ID
    return `key-${crypto.createHash('sha256').update(rawKey).digest('hex').substring(0, 8)}`;
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up test data...');
    
    // In a real implementation, this would clean up the test customer and keys
    // For now, we'll just log what we would clean up
    console.log(`  🗑️ Would clean up customer: ${this.testCustomerId}`);
    console.log(`  🗑️ Would clean up ${this.generatedKeys.length} API keys`);
    
    console.log('  ✅ Cleanup completed');
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new ApiKeyTester();
  tester.runAllTests().catch(console.error);
}

module.exports = ApiKeyTester;