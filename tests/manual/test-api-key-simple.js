#!/usr/bin/env node

/**
 * Simple API Key Management Test
 * Tests basic functionality that exists (validate-api-key)
 */

const SUPABASE_URL = process.env.SUPABASE_URL || 'http://127.0.0.1:14321';
const SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

async function testBasicApiValidation() {
  console.log('🚀 Testing Basic API Key Validation');
  console.log('─'.repeat(50));

  try {
    // Test 1: Invalid key format
    console.log('  ❌ Testing invalid key format...');
    const response = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': 'invalid-key-format'
      },
      body: JSON.stringify({})
    });

    const result = await response.json();
    console.log(`    Status: ${response.status}`);
    console.log(`    Response:`, result);

    if (response.status === 401 && result.error) {
      console.log('    ✅ Invalid key correctly rejected');
    } else {
      console.log('    ❌ Expected 401 error for invalid key');
    }

    // Test 2: Non-existent key
    console.log('  ❌ Testing non-existent key...');
    const fakeKey = 'skt_' + 'a'.repeat(32);
    const response2 = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': fakeKey
      },
      body: JSON.stringify({})
    });

    const result2 = await response2.json();
    console.log(`    Status: ${response2.status}`);
    console.log(`    Response:`, result2);

    if (response2.status === 401 && result2.error) {
      console.log('    ✅ Non-existent key correctly rejected');
    } else {
      console.log('    ❌ Expected 401 error for non-existent key');
    }

    console.log('\n✅ Basic validation tests completed!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Test direct database access
async function testDatabaseConnection() {
  console.log('\n🗄️ Testing Database Connection');
  console.log('─'.repeat(50));

  try {
    // Test database access via supabase client
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

    // Test simple query
    const { data: _data, error } = await supabase.from('customers').select('count').limit(1);

    if (error) {
      console.log('    ❌ Database connection failed:', error.message);
    } else {
      console.log('    ✅ Database connection successful');
      console.log('    ✅ Can query customers table');
    }

  } catch (error) {
    console.log('    ❌ Database test failed:', error.message);
  }
}

async function runAllTests() {
  await testBasicApiValidation();
  await testDatabaseConnection();
}

// Run tests when script is executed directly
runAllTests().catch(console.error);