#!/usr/bin/env node

/**
 * GitHub Issue #4 Validation Test
 *
 * Validates all acceptance criteria from GitHub issue #4:
 * [Epic 1][Story 4] Basic API Key Management
 */

const SUPABASE_URL = process.env.SUPABASE_URL || 'http://127.0.0.1:14321';
const SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SERVICE_ROLE_KEY) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  process.exit(1);
}

class GitHubIssue4Validator {
  constructor() {
    this.testCustomerId = '00000000-0000-0000-0000-000000000001'; // Use the existing test customer
    this.generatedKeys = [];
    this.results = {
      passed: 0,
      failed: 0,
      details: []
    };
  }

  log(message, passed = true) {
    const status = passed ? '✅' : '❌';
    console.log(`  ${status} ${message}`);
    this.results.details.push({ message, passed });
    if (passed) this.results.passed++;
    else this.results.failed++;
  }

  async validateRequirement(description, testFn) {
    console.log(`\n📋 ${description}`);
    console.log('─'.repeat(60));
    try {
      await testFn();
    } catch (error) {
      this.log(`Test failed with error: ${error.message}`, false);
    }
  }

  async runAllValidations() {
    console.log('🎯 GitHub Issue #4 - Basic API Key Management Validation');
    console.log('═'.repeat(80));

    try {
      await this.validateRequirement(
        'Generate test key (skt_) with configurable credit allocation',
        () => this.validateTestKeyGeneration()
      );

      await this.validateRequirement(
        'Generate production key (skp_) with separate credit allocation',
        () => this.validateProductionKeyGeneration()
      );

      await this.validateRequirement(
        'API key hashing and secure storage in database',
        () => this.validateKeyHashing()
      );

      await this.validateRequirement(
        'Basic key validation endpoint for customer testing',
        () => this.validateKeyValidationEndpoint()
      );

      await this.validateRequirement(
        'Key status tracking (active, suspended, expired)',
        () => this.validateKeyStatusTracking()
      );

      await this.validateRequirement(
        '7-day retention policy implementation for test keys',
        () => this.validateRetentionPolicy()
      );

      await this.validateRequirement(
        'Admin endpoint to view key status and basic usage',
        () => this.validateAdminEndpoints()
      );

      await this.validateRequirement(
        'Credit balance tracking and enforcement',
        () => this.validateCreditTracking()
      );

      await this.validateRequirement(
        'Key revocation capability',
        () => this.validateKeyRevocation()
      );

      this.printSummary();

    } catch (error) {
      console.error('\n💥 Validation suite failed:', error.message);
      process.exit(1);
    }
  }

  async validateTestKeyGeneration() {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/admin-generate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customerId: this.testCustomerId,
        keyType: 'test',
        credits: 100
      })
    });

    const result = await response.json();

    this.log(`API responds with status ${response.status}`, response.status === 200);
    this.log(`Response has success=true`, result.success === true);
    this.log(`Generated key has skt_ prefix`, result.data?.rawKey?.startsWith('skt_'));
    this.log(`Key follows format skt_[32hex]`, /^skt_[a-f0-9]{32}$/.test(result.data?.rawKey || ''));
    this.log(`Credits set to 100`, result.data?.credits === 100);
    this.log(`Key type is test`, result.data?.keyType === 'test');
    this.log(`Has expiration date`, !!result.data?.expiresAt);

    if (result.success) {
      this.generatedKeys.push({ rawKey: result.data.rawKey, keyId: result.data.keyId, type: 'test' });
    }
  }

  async validateProductionKeyGeneration() {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/admin-generate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customerId: this.testCustomerId,
        keyType: 'production',
        credits: 1000
      })
    });

    const result = await response.json();

    this.log(`API responds with status ${response.status}`, response.status === 200);
    this.log(`Response has success=true`, result.success === true);
    this.log(`Generated key has skp_ prefix`, result.data?.rawKey?.startsWith('skp_'));
    this.log(`Key follows format skp_[32hex]`, /^skp_[a-f0-9]{32}$/.test(result.data?.rawKey || ''));
    this.log(`Credits set to 1000`, result.data?.credits === 1000);
    this.log(`Key type is production`, result.data?.keyType === 'production');
    this.log(`No expiration date for production`, !result.data?.expiresAt);

    if (result.success) {
      this.generatedKeys.push({ rawKey: result.data.rawKey, keyId: result.data.keyId, type: 'production' });
    }
  }

  async validateKeyHashing() {
    // Check database to ensure keys are hashed
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

    const { data: keys } = await supabase
      .from('api_keys')
      .select('key_hash, key_prefix')
      .eq('customer_id', this.testCustomerId);

    this.log(`Found keys in database`, keys && keys.length > 0);

    if (keys && keys.length > 0) {
      const testKey = keys[0];
      this.log(`Key hash is SHA-256 format (64 hex chars)`, /^[a-f0-9]{64}$/.test(testKey.key_hash));
      this.log(`Raw key not stored in database`, !keys.some(k => k.key_hash.startsWith('skt_') || k.key_hash.startsWith('skp_')));
    }
  }

  async validateKeyValidationEndpoint() {
    if (this.generatedKeys.length === 0) {
      this.log('No keys available for validation test', false);
      return;
    }

    const testKey = this.generatedKeys.find(k => k.type === 'test');
    if (!testKey) {
      this.log('No test key available for validation', false);
      return;
    }

    // Test valid key
    const response = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testKey.rawKey}`
      },
      body: JSON.stringify({})
    });

    const result = await response.json();

    this.log(`Valid key accepted with 200 status`, response.status === 200);
    this.log(`Validation response has success=true`, result.success === true);
    this.log(`Returns customer context`, !!result.data?.customerId);
    this.log(`Returns key type`, !!result.data?.keyType);
    this.log(`Returns credit balance`, typeof result.data?.credits === 'number');

    // Test invalid key
    const invalidResponse = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-key'
      },
      body: JSON.stringify({})
    });

    this.log(`Invalid key rejected with 401 status`, invalidResponse.status === 401);
  }

  async validateKeyStatusTracking() {
    if (this.generatedKeys.length === 0) {
      this.log('No keys available for status tracking test', false);
      return;
    }

    const testKey = this.generatedKeys.find(k => k.type === 'test');
    if (!testKey) {
      this.log('No test key available for status test', false);
      return;
    }

    // Test key suspension
    const suspendResponse = await fetch(`${SUPABASE_URL}/functions/v1/admin-manage-keys`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        keyId: testKey.keyId,
        action: 'suspend'
      })
    });

    const suspendResult = await suspendResponse.json();
    this.log(`Key suspension works`, suspendResponse.status === 200 && suspendResult.success);

    // Test that suspended key is rejected
    const validateSuspendedResponse = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testKey.rawKey}`
      },
      body: JSON.stringify({})
    });

    this.log(`Suspended key rejected`, validateSuspendedResponse.status === 401);

    // Reactivate key
    const activateResponse = await fetch(`${SUPABASE_URL}/functions/v1/admin-manage-keys`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        keyId: testKey.keyId,
        action: 'activate'
      })
    });

    const activateResult = await activateResponse.json();
    this.log(`Key reactivation works`, activateResponse.status === 200 && activateResult.success);
  }

  async validateRetentionPolicy() {
    // Check that test keys have 7-day expiry
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

    const { data: testKeys } = await supabase
      .from('api_keys')
      .select('expires_at, key_type, created_at')
      .eq('customer_id', this.testCustomerId)
      .eq('key_type', 'test');

    if (testKeys && testKeys.length > 0) {
      const testKey = testKeys[0];
      const createdAt = new Date(testKey.created_at);
      const expiresAt = new Date(testKey.expires_at);
      const daysDiff = (expiresAt - createdAt) / (1000 * 60 * 60 * 24);

      this.log(`Test key has expiration date`, !!testKey.expires_at);
      this.log(`Test key expires in ~7 days`, Math.abs(daysDiff - 7) < 1);
    } else {
      this.log('No test keys found for retention policy check', false);
    }

    // Check that production keys don't expire
    const { data: prodKeys } = await supabase
      .from('api_keys')
      .select('expires_at, key_type')
      .eq('customer_id', this.testCustomerId)
      .eq('key_type', 'production');

    if (prodKeys && prodKeys.length > 0) {
      const prodKey = prodKeys[0];
      this.log(`Production key has no expiration`, !prodKey.expires_at);
    }
  }

  async validateAdminEndpoints() {
    if (this.generatedKeys.length === 0) {
      this.log('No keys available for admin endpoint test', false);
      return;
    }

    const testKey = this.generatedKeys.find(k => k.type === 'test');
    if (!testKey) {
      this.log('No test key available for admin test', false);
      return;
    }

    // Test key status query
    const statusResponse = await fetch(`${SUPABASE_URL}/functions/v1/admin-manage-keys?keyId=${testKey.keyId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      }
    });

    const statusResult = await statusResponse.json();
    this.log(`Admin status endpoint works`, statusResponse.status === 200);
    this.log(`Status response has key info`, !!statusResult.data?.keyInfo);
    this.log(`Status response has usage summary`, statusResult.data?.usageSummary !== undefined);
  }

  async validateCreditTracking() {
    if (this.generatedKeys.length === 0) {
      this.log('No keys available for credit tracking test', false);
      return;
    }

    const testKey = this.generatedKeys.find(k => k.type === 'test');
    if (!testKey) {
      this.log('No test key available for credit test', false);
      return;
    }

    // Test credit consumption
    const consumeResponse = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testKey.rawKey}`
      },
      body: JSON.stringify({ consumeCredit: true })
    });

    const consumeResult = await consumeResponse.json();
    this.log(`Credit consumption endpoint works`, consumeResponse.status === 200);

    if (consumeResponse.status === 200) {
      this.log(`Credit balance decreases`, consumeResult.data.credits < 100);
    }

    // Test credit exhaustion (set credits to 0)
    const { createClient } = await import('@supabase/supabase-js');
    const supabase = createClient(SUPABASE_URL, SERVICE_ROLE_KEY);

    await supabase
      .from('api_keys')
      .update({ credits: 0 })
      .eq('id', testKey.keyId);

    const exhaustedResponse = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${testKey.rawKey}`
      },
      body: JSON.stringify({ consumeCredit: true })
    });

    this.log(`Credit exhaustion enforced`, exhaustedResponse.status === 402);
  }

  async validateKeyRevocation() {
    if (this.generatedKeys.length === 0) {
      this.log('No keys available for revocation test', false);
      return;
    }

    // Generate a new key for revocation test
    const response = await fetch(`${SUPABASE_URL}/functions/v1/admin-generate-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        customerId: this.testCustomerId,
        keyType: 'test',
        credits: 50
      })
    });

    const result = await response.json();

    if (!result.success) {
      this.log('Could not generate key for revocation test', false);
      return;
    }

    // Revoke the key
    const revokeResponse = await fetch(`${SUPABASE_URL}/functions/v1/admin-manage-keys`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SERVICE_ROLE_KEY}`
      },
      body: JSON.stringify({
        keyId: result.data.keyId
      })
    });

    const revokeResult = await revokeResponse.json();
    this.log(`Key revocation works`, revokeResponse.status === 200 && revokeResult.success);

    // Test that revoked key is rejected
    const validateRevokedResponse = await fetch(`${SUPABASE_URL}/functions/v1/validate-api-key`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${result.data.rawKey}`
      },
      body: JSON.stringify({})
    });

    this.log(`Revoked key rejected`, validateRevokedResponse.status === 401);
  }

  printSummary() {
    console.log('\n' + '═'.repeat(80));
    console.log('📊 VALIDATION SUMMARY');
    console.log('═'.repeat(80));

    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${Math.round((this.results.passed / (this.results.passed + this.results.failed)) * 100)}%`);

    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.details
        .filter(d => !d.passed)
        .forEach(d => console.log(`   • ${d.message}`));
    }

    console.log('\n🎯 GitHub Issue #4 Implementation Status:');
    if (this.results.failed === 0) {
      console.log('✅ ALL ACCEPTANCE CRITERIA MET - Ready for code review!');
    } else {
      console.log('❌ Some acceptance criteria need attention');
    }
  }
}

// Run validation when script is executed directly
const validator = new GitHubIssue4Validator();
validator.runAllValidations().catch(console.error);