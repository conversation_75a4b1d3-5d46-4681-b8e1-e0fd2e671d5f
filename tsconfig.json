{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "noEmit": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "useUnknownInCatchVariables": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": false, "isolatedModules": true, "verbatimModuleSyntax": true, "resolveJsonModule": true, "allowJs": false, "checkJs": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/types/*": ["./types/*"], "@/tests/*": ["./tests/*"]}}, "include": ["**/*.ts", "tests/**/*.ts", "types/**/*.ts"], "exclude": ["node_modules", "dist", "coverage", ".tmp", "supabase/functions/**/*.ts"]}