import { describe, it, expect } from 'bun:test';
import {
  FileValidator,
  ApiKeyLimitValidator,
  FileSecurityValidator,
  FileUploadValidator,
  type FileUploadLimits
} from './supabase/functions/_shared/file-validation.ts';

describe('FileValidator', () => {
  describe('validateFileType', () => {
    it('should accept PDF files', () => {
      const mockPdfFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });

      const _result = FileValidator.validateFileType(mockPdfFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('application/pdf');
      expect(result.metadata?.originalName).toBe('test.pdf');
    });

    it('should accept DOCX files', () => {
      const mockDocxFile = new File(['content'], 'test.docx', {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });

      const _result = FileValidator.validateFileType(mockDocxFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    });

    it('should accept XLSX files', () => {
      const mockXlsxFile = new File(['content'], 'test.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });

      const _result = FileValidator.validateFileType(mockXlsxFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    });

    it('should accept JPEG files', () => {
      const mockJpegFile = new File(['content'], 'test.jpg', { type: 'image/jpeg' });

      const _result = FileValidator.validateFileType(mockJpegFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('image/jpeg');
    });

    it('should accept PNG files', () => {
      const mockPngFile = new File(['content'], 'test.png', { type: 'image/png' });

      const _result = FileValidator.validateFileType(mockPngFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('image/png');
    });

    it('should reject unsupported file types', () => {
      const mockUnsupportedFile = new File(['content'], 'test.txt', { type: 'text/plain' });

      const _result = FileValidator.validateFileType(mockUnsupportedFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Unsupported file type');
      expect(result.error).toContain('text/plain');
    });
  });

  describe('validateFileSize', () => {
    const defaultLimits: FileUploadLimits = {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: ['application/pdf', 'image/jpeg', 'image/png']
    };

    it('should accept files within size limit', () => {
      const smallFile = new File(['small content'], 'test.pdf', { type: 'application/pdf' });

      const _result = FileValidator.validateFileSize(smallFile, defaultLimits);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.size).toBe(smallFile.size);
    });

    it('should reject files exceeding size limit', () => {
      // Create a mock large file by setting size property
      const largeContent = 'x'.repeat(1000); // Small content but we'll mock the size
      const largeFile = new File([largeContent], 'large.pdf', { type: 'application/pdf' });

      // Mock the size property to simulate a large file
      Object.defineProperty(largeFile, 'size', {
        value: 60 * 1024 * 1024, // 60MB
        writable: false
      });

      const _result = FileValidator.validateFileSize(largeFile, defaultLimits);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File too large');
      expect(result.error).toContain('60MB');
      expect(result.error).toContain('50MB');
    });

    it('should reject empty files', () => {
      const emptyFile = new File([], 'empty.pdf', { type: 'application/pdf' });

      const _result = FileValidator.validateFileSize(emptyFile, defaultLimits);
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('File is empty');
    });
  });

  describe('validateFileHeader', () => {
    it('should validate PDF file headers', async () => {
      // Create a mock PDF file with correct header
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const mockPdfFile = new File([pdfHeader], 'test.pdf', { type: 'application/pdf' });

      const _result = await FileValidator.validateFileHeader(mockPdfFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('application/pdf');
    });

    it('should validate JPEG file headers', async () => {
      // Create a mock JPEG file with correct header
      const jpegHeader = new Uint8Array([0xFF, 0xD8, 0xFF]);
      const mockJpegFile = new File([jpegHeader], 'test.jpg', { type: 'image/jpeg' });

      const _result = await FileValidator.validateFileHeader(mockJpegFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('image/jpeg');
    });

    it('should validate PNG file headers', async () => {
      // Create a mock PNG file with correct header
      const pngHeader = new Uint8Array([0x89, 0x50, 0x4E, 0x47]);
      const mockPngFile = new File([pngHeader], 'test.png', { type: 'image/png' });

      const _result = await FileValidator.validateFileHeader(mockPngFile);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('image/png');
    });

    it('should reject files with mismatched headers', async () => {
      // Create a file with wrong header for its type
      const wrongHeader = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
      const mockFile = new File([wrongHeader], 'test.pdf', { type: 'application/pdf' });

      const _result = await FileValidator.validateFileHeader(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File header mismatch');
      expect(result.error).toContain('security threat');
    });

    it('should reject unsupported file types for header validation', async () => {
      const unsupportedFile = new File(['content'], 'test.txt', { type: 'text/plain' });

      const _result = await FileValidator.validateFileHeader(unsupportedFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File type not supported for header validation');
    });
  });

  describe('validateFile (comprehensive)', () => {
    const testLimits: FileUploadLimits = {
      maxFileSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png'
      ]
    };

    it('should validate a complete valid file', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const validFile = new File([pdfHeader], 'document.pdf', { type: 'application/pdf' });

      const _result = await FileValidator.validateFile(validFile, testLimits);
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('application/pdf');
      expect(result.metadata?.originalName).toBe('document.pdf');
      expect(result.metadata?.size).toBeGreaterThan(0);
    });

    it('should provide detailed metadata for valid files', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
      const validFile = new File([pdfHeader], 'important-document.pdf', { type: 'application/pdf' });

      const _result = await FileValidator.validateFile(validFile, testLimits);
      expect(result.isValid).toBe(true);
      expect(result.metadata).toBeDefined();
      expect(result.metadata?.originalName).toBe('important-document.pdf');
      expect(result.metadata?.type).toBe('application/pdf');
      expect(result.metadata?.size).toBe(validFile.size);
      expect(result.metadata?.createdAt).toBeInstanceOf(Date);
    });

    it('should fail validation for unsupported file type', async () => {
      const invalidFile = new File(['content'], 'bad.exe', { type: 'application/exe' });

      const _result = await FileValidator.validateFile(invalidFile, testLimits);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Unsupported file type');
    });

    it('should fail validation for header mismatch', async () => {
      // Create a PDF file with wrong header
      const wrongHeader = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
      const invalidFile = new File([wrongHeader], 'fake.pdf', { type: 'application/pdf' });

      const _result = await FileValidator.validateFile(invalidFile, testLimits);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File header mismatch');
    });
  });

  describe('extractFileMetadata', () => {
    it('should extract basic file metadata', () => {
      const testFile = new File(['content'], 'test-document.pdf', { type: 'application/pdf' });

      const metadata = FileValidator.extractFileMetadata(testFile);
      expect(metadata).toBeDefined();
      expect(metadata.originalName).toBe('test-document.pdf');
      expect(metadata.type).toBe('application/pdf');
      expect(metadata.size).toBe(testFile.size);
      expect(metadata.createdAt).toBeInstanceOf(Date);
    });
  });

  describe('getSupportedTypes', () => {
    it('should return list of supported MIME types', () => {
      const supportedTypes = FileValidator.getSupportedTypes();

      expect(supportedTypes).toEqual([
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'image/jpeg',
        'image/png'
      ]);
    });

    it('should include all required file types from the story', () => {
      const supportedTypes = FileValidator.getSupportedTypes();

      expect(supportedTypes).toContain('application/pdf');
      expect(supportedTypes).toContain('application/vnd.openxmlformats-officedocument.wordprocessingml.document');
      expect(supportedTypes).toContain('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(supportedTypes).toContain('image/jpeg');
      expect(supportedTypes).toContain('image/png');
    });
  });

  describe('calculateFileHash', () => {
    it('should calculate consistent hash for same file content', async () => {
      const file1 = new File(['test content'], 'file1.pdf', { type: 'application/pdf' });
      const file2 = new File(['test content'], 'file2.pdf', { type: 'application/pdf' });

      const hash1 = await FileValidator.calculateFileHash(file1);
      const hash2 = await FileValidator.calculateFileHash(file2);

      expect(hash1).toBe(hash2);
      expect(hash1).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex string
    });

    it('should produce different hashes for different content', async () => {
      const file1 = new File(['content A'], 'file1.pdf', { type: 'application/pdf' });
      const file2 = new File(['content B'], 'file2.pdf', { type: 'application/pdf' });

      const hash1 = await FileValidator.calculateFileHash(file1);
      const hash2 = await FileValidator.calculateFileHash(file2);

      expect(hash1).not.toBe(hash2);
    });
  });
});

// API Key File Upload Limits Tests
describe('ApiKeyLimitValidator', () => {
  describe('getUploadLimits', () => {
    it('should return test key limits (10MB)', () => {
      const testLimits = ApiKeyLimitValidator.getUploadLimits('test');

      expect(testLimits.keyType).toBe('test');
      expect(testLimits.maxFileSize).toBe(10 * 1024 * 1024); // 10MB
      expect(testLimits.dailyUploadLimit).toBe(100);
    });

    it('should return production key limits (50MB)', () => {
      const prodLimits = ApiKeyLimitValidator.getUploadLimits('production');

      expect(prodLimits.keyType).toBe('production');
      expect(prodLimits.maxFileSize).toBe(50 * 1024 * 1024); // 50MB
      expect(prodLimits.dailyUploadLimit).toBe(1000);
    });
  });

  describe('validateFileAgainstKeyLimits', () => {
    it('should accept files within test key limits', () => {
      const smallFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });

      const _result = ApiKeyLimitValidator.validateFileAgainstKeyLimits(smallFile, 'test');
      expect(result.isValid).toBe(true);
    });

    it('should reject files exceeding test key limits', () => {
      const largeContent = 'x'.repeat(1000);
      const largeFile = new File([largeContent], 'large.pdf', { type: 'application/pdf' });

      // Mock size to exceed test key limit (10MB)
      Object.defineProperty(largeFile, 'size', {
        value: 15 * 1024 * 1024, // 15MB
        writable: false
      });

      const _result = ApiKeyLimitValidator.validateFileAgainstKeyLimits(largeFile, 'test');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File too large for test key');
      expect(result.error).toContain('15MB');
      expect(result.error).toContain('10MB');
    });

    it('should accept larger files for production keys', () => {
      const largeContent = 'x'.repeat(1000);
      const largeFile = new File([largeContent], 'large.pdf', { type: 'application/pdf' });

      // Mock size within production key limit (50MB)
      Object.defineProperty(largeFile, 'size', {
        value: 30 * 1024 * 1024, // 30MB
        writable: false
      });

      const _result = ApiKeyLimitValidator.validateFileAgainstKeyLimits(largeFile, 'production');
      expect(result.isValid).toBe(true);
    });
  });

  describe('getFileUploadLimits', () => {
    it('should return FileUploadLimits interface for test keys', () => {
      const limits = ApiKeyLimitValidator.getFileUploadLimits('test');

      expect(limits.maxFileSize).toBe(10 * 1024 * 1024);
      expect(limits.allowedTypes).toEqual(FileValidator.getSupportedTypes());
    });

    it('should return FileUploadLimits interface for production keys', () => {
      const limits = ApiKeyLimitValidator.getFileUploadLimits('production');

      expect(limits.maxFileSize).toBe(50 * 1024 * 1024);
      expect(limits.allowedTypes).toEqual(FileValidator.getSupportedTypes());
    });
  });
});

// Security Validation Tests
describe('FileSecurityValidator', () => {
  describe('scanForMaliciousContent', () => {
    it('should accept safe files', async () => {
      const safeFile = new File(['safe content'], 'document.pdf', { type: 'application/pdf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(safeFile);
      expect(result.isValid).toBe(true);
    });

    it('should reject files with suspicious executable headers', async () => {
      // Create file with PE header (Windows executable)
      const peHeader = new Uint8Array([0x4D, 0x5A]); // MZ
      const suspiciousFile = new File([peHeader], 'malware.pdf', { type: 'application/pdf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(suspiciousFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Suspicious content detected');
      expect(result.error).toContain('PE Header');
    });

    it('should reject files with ELF headers', async () => {
      // Create file with ELF header (Linux executable)
      const elfHeader = new Uint8Array([0x7F, 0x45, 0x4C, 0x46]); // ELF
      const suspiciousFile = new File([elfHeader], 'malware.pdf', { type: 'application/pdf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(suspiciousFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('ELF Header');
    });

    it('should reject files with path traversal in filename', async () => {
      const maliciousFile = new File(['content'], '../../../etc/passwd', { type: 'application/pdf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(maliciousFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('path traversal characters detected');
    });

    it('should reject files with backslash path separators', async () => {
      const maliciousFile = new File(['content'], 'folder\\..\\file.pdf', { type: 'application/pdf' });

      const _result = await FileSecurityValidator.scanForMaliciousContent(maliciousFile);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('path traversal characters detected');
    });
  });
});

// Complete Upload Validation Pipeline Tests
describe('FileUploadValidator', () => {
  describe('validateUpload', () => {
    it('should validate a complete safe upload for test key', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const validFile = new File([pdfHeader], 'safe-document.pdf', { type: 'application/pdf' });

      const _result = await FileUploadValidator.validateUpload(validFile, 'test');
      expect(result.isValid).toBe(true);
      expect(result.metadata?.originalName).toBe('safe-document.pdf');
      expect(result.metadata?.hash).toMatch(/^[a-f0-9]{64}$/);
    });

    it('should validate a complete safe upload for production key', async () => {
      const pngHeader = new Uint8Array([0x89, 0x50, 0x4E, 0x47]); // PNG
      const validFile = new File([pngHeader], 'document-scan.png', { type: 'image/png' });

      const _result = await FileUploadValidator.validateUpload(validFile, 'production');
      expect(result.isValid).toBe(true);
      expect(result.metadata?.type).toBe('image/png');
    });

    it('should reject uploads failing any validation step', async () => {
      // File too large for test key
      const largeContent = 'x'.repeat(1000);
      const largeFile = new File([largeContent], 'huge.pdf', { type: 'application/pdf' });

      Object.defineProperty(largeFile, 'size', {
        value: 15 * 1024 * 1024, // 15MB (exceeds test key 10MB limit)
        writable: false
      });

      const _result = await FileUploadValidator.validateUpload(largeFile, 'test');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('File too large for test key');
    });

    it('should reject suspicious files', async () => {
      // Create file with executable header
      const peHeader = new Uint8Array([0x4D, 0x5A]); // MZ (PE header)
      const suspiciousFile = new File([peHeader], 'malware.pdf', { type: 'application/pdf' });

      const _result = await FileUploadValidator.validateUpload(suspiciousFile, 'production');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Suspicious content detected');
    });

    it('should handle validation errors gracefully', async () => {
      // Test with undefined file (edge case)
      const _result = await FileUploadValidator.validateUpload(null as any, 'test');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Upload validation failed');
    });
  });
});