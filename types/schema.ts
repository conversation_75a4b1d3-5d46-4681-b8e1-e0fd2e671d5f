// JSON Schema Validation Types
// Epic 3, Story 5: JSON Schema Validation

export interface JSONSchema {
  $schema?: string;
  type: 'object' | 'array' | 'string' | 'number' | 'integer' | 'boolean' | 'null';
  properties?: Record<string, JSONSchema>;
  items?: JSONSchema;
  required?: string[];
  additionalProperties?: boolean;
  
  // String validation
  pattern?: string;
  format?: string;
  minLength?: number;
  maxLength?: number;
  enum?: any[];

  // Number validation
  minimum?: number;
  maximum?: number;
  exclusiveMinimum?: number;
  exclusiveMaximum?: number;
  multipleOf?: number;

  // Array validation
  minItems?: number;
  maxItems?: number;
  uniqueItems?: boolean;

  // Object validation
  minProperties?: number;
  maxProperties?: number;

  // Generic
  default?: any;
  description?: string;
  examples?: any[];
  title?: string;
}

export interface ValidationError {
  field: string;
  message: string;
  value: any;
  expected: string;
  severity: 'error' | 'warning';
}

export interface SchemaValidationResult {
  valid: boolean;
  errors?: ValidationError[];
  data?: any;
}

export interface ProcessedOutput {
  success: boolean;
  data: any;
  validation_passed: boolean;
  validation_errors?: ValidationError[];
  warning?: string;
}

export interface CompatibilityIssue {
  type: 'breaking' | 'warning' | 'info';
  field: string;
  message: string;
  impact: string;
}

export interface CompatibilityResult {
  compatible: boolean;
  issues: CompatibilityIssue[];
}

export interface FieldDefinition {
  name: string;
  type: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
  required: boolean;
  description?: string;
  
  // String properties
  format?: string;
  pattern?: string;
  minLength?: number;
  maxLength?: number;
  enum?: string[];

  // Number properties
  minimum?: number;
  maximum?: number;

  // Array properties
  items?: FieldDefinition;
  minItems?: number;
  maxItems?: number;

  // Object properties
  properties?: Record<string, FieldDefinition>;

  // Generic
  default?: any;
}

export interface CompiledSchema {
  validator: any; // AJV ValidateFunction
  compiledAt: Date;
  schemaVersion: string;
}

export interface Agent {
  id: string;
  output_schema: JSONSchema;
  updated_at: string;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  compilations: number;
  hit_rate: number;
  cache_size: number;
}

// Custom format validation function type
export type FormatValidator = (data: any) => boolean;

// Schema compilation options
export interface SchemaCompilationOptions {
  allErrors?: boolean;
  removeAdditional?: boolean;
  useDefaults?: boolean;
  coerceTypes?: boolean;
  verbose?: boolean;
}

// Agent schema update event
export interface SchemaUpdateEvent {
  agentId: string;
  oldSchema: JSONSchema;
  newSchema: JSONSchema;
  compatibility: CompatibilityResult;
  timestamp: Date;
}

// Schema validation configuration
export interface ValidationConfig {
  enableCaching?: boolean;
  cacheSize?: number;
  performanceMode?: boolean;
  strictMode?: boolean;
  bypassValidation?: boolean; // For testing/debugging
}

// Error codes for different validation failures
export enum ValidationErrorCode {
  SCHEMA_COMPILATION_FAILED = 'SCHEMA_COMPILATION_FAILED',
  AGENT_NOT_FOUND = 'AGENT_NOT_FOUND',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  COMPATIBILITY_CHECK_FAILED = 'COMPATIBILITY_CHECK_FAILED',
  CACHE_ERROR = 'CACHE_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR'
}

// Detailed validation context for debugging
export interface ValidationContext {
  agentId: string;
  schemaVersion: string;
  validatedAt: Date;
  performanceMetrics: {
    compilationTime?: number;
    validationTime: number;
    cacheHit: boolean;
  };
}