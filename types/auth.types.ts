// Authentication system types for IDP Platform

export interface ApiKeyValidationRequest {
  api_key: string
  include_hash?: boolean
}

export interface ApiKeyValidationResponse {
  valid: boolean
  customer_id?: string
  key_type?: 'test' | 'production'
  credits?: number
  rate_limit_remaining?: number
  correlation_id: string
  key_hash?: string // Only included if include_hash is true
}

export interface RateLimitInfo {
  limit: number
  remaining: number
  reset: number // Unix timestamp
  retry_after?: number // Seconds to wait if exceeded
}

export interface AuthResult {
  customerId: string
  keyType: 'test' | 'production'
  credits: number
  apiKeyId: string
  rateLimit: RateLimitInfo
}

export interface AuditLogEntry {
  correlation_id: string
  customer_id?: string
  api_key_id?: string
  action: 'api_key_validation' | 'rate_limit_exceeded' | 'jwt_validation' | 'authentication_failed'
  status: 'success' | 'failure'
  ip_address?: string
  user_agent?: string
  error_message?: string
  timestamp: string
  details?: Record<string, any>
}

export interface JwtPayload {
  sub: string // User ID
  iat: number // Issued at
  exp: number // Expires at
  aud: string // Audience
  iss: string // Issuer
  role?: string // User role
}

export interface ApiKeyRecord {
  id: string
  customer_id: string
  key_hash: string
  key_type: 'test' | 'production'
  credits: number
  rate_limit: number
  revoked: boolean
  created_at: string
  last_used_at?: string
}

export interface RateLimitRecord {
  api_key_id: string
  window_start: string
  request_count: number
  updated_at: string
}

export interface AuthenticationError extends Error {
  status: number
  code: string
  correlation_id?: string
}

export interface AdminAuthRequest {
  jwt_token: string
}

export interface AdminAuthResponse {
  valid: boolean
  user_id?: string
  role?: string
  correlation_id: string
}

// Utility types for API key format validation
export type ApiKeyPrefix = 'skt_' | 'skp_'
export type ApiKeyFormat = `${ApiKeyPrefix}${string}`

// Constants for validation
export const API_KEY_REGEX = /^sk[tp]_[a-zA-Z0-9]{32,}$/
export const DEFAULT_RATE_LIMIT = 100 // requests per minute
export const RATE_LIMIT_WINDOW = 60 // seconds