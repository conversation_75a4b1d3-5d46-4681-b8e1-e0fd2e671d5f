/**
 * Usage Tracking & Credit System Type Definitions
 * 
 * Comprehensive types for tracking document processing costs, customer pricing,
 * and credit management with dual metrics architecture for 60%+ profit margins.
 * 
 * Aligned with GitHub Issue #11 - Epic 2, Story 5
 */

import type { Database } from './database.types';

// Base database table types
export type UsageLogRow = Database['public']['Tables']['usage_logs']['Row'];
export type UsageLogInsert = Database['public']['Tables']['usage_logs']['Insert'];
export type UsageLogUpdate = Database['public']['Tables']['usage_logs']['Update'];

export type ApiKeyRow = Database['public']['Tables']['api_keys']['Row'];
export type CustomerRow = Database['public']['Tables']['customers']['Row'];

// =============================================================================
// USAGE TRACKING INTERFACES
// =============================================================================

/**
 * Core usage tracking record with dual metrics (cost vs price)
 */
export interface UsageRecord {
  id: string;
  customerId: string;
  apiKeyId: string;
  documentId: string | null;
  operationType: 'document_processing' | 'agent_management' | 'api_validation' | 'data_export';
  modelUsed: string | null;
  inputTokens: number | null;
  outputTokens: number | null;
  modelCostUsd: number;      // What we pay AI provider
  customerPriceUsd: number;  // What customer pays us
  profitMarginPercent: number | null; // Calculated field
  creditsUsed: number;
  processingTimeMs: number | null;
  success: boolean;
  errorCode: string | null;
  errorMessage: string | null;
  requestId: string | null;
  ipAddress: string | null;
  userAgent: string | null;
  metadata: Record<string, any>;
  createdAt: Date;
}

/**
 * Processing result from AI service calls
 */
export interface ProcessingResult {
  success: boolean;
  documentId: string;
  agentId: string;
  model: 'openai' | 'claude' | 'llamaparse';
  inputTokens: number;
  outputTokens: number;
  costUsd: number;           // Cost we pay to AI provider
  processingTimeMs: number;
  confidence: number;        // 0-1 extraction confidence
  extractedData?: any;
  errorMessage?: string;
}

/**
 * Customer context for pricing calculations
 */
export interface CustomerContext {
  id: string;
  tier: 'standard' | 'premium' | 'enterprise';
  companyName: string;
  email: string;
  status: 'active' | 'inactive' | 'suspended';
  settings: Record<string, any>;
}

/**
 * API key context for credit management
 */
export interface ApiKeyContext {
  id: string;
  customerId: string;
  keyType: 'test' | 'production';
  credits: number;
  maxCredits: number | null;
  rateLimits: {
    requestsPerMinute: number;
    requestsPerHour: number;
    requestsPerDay: number;
  };
  status: string;
  lastUsedAt: Date | null;
}

// =============================================================================
// CREDIT MANAGEMENT INTERFACES
// =============================================================================

/**
 * Credit transaction record
 */
export interface CreditTransaction {
  success: boolean;
  newBalance: number;
  amount: number;
  type: 'deduction' | 'addition' | 'refund';
  description: string;
  timestamp: Date;
  error?: string;
}

/**
 * Credit balance check result
 */
export interface CreditBalanceCheck {
  hasInsufficientCredits: boolean;
  currentBalance: number;
  requiredCredits: number;
  shortfall?: number;
}

/**
 * Low balance alert data
 */
export interface LowBalanceAlert {
  apiKeyId: string;
  customerId: string;
  currentBalance: number;
  threshold: number;
  suggestedTopup: number;
  alertType: 'warning' | 'critical' | 'exhausted';
}

/**
 * Credit usage estimation
 */
export interface CreditEstimate {
  estimatedCredits: number;
  estimatedCostUsd: number;
  modelTier: 'fast' | 'balanced' | 'smart' | 'vision';
  documentSize: number;
  documentType: string;
}

// =============================================================================
// COST CALCULATION INTERFACES
// =============================================================================

/**
 * Dynamic pricing tier configuration
 */
export interface PricingTier {
  name: 'starter' | 'professional' | 'enterprise';
  markupMultiplier: number;  // e.g., 1.6 = 60% markup
  freeCredits: number;       // Initial free credits
  rateLimit: number;         // Requests per hour
  features: string[];
}

/**
 * Model cost configuration
 */
export interface ModelCostConfig {
  modelName: string;
  provider: 'openai' | 'claude' | 'llamaparse';
  inputTokenCost: number;    // Cost per 1K input tokens
  outputTokenCost: number;   // Cost per 1K output tokens
  tier: 'fast' | 'balanced' | 'smart' | 'vision';
  enabled: boolean;
}

/**
 * Cost calculation result
 */
export interface CostCalculation {
  modelCostUsd: number;
  customerPriceUsd: number;
  profitMarginPercent: number;
  creditsToDeduct: number;
  breakdown: {
    inputTokens: number;
    outputTokens: number;
    inputCost: number;
    outputCost: number;
    markup: number;
    tier: string;
  };
}

// =============================================================================
// USAGE ANALYTICS INTERFACES
// =============================================================================

/**
 * Usage analytics for admin dashboard
 */
export interface UsageAnalytics {
  period: 'day' | 'week' | 'month' | 'year';
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  totalCostUsd: number;
  totalRevenueUsd: number;
  averageProfitMargin: number;
  totalProcessingTimeMs: number;
  modelBreakdown: ModelUsageBreakdown[];
  customerBreakdown: CustomerUsageBreakdown[];
}

/**
 * Model usage breakdown
 */
export interface ModelUsageBreakdown {
  modelName: string;
  requestCount: number;
  totalCostUsd: number;
  totalRevenueUsd: number;
  averageProcessingTimeMs: number;
  successRate: number;
}

/**
 * Customer usage breakdown
 */
export interface CustomerUsageBreakdown {
  customerId: string;
  companyName: string;
  requestCount: number;
  totalRevenueUsd: number;
  averageProfitMargin: number;
  tier: string;
}

/**
 * Billing export data
 */
export interface BillingExport {
  customerId: string;
  billingPeriod: {
    start: Date;
    end: Date;
  };
  totalAmountUsd: number;
  totalCreditsUsed: number;
  requestCount: number;
  lineItems: BillingLineItem[];
}

/**
 * Billing line item
 */
export interface BillingLineItem {
  date: Date;
  description: string;
  amountUsd: number;
  creditsUsed: number;
  modelUsed: string;
  documentType: string;
}

// =============================================================================
// ERROR HANDLING INTERFACES
// =============================================================================

/**
 * Insufficient credits error
 */
export class InsufficientCreditsError extends Error {
  constructor(
    message: string,
    public readonly required: number,
    public readonly available: number,
    public readonly shortfall: number
  ) {
    super(message);
    this.name = 'InsufficientCreditsError';
  }
}

/**
 * Rate limit exceeded error
 */
export class RateLimitExceededError extends Error {
  constructor(
    message: string,
    public readonly limit: number,
    public readonly windowType: 'minute' | 'hour' | 'day',
    public readonly resetTime: Date
  ) {
    super(message);
    this.name = 'RateLimitExceededError';
  }
}

/**
 * Low profit margin alert
 */
export interface ProfitMarginAlert {
  customerId: string;
  modelUsed: string;
  actualMargin: number;
  targetMargin: number; // 60% minimum
  costUsd: number;
  revenueUsd: number;
  alertLevel: 'warning' | 'critical';
  recommendedAction: string;
}

// =============================================================================
// AUDIT TRAIL INTERFACES
// =============================================================================

/**
 * Usage audit event
 */
export interface UsageAuditEvent {
  eventType: 'credit_deduction' | 'cost_calculation' | 'pricing_override' | 'margin_alert';
  customerId: string;
  apiKeyId: string;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  reason: string;
  metadata: Record<string, any>;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

// =============================================================================
// CONFIGURATION INTERFACES
// =============================================================================

/**
 * Usage tracking configuration
 */
export interface UsageTrackingConfig {
  enableRealTimeTracking: boolean;
  minimumProfitMargin: number; // Default 60%
  lowBalanceThreshold: number; // Default 10% of initial credits
  alertWebhookUrl?: string;
  enableUsageExport: boolean;
  retentionPeriodDays: number; // Default 90 days
  pricingTiers: Record<string, PricingTier>;
  modelCosts: Record<string, ModelCostConfig>;
}

/**
 * Real-time usage metrics
 */
export interface RealtimeUsageMetrics {
  timestamp: Date;
  activeApiKeys: number;
  requestsPerSecond: number;
  averageProcessingTimeMs: number;
  currentProfitMargin: number;
  creditsConsumedLastHour: number;
  errorRate: number;
  topModels: string[];
  alertCount: number;
}

// =============================================================================
// TYPE GUARDS AND VALIDATORS
// =============================================================================

/**
 * Type guard for usage record
 */
export function isUsageRecord(obj: any): obj is UsageRecord {
  return (
    typeof obj === 'object' &&
    typeof obj.customerId === 'string' &&
    typeof obj.apiKeyId === 'string' &&
    typeof obj.modelCostUsd === 'number' &&
    typeof obj.customerPriceUsd === 'number' &&
    typeof obj.creditsUsed === 'number' &&
    typeof obj.success === 'boolean'
  );
}

/**
 * Type guard for processing result
 */
export function isProcessingResult(obj: any): obj is ProcessingResult {
  return (
    typeof obj === 'object' &&
    typeof obj.success === 'boolean' &&
    typeof obj.documentId === 'string' &&
    typeof obj.model === 'string' &&
    typeof obj.costUsd === 'number'
  );
}

/**
 * Type guard for customer context
 */
export function isCustomerContext(obj: any): obj is CustomerContext {
  return (
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.tier === 'string' &&
    ['standard', 'premium', 'enterprise'].includes(obj.tier)
  );
}

// =============================================================================
// DEFAULT CONFIGURATIONS
// =============================================================================

/**
 * Default pricing tiers
 */
export const DEFAULT_PRICING_TIERS: Record<string, PricingTier> = {
  starter: {
    name: 'starter',
    markupMultiplier: 2.0,    // 100% markup
    freeCredits: 100,         // $1 worth
    rateLimit: 50,            // 50 requests/hour
    features: ['basic_processing', 'default_agents']
  },
  professional: {
    name: 'professional',
    markupMultiplier: 1.6,    // 60% markup
    freeCredits: 1000,        // $10 worth
    rateLimit: 500,           // 500 requests/hour
    features: ['advanced_processing', 'custom_agents', 'priority_support']
  },
  enterprise: {
    name: 'enterprise',
    markupMultiplier: 1.4,    // 40% markup
    freeCredits: 5000,        // $50 worth
    rateLimit: 5000,          // 5000 requests/hour
    features: ['premium_processing', 'unlimited_agents', 'dedicated_support', 'sla']
  }
};

/**
 * Default model costs (per 1K tokens)
 */
export const DEFAULT_MODEL_COSTS: Record<string, ModelCostConfig> = {
  'gpt-4o-mini': {
    modelName: 'gpt-4o-mini',
    provider: 'openai',
    inputTokenCost: 0.00015,   // $0.15/1M tokens
    outputTokenCost: 0.0006,   // $0.60/1M tokens
    tier: 'fast',
    enabled: true
  },
  'gpt-4o': {
    modelName: 'gpt-4o',
    provider: 'openai',
    inputTokenCost: 0.0025,    // $2.50/1M tokens
    outputTokenCost: 0.01,     // $10/1M tokens
    tier: 'smart',
    enabled: true
  },
  'claude-3-haiku': {
    modelName: 'claude-3-haiku',
    provider: 'claude',
    inputTokenCost: 0.00025,   // $0.25/1M tokens
    outputTokenCost: 0.00125,  // $1.25/1M tokens
    tier: 'fast',
    enabled: true
  },
  'claude-3.5-sonnet': {
    modelName: 'claude-3.5-sonnet',
    provider: 'claude',
    inputTokenCost: 0.003,     // $3/1M tokens
    outputTokenCost: 0.015,    // $15/1M tokens
    tier: 'smart',
    enabled: true
  }
};

/**
 * Default usage tracking configuration
 */
export const DEFAULT_USAGE_CONFIG: UsageTrackingConfig = {
  enableRealTimeTracking: true,
  minimumProfitMargin: 60,     // 60% minimum
  lowBalanceThreshold: 0.1,    // 10% of initial credits
  enableUsageExport: true,
  retentionPeriodDays: 90,
  pricingTiers: DEFAULT_PRICING_TIERS,
  modelCosts: DEFAULT_MODEL_COSTS
};