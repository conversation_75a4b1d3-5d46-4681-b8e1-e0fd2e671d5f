export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      agent_change_log: {
        Row: {
          agent_id: string
          change_type: string
          changes: Json
          customer_id: string
          id: string
          ip_address: unknown | null
          previous_values: Json | null
          session_id: string | null
          timestamp: string
          user_agent: string | null
          validation_passed: boolean
          validation_results: Json | null
        }
        Insert: {
          agent_id: string
          change_type: string
          changes: Json
          customer_id: string
          id?: string
          ip_address?: unknown | null
          previous_values?: Json | null
          session_id?: string | null
          timestamp?: string
          user_agent?: string | null
          validation_passed?: boolean
          validation_results?: Json | null
        }
        Update: {
          agent_id?: string
          change_type?: string
          changes?: Json
          customer_id?: string
          id?: string
          ip_address?: unknown | null
          previous_values?: Json | null
          session_id?: string | null
          timestamp?: string
          user_agent?: string | null
          validation_passed?: boolean
          validation_results?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_change_log_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_change_log_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_clone_configs: {
        Row: {
          agent_id: string
          clone_operation_id: string
          clone_settings: Json | null
          clone_source_type: string
          created_at: string | null
          customer_id: string
          id: string
          updated_at: string | null
        }
        Insert: {
          agent_id: string
          clone_operation_id: string
          clone_settings?: Json | null
          clone_source_type?: string
          created_at?: string | null
          customer_id: string
          id?: string
          updated_at?: string | null
        }
        Update: {
          agent_id?: string
          clone_operation_id?: string
          clone_settings?: Json | null
          clone_source_type?: string
          created_at?: string | null
          customer_id?: string
          id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_clone_configs_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: true
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_clone_configs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_cloning_audit_log: {
        Row: {
          api_key_id: string | null
          created_at: string | null
          customer_id: string
          error_message: string | null
          id: string
          operation_details: Json | null
          operation_id: string
          operation_status: string
          operation_type: string
          source_agent_id: string | null
          target_agent_id: string | null
        }
        Insert: {
          api_key_id?: string | null
          created_at?: string | null
          customer_id: string
          error_message?: string | null
          id?: string
          operation_details?: Json | null
          operation_id: string
          operation_status: string
          operation_type: string
          source_agent_id?: string | null
          target_agent_id?: string | null
        }
        Update: {
          api_key_id?: string | null
          created_at?: string | null
          customer_id?: string
          error_message?: string | null
          id?: string
          operation_details?: Json | null
          operation_id?: string
          operation_status?: string
          operation_type?: string
          source_agent_id?: string | null
          target_agent_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_cloning_audit_log_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_cloning_audit_log_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_cloning_audit_log_source_agent_id_fkey"
            columns: ["source_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_cloning_audit_log_target_agent_id_fkey"
            columns: ["target_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_customization_tracking: {
        Row: {
          agent_id: string
          changes_made: Json
          customer_id: string
          customization_type: string
          customized_at: string
          id: string
          metadata: Json | null
          parent_agent_id: string
          performance_after: Json | null
          performance_before: Json | null
          performance_evaluated_at: string | null
          performance_evaluation_period_days: number | null
        }
        Insert: {
          agent_id: string
          changes_made: Json
          customer_id: string
          customization_type: string
          customized_at?: string
          id?: string
          metadata?: Json | null
          parent_agent_id: string
          performance_after?: Json | null
          performance_before?: Json | null
          performance_evaluated_at?: string | null
          performance_evaluation_period_days?: number | null
        }
        Update: {
          agent_id?: string
          changes_made?: Json
          customer_id?: string
          customization_type?: string
          customized_at?: string
          id?: string
          metadata?: Json | null
          parent_agent_id?: string
          performance_after?: Json | null
          performance_before?: Json | null
          performance_evaluated_at?: string | null
          performance_evaluation_period_days?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_customization_tracking_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_customization_tracking_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_customization_tracking_parent_agent_id_fkey"
            columns: ["parent_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_inheritance: {
        Row: {
          child_agent_id: string
          created_at: string | null
          id: string
          parent_agent_id: string
          updated_at: string | null
        }
        Insert: {
          child_agent_id: string
          created_at?: string | null
          id?: string
          parent_agent_id: string
          updated_at?: string | null
        }
        Update: {
          child_agent_id?: string
          created_at?: string | null
          id?: string
          parent_agent_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_inheritance_child_agent_id_fkey"
            columns: ["child_agent_id"]
            isOneToOne: true
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_inheritance_parent_agent_id_fkey"
            columns: ["parent_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_performance_daily: {
        Row: {
          agent_id: string
          avg_accuracy_score: number | null
          avg_confidence_score: number | null
          avg_cost_per_request: number
          avg_input_tokens: number
          avg_output_tokens: number
          avg_processing_time_ms: number
          created_at: string
          date: string
          document_type: string
          error_breakdown: Json | null
          failed_requests: number
          id: string
          max_accuracy_score: number | null
          max_processing_time_ms: number
          median_processing_time_ms: number
          min_accuracy_score: number | null
          min_processing_time_ms: number
          model_distribution: Json | null
          p95_processing_time_ms: number
          successful_requests: number
          total_cost_usd: number
          total_input_tokens: number
          total_output_tokens: number
          total_requests: number
          updated_at: string
        }
        Insert: {
          agent_id: string
          avg_accuracy_score?: number | null
          avg_confidence_score?: number | null
          avg_cost_per_request?: number
          avg_input_tokens?: number
          avg_output_tokens?: number
          avg_processing_time_ms?: number
          created_at?: string
          date: string
          document_type: string
          error_breakdown?: Json | null
          failed_requests?: number
          id?: string
          max_accuracy_score?: number | null
          max_processing_time_ms?: number
          median_processing_time_ms?: number
          min_accuracy_score?: number | null
          min_processing_time_ms?: number
          model_distribution?: Json | null
          p95_processing_time_ms?: number
          successful_requests?: number
          total_cost_usd?: number
          total_input_tokens?: number
          total_output_tokens?: number
          total_requests?: number
          updated_at?: string
        }
        Update: {
          agent_id?: string
          avg_accuracy_score?: number | null
          avg_confidence_score?: number | null
          avg_cost_per_request?: number
          avg_input_tokens?: number
          avg_output_tokens?: number
          avg_processing_time_ms?: number
          created_at?: string
          date?: string
          document_type?: string
          error_breakdown?: Json | null
          failed_requests?: number
          id?: string
          max_accuracy_score?: number | null
          max_processing_time_ms?: number
          median_processing_time_ms?: number
          min_accuracy_score?: number | null
          min_processing_time_ms?: number
          model_distribution?: Json | null
          p95_processing_time_ms?: number
          successful_requests?: number
          total_cost_usd?: number
          total_input_tokens?: number
          total_output_tokens?: number
          total_requests?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_performance_daily_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_performance_logs: {
        Row: {
          accuracy_score: number | null
          agent_id: string
          confidence_score: number | null
          correlation_id: string
          cost_usd: number
          created_at: string
          customer_id: string
          document_id: string | null
          document_type: string
          error_message: string | null
          error_type: string | null
          id: string
          input_tokens: number
          metadata: Json | null
          model_used: string
          output_tokens: number
          processing_time_ms: number
          success: boolean
          timestamp: string
        }
        Insert: {
          accuracy_score?: number | null
          agent_id: string
          confidence_score?: number | null
          correlation_id: string
          cost_usd?: number
          created_at?: string
          customer_id: string
          document_id?: string | null
          document_type: string
          error_message?: string | null
          error_type?: string | null
          id?: string
          input_tokens?: number
          metadata?: Json | null
          model_used: string
          output_tokens?: number
          processing_time_ms: number
          success: boolean
          timestamp?: string
        }
        Update: {
          accuracy_score?: number | null
          agent_id?: string
          confidence_score?: number | null
          correlation_id?: string
          cost_usd?: number
          created_at?: string
          customer_id?: string
          document_id?: string | null
          document_type?: string
          error_message?: string | null
          error_type?: string | null
          id?: string
          input_tokens?: number
          metadata?: Json | null
          model_used?: string
          output_tokens?: number
          processing_time_ms?: number
          success?: boolean
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_performance_logs_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_performance_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_performance_logs_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_performance_metrics: {
        Row: {
          accuracy_score: number
          agent_id: string
          avg_processing_time_ms: number
          confidence_score: number
          created_at: string
          failed_extractions: number
          id: string
          successful_extractions: number
          test_document_count: number
          updated_at: string
        }
        Insert: {
          accuracy_score: number
          agent_id: string
          avg_processing_time_ms: number
          confidence_score: number
          created_at?: string
          failed_extractions?: number
          id?: string
          successful_extractions?: number
          test_document_count?: number
          updated_at?: string
        }
        Update: {
          accuracy_score?: number
          agent_id?: string
          avg_processing_time_ms?: number
          confidence_score?: number
          created_at?: string
          failed_extractions?: number
          id?: string
          successful_extractions?: number
          test_document_count?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_performance_metrics_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: true
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_preview_sessions: {
        Row: {
          agent_id: string
          created_at: string
          customer_id: string
          expires_at: string
          id: string
          performance_metrics: Json | null
          preview_config: Json
          preview_results: Json
          session_token: string
          test_documents: Json
        }
        Insert: {
          agent_id: string
          created_at?: string
          customer_id: string
          expires_at?: string
          id?: string
          performance_metrics?: Json | null
          preview_config: Json
          preview_results: Json
          session_token?: string
          test_documents: Json
        }
        Update: {
          agent_id?: string
          created_at?: string
          customer_id?: string
          expires_at?: string
          id?: string
          performance_metrics?: Json | null
          preview_config?: Json
          preview_results?: Json
          session_token?: string
          test_documents?: Json
        }
        Relationships: [
          {
            foreignKeyName: "agent_preview_sessions_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_preview_sessions_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_versions: {
        Row: {
          agent_id: string
          category: string
          changes_summary: Json | null
          created_at: string
          created_by: string
          description: string | null
          id: string
          is_current: boolean
          is_published: boolean
          name: string
          output_schema: Json
          parent_version_id: string | null
          performance_metrics: Json | null
          processing_config: Json
          system_prompt: string
          version_name: string
          version_number: string
        }
        Insert: {
          agent_id: string
          category: string
          changes_summary?: Json | null
          created_at?: string
          created_by: string
          description?: string | null
          id?: string
          is_current?: boolean
          is_published?: boolean
          name: string
          output_schema: Json
          parent_version_id?: string | null
          performance_metrics?: Json | null
          processing_config?: Json
          system_prompt: string
          version_name: string
          version_number: string
        }
        Update: {
          agent_id?: string
          category?: string
          changes_summary?: Json | null
          created_at?: string
          created_by?: string
          description?: string | null
          id?: string
          is_current?: boolean
          is_published?: boolean
          name?: string
          output_schema?: Json
          parent_version_id?: string | null
          performance_metrics?: Json | null
          processing_config?: Json
          system_prompt?: string
          version_name?: string
          version_number?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_versions_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_versions_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_versions_parent_version_id_fkey"
            columns: ["parent_version_id"]
            isOneToOne: false
            referencedRelation: "agent_versions"
            referencedColumns: ["id"]
          },
        ]
      }
      agents: {
        Row: {
          accuracy_rating: number | null
          agent_id: string
          avg_processing_time_ms: number | null
          category: string
          clone_count: number | null
          clone_permissions: Json | null
          cloned_from: string | null
          created_at: string
          current_version: string | null
          customer_id: string | null
          customization_url: string | null
          description: string | null
          id: string
          is_cloneable: boolean | null
          is_customizable: boolean
          is_default: boolean
          json_schema: Json
          last_customized_at: string | null
          last_customized_by: string | null
          last_performance_update: string | null
          last_used_at: string | null
          name: string
          output_schema: Json
          performance_stats: Json | null
          performance_trend: string | null
          processing_config: Json
          prompt: string
          status: string
          success_rate: number | null
          system_prompt: string
          updated_at: string
          usage_count: number
          version: number
        }
        Insert: {
          accuracy_rating?: number | null
          agent_id: string
          avg_processing_time_ms?: number | null
          category: string
          clone_count?: number | null
          clone_permissions?: Json | null
          cloned_from?: string | null
          created_at?: string
          current_version?: string | null
          customer_id?: string | null
          customization_url?: string | null
          description?: string | null
          id?: string
          is_cloneable?: boolean | null
          is_customizable?: boolean
          is_default?: boolean
          json_schema: Json
          last_customized_at?: string | null
          last_customized_by?: string | null
          last_performance_update?: string | null
          last_used_at?: string | null
          name: string
          output_schema: Json
          performance_stats?: Json | null
          performance_trend?: string | null
          processing_config?: Json
          prompt: string
          status?: string
          success_rate?: number | null
          system_prompt: string
          updated_at?: string
          usage_count?: number
          version?: number
        }
        Update: {
          accuracy_rating?: number | null
          agent_id?: string
          avg_processing_time_ms?: number | null
          category?: string
          clone_count?: number | null
          clone_permissions?: Json | null
          cloned_from?: string | null
          created_at?: string
          current_version?: string | null
          customer_id?: string | null
          customization_url?: string | null
          description?: string | null
          id?: string
          is_cloneable?: boolean | null
          is_customizable?: boolean
          is_default?: boolean
          json_schema?: Json
          last_customized_at?: string | null
          last_customized_by?: string | null
          last_performance_update?: string | null
          last_used_at?: string | null
          name?: string
          output_schema?: Json
          performance_stats?: Json | null
          performance_trend?: string | null
          processing_config?: Json
          prompt?: string
          status?: string
          success_rate?: number | null
          system_prompt?: string
          updated_at?: string
          usage_count?: number
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "agents_cloned_from_fkey"
            columns: ["cloned_from"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agents_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agents_last_customized_by_fkey"
            columns: ["last_customized_by"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      aggregation_jobs: {
        Row: {
          agent_id: string
          attempts: number
          completed_at: string | null
          created_at: string
          error_message: string | null
          id: string
          job_type: string
          last_error: Json | null
          max_attempts: number
          metadata: Json | null
          priority: number
          processing_time_ms: number | null
          started_at: string | null
          status: string
          target_date: string | null
        }
        Insert: {
          agent_id: string
          attempts?: number
          completed_at?: string | null
          created_at?: string
          error_message?: string | null
          id?: string
          job_type: string
          last_error?: Json | null
          max_attempts?: number
          metadata?: Json | null
          priority?: number
          processing_time_ms?: number | null
          started_at?: string | null
          status?: string
          target_date?: string | null
        }
        Update: {
          agent_id?: string
          attempts?: number
          completed_at?: string | null
          created_at?: string
          error_message?: string | null
          id?: string
          job_type?: string
          last_error?: Json | null
          max_attempts?: number
          metadata?: Json | null
          priority?: number
          processing_time_ms?: number | null
          started_at?: string | null
          status?: string
          target_date?: string | null
        }
        Relationships: []
      }
      aggregation_performance: {
        Row: {
          agent_id: string | null
          cpu_usage_percent: number | null
          error_details: Json | null
          executed_at: string
          execution_time_ms: number
          id: string
          job_type: string
          memory_usage_mb: number | null
          metadata: Json | null
          records_processed: number
          status: string
        }
        Insert: {
          agent_id?: string | null
          cpu_usage_percent?: number | null
          error_details?: Json | null
          executed_at?: string
          execution_time_ms: number
          id?: string
          job_type: string
          memory_usage_mb?: number | null
          metadata?: Json | null
          records_processed?: number
          status: string
        }
        Update: {
          agent_id?: string | null
          cpu_usage_percent?: number | null
          error_details?: Json | null
          executed_at?: string
          execution_time_ms?: number
          id?: string
          job_type?: string
          memory_usage_mb?: number | null
          metadata?: Json | null
          records_processed?: number
          status?: string
        }
        Relationships: []
      }
      api_keys: {
        Row: {
          created_at: string
          created_by: string | null
          credit_pool_id: string | null
          credits_allocated: number
          credits_used: number
          customer_id: string
          description: string | null
          expires_at: string | null
          id: string
          is_active: boolean
          is_revoked: boolean
          key_hash: string
          key_prefix: string
          key_type: string
          last_used_at: string | null
          last_used_ip: unknown | null
          low_balance_threshold: number | null
          name: string
          rate_limit_per_hour: number
          rate_limit_per_minute: number
          scope_restrictions: Json | null
          suspended_at: string | null
          suspension_reason: string | null
          tags: Json | null
          usage_notes: string | null
        }
        Insert: {
          created_at?: string
          created_by?: string | null
          credit_pool_id?: string | null
          credits_allocated?: number
          credits_used?: number
          customer_id: string
          description?: string | null
          expires_at?: string | null
          id?: string
          is_active?: boolean
          is_revoked?: boolean
          key_hash: string
          key_prefix: string
          key_type: string
          last_used_at?: string | null
          last_used_ip?: unknown | null
          low_balance_threshold?: number | null
          name: string
          rate_limit_per_hour?: number
          rate_limit_per_minute?: number
          scope_restrictions?: Json | null
          suspended_at?: string | null
          suspension_reason?: string | null
          tags?: Json | null
          usage_notes?: string | null
        }
        Update: {
          created_at?: string
          created_by?: string | null
          credit_pool_id?: string | null
          credits_allocated?: number
          credits_used?: number
          customer_id?: string
          description?: string | null
          expires_at?: string | null
          id?: string
          is_active?: boolean
          is_revoked?: boolean
          key_hash?: string
          key_prefix?: string
          key_type?: string
          last_used_at?: string | null
          last_used_ip?: unknown | null
          low_balance_threshold?: number | null
          name?: string
          rate_limit_per_hour?: number
          rate_limit_per_minute?: number
          scope_restrictions?: Json | null
          suspended_at?: string | null
          suspension_reason?: string | null
          tags?: Json | null
          usage_notes?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "api_keys_credit_pool_id_fkey"
            columns: ["credit_pool_id"]
            isOneToOne: false
            referencedRelation: "credit_pools"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "api_keys_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          actor_id: string | null
          actor_type: string
          created_at: string
          customer_id: string | null
          details: Json
          event_type: string
          id: string
          ip_address: unknown | null
          resource_id: string | null
          resource_type: string
          risk_level: string
          session_id: string | null
          user_agent: string | null
        }
        Insert: {
          action: string
          actor_id?: string | null
          actor_type?: string
          created_at?: string
          customer_id?: string | null
          details?: Json
          event_type: string
          id?: string
          ip_address?: unknown | null
          resource_id?: string | null
          resource_type: string
          risk_level?: string
          session_id?: string | null
          user_agent?: string | null
        }
        Update: {
          action?: string
          actor_id?: string | null
          actor_type?: string
          created_at?: string
          customer_id?: string | null
          details?: Json
          event_type?: string
          id?: string
          ip_address?: unknown | null
          resource_id?: string | null
          resource_type?: string
          risk_level?: string
          session_id?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      billing_events: {
        Row: {
          amount_cents: number | null
          created_at: string | null
          credits_affected: number | null
          currency: string | null
          customer_id: string
          event_type: string
          id: string
          invoice_id: string | null
          metadata: Json | null
          processed_at: string | null
          stripe_event_id: string | null
          subscription_id: string | null
        }
        Insert: {
          amount_cents?: number | null
          created_at?: string | null
          credits_affected?: number | null
          currency?: string | null
          customer_id: string
          event_type: string
          id?: string
          invoice_id?: string | null
          metadata?: Json | null
          processed_at?: string | null
          stripe_event_id?: string | null
          subscription_id?: string | null
        }
        Update: {
          amount_cents?: number | null
          created_at?: string | null
          credits_affected?: number | null
          currency?: string | null
          customer_id?: string
          event_type?: string
          id?: string
          invoice_id?: string | null
          metadata?: Json | null
          processed_at?: string | null
          stripe_event_id?: string | null
          subscription_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "billing_events_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_alerts: {
        Row: {
          alert_type: string
          api_key_id: string | null
          created_at: string | null
          customer_id: string
          id: string
          is_enabled: boolean
          last_triggered_at: string | null
          notification_channels: Json | null
          threshold_percentage: number | null
          threshold_value: number | null
          updated_at: string | null
        }
        Insert: {
          alert_type: string
          api_key_id?: string | null
          created_at?: string | null
          customer_id: string
          id?: string
          is_enabled?: boolean
          last_triggered_at?: string | null
          notification_channels?: Json | null
          threshold_percentage?: number | null
          threshold_value?: number | null
          updated_at?: string | null
        }
        Update: {
          alert_type?: string
          api_key_id?: string | null
          created_at?: string | null
          customer_id?: string
          id?: string
          is_enabled?: boolean
          last_triggered_at?: string | null
          notification_channels?: Json | null
          threshold_percentage?: number | null
          threshold_value?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credit_alerts_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_alerts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_pools: {
        Row: {
          allocated_credits: number
          available_credits: number | null
          created_at: string | null
          customer_id: string
          expires_at: string | null
          id: string
          is_active: boolean
          pool_name: string
          total_credits: number
          updated_at: string | null
        }
        Insert: {
          allocated_credits?: number
          available_credits?: number | null
          created_at?: string | null
          customer_id: string
          expires_at?: string | null
          id?: string
          is_active?: boolean
          pool_name: string
          total_credits?: number
          updated_at?: string | null
        }
        Update: {
          allocated_credits?: number
          available_credits?: number | null
          created_at?: string | null
          customer_id?: string
          expires_at?: string | null
          id?: string
          is_active?: boolean
          pool_name?: string
          total_credits?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credit_pools_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_transactions: {
        Row: {
          admin_notes: string | null
          admin_user_id: string | null
          amount: number
          api_key_id: string | null
          balance_after: number
          balance_before: number
          correlation_id: string | null
          created_at: string
          customer_id: string
          id: string
          invoice_id: string | null
          metadata: Json | null
          payment_reference: string | null
          processed_at: string | null
          reason: string | null
          stripe_payment_intent_id: string | null
          transaction_type: string
        }
        Insert: {
          admin_notes?: string | null
          admin_user_id?: string | null
          amount: number
          api_key_id?: string | null
          balance_after?: number
          balance_before?: number
          correlation_id?: string | null
          created_at?: string
          customer_id: string
          id?: string
          invoice_id?: string | null
          metadata?: Json | null
          payment_reference?: string | null
          processed_at?: string | null
          reason?: string | null
          stripe_payment_intent_id?: string | null
          transaction_type: string
        }
        Update: {
          admin_notes?: string | null
          admin_user_id?: string | null
          amount?: number
          api_key_id?: string | null
          balance_after?: number
          balance_before?: number
          correlation_id?: string | null
          created_at?: string
          customer_id?: string
          id?: string
          invoice_id?: string | null
          metadata?: Json | null
          payment_reference?: string | null
          processed_at?: string | null
          reason?: string | null
          stripe_payment_intent_id?: string | null
          transaction_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "credit_transactions_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_transactions_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_clone_limits: {
        Row: {
          clones_used_this_month: number
          created_at: string | null
          current_month: string
          customer_id: string
          id: string
          max_bulk_clone_size: number
          max_clones_per_month: number
          tier: string
          updated_at: string | null
        }
        Insert: {
          clones_used_this_month?: number
          created_at?: string | null
          current_month?: string
          customer_id: string
          id?: string
          max_bulk_clone_size?: number
          max_clones_per_month?: number
          tier: string
          updated_at?: string | null
        }
        Update: {
          clones_used_this_month?: number
          created_at?: string | null
          current_month?: string
          customer_id?: string
          id?: string
          max_bulk_clone_size?: number
          max_clones_per_month?: number
          tier?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "customer_clone_limits_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: true
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          billing_cycle: string
          company_name: string | null
          contact_email: string | null
          created_at: string
          credits_available: number
          credits_purchased: number
          credits_used: number
          credits_used_lifetime: number
          customer_id: string
          deleted_at: string | null
          email: string
          id: string
          last_active_at: string | null
          last_billing_date: string | null
          name: string
          notes: string | null
          status: string
          suspended_at: string | null
          suspension_reason: string | null
          tier: string
          tier_settings: Json | null
          updated_at: string
        }
        Insert: {
          billing_cycle?: string
          company_name?: string | null
          contact_email?: string | null
          created_at?: string
          credits_available?: number
          credits_purchased?: number
          credits_used?: number
          credits_used_lifetime?: number
          customer_id: string
          deleted_at?: string | null
          email: string
          id?: string
          last_active_at?: string | null
          last_billing_date?: string | null
          name: string
          notes?: string | null
          status?: string
          suspended_at?: string | null
          suspension_reason?: string | null
          tier?: string
          tier_settings?: Json | null
          updated_at?: string
        }
        Update: {
          billing_cycle?: string
          company_name?: string | null
          contact_email?: string | null
          created_at?: string
          credits_available?: number
          credits_purchased?: number
          credits_used?: number
          credits_used_lifetime?: number
          customer_id?: string
          deleted_at?: string | null
          email?: string
          id?: string
          last_active_at?: string | null
          last_billing_date?: string | null
          name?: string
          notes?: string | null
          status?: string
          suspended_at?: string | null
          suspension_reason?: string | null
          tier?: string
          tier_settings?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
      documents: {
        Row: {
          agent_id: string | null
          content_preview: string | null
          created_at: string
          customer_id: string
          document_hash: string
          file_size: number | null
          id: string
          mime_type: string | null
          original_filename: string | null
          processed_at: string
          processing_time_ms: number | null
          retention_expires_at: string | null
          status: string
          storage_path: string | null
          updated_at: string
        }
        Insert: {
          agent_id?: string | null
          content_preview?: string | null
          created_at?: string
          customer_id: string
          document_hash: string
          file_size?: number | null
          id?: string
          mime_type?: string | null
          original_filename?: string | null
          processed_at?: string
          processing_time_ms?: number | null
          retention_expires_at?: string | null
          status?: string
          storage_path?: string | null
          updated_at?: string
        }
        Update: {
          agent_id?: string | null
          content_preview?: string | null
          created_at?: string
          customer_id?: string
          document_hash?: string
          file_size?: number | null
          id?: string
          mime_type?: string | null
          original_filename?: string | null
          processed_at?: string
          processing_time_ms?: number | null
          retention_expires_at?: string | null
          status?: string
          storage_path?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      extraction_results: {
        Row: {
          agent_id: string
          confidence_score: number | null
          created_at: string
          customer_id: string
          document_id: string
          extracted_data: Json
          id: string
          input_tokens: number | null
          model_used: string
          model_version: string | null
          output_tokens: number | null
          processing_cost_usd: number | null
          processing_time_ms: number | null
          schema_valid: boolean
          validation_errors: Json | null
        }
        Insert: {
          agent_id: string
          confidence_score?: number | null
          created_at?: string
          customer_id: string
          document_id: string
          extracted_data: Json
          id?: string
          input_tokens?: number | null
          model_used: string
          model_version?: string | null
          output_tokens?: number | null
          processing_cost_usd?: number | null
          processing_time_ms?: number | null
          schema_valid?: boolean
          validation_errors?: Json | null
        }
        Update: {
          agent_id?: string
          confidence_score?: number | null
          created_at?: string
          customer_id?: string
          document_id?: string
          extracted_data?: Json
          id?: string
          input_tokens?: number | null
          model_used?: string
          model_version?: string | null
          output_tokens?: number | null
          processing_cost_usd?: number | null
          processing_time_ms?: number | null
          schema_valid?: boolean
          validation_errors?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "extraction_results_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "extraction_results_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "extraction_results_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
      performance_alerts: {
        Row: {
          acknowledged: boolean
          acknowledged_at: string | null
          acknowledged_by: string | null
          actual_value: number | null
          agent_id: string
          alert_type: string
          created_at: string
          customer_id: string | null
          id: string
          message: string
          metadata: Json | null
          resolved: boolean
          resolved_at: string | null
          resolved_by: string | null
          severity: string
          threshold_value: number | null
          updated_at: string
        }
        Insert: {
          acknowledged?: boolean
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          actual_value?: number | null
          agent_id: string
          alert_type: string
          created_at?: string
          customer_id?: string | null
          id?: string
          message: string
          metadata?: Json | null
          resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          severity: string
          threshold_value?: number | null
          updated_at?: string
        }
        Update: {
          acknowledged?: boolean
          acknowledged_at?: string | null
          acknowledged_by?: string | null
          actual_value?: number | null
          agent_id?: string
          alert_type?: string
          created_at?: string
          customer_id?: string | null
          id?: string
          message?: string
          metadata?: Json | null
          resolved?: boolean
          resolved_at?: string | null
          resolved_by?: string | null
          severity?: string
          threshold_value?: number | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "performance_alerts_acknowledged_by_fkey"
            columns: ["acknowledged_by"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "performance_alerts_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "performance_alerts_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "performance_alerts_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      performance_metrics: {
        Row: {
          created_date: string | null
          customer_id: string | null
          duration_ms: number
          error_message: string | null
          id: string
          metadata: Json | null
          operation: string
          success: boolean | null
          timestamp: string
        }
        Insert: {
          created_date?: string | null
          customer_id?: string | null
          duration_ms: number
          error_message?: string | null
          id?: string
          metadata?: Json | null
          operation: string
          success?: boolean | null
          timestamp?: string
        }
        Update: {
          created_date?: string | null
          customer_id?: string | null
          duration_ms?: number
          error_message?: string | null
          id?: string
          metadata?: Json | null
          operation?: string
          success?: boolean | null
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "performance_metrics_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      processing_status: {
        Row: {
          completed_at: string | null
          created_at: string | null
          current_stage: string | null
          document_id: string
          error_message: string | null
          id: string
          metadata: Json | null
          stage: string
          stages: Json | null
          started_at: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          completed_at?: string | null
          created_at?: string | null
          current_stage?: string | null
          document_id: string
          error_message?: string | null
          id?: string
          metadata?: Json | null
          stage: string
          stages?: Json | null
          started_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          completed_at?: string | null
          created_at?: string | null
          current_stage?: string | null
          document_id?: string
          error_message?: string | null
          id?: string
          metadata?: Json | null
          stage?: string
          stages?: Json | null
          started_at?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "processing_status_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
      rate_limit_analytics: {
        Row: {
          api_key_id: string | null
          avg_response_time_ms: number | null
          blocked_requests: number
          burst_requests: number
          burst_utilization_percent: number | null
          computed_at: string | null
          customer_id: string | null
          endpoint_pattern: string | null
          id: string
          limit_utilization_percent: number | null
          top_regions: Json | null
          total_requests: number
          window_end: string
          window_start: string
          window_type: string
        }
        Insert: {
          api_key_id?: string | null
          avg_response_time_ms?: number | null
          blocked_requests?: number
          burst_requests?: number
          burst_utilization_percent?: number | null
          computed_at?: string | null
          customer_id?: string | null
          endpoint_pattern?: string | null
          id?: string
          limit_utilization_percent?: number | null
          top_regions?: Json | null
          total_requests?: number
          window_end: string
          window_start: string
          window_type: string
        }
        Update: {
          api_key_id?: string | null
          avg_response_time_ms?: number | null
          blocked_requests?: number
          burst_requests?: number
          burst_utilization_percent?: number | null
          computed_at?: string | null
          customer_id?: string | null
          endpoint_pattern?: string | null
          id?: string
          limit_utilization_percent?: number | null
          top_regions?: Json | null
          total_requests?: number
          window_end?: string
          window_start?: string
          window_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "rate_limit_analytics_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rate_limit_analytics_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      rate_limit_violations: {
        Row: {
          api_key_id: string | null
          attempted_requests: number
          burst_exceeded: boolean | null
          created_at: string | null
          customer_id: string | null
          endpoint_called: string
          geographic_region: string | null
          id: string
          ip_address: unknown | null
          limit_exceeded: number
          method: string
          rate_limit_id: string | null
          resolved_at: string | null
          response_code: number
          retry_after_seconds: number | null
          user_agent: string | null
          violation_time: string | null
        }
        Insert: {
          api_key_id?: string | null
          attempted_requests?: number
          burst_exceeded?: boolean | null
          created_at?: string | null
          customer_id?: string | null
          endpoint_called: string
          geographic_region?: string | null
          id?: string
          ip_address?: unknown | null
          limit_exceeded: number
          method: string
          rate_limit_id?: string | null
          resolved_at?: string | null
          response_code: number
          retry_after_seconds?: number | null
          user_agent?: string | null
          violation_time?: string | null
        }
        Update: {
          api_key_id?: string | null
          attempted_requests?: number
          burst_exceeded?: boolean | null
          created_at?: string | null
          customer_id?: string | null
          endpoint_called?: string
          geographic_region?: string | null
          id?: string
          ip_address?: unknown | null
          limit_exceeded?: number
          method?: string
          rate_limit_id?: string | null
          resolved_at?: string | null
          response_code?: number
          retry_after_seconds?: number | null
          user_agent?: string | null
          violation_time?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rate_limit_violations_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rate_limit_violations_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rate_limit_violations_rate_limit_id_fkey"
            columns: ["rate_limit_id"]
            isOneToOne: false
            referencedRelation: "rate_limits"
            referencedColumns: ["id"]
          },
        ]
      }
      rate_limit_whitelist: {
        Row: {
          api_key_id: string | null
          approval_reason: string
          approved_by: string | null
          bypass_all_limits: boolean | null
          created_at: string | null
          customer_id: string | null
          id: string
          is_active: boolean | null
          requested_by: string
          specific_endpoints: string[] | null
          updated_at: string | null
          valid_from: string | null
          valid_until: string
          whitelist_type: string
        }
        Insert: {
          api_key_id?: string | null
          approval_reason: string
          approved_by?: string | null
          bypass_all_limits?: boolean | null
          created_at?: string | null
          customer_id?: string | null
          id?: string
          is_active?: boolean | null
          requested_by: string
          specific_endpoints?: string[] | null
          updated_at?: string | null
          valid_from?: string | null
          valid_until: string
          whitelist_type: string
        }
        Update: {
          api_key_id?: string | null
          approval_reason?: string
          approved_by?: string | null
          bypass_all_limits?: boolean | null
          created_at?: string | null
          customer_id?: string | null
          id?: string
          is_active?: boolean | null
          requested_by?: string
          specific_endpoints?: string[] | null
          updated_at?: string | null
          valid_from?: string | null
          valid_until?: string
          whitelist_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "rate_limit_whitelist_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rate_limit_whitelist_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      rate_limits: {
        Row: {
          algorithm_type: string | null
          api_key_id: string | null
          burst_allowance: number | null
          burst_used: number | null
          created_at: string
          created_by: string | null
          current_usage: number | null
          customer_id: string | null
          endpoint_pattern: string | null
          endpoint_patterns: string[] | null
          geographic_regions: string[] | null
          id: string
          is_active: boolean
          limit_type: string
          limit_value: number
          priority: number | null
          priority_level: number | null
          reset_at: string
          updated_at: string
        }
        Insert: {
          algorithm_type?: string | null
          api_key_id?: string | null
          burst_allowance?: number | null
          burst_used?: number | null
          created_at?: string
          created_by?: string | null
          current_usage?: number | null
          customer_id?: string | null
          endpoint_pattern?: string | null
          endpoint_patterns?: string[] | null
          geographic_regions?: string[] | null
          id?: string
          is_active?: boolean
          limit_type: string
          limit_value: number
          priority?: number | null
          priority_level?: number | null
          reset_at: string
          updated_at?: string
        }
        Update: {
          algorithm_type?: string | null
          api_key_id?: string | null
          burst_allowance?: number | null
          burst_used?: number | null
          created_at?: string
          created_by?: string | null
          current_usage?: number | null
          customer_id?: string | null
          endpoint_pattern?: string | null
          endpoint_patterns?: string[] | null
          geographic_regions?: string[] | null
          id?: string
          is_active?: boolean
          limit_type?: string
          limit_value?: number
          priority?: number | null
          priority_level?: number | null
          reset_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "rate_limits_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rate_limits_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      usage_logs: {
        Row: {
          agent_id: string | null
          api_key_id: string
          created_at: string
          credits_used: number
          customer_id: string
          customer_price_usd: number | null
          document_id: string | null
          endpoint: string
          error_message: string | null
          id: string
          input_tokens: number | null
          ip_address: unknown | null
          method: string
          model_cost_usd: number | null
          model_used: string | null
          output_tokens: number | null
          processing_time_ms: number | null
          request_id: string | null
          response_size_bytes: number | null
          status_code: number
          success: boolean
          user_agent: string | null
        }
        Insert: {
          agent_id?: string | null
          api_key_id: string
          created_at?: string
          credits_used?: number
          customer_id: string
          customer_price_usd?: number | null
          document_id?: string | null
          endpoint: string
          error_message?: string | null
          id?: string
          input_tokens?: number | null
          ip_address?: unknown | null
          method: string
          model_cost_usd?: number | null
          model_used?: string | null
          output_tokens?: number | null
          processing_time_ms?: number | null
          request_id?: string | null
          response_size_bytes?: number | null
          status_code: number
          success?: boolean
          user_agent?: string | null
        }
        Update: {
          agent_id?: string | null
          api_key_id?: string
          created_at?: string
          credits_used?: number
          customer_id?: string
          customer_price_usd?: number | null
          document_id?: string | null
          endpoint?: string
          error_message?: string | null
          id?: string
          input_tokens?: number | null
          ip_address?: unknown | null
          method?: string
          model_cost_usd?: number | null
          model_used?: string | null
          output_tokens?: number | null
          processing_time_ms?: number | null
          request_id?: string | null
          response_size_bytes?: number | null
          status_code?: number
          success?: boolean
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      daily_usage_summary: {
        Row: {
          api_key_id: string | null
          avg_processing_time: number | null
          customer_id: string | null
          failed_requests: number | null
          successful_requests: number | null
          total_credits: number | null
          total_model_cost: number | null
          total_requests: number | null
          total_revenue: number | null
          unique_agents_used: number | null
          usage_date: string | null
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      monthly_usage_summary: {
        Row: {
          avg_processing_time: number | null
          customer_id: string | null
          failed_requests: number | null
          profit_margin_percent: number | null
          successful_requests: number | null
          total_credits: number | null
          total_model_cost: number | null
          total_requests: number | null
          total_revenue: number | null
          usage_month: string | null
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      performance_summary: {
        Row: {
          avg_duration_ms: number | null
          error_count: number | null
          error_rate_percent: number | null
          max_duration_ms: number | null
          median_duration_ms: number | null
          min_duration_ms: number | null
          operation: string | null
          p95_duration_ms: number | null
          total_operations: number | null
        }
        Relationships: []
      }
      slow_operations: {
        Row: {
          customer_id: string | null
          duration_ms: number | null
          error_message: string | null
          metadata: Json | null
          operation: string | null
          timestamp: string | null
        }
        Relationships: [
          {
            foreignKeyName: "performance_metrics_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      bulk_api_key_operation: {
        Args: {
          p_admin_user_id: string
          p_key_ids: string[]
          p_operation: string
          p_parameters?: Json
        }
        Returns: Json
      }
      bulk_credit_operation: {
        Args: {
          p_admin_notes?: string
          p_admin_user_id: string
          p_amount: number
          p_customer_ids: string[]
          p_operation: string
        }
        Returns: Json
      }
      can_clone_agent: {
        Args: { p_agent_id: string; p_customer_id: string }
        Returns: boolean
      }
      check_aggregation_system_health: {
        Args: Record<PropertyKey, never>
        Returns: {
          check_name: string
          details: Json
          message: string
          status: string
        }[]
      }
      check_credit_alerts: {
        Args: { p_customer_id?: string }
        Returns: Json
      }
      check_rate_limit: {
        Args: {
          p_api_key_id?: string
          p_customer_id?: string
          p_endpoint?: string
          p_requests_to_add?: number
        }
        Returns: Json
      }
      cleanup_expired_preview_sessions: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_old_aggregation_jobs: {
        Args: { p_days_to_keep?: number }
        Returns: number
      }
      cleanup_performance_metrics: {
        Args: { retention_days?: number }
        Returns: number
      }
      create_agent_version: {
        Args: {
          p_agent_id: string
          p_changes_summary?: Json
          p_customer_id: string
          p_version_name: string
        }
        Returns: string
      }
      generate_api_key_hash: {
        Args: { raw_key: string }
        Returns: string
      }
      get_aggregation_job_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          avg_processing_time_ms: number
          count: number
          oldest_pending_age_minutes: number
          status: string
        }[]
      }
      get_current_agent_version: {
        Args: { agent_uuid: string }
        Returns: string
      }
      get_customer_clone_limits: {
        Args: { p_customer_id: string }
        Returns: {
          can_clone: boolean
          clones_used_this_month: number
          max_bulk_clone_size: number
          max_clones_per_month: number
        }[]
      }
      get_customer_tier_settings: {
        Args: { customer_tier: string }
        Returns: Json
      }
      get_default_agents_by_category: {
        Args: { agent_category?: string }
        Returns: {
          agent_id: string
          category: string
          id: string
          json_schema: Json
          name: string
          prompt: string
          version: number
        }[]
      }
      get_next_aggregation_job: {
        Args: Record<PropertyKey, never>
        Returns: {
          agent_id: string
          job_id: string
          job_type: string
          target_date: string
        }[]
      }
      get_performance_percentiles: {
        Args: { operation_name: string; time_window_hours?: number }
        Returns: {
          p50: number
          p75: number
          p90: number
          p95: number
          p99: number
        }[]
      }
      increment_clone_usage: {
        Args: { p_customer_id: string; p_increment?: number }
        Returns: boolean
      }
      process_credit_transaction: {
        Args:
          | {
              p_admin_notes?: string
              p_admin_user_id?: string
              p_amount: number
              p_api_key_id?: string
              p_customer_id: string
              p_metadata?: Json
              p_payment_reference?: string
              p_transaction_type: string
            }
          | {
              p_admin_notes?: string
              p_amount: number
              p_correlation_id?: string
              p_customer_id: string
              p_payment_reference?: string
              p_transaction_type: string
            }
        Returns: Json
      }
      process_daily_aggregation_job: {
        Args: { p_job_id: string }
        Returns: boolean
      }
      queue_aggregation_job: {
        Args: {
          p_agent_id: string
          p_job_type: string
          p_priority?: number
          p_target_date?: string
        }
        Returns: string
      }
      reset_rate_limit_window: {
        Args: { limit_id: string }
        Returns: undefined
      }
      rollback_agent_to_version: {
        Args: {
          p_agent_id: string
          p_customer_id: string
          p_version_id: string
        }
        Returns: boolean
      }
      update_agent_performance_daily: {
        Args: { p_agent_id: string; p_date: string; p_document_type: string }
        Returns: undefined
      }
      update_agent_summary_performance: {
        Args: { p_agent_id: string }
        Returns: undefined
      }
      validate_agent_processing_config: {
        Args: { config: Json }
        Returns: Json
      }
      validate_agent_schema: {
        Args: { schema_json: Json }
        Returns: boolean
      }
      validate_api_key_scope: {
        Args: {
          p_agent_id?: string
          p_endpoint?: string
          p_file_size?: number
          p_key_hash: string
        }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const

