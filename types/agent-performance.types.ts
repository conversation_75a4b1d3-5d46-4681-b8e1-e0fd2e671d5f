/**
 * Agent Performance Tracking Types
 * 
 * Type definitions for GitHub Issue #18: Agent Performance Tracking
 * Defines interfaces for performance metrics, benchmarking, pattern analysis, and alerts
 */

export interface AgentPerformanceMetrics {
  agent_id: string;
  customer_id: string;
  document_type: string;
  processing_time_ms: number;
  accuracy_score: number;
  confidence_score: number;
  success: boolean;
  error_type?: string;
  model_used: string;
  cost_usd: number;
  timestamp: Date;
  correlation_id: string;
  metadata?: Record<string, unknown>;
}

export interface AgentSummary {
  avgProcessingTime: number;
  avgAccuracy: number;
  avgConfidence: number;
  successRate: number;
  totalCost: number;
  requestCount: number;
  lastUpdated: Date;
}

export interface DailyAggregation {
  agent_id: string;
  document_type: string;
  date: string;
  total_requests: number;
  successful_requests: number;
  avg_processing_time_ms: number;
  avg_accuracy_score: number;
  avg_confidence_score: number;
  total_cost_usd: number;
  error_breakdown: Record<string, number>;
}

export interface AgentMetrics {
  requestCount: number;
  successRate: number;
  avgProcessingTime: number;
  avgAccuracy: number;
  avgConfidence: number;
  avgCost: number;
  errorTypes: ErrorTypeAnalysis[];
}

export interface ErrorTypeAnalysis {
  error_type: string;
  count: number;
  percentage: number;
  recent_increase: boolean;
}

export interface BenchmarkReport {
  agent_id: string;
  agent_name: string;
  parent_agent_id: string;
  parent_agent_name: string;
  comparison: {
    processing_time: MetricComparison;
    accuracy: MetricComparison;
    success_rate: MetricComparison;
    cost_efficiency: MetricComparison;
  };
  recommendation: string;
  generated_at: Date;
  timeframe: string;
}

export interface MetricComparison {
  custom: number;
  default: number;
  improvement_percent: number;
  trend: 'improving' | 'declining' | 'stable';
}

export interface CustomizationInsights {
  total_custom_agents: number;
  analyzed_date: Date;
  patterns: {
    prompt_modifications: PromptPatternAnalysis;
    schema_modifications: SchemaPatternAnalysis;
    popular_additions: PopularAddition[];
    performance_improvements: PerformanceImprovement[];
  };
  recommendations: PlatformRecommendation[];
}

export interface PromptPatternAnalysis {
  common_additions: KeywordPattern[];
  common_removals: KeywordPattern[];
  by_category: Record<string, CategoryPromptPattern>;
  modification_frequency: number;
}

export interface KeywordPattern {
  keywords: string[];
  usage_count: number;
  categories: string[];
  performance_impact: 'positive' | 'negative' | 'neutral';
}

export interface CategoryPromptPattern {
  category: string;
  most_common_additions: string[];
  most_common_removals: string[];
  avg_modification_length: number;
}

export interface SchemaPatternAnalysis {
  common_fields: SchemaFieldPattern[];
  field_type_changes: FieldTypeChange[];
  by_category: Record<string, CategorySchemaPattern>;
}

export interface SchemaFieldPattern {
  field_name: string;
  field_type: string;
  usage_count: number;
  categories: string[];
  typical_position: 'beginning' | 'middle' | 'end';
}

export interface FieldTypeChange {
  field_name: string;
  from_type: string;
  to_type: string;
  frequency: number;
  performance_impact: number;
}

export interface CategorySchemaPattern {
  category: string;
  most_added_fields: string[];
  most_modified_fields: string[];
  avg_fields_added: number;
}

export interface PopularAddition {
  type: 'prompt' | 'schema';
  description: string;
  usage_count: number;
  performance_improvement: number;
  recommended_for_default: boolean;
}

export interface PerformanceImprovement {
  pattern_description: string;
  agents_using: number;
  avg_improvement_percent: number;
  metric_improved: 'accuracy' | 'speed' | 'cost' | 'success_rate';
}

export interface PlatformRecommendation {
  type: 'new_default_agent' | 'schema_enhancement' | 'prompt_improvement' | 'performance_optimization';
  priority: 'high' | 'medium' | 'low';
  description: string;
  evidence: unknown[];
  estimated_adoption: string;
  estimated_impact: string;
  implementation_effort: 'small' | 'medium' | 'large';
}

export interface _PerformanceAlert {
  type: 'slow_processing' | 'low_accuracy' | 'high_error_rate' | 'cost_spike' | 'degraded_performance';
  severity: 'info' | 'warning' | 'error' | 'critical';
  agent_id: string;
  customer_id?: string;
  message: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
  threshold_value?: number;
  actual_value?: number;
}

export interface AlertConfig {
  slow_processing_threshold_ms: number;
  low_accuracy_threshold: number;
  high_error_rate_threshold: number;
  cost_spike_threshold_percent: number;
  evaluation_window_minutes: number;
}

export interface PerformanceThresholds {
  processing_time: {
    warning: number;
    error: number;
    critical: number;
  };
  accuracy: {
    warning: number;
    error: number;
    critical: number;
  };
  error_rate: {
    warning: number;
    error: number;
    critical: number;
  };
  cost_efficiency: {
    warning: number;
    error: number;
  };
}

export interface AgentPerformanceRecord {
  id: string;
  agent_id: string;
  customer_id: string;
  document_id: string;
  document_type: string;
  processing_time_ms: number;
  accuracy_score: number;
  confidence_score: number;
  success: boolean;
  error_type?: string;
  error_message?: string;
  model_used: string;
  input_tokens: number;
  output_tokens: number;
  cost_usd: number;
  correlation_id: string;
  timestamp: Date;
  metadata: Record<string, unknown>;
}

export interface AgentPerformanceDaily {
  id: string;
  agent_id: string;
  document_type: string;
  date: string;
  total_requests: number;
  successful_requests: number;
  avg_processing_time_ms: number;
  median_processing_time_ms: number;
  p95_processing_time_ms: number;
  avg_accuracy_score: number;
  avg_confidence_score: number;
  total_cost_usd: number;
  avg_cost_per_request: number;
  error_breakdown: Record<string, number>;
  model_distribution: Record<string, number>;
  created_at: Date;
  updated_at: Date;
}

export interface AgentCustomizationRecord {
  id: string;
  agent_id: string;
  parent_agent_id: string;
  customer_id: string;
  customization_type: 'prompt' | 'schema' | 'config';
  changes_made: Record<string, unknown>;
  performance_before: AgentMetrics;
  performance_after: AgentMetrics;
  customized_at: Date;
  performance_evaluation_period_days: number;
}

export interface AlertNotification {
  id: string;
  alert_id: string;
  notification_type: 'email' | 'webhook' | 'in_app';
  recipient: string;
  sent_at: Date;
  delivery_status: 'pending' | 'sent' | 'failed';
  retry_count: number;
}

// Request/Response types for API endpoints
export interface RecordPerformanceRequest {
  metrics: AgentPerformanceMetrics;
}

export interface RecordPerformanceResponse {
  success: boolean;
  metricsRecorded: boolean;
  aggregationsUpdated: boolean;
  alertsTriggered: _PerformanceAlert[];
  error?: string;
}

export interface GetBenchmarkRequest {
  agentId: string;
  timeframe?: 'day' | 'week' | 'month';
  compareWith?: string; // Parent agent ID override
}

export interface GetBenchmarkResponse {
  success: boolean;
  benchmark: BenchmarkReport;
  error?: string;
}

export interface GetCustomizationInsightsRequest {
  timeframe?: 'week' | 'month' | 'quarter';
  category?: string;
  minUsageCount?: number;
}

export interface GetCustomizationInsightsResponse {
  success: boolean;
  insights: CustomizationInsights;
  error?: string;
}

export interface Get_PerformanceAlertsRequest {
  agentId?: string;
  customerId?: string;
  severity?: string[];
  limit?: number;
  since?: Date;
}

export interface Get_PerformanceAlertsResponse {
  success: boolean;
  alerts: _PerformanceAlert[];
  total_count: number;
  error?: string;
}

export interface GetAgentSummaryRequest {
  agentId: string;
  timeframeDays?: number;
}

export interface GetAgentSummaryResponse {
  success: boolean;
  summary: AgentSummary;
  trends: {
    processing_time: 'improving' | 'declining' | 'stable';
    accuracy: 'improving' | 'declining' | 'stable';
    usage: 'increasing' | 'decreasing' | 'stable';
  };
  error?: string;
}

// Database table types for schema migrations
export interface AgentPerformanceLogsTable {
  id: string;
  agent_id: string;
  customer_id: string;
  document_id: string;
  document_type: string;
  processing_time_ms: number;
  accuracy_score: number;
  confidence_score: number;
  success: boolean;
  error_type?: string;
  error_message?: string;
  model_used: string;
  input_tokens: number;
  output_tokens: number;
  cost_usd: number;
  correlation_id: string;
  timestamp: Date;
  metadata: Record<string, unknown>;
  created_at: Date;
}

export interface AgentPerformanceDailyTable {
  id: string;
  agent_id: string;
  document_type: string;
  date: string;
  total_requests: number;
  successful_requests: number;
  avg_processing_time_ms: number;
  median_processing_time_ms: number;
  p95_processing_time_ms: number;
  avg_accuracy_score: number;
  avg_confidence_score: number;
  total_cost_usd: number;
  avg_cost_per_request: number;
  error_breakdown: Record<string, number>;
  model_distribution: Record<string, number>;
  created_at: Date;
  updated_at: Date;
}

export interface _PerformanceAlertsTable {
  id: string;
  agent_id: string;
  customer_id?: string;
  alert_type: string;
  severity: string;
  message: string;
  threshold_value?: number;
  actual_value?: number;
  metadata: Record<string, unknown>;
  acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: Date;
  resolved: boolean;
  resolved_by?: string;
  resolved_at?: Date;
  created_at: Date;
  updated_at: Date;
}