{"name": "idp-platform", "version": "0.1.0", "private": true, "type": "module", "scripts": {"type-check": "tsc --noEmit", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage", "test:manual": "node tests/run-manual-tests.js", "test:manual:auth": "node tests/integration/manual/test-auth.js", "test:manual:api": "node tests/integration/manual/test-api.js", "test:manual:database": "node tests/integration/manual/test-database.js", "test:manual:integration": "node tests/integration/manual/test-integration.js", "db:types": "supabase gen types typescript --local > types/database.types.ts", "db:types:remote": "supabase gen types typescript --project-id $SUPABASE_PROJECT_ID > types/database.types.ts", "db:reset": "supabase db reset", "db:migrate": "supabase migration up", "db:seed": "supabase seed run", "db:start": "supabase start", "db:stop": "supabase stop", "db:status": "supabase status", "supabase:login": "supabase login", "supabase:link": "supabase link", "supabase:deploy": "supabase db push", "functions:serve": "supabase functions serve", "functions:deploy": "supabase functions deploy", "functions:logs": "supabase functions logs", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky install", "clean": "rm -rf dist coverage .tmp", "clean:all": "bun run clean && rm -rf node_modules"}, "dependencies": {"@supabase/supabase-js": "^2.57.4", "zod": "^4.1.11", "date-fns": "^4.1.0", "file-type": "^21", "pdf-parse": "^1.1.1", "mammoth": "^1.11.0", "xlsx": "^0.18.5", "sharp": "^0.34.4", "dompurify": "^3.2.7", "jsdom": "^27"}, "devDependencies": {"@types/node": "^24.5.2", "@types/dompurify": "^3.2.0", "@types/jsdom": "^21.1.7", "@types/pdf-parse": "^1.1.5", "typescript": "^5.9.2", "eslint": "^9.36.0", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "prettier": "^3.6.2", "husky": "^9.1.7", "lint-staged": "^16.1.6", "happy-dom": "^18.0.1", "msw": "^2.11.3", "supabase": "^2.40.7"}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "engines": {"node": ">=20.0.0", "bun": ">=1.1.0"}, "packageManager": "bun@1.1.29"}