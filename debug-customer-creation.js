// Debug customer creation issue
const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const customerData = {
  customer_id: `test-debug-${Date.now()}`,
  name: 'Debug Test Company',
  email: '<EMAIL>',
  tier: 'professional'
};

console.log('Testing customer creation...');

fetch('http://127.0.0.1:14321/functions/v1/admin-customers', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${serviceRoleKey}`,
    'x-correlation-id': 'debug-test-123'
  },
  body: JSON.stringify(customerData)
})
.then(async response => {
  console.log('Status:', response.status);
  const data = await response.text();
  console.log('Response:', data);
})
.catch(error => {
  console.error('Error:', error);
});