#!/bin/bash

# Agent Customization Quality Gate Validation Script
# This script validates all aspects of the agent customization implementation
# for CI/CD pipeline integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REQUIRED_PASS_RATE=100
REQUIRED_COVERAGE=90
MAX_TEST_DURATION=120 # seconds
PERFORMANCE_THRESHOLD=500 # ms average response time

echo -e "${BLUE}🚀 Starting Agent Customization Quality Gate Validation${NC}"
echo "=================================================="

# Function to print status
print_status() {
    local status=$1
    local message=$2
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✅ $message${NC}"
    elif [ "$status" = "FAIL" ]; then
        echo -e "${RED}❌ $message${NC}"
    elif [ "$status" = "WARN" ]; then
        echo -e "${YELLOW}⚠️  $message${NC}"
    else
        echo -e "${BLUE}ℹ️  $message${NC}"
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "INFO" "Checking prerequisites..."
    
    # Check if Supabase is running
    if ! pgrep -f "supabase" > /dev/null; then
        print_status "FAIL" "Supabase is not running"
        echo "Please start Supabase with: supabase start"
        exit 1
    fi
    
    # Check if Edge Functions are served
    if ! curl -s http://localhost:54321/functions/v1/health > /dev/null 2>&1; then
        print_status "WARN" "Edge Functions may not be running"
        echo "Starting Edge Functions..."
        supabase functions serve --no-verify-jwt &
        sleep 10
    fi
    
    # Check Node.js/Bun
    if ! command -v bun &> /dev/null; then
        print_status "FAIL" "Bun is required but not installed"
        exit 1
    fi
    
    print_status "PASS" "Prerequisites check completed"
}

# Function to run unit tests
run_unit_tests() {
    print_status "INFO" "Running unit tests..."
    
    start_time=$(date +%s)
    
    # Run unit tests with coverage
    if bun test --timeout 45000 --coverage tests/unit/agent-customization.test.ts > test_output.log 2>&1; then
        end_time=$(date +%s)
        duration=$((end_time - start_time))
        
        # Extract pass/fail counts
        pass_count=$(grep -o "[0-9]\+ pass" test_output.log | grep -o "[0-9]\+" | head -1)
        fail_count=$(grep -o "[0-9]\+ fail" test_output.log | grep -o "[0-9]\+" | head -1)
        
        if [ -z "$fail_count" ]; then fail_count=0; fi
        if [ -z "$pass_count" ]; then pass_count=0; fi
        
        total_tests=$((pass_count + fail_count))
        pass_rate=0
        if [ $total_tests -gt 0 ]; then
            pass_rate=$((pass_count * 100 / total_tests))
        fi
        
        if [ $duration -gt $MAX_TEST_DURATION ]; then
            print_status "WARN" "Unit tests took ${duration}s (threshold: ${MAX_TEST_DURATION}s)"
        fi
        
        if [ $pass_rate -eq $REQUIRED_PASS_RATE ] && [ $fail_count -eq 0 ]; then
            print_status "PASS" "Unit tests: ${pass_count}/${total_tests} passed (${pass_rate}%) in ${duration}s"
        else
            print_status "FAIL" "Unit tests: ${pass_count}/${total_tests} passed (${pass_rate}%) - Required: ${REQUIRED_PASS_RATE}%"
            cat test_output.log
            return 1
        fi
    else
        print_status "FAIL" "Unit tests failed to run"
        cat test_output.log
        return 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "INFO" "Running integration tests..."
    
    # Note: Integration tests require actual Edge Functions
    if bun test --timeout 60000 tests/integration/agent-customization-api.test.ts > integration_output.log 2>&1; then
        print_status "PASS" "Integration tests completed successfully"
    else
        print_status "WARN" "Integration tests failed (may require live services)"
        # Don't fail the pipeline for integration tests in CI
        cat integration_output.log | head -20
    fi
}

# Function to run performance tests
run_performance_tests() {
    print_status "INFO" "Running performance benchmarks..."
    
    if bun test --timeout 120000 tests/performance/agent-customization-performance.test.ts > performance_output.log 2>&1; then
        # Extract performance metrics
        if grep -q "Average Response Time:" performance_output.log; then
            avg_time=$(grep "Average Response Time:" performance_output.log | grep -o "[0-9]\+\.[0-9]\+")
            if (( $(echo "$avg_time < $PERFORMANCE_THRESHOLD" | bc -l) )); then
                print_status "PASS" "Performance tests: Average response time ${avg_time}ms (threshold: ${PERFORMANCE_THRESHOLD}ms)"
            else
                print_status "WARN" "Performance tests: Average response time ${avg_time}ms exceeds threshold"
            fi
        else
            print_status "PASS" "Performance tests completed"
        fi
    else
        print_status "WARN" "Performance tests failed (may require live services)"
        cat performance_output.log | head -10
    fi
}

# Function to validate code quality
validate_code_quality() {
    print_status "INFO" "Validating code quality..."
    
    # TypeScript compilation check
    if bun run type-check > /dev/null 2>&1; then
        print_status "PASS" "TypeScript compilation successful"
    else
        print_status "FAIL" "TypeScript compilation failed"
        bun run type-check
        return 1
    fi
    
    # Linting check
    if bun run lint > /dev/null 2>&1; then
        print_status "PASS" "Code linting successful"
    else
        print_status "WARN" "Linting issues found"
        bun run lint | head -10
    fi
    
    # Check test file completeness
    if [ -f "tests/unit/agent-customization.test.ts" ] && 
       [ -f "tests/integration/agent-customization-api.test.ts" ] &&
       [ -f "tests/performance/agent-customization-performance.test.ts" ]; then
        print_status "PASS" "All required test files present"
    else
        print_status "FAIL" "Missing required test files"
        return 1
    fi
}

# Function to validate implementation completeness
validate_implementation() {
    print_status "INFO" "Validating implementation completeness..."
    
    # Check for required functions/files
    required_files=(
        "supabase/functions/shared/rate-limiter.ts"
        "supabase/functions/shared/observability.ts"
    )
    
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_status "PASS" "Required file exists: $file"
        else
            print_status "FAIL" "Missing required file: $file"
            return 1
        fi
    done
    
    # Check for implementation keywords in test files
    if grep -q "Not implemented yet" tests/unit/agent-customization.test.ts; then
        print_status "FAIL" "Unit tests contain unimplemented functions"
        grep -n "Not implemented yet" tests/unit/agent-customization.test.ts
        return 1
    else
        print_status "PASS" "All unit test functions implemented"
    fi
}

# Function to generate quality report
generate_report() {
    print_status "INFO" "Generating quality report..."
    
    report_file="qa-validation-report.md"
    
    cat > "$report_file" << EOF
# Agent Customization Quality Validation Report

**Generated:** $(date)
**Pipeline:** CI/CD Validation
**Status:** $OVERALL_STATUS

## Test Results Summary

### Unit Tests
- **Status:** $UNIT_TEST_STATUS
- **Pass Rate:** $pass_rate% ($pass_count/$total_tests tests)
- **Duration:** ${duration}s

### Integration Tests
- **Status:** $INTEGRATION_TEST_STATUS
- **Note:** Integration tests require live services

### Performance Tests
- **Status:** $PERFORMANCE_TEST_STATUS
- **Average Response Time:** ${avg_time:-"N/A"}ms
- **Threshold:** ${PERFORMANCE_THRESHOLD}ms

### Code Quality
- **TypeScript Compilation:** $TYPESCRIPT_STATUS
- **Linting:** $LINT_STATUS
- **Implementation Completeness:** $IMPLEMENTATION_STATUS

## Quality Gate Decision

EOF

    if [ "$OVERALL_STATUS" = "PASS" ]; then
        cat >> "$report_file" << EOF
✅ **APPROVED FOR PRODUCTION**

All quality gates have been satisfied:
- Unit tests: 100% pass rate
- Code quality: Acceptable
- Implementation: Complete
- Performance: Within thresholds

The Agent Customization feature is ready for production deployment.
EOF
    else
        cat >> "$report_file" << EOF
❌ **REQUIRES REMEDIATION**

Quality gate failures detected. Please address the issues above before proceeding to production.

Critical issues must be resolved before deployment.
EOF
    fi
    
    print_status "INFO" "Quality report generated: $report_file"
}

# Main execution flow
main() {
    # Initialize status variables
    OVERALL_STATUS="PASS"
    UNIT_TEST_STATUS="UNKNOWN"
    INTEGRATION_TEST_STATUS="UNKNOWN"
    PERFORMANCE_TEST_STATUS="UNKNOWN"
    TYPESCRIPT_STATUS="UNKNOWN"
    LINT_STATUS="UNKNOWN"
    IMPLEMENTATION_STATUS="UNKNOWN"
    
    # Cleanup previous runs
    rm -f test_output.log integration_output.log performance_output.log
    
    # Run all validations
    check_prerequisites || { OVERALL_STATUS="FAIL"; }
    
    if run_unit_tests; then
        UNIT_TEST_STATUS="PASS"
    else
        UNIT_TEST_STATUS="FAIL"
        OVERALL_STATUS="FAIL"
    fi
    
    if run_integration_tests; then
        INTEGRATION_TEST_STATUS="PASS"
    else
        INTEGRATION_TEST_STATUS="WARN"
        # Don't fail overall for integration tests
    fi
    
    if run_performance_tests; then
        PERFORMANCE_TEST_STATUS="PASS"
    else
        PERFORMANCE_TEST_STATUS="WARN"
        # Don't fail overall for performance tests in CI
    fi
    
    if validate_code_quality; then
        TYPESCRIPT_STATUS="PASS"
        LINT_STATUS="PASS"
    else
        TYPESCRIPT_STATUS="FAIL"
        LINT_STATUS="FAIL"
        OVERALL_STATUS="FAIL"
    fi
    
    if validate_implementation; then
        IMPLEMENTATION_STATUS="PASS"
    else
        IMPLEMENTATION_STATUS="FAIL"
        OVERALL_STATUS="FAIL"
    fi
    
    # Generate final report
    generate_report
    
    # Final status
    echo "=================================================="
    if [ "$OVERALL_STATUS" = "PASS" ]; then
        print_status "PASS" "Quality Gate: APPROVED FOR PRODUCTION"
        echo -e "${GREEN}🎉 Agent Customization implementation meets all quality standards!${NC}"
        exit 0
    else
        print_status "FAIL" "Quality Gate: REQUIRES REMEDIATION"
        echo -e "${RED}🚫 Agent Customization implementation has quality issues that must be addressed.${NC}"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    print_status "INFO" "Cleaning up temporary files..."
    rm -f test_output.log integration_output.log performance_output.log
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function
main "$@"