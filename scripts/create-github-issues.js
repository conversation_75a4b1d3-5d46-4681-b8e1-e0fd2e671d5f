#!/usr/bin/env node

/**
 * GitHub Issues Creation Script for IDP Platform
 *
 * Usage:
 * 1. Install: npm install @octokit/rest
 * 2. Set environment variable: GITHUB_TOKEN=your_personal_access_token
 * 3. Run: node scripts/create-github-issues.js
 */

const { Octokit } = require("@octokit/rest");

const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

const REPO_OWNER = "GPT-Integrators";
const REPO_NAME = "IDP-Platform";

// Epic 1 Stories
const stories = [
  {
    title: "[Epic 1] Project Infrastructure Setup",
    milestone: 1, // Epic 1 milestone number (you'll need to get this from GitHub)
    labels: ["story", "epic-1", "priority-high", "effort-M"],
    body: `## 🎯 User Story
As a **Platform Administrator**,
I want **a properly configured Supabase project with Edge Functions**,
So that **I have a scalable foundation for the document processing platform**.

## ✅ Acceptance Criteria
- [ ] Supabase project created with PostgreSQL database configured
- [ ] Edge Functions runtime environment setup with Deno configuration
- [ ] Environment variables configured for development and production
- [ ] Health check endpoint (\`GET /api/v1/health\`) returns system status
- [ ] Database connection pooling configured for concurrent requests

## 🏗️ Implementation Notes
**Core Technology Stack:**
- Runtime: Deno within Supabase Edge Functions
- Database: PostgreSQL 17 with Row-Level Security
- AI Integration: Multi-model fallback chain

**Key Files:**
- \`supabase/config.toml\` - Local development config
- \`supabase/functions/health/index.ts\` - Health endpoint
- \`.env\` - Environment variables

## 📊 Metadata
- **Effort**: M (1-3 days)
- **Priority**: High`
  },

  {
    title: "[Epic 1] Database Schema Foundation",
    milestone: 1,
    labels: ["story", "epic-1", "priority-high", "effort-L"],
    body: `## 🎯 User Story
As a **Platform Administrator**,
I want **a comprehensive database schema with proper security**,
So that **all platform data is structured and protected**.

## ✅ Acceptance Criteria
- [ ] All tables created with comprehensive comments
- [ ] Row-Level Security (RLS) policies implemented
- [ ] Database indexes created for performance
- [ ] API key storage with SHA-256 hashing
- [ ] TypeScript types auto-generated

## 🏗️ Implementation Notes
**Core Tables:**
1. \`customers\` - Company accounts with tier management
2. \`api_keys\` - SHA-256 hashed keys with dual credit systems
3. \`agents\` - Versioned default agents + customizations
4. \`documents\` - Processed documents with retention
5. \`usage_logs\` - Dual metrics tracking
6. \`audit_logs\` - Security event logging

**Files:**
- \`supabase/migrations/[timestamp]_initial_schema.sql\`
- \`types/database.types.ts\` (auto-generated)

## 📊 Metadata
- **Effort**: L (3-5 days)
- **Priority**: High`
  }

  // Add more stories here...
];

async function createIssues() {
  console.log("🚀 Creating GitHub issues for IDP Platform...");

  for (const story of stories) {
    try {
      const response = await octokit.rest.issues.create({
        owner: REPO_OWNER,
        repo: REPO_NAME,
        title: story.title,
        body: story.body,
        milestone: story.milestone,
        labels: story.labels,
      });

      console.log(`✅ Created: ${story.title} (#${response.data.number})`);
    } catch (error) {
      console.error(`❌ Failed to create: ${story.title}`, error.message);
    }
  }

  console.log("🎉 Issue creation complete!");
}

if (require.main === module) {
  if (!process.env.GITHUB_TOKEN) {
    console.error("❌ GITHUB_TOKEN environment variable required");
    process.exit(1);
  }

  createIssues().catch(console.error);
}

module.exports = { stories, createIssues };