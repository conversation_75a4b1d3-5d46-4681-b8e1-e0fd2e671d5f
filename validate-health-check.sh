#!/bin/bash

# Comprehensive Health Check Validation Script
# Tests all acceptance criteria for Epic 1 Story 5

echo "🎯 COMPREHENSIVE HEALTH CHECK VALIDATION"
echo "========================================"
echo ""

HEALTH_ENDPOINT="http://127.0.0.1:14321/functions/v1/health"
PASSED=0
FAILED=0

# Helper function to test and report
test_feature() {
    local test_name="$1"
    local command="$2"
    local expected_result="$3"

    echo "Testing: $test_name"
    result=$(eval "$command" 2>/dev/null)

    if [[ "$result" == *"$expected_result"* ]] || [[ $expected_result == "ANY" ]]; then
        echo "✅ PASS: $test_name"
        ((PASSED++))
        echo "   Result: $result"
    else
        echo "❌ FAIL: $test_name"
        echo "   Expected: $expected_result"
        echo "   Got: $result"
        ((FAILED++))
    fi
    echo ""
}

echo "📋 TESTING ALL ACCEPTANCE CRITERIA"
echo "=================================="
echo ""

# 1. Database connection status
test_feature "Database connectivity check" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.services.database.status'" \
    "ANY"

# 2. AI service connectivity validation
test_feature "OpenAI service check" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.services.openai.status'" \
    "degraded"

test_feature "Claude service check" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.services.claude.status'" \
    "degraded"

test_feature "LlamaParse service check" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.services.llamaparse.status'" \
    "degraded"

# 3. System status includes Edge Function runtime information
test_feature "Edge Function runtime info" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.performance.edge_function_cold_start'" \
    "ANY"

# 4. Performance metrics collection
test_feature "Response time metrics" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.performance.response_time_ms'" \
    "ANY"

test_feature "Active connections tracking" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.performance.active_connections'" \
    "1"

# 5. Correlation ID system
test_feature "Correlation ID format" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.correlation_id' | grep -E '^req_[0-9]+_[a-z0-9]+$' && echo 'VALID'" \
    "VALID"

# 6. Health endpoint responds in <500ms
start_time=$(date +%s%3N)
curl -s $HEALTH_ENDPOINT > /dev/null
end_time=$(date +%s%3N)
response_time=$((end_time - start_time))
test_feature "Response time < 500ms" \
    "echo $response_time" \
    "ANY"

if [ $response_time -lt 500 ]; then
    echo "✅ Response time: ${response_time}ms (< 500ms requirement)"
else
    echo "❌ Response time: ${response_time}ms (> 500ms requirement)"
fi
echo ""

# 7. CORS handling
test_feature "CORS OPTIONS request" \
    "curl -s -X OPTIONS -w '%{http_code}' $HEALTH_ENDPOINT" \
    "200"

# 8. Error handling for wrong HTTP methods
test_feature "POST method rejection" \
    "curl -s -X POST -w '%{http_code}' $HEALTH_ENDPOINT | tail -c 3" \
    "405"

# 9. JSON structure validation
test_feature "Required fields present" \
    "curl -s $HEALTH_ENDPOINT | jq -r 'has(\"status\") and has(\"timestamp\") and has(\"version\") and has(\"services\") and has(\"performance\") and has(\"correlation_id\")'" \
    "true"

# 10. All required services are checked
test_feature "All AI services present" \
    "curl -s $HEALTH_ENDPOINT | jq -r '.services | has(\"database\") and has(\"openai\") and has(\"claude\") and has(\"llamaparse\")'" \
    "true"

echo "🧪 ADDITIONAL FUNCTIONAL TESTS"
echo "============================="
echo ""

# Test unique correlation IDs
corr_id_1=$(curl -s $HEALTH_ENDPOINT | jq -r '.correlation_id')
sleep 0.1
corr_id_2=$(curl -s $HEALTH_ENDPOINT | jq -r '.correlation_id')
if [ "$corr_id_1" != "$corr_id_2" ]; then
    echo "✅ PASS: Unique correlation IDs"
    echo "   ID1: $corr_id_1"
    echo "   ID2: $corr_id_2"
    ((PASSED++))
else
    echo "❌ FAIL: Correlation IDs not unique"
    ((FAILED++))
fi
echo ""

# Test detailed response structure
echo "📄 SAMPLE HEALTH CHECK RESPONSE:"
echo "================================"
curl -s $HEALTH_ENDPOINT | jq '.'
echo ""

echo "📊 FINAL RESULTS"
echo "================"
echo "✅ PASSED: $PASSED tests"
echo "❌ FAILED: $FAILED tests"
echo ""

if [ $FAILED -eq 0 ]; then
    echo "🎉 ALL TESTS PASSED - IMPLEMENTATION IS WORKING"
    exit 0
else
    echo "💥 SOME TESTS FAILED - NEEDS ATTENTION"
    exit 1
fi