name: Project Management Automation

on:
  issues:
    types: [assigned, unassigned, closed, reopened]
  pull_request:
    types: [opened, closed, ready_for_review, converted_to_draft]

jobs:
  move_assigned_issue_to_in_progress:
    if: github.event_name == 'issues' && github.event.action == 'assigned'
    runs-on: ubuntu-latest
    steps:
      - name: Move assigned issue to In Progress
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: In Progress
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  move_pr_to_review:
    if: github.event_name == 'pull_request' && (github.event.action == 'opened' || github.event.action == 'ready_for_review')
    runs-on: ubuntu-latest
    steps:
      - name: Get linked issue
        id: get_issue
        uses: actions/github-script@v6
        with:
          script: |
            const pr = context.payload.pull_request;
            const body = pr.body || '';
            
            // Look for "Closes #123" or "Fixes #123" patterns
            const issueMatch = body.match(/(?:closes|fixes|resolves)\s+#(\d+)/i);
            if (issueMatch) {
              const issueNumber = issueMatch[1];
              return { issue_number: issueNumber };
            }
            return null;

      - name: Move linked issue to PR Review
        if: steps.get_issue.outputs.result != 'null'
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: PR Review
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  move_merged_pr_to_done:
    if: github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Move merged PR issue to Done
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: Done
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  move_closed_pr_back_to_backlog:
    if: github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == false
    runs-on: ubuntu-latest
    steps:
      - name: Move closed PR issue back to Backlog
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: Backlog
          repo-token: ${{ secrets.GITHUB_TOKEN }}