name: Deploy Edge Functions

on:
  push:
    branches: [main]
    paths:
      - 'supabase/functions/**'
      - '.github/workflows/deploy.yml'
  pull_request:
    branches: [main]
    paths:
      - 'supabase/functions/**'

env:
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPABASE_PROJECT_ID: ${{ secrets.SUPABASE_PROJECT_ID }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Start Supabase local development setup
        run: supabase start

      - name: Run tests
        run: bun test tests/unit/health.test.ts

      - name: Lint Edge Functions
        run: |
          cd supabase/functions
          deno lint --no-lock **/*.ts

      - name: Type check Edge Functions
        run: |
          cd supabase/functions
          deno check --no-lock **/*.ts

  deploy:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest

      - name: Deploy functions
        run: |
          supabase functions deploy --project-ref $SUPABASE_PROJECT_ID