# ================================================================================
# EPIC 5: PRODUCTION DEPLOYMENT PIPELINE
# ================================================================================

name: Production Deployment Pipeline

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      skip_tests:
        description: 'Skip test execution'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  DENO_VERSION: '1.43.0'

jobs:
  # ============================================================================
  # CODE QUALITY AND SECURITY CHECKS
  # ============================================================================
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    outputs:
      security-scan-passed: ${{ steps.security.outputs.passed }}
      quality-score: ${{ steps.quality.outputs.score }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Full history for better analysis
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest
      
      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: ${{ env.DENO_VERSION }}
      
      - name: Install dependencies
        run: bun install
      
      - name: TypeScript type checking
        run: |
          echo "::group::TypeScript Type Checking"
          bun run type-check
          echo "::endgroup::"
      
      - name: Lint Edge Functions
        run: |
          echo "::group::Deno Lint"
          cd supabase/functions
          deno lint --no-lock
          echo "::endgroup::"
      
      - name: Security scanning
        id: security
        run: |
          echo "::group::Security Scanning"
          # Check for hardcoded secrets
          if grep -r "sk-" . --exclude-dir=node_modules --exclude-dir=.git --exclude="*.md" --exclude="*.yml"; then
            echo "❌ Potential hardcoded API keys found"
            exit 1
          fi
          
          # Check for common security issues
          if grep -r "eval(" . --exclude-dir=node_modules --exclude-dir=.git; then
            echo "❌ Dangerous eval() usage found"
            exit 1
          fi
          
          echo "✅ Security scan passed"
          echo "passed=true" >> $GITHUB_OUTPUT
          echo "::endgroup::"
      
      - name: Code quality assessment
        id: quality
        run: |
          echo "::group::Code Quality Assessment"
          # Count TODO/FIXME comments
          todo_count=$(grep -r "TODO\|FIXME" . --exclude-dir=node_modules --exclude-dir=.git | wc -l)
          
          # Count TypeScript any usage
          any_count=$(grep -r ": any" . --include="*.ts" --exclude-dir=node_modules | wc -l)
          
          # Calculate quality score (0-100)
          quality_score=$((100 - todo_count - any_count * 2))
          quality_score=$((quality_score > 0 ? quality_score : 0))
          
          echo "TODO/FIXME comments: $todo_count"
          echo "TypeScript 'any' usage: $any_count"
          echo "Quality score: $quality_score/100"
          echo "score=$quality_score" >> $GITHUB_OUTPUT
          echo "::endgroup::"

  # ============================================================================
  # COMPREHENSIVE TESTING
  # ============================================================================
  testing:
    name: Comprehensive Testing
    runs-on: ubuntu-latest
    needs: quality-checks
    if: ${{ !inputs.skip_tests }}
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
      
      - name: Install dependencies
        run: bun install
      
      - name: Start Supabase services
        run: |
          supabase start --exclude studio,storage-api,imgproxy,inbucket
          echo "SUPABASE_URL=$(supabase status -o env | grep SUPABASE_URL | cut -d'=' -f2)" >> $GITHUB_ENV
          echo "SUPABASE_ANON_KEY=$(supabase status -o env | grep SUPABASE_ANON_KEY | cut -d'=' -f2)" >> $GITHUB_ENV
          echo "SUPABASE_SERVICE_ROLE_KEY=$(supabase status -o env | grep SUPABASE_SERVICE_ROLE_KEY | cut -d'=' -f2)" >> $GITHUB_ENV
      
      - name: Apply database migrations
        run: |
          supabase db push
          bun run db:seed
      
      - name: Run unit tests
        run: |
          echo "::group::Unit Tests"
          bun test --timeout 30000
          echo "::endgroup::"
      
      - name: Run integration tests
        run: |
          echo "::group::Integration Tests"
          bun run test:integration
          echo "::endgroup::"
      
      - name: Run Edge Function tests
        run: |
          echo "::group::Edge Function Tests"
          cd supabase/functions
          deno test --allow-all --no-lock
          echo "::endgroup::"
      
      - name: Performance benchmarks
        run: |
          echo "::group::Performance Benchmarks"
          bun run test:performance || echo "Performance tests are optional"
          echo "::endgroup::"
      
      - name: Security tests
        run: |
          echo "::group::Security Tests"
          bun run test:security || echo "Security tests are optional"
          echo "::endgroup::"

  # ============================================================================
  # STAGING DEPLOYMENT
  # ============================================================================
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-checks, testing]
    if: ${{ (github.ref == 'refs/heads/main' || inputs.environment == 'staging') && always() && needs.quality-checks.result == 'success' }}
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
      
      - name: Deploy to staging
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          STAGING_PROJECT_REF: ${{ vars.STAGING_PROJECT_REF }}
        run: |
          echo "::group::Staging Deployment"
          
          # Link to staging project
          supabase link --project-ref $STAGING_PROJECT_REF
          
          # Apply database migrations
          supabase db push
          
          # Deploy Edge Functions
          supabase functions deploy --no-verify-jwt
          
          # Set environment variables
          supabase secrets set --env-file .env.staging
          
          echo "✅ Staging deployment completed"
          echo "::endgroup::"
      
      - name: Staging health check
        env:
          STAGING_URL: ${{ vars.STAGING_SUPABASE_URL }}
        run: |
          echo "::group::Staging Health Check"
          
          # Wait for deployment to stabilize
          sleep 30
          
          # Test health endpoint
          response=$(curl -s -o /dev/null -w "%{http_code}" "$STAGING_URL/functions/v1/health")
          if [ "$response" != "200" ]; then
            echo "❌ Staging health check failed: HTTP $response"
            exit 1
          fi
          
          echo "✅ Staging health check passed"
          echo "::endgroup::"
      
      - name: Staging smoke tests
        env:
          STAGING_URL: ${{ vars.STAGING_SUPABASE_URL }}
          STAGING_ANON_KEY: ${{ vars.STAGING_SUPABASE_ANON_KEY }}
        run: |
          echo "::group::Staging Smoke Tests"
          
          # Test API endpoints
          curl -f "$STAGING_URL/functions/v1/health" || exit 1
          
          # Test with invalid API key (should return 401)
          response=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: Bearer invalid-key" \
            "$STAGING_URL/functions/v1/validate-api-key")
          
          if [ "$response" != "401" ]; then
            echo "❌ API key validation test failed: expected 401, got $response"
            exit 1
          fi
          
          echo "✅ Staging smoke tests passed"
          echo "::endgroup::"

  # ============================================================================
  # PRODUCTION DEPLOYMENT
  # ============================================================================
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-checks, testing, deploy-staging]
    if: ${{ (github.ref == 'refs/heads/main' || inputs.environment == 'production') && always() && needs.deploy-staging.result == 'success' }}
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
      
      - name: Production deployment with rollback capability
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          PRODUCTION_PROJECT_REF: ${{ vars.PRODUCTION_PROJECT_REF }}
        run: |
          echo "::group::Production Deployment"
          
          # Link to production project
          supabase link --project-ref $PRODUCTION_PROJECT_REF
          
          # Create deployment snapshot for rollback
          echo "Creating pre-deployment snapshot..."
          # This would create a backup/snapshot for rollback capability
          
          # Apply database migrations
          echo "Applying database migrations..."
          supabase db push
          
          # Deploy Edge Functions with zero-downtime
          echo "Deploying Edge Functions..."
          supabase functions deploy --no-verify-jwt
          
          # Update environment variables
          echo "Updating environment configuration..."
          supabase secrets set --env-file .env.production
          
          echo "✅ Production deployment completed"
          echo "::endgroup::"
      
      - name: Production health verification
        env:
          PRODUCTION_URL: ${{ vars.PRODUCTION_SUPABASE_URL }}
        run: |
          echo "::group::Production Health Verification"
          
          # Wait for deployment to stabilize
          sleep 60
          
          # Extended health checks
          for i in {1..5}; do
            echo "Health check attempt $i/5..."
            response=$(curl -s -o /dev/null -w "%{http_code}" "$PRODUCTION_URL/functions/v1/health")
            
            if [ "$response" = "200" ]; then
              echo "✅ Health check $i passed"
            else
              echo "❌ Health check $i failed: HTTP $response"
              if [ $i -eq 5 ]; then
                echo "Production deployment failed health checks"
                exit 1
              fi
              sleep 15
            fi
          done
          
          echo "✅ Production health verification completed"
          echo "::endgroup::"
      
      - name: Production monitoring setup
        env:
          PRODUCTION_URL: ${{ vars.PRODUCTION_SUPABASE_URL }}
        run: |
          echo "::group::Production Monitoring Setup"
          
          # Enable monitoring and alerting
          # This would configure monitoring thresholds, alerts, etc.
          echo "Monitoring and alerting configured"
          
          # Test performance endpoints
          curl -f "$PRODUCTION_URL/functions/v1/health" || exit 1
          
          echo "✅ Production monitoring active"
          echo "::endgroup::"

  # ============================================================================
  # POST-DEPLOYMENT VALIDATION
  # ============================================================================
  post-deployment:
    name: Post-Deployment Validation
    runs-on: ubuntu-latest
    needs: deploy-production
    if: ${{ always() && needs.deploy-production.result == 'success' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: End-to-end testing
        env:
          PRODUCTION_URL: ${{ vars.PRODUCTION_SUPABASE_URL }}
        run: |
          echo "::group::End-to-End Testing"
          
          # Run critical path tests
          # Test API authentication flow
          # Test document processing pipeline
          # Test monitoring and alerting
          
          echo "✅ End-to-end tests completed"
          echo "::endgroup::"
      
      - name: Performance validation
        env:
          PRODUCTION_URL: ${{ vars.PRODUCTION_SUPABASE_URL }}
        run: |
          echo "::group::Performance Validation"
          
          # Measure response times
          start_time=$(date +%s%N)
          curl -s "$PRODUCTION_URL/functions/v1/health" > /dev/null
          end_time=$(date +%s%N)
          
          response_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
          
          echo "Health endpoint response time: ${response_time}ms"
          
          if [ $response_time -gt 5000 ]; then
            echo "❌ Response time too high: ${response_time}ms"
            exit 1
          fi
          
          echo "✅ Performance validation passed"
          echo "::endgroup::"
      
      - name: Security validation
        env:
          PRODUCTION_URL: ${{ vars.PRODUCTION_SUPABASE_URL }}
        run: |
          echo "::group::Security Validation"
          
          # Test security headers
          headers=$(curl -s -I "$PRODUCTION_URL/functions/v1/health")
          
          if ! echo "$headers" | grep -q "X-Content-Type-Options: nosniff"; then
            echo "❌ Missing security header: X-Content-Type-Options"
            exit 1
          fi
          
          if ! echo "$headers" | grep -q "X-Frame-Options: DENY"; then
            echo "❌ Missing security header: X-Frame-Options"
            exit 1
          fi
          
          echo "✅ Security validation passed"
          echo "::endgroup::"
      
      - name: Deployment notification
        if: always()
        run: |
          echo "::group::Deployment Notification"
          
          status="${{ needs.deploy-production.result }}"
          
          if [ "$status" = "success" ]; then
            echo "🚀 Production deployment completed successfully!"
            echo "✅ All validation checks passed"
          else
            echo "❌ Production deployment failed"
            echo "🔄 Consider rollback procedures"
          fi
          
          echo "Deployment time: $(date)"
          echo "Commit: ${{ github.sha }}"
          echo "::endgroup::"

  # ============================================================================
  # ROLLBACK CAPABILITY
  # ============================================================================
  rollback:
    name: Emergency Rollback
    runs-on: ubuntu-latest
    if: ${{ failure() && github.ref == 'refs/heads/main' }}
    environment: production
    
    steps:
      - name: Checkout previous version
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          ref: ${{ github.event.before }}
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
      
      - name: Execute rollback
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
          PRODUCTION_PROJECT_REF: ${{ vars.PRODUCTION_PROJECT_REF }}
        run: |
          echo "::group::Emergency Rollback"
          
          # Link to production project
          supabase link --project-ref $PRODUCTION_PROJECT_REF
          
          # Rollback to previous Edge Function version
          echo "Rolling back Edge Functions..."
          supabase functions deploy --no-verify-jwt
          
          # Note: Database rollbacks should be handled carefully
          # and may require manual intervention
          
          echo "⚠️ Emergency rollback completed"
          echo "🔍 Manual verification required"
          echo "::endgroup::"