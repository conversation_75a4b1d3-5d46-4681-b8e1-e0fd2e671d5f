import { describe, it, expect, beforeAll } from 'bun:test';
import { createClient } from '@supabase/supabase-js';

/**
 * Integration tests for file upload and extraction workflow
 *
 * These tests verify the complete file upload pipeline including:
 * - API key authentication
 * - File validation
 * - Document processing
 * - Database storage
 * - Error handling
 */

// Test configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'http://localhost:54321';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || 'test-key';
const EXTRACT_ENDPOINT = `${SUPABASE_URL}/functions/v1/extract`;

// Test API keys (these should be valid test keys)
const TEST_API_KEY = process.env.TEST_API_KEY || 'skt_757b84167573082987954119fbbdfaa9';
const PROD_API_KEY = process.env.PROD_API_KEY || 'skp_1beb6b4ced6c009ac4be017c066ba304';

interface ExtractResponse {
  success: boolean;
  data?: {
    documentId: string;
    extractedData?: any;
    confidence?: number;
    processingTime?: number;
    model?: string;
    status: string;
    creditsUsed: number;
    remainingCredits: number;
  };
  error?: string;
  timestamp: string;
}

describe('File Upload Integration Tests', () => {
  let supabase: any;

  beforeAll(async () => {
    // Initialize Supabase client for test verification
    supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

    // Verify Supabase is running
    const { error } = await supabase.from('customers').select('id').limit(1);
    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows" which is fine
      throw new Error(`Supabase not accessible: ${error.message}`);
    }
  });

  describe('Authentication', () => {
    it('should reject requests without API key', async () => {
      const formData = new FormData();
      formData.append('document', new File(['test content'], 'test.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        body: formData
      });

      expect(response.status).toBe(401);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Authorization');
    });

    it('should reject requests with invalid API key', async () => {
      const formData = new FormData();
      formData.append('document', new File(['test content'], 'test.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer invalid_key_12345'
        },
        body: formData
      });

      expect(response.status).toBe(401);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid API key');
    });

    it('should accept requests with valid test API key', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], 'test.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      // Should get 200 or validation error, not auth error
      expect(response.status).not.toBe(401);
    });
  });

  describe('File Validation', () => {
    it('should reject requests without file', async () => {
      const formData = new FormData();
      // No file attached

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Missing required field: document');
    });

    it('should accept valid PDF files', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], 'valid.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const result: ExtractResponse = await response.json();

      if (result.success) {
        expect(response.status).toBe(200);
        expect(result.data?.documentId).toBeDefined();
        expect(result.data?.status).toMatch(/processing|completed/);
      } else {
        // If it fails, it should be due to business logic, not validation
        expect(result.error).not.toContain('Unsupported file type');
        expect(result.error).not.toContain('File header mismatch');
      }
    });

    it('should accept valid JPEG files', async () => {
      const jpegHeader = new Uint8Array([0xFF, 0xD8, 0xFF]);
      const formData = new FormData();
      formData.append('document', new File([jpegHeader], 'scan.jpg', { type: 'image/jpeg' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const result: ExtractResponse = await response.json();

      if (result.success) {
        expect(response.status).toBe(200);
        expect(result.data?.documentId).toBeDefined();
      } else {
        // Should not fail on file type validation
        expect(result.error).not.toContain('Unsupported file type');
      }
    });

    it('should reject unsupported file types', async () => {
      const formData = new FormData();
      formData.append('document', new File(['executable content'], 'malware.exe', { type: 'application/exe' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Unsupported file type');
    });

    it('should reject files with wrong headers', async () => {
      // Create a file claiming to be PDF but with wrong header
      const wrongHeader = new Uint8Array([0x00, 0x00, 0x00, 0x00]);
      const formData = new FormData();
      formData.append('document', new File([wrongHeader], 'fake.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('File header mismatch');
    });

    it('should reject files exceeding test key size limit', async () => {
      // Create a large file content that exceeds 10MB test limit
      const largeContent = new Array(11 * 1024 * 1024).fill('x').join(''); // 11MB string
      const formData = new FormData();
      formData.append('document', new File([largeContent], 'huge.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('File too large');
      expect(result.error).toContain('test key');
    });

    it('should accept larger files for production keys', async () => {
      // Create a 30MB file (within production 50MB limit)
      const largeContent = new Array(30 * 1024 * 1024).fill('x').join('');
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const formData = new FormData();
      formData.append('document', new File([pdfHeader, ...new TextEncoder().encode(largeContent)], 'large.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${PROD_API_KEY}`
        },
        body: formData
      });

      // Should not fail on size for production key
      const _result = await response.json();
      if (!result.success && result.error?.includes('File too large')) {
        // This would indicate the test is working correctly for prod keys
        expect(result.error).not.toContain('test key');
      }
    });
  });

  describe('Security Validation', () => {
    it('should reject files with executable headers', async () => {
      // Windows PE executable header
      const peHeader = new Uint8Array([0x4D, 0x5A]); // MZ
      const formData = new FormData();
      formData.append('document', new File([peHeader], 'malware.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('Suspicious content detected');
    });

    it('should reject files with path traversal names', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], '../../../etc/passwd', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toContain('path traversal');
    });
  });

  describe('Document Processing', () => {
    it('should process valid documents and return document ID', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]); // %PDF
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], 'business-document.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const result: ExtractResponse = await response.json();

      if (result.success) {
        expect(response.status).toBe(200);
        expect(result.data?.documentId).toMatch(/^[a-f0-9-]{36}$/); // UUID format
        expect(result.data?.status).toMatch(/processing|completed/);
        expect(result.data?.creditsUsed).toBeGreaterThan(0);
        expect(result.data?.processingTime).toBeGreaterThan(0);
        expect(result.timestamp).toBeDefined();
      }
    });

    it('should track credits and return remaining balance', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], 'credit-test.pdf', { type: 'application/pdf' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const result: ExtractResponse = await response.json();

      if (result.success) {
        expect(result.data?.creditsUsed).toBeGreaterThan(0);
        expect(result.data?.remainingCredits).toBeGreaterThanOrEqual(0);
      }
    });

    it('should handle agent selection', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], 'invoice.pdf', { type: 'application/pdf' }));
      formData.append('agentId', 'invoice-agent-123');

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      // Should not fail due to agent ID being provided
      const _result = await response.json();
      expect(response.status).not.toBe(400);
    });

    it('should handle custom prompts', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]);
      const formData = new FormData();
      formData.append('document', new File([pdfHeader], 'custom.pdf', { type: 'application/pdf' }));
      formData.append('customPrompt', 'Extract all financial data from this document');

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      // Should not fail due to custom prompt
      const _result = await response.json();
      expect(response.status).not.toBe(400);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed requests gracefully', async () => {
      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ invalid: 'data' })
      });

      expect(response.status).toBe(400);
      const _result = await response.json();
      expect(result.success).toBe(false);
    });

    it('should return proper error structure', async () => {
      const formData = new FormData();
      formData.append('document', new File(['bad content'], 'bad.txt', { type: 'text/plain' }));

      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${TEST_API_KEY}`
        },
        body: formData
      });

      const _result = await response.json();
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('timestamp');
      expect(result.success).toBe(false);
      expect(result).toHaveProperty('error');
      expect(typeof result.error).toBe('string');
    });
  });

  describe('CORS Support', () => {
    it('should handle CORS preflight requests', async () => {
      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'OPTIONS'
      });

      expect(response.status).toBe(200);
      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
      expect(response.headers.get('Access-Control-Allow-Methods')).toContain('POST');
      expect(response.headers.get('Access-Control-Allow-Headers')).toContain('Authorization');
    });

    it('should include CORS headers in responses', async () => {
      const formData = new FormData();
      const response = await fetch(EXTRACT_ENDPOINT, {
        method: 'POST',
        body: formData
      });

      expect(response.headers.get('Access-Control-Allow-Origin')).toBe('*');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits after many requests', async () => {
      const pdfHeader = new Uint8Array([0x25, 0x50, 0x44, 0x46]);

      // Make multiple rapid requests to trigger rate limiting
      const requests = Array.from({ length: 10 }, () => {
        const formData = new FormData();
        formData.append('document', new File([pdfHeader], 'rate-test.pdf', { type: 'application/pdf' }));

        return fetch(EXTRACT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${TEST_API_KEY}`
          },
          body: formData
        });
      });

      const responses = await Promise.all(requests);

      // At least one should succeed, and if rate limited, should get 429
      const statusCodes = responses.map(r => r.status);
      const hasSuccess = statusCodes.some(code => code === 200);
      const hasRateLimit = statusCodes.some(code => code === 429);

      // Either all succeed (rate limiting not triggered) or some are rate limited
      expect(hasSuccess || hasRateLimit).toBe(true);

      if (hasRateLimit) {
        const rateLimitedResponse = responses.find(r => r.status === 429);
        const _result = await rateLimitedResponse?.json();
        expect(result?.error).toContain('Rate limit exceeded');
      }
    }, 30000); // 30 second timeout for this test
  });
});

describe('Method Validation', () => {
  it('should reject non-POST requests', async () => {
    const getResponse = await fetch(EXTRACT_ENDPOINT, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${TEST_API_KEY}`
      }
    });

    expect(getResponse.status).toBe(405);
    const _result = await getResponse.json();
    expect(result.success).toBe(false);
    expect(result.error).toContain('Method not allowed');
  });

  it('should reject PUT requests', async () => {
    const putResponse = await fetch(EXTRACT_ENDPOINT, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${TEST_API_KEY}`
      }
    });

    expect(putResponse.status).toBe(405);
  });

  it('should reject DELETE requests', async () => {
    const deleteResponse = await fetch(EXTRACT_ENDPOINT, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${TEST_API_KEY}`
      }
    });

    expect(deleteResponse.status).toBe(405);
  });
});