# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The IDP Platform is an **API-first document processing service** that transforms unstructured documents (PDFs, images, spreadsheets) into structured JSON data using AI models. This is a pure backend API platform with no user interface.

## Key Architecture Concepts

### Dual API Key System
- **Test Keys** (`skt_`): 7-day data retention, configurable credits, full functionality for development
- **Production Keys** (`skp_`): Separate credit allocation, configurable billing, production rate limits

### Multi-Model AI Fallbacks
- Primary: OpenAI → Claude → LlamaParse for 99.5% uptime
- Intelligent model routing for cost optimization (60%+ profit margins)
- Each document processing attempt tries models in order until success

### Agent-Based Processing
- Default agents provided by platform
- Customers can clone and customize agents
- Each agent has a prompt template and JSON schema for extraction
- Version-controlled agent system

## Development Commands

### Environment Setup
```bash
# Start local development environment
supabase start

# Generate TypeScript types from database schema
npm run db:types

# Apply database migrations
npm run db:migrate

# Seed database with default agents
npm run db:seed
```

### Testing
```bash
# Run all tests (Bun Test framework)
npm test

# Run specific test types
npm run test:unit              # Unit tests only
npm run test:integration       # Integration tests only
npm run test:coverage          # With coverage report

# Manual testing (interactive)
npm run test:manual           # Interactive test menu
npm run test:manual:auth      # API authentication tests
npm run test:manual:api       # API endpoint tests
npm run test:manual:database  # Database operation tests
```

### Edge Functions (Deno/TypeScript)
```bash
# Serve functions locally with hot reload
npm run functions:serve

# Deploy functions to production
npm run functions:deploy

# View function logs
npm run functions:logs
```

### Database Operations
```bash
# Reset database with fresh data
npm run db:reset

# Generate types after schema changes
npm run db:types:remote        # From production
npm run db:types               # From local
```

### Code Quality
```bash
# Lint and fix TypeScript code
npm run lint
npm run lint:fix

# Format code with Prettier
npm run format

# Type check without building
npm run type-check
```

## Technology Stack

### Runtime Environment
- **Supabase**: Backend-as-a-Service platform
- **PostgreSQL**: Database with pgvector extension for embeddings
- **Deno**: TypeScript runtime for Edge Functions
- **Bun**: JavaScript runtime for local development and testing

### AI Integration
- **OpenAI API**: Primary AI model for document processing
- **Claude API**: Secondary fallback model
- **LlamaParse API**: Specialized PDF parsing fallback

### Key Dependencies
- `@supabase/supabase-js`: Database client
- `zod`: Schema validation
- `sharp`: Image processing
- `pdf-parse`: PDF text extraction
- `mammoth`: Word document processing
- `xlsx`: Spreadsheet processing

## Critical Development Patterns

### API Key Authentication
All endpoints require API key validation:
```typescript
const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
const { customerId, keyType, credits } = await validateApiKey(apiKey);
```

### Multi-Model Processing Pattern
```typescript
try {
  return await processWithOpenAI(document, agent);
} catch (error) {
  try {
    return await processWithClaude(document, agent);
  } catch (error) {
    return await processWithLlamaParse(document, agent);
  }
}
```

### Cost and Credit Tracking
Every processing operation must track:
- Input/output tokens
- Model cost (what we pay)
- Customer price (what customer pays)
- Credits used
- Processing time

## Database Schema Key Tables

### Core Tables
- `customers`: Customer account information
- `api_keys`: Hashed API keys with credits and limits
- `agents`: Default and custom extraction agents
- `documents`: Processed document metadata
- `extraction_results`: AI processing results with embeddings
- `usage_logs`: Cost tracking and audit trail

### Important Relationships
- API keys belong to customers
- Agents can be default (platform-provided) or custom (customer-owned)
- Extraction results link to documents and track which agent was used
- Usage logs track costs per customer per API key

## Security Requirements

### Input Validation
- Validate all file uploads (type, size, malware scanning)
- Sanitize user prompts for prompt injection protection
- Rate limiting per API key
- Comprehensive audit logging

### API Key Security
- Never store raw API keys (hash with SHA-256 + salt)
- Implement proper key rotation
- Support key revocation
- Track usage per key

## Testing Framework: Bun Test

### Required Patterns
```typescript
import { describe, it, expect, beforeEach } from 'bun:test';

describe('Feature Name', () => {
  it('should describe specific behavior', async () => {
    // Arrange
    const testData = { /* proper typed data */ };
    
    // Act
    const result = await functionUnderTest(testData);
    
    // Assert
    expect(result.success).toBe(true);
  });
});
```

### Forbidden Patterns
- Never use `any` types in tests
- Always use Bun test imports, not Jest
- Include proper error handling in tests
- Use typed mocks and interfaces

## Performance Targets

- **API Response**: < 500ms for non-processing endpoints
- **Document Processing**: < 5s for standard documents
- **Platform Uptime**: 99.5% with fallback systems
- **Extraction Accuracy**: >95% for structured documents

## Local Development Ports

When Supabase is running locally:
- API Gateway: `http://localhost:54321`
- Database: `postgresql://postgres:postgres@localhost:54322/postgres`
- Studio (Database UI): `http://localhost:54323`
- Edge Functions: `http://localhost:54321/functions/v1/`

## Common Issues and Solutions

### Edge Function Deployment
- Never create or commit Deno lockfiles (`deno.lock`)
- Always use `--no-lock` flag for Deno commands
- Use exact version pinning in import maps

### Database Migrations
- Never create functions in `auth` schema (use `public` schema)
- Test migrations with `npm run db:reset`
- Always add comprehensive comments to schema changes

### TypeScript Strict Mode
- Enable all strict TypeScript settings
- Use specific interfaces instead of `any`
- Implement proper error handling with typed catch blocks

## File Structure

```
├── supabase/
│   ├── functions/          # Edge Functions (Deno/TypeScript)
│   └── migrations/         # Database schema migrations
├── tests/
│   ├── unit/              # Bun unit tests
│   ├── integration/       # Bun integration tests
│   └── manual/           # Interactive test scripts
├── types/
│   └── database.types.ts  # Auto-generated from schema
├── docs/
│   ├── architecture/      # Technical documentation
│   └── prd.md            # Product requirements
└── package.json           # Dependencies and scripts
```

## Emergency Procedures

### If Supabase Functions Won't Start
1. Check for lockfiles: `find . -name "deno.lock" | xargs rm -f`
2. Restart Supabase: `supabase stop && supabase start`
3. Verify environment variables in `.env`

### If Tests Are Failing
1. Ensure Supabase is running: `supabase status`
2. Reset database: `npm run db:reset`
3. Regenerate types: `npm run db:types`
4. Check API keys are valid (not demo/example keys)

### If Database Migration Fails
1. Check for functions in `auth` schema (move to `public`)
2. Verify migration syntax
3. Test with: `npm run db:reset`