#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = 'http://localhost:54321';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function checkSchemas() {
  const { data: agents, error } = await supabase
    .from('agents')
    .select('agent_id, json_schema')
    .eq('is_default', true)
    .limit(1);

  if (error) {
    console.error('Error:', error);
    return;
  }

  console.log('Sample schema structure:');
  console.log(JSON.stringify(agents[0].json_schema, null, 2));
}

checkSchemas();