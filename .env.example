# Supabase Configuration
SUPABASE_URL=http://127.0.0.1:54321
SUPABASE_ANON_KEY=your_local_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_local_service_role_key_here

# Production Supabase (when deployed)
SUPABASE_URL_PROD=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY_PROD=your_production_anon_key_here
SUPABASE_SERVICE_ROLE_KEY_PROD=your_production_service_role_key_here
SUPABASE_PROJECT_ID=your_project_id_here

# AI Model API Keys (for document processing)
OPENAI_API_KEY=sk-your_openai_api_key_here
CLAUDE_API_KEY=sk-ant-your_claude_api_key_here
LLAMAPARSE_API_KEY=llx_your_llamaparse_api_key_here

# Platform Configuration
ENVIRONMENT=development
API_VERSION=v1
MAX_FILE_SIZE_MB=50
DEFAULT_RATE_LIMIT_PER_MINUTE=100
DEFAULT_TEST_CREDITS=1000

# Security
JWT_SECRET=your_jwt_secret_for_api_keys
API_KEY_SALT=your_api_key_hashing_salt
ENCRYPTION_KEY=your_32_char_encryption_key_here

# External Services (optional)
VIRUS_SCAN_ENABLED=false
VIRUS_SCAN_API_KEY=your_virus_scan_api_key
EXTERNAL_AUDIT_ENDPOINT=https://your-audit-service.com/api/logs

# Development/Testing
LOG_LEVEL=info
ENABLE_CORS=true
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000