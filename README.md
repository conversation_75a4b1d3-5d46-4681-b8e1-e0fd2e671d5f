# IDP Platform

> **API-First Document Processing Service**
> Transform unstructured documents into structured JSON data using customizable AI agents. Reduce processing costs from $0.10-$1.00 per document while maintaining 99.5% uptime and >95% extraction accuracy.

[![Supabase](https://img.shields.io/badge/Powered%20by-Supabase-3ECF8E?logo=supabase)](https://supabase.com)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6+-blue?logo=typescript)](https://www.typescriptlang.org)
[![Bun](https://img.shields.io/badge/Runtime-Bun-f472b6?logo=bun)](https://bun.sh)
[![Deno](https://img.shields.io/badge/Edge%20Functions-Deno-black?logo=deno)](https://deno.land)

---

## 🎯 Overview

The **IDP Platform** is a pure API backend service that transforms unstructured documents (PDFs, images, spreadsheets) into structured JSON data using AI models. Built for developers who need reliable document processing without UI complexity.

### Key Features (Per PRD v1.0)

- **🔄 Multi-Model Fallbacks**: OpenAI → Claude → LlamaParse achieving 99.5% uptime
- **🔑 Dual API Key System**: Test keys (`skt_`) with 7-day retention, production keys (`skp_`) with configurable billing
- **🤖 Customizable Agents**: Versioned default agents with customer cloning and customization capabilities
- **💰 60%+ Profit Margins**: Intelligent model routing maintaining cost efficiency
- **🛡️ Enterprise Security**: SHA-256 API key hashing, comprehensive audit logging, prompt injection protection
- **📊 Dual Metrics Tracking**: Internal cost vs. customer price visibility with transparent credit management

### Quick Example

```bash
# Extract data from an invoice
curl -X POST https://your-project.supabase.co/functions/v1/extract \
  -H "Authorization: Bearer skt_live_..." \
  -H "Content-Type: application/json" \
  -d '{
    "document_url": "https://example.com/invoice.pdf",
    "agent_id": "invoice_extractor_v2"
  }'

# Response
{
  "success": true,
  "data": {
    "invoice_number": "INV-2025-001",
    "vendor": "Office Supply Co",
    "total": 145.67,
    "date": "2025-09-21",
    "line_items": [...]
  },
  "confidence": 0.96,
  "processing_time_ms": 1200,
  "credits_used": 15
}
```

---

## 🚀 Quick Start

### Prerequisites

- **Node.js 20+** or **Bun 1.1+**
- **Supabase CLI** (`npm install -g supabase`)
- **Deno 1.43+** (for Edge Functions)

### 1. Clone and Install

```bash
git clone https://github.com/your-org/idp-platform.git
cd idp-platform
npm install  # or bun install
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Start Supabase local development
supabase start

# The output will show your local credentials:
# API URL: http://127.0.0.1:14321
# anon key: eyJhbGciOiJIUzI1NiIs...
# service_role key: eyJhbGciOiJIUzI1NiIs...
```

### 3. Configure Environment

Edit `.env` with your API keys:

```bash
# Supabase (auto-filled from supabase start)
SUPABASE_URL=http://127.0.0.1:14321
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIs...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIs...

# AI Model APIs (required)
OPENAI_API_KEY=sk-your_openai_key_here
CLAUDE_API_KEY=sk-ant-your_claude_key_here
LLAMAPARSE_API_KEY=llx-your_llamaparse_key_here

# Security (generate secure values)
JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-long
API_KEY_SALT=your_secure_salt_for_api_key_hashing
ENCRYPTION_KEY=your_32_character_encryption_key_here
```

### 4. Database Setup

```bash
# Apply database migrations
npm run db:migrate

# Generate TypeScript types
npm run db:types

# Seed with default agents (optional)
npm run db:seed
```

### 5. Start Development

```bash
# Terminal 1: Serve Edge Functions
npm run functions:serve

# Terminal 2: Run tests
npm test

# Open Supabase Studio
npm run studio  # http://127.0.0.1:14323
```

---

## 📚 API Documentation

### Core Endpoints (Per PRD Requirements)

| Endpoint | Method | Description | PRD Reference |
|----------|--------|-------------|--------------|
| `/functions/v1/extract` | POST | Process document and extract data | FR9 |
| `/functions/v1/agents` | GET | List available extraction agents | FR6 |
| `/functions/v1/agents/clone` | POST | Clone agent for customization | FR7 |
| `/functions/v1/agents/{id}` | PUT | Update custom agent | FR8 |
| `/admin/keys/{keyId}/credits` | POST | Add credits to API key | FR10 |

### Authentication

All API requests require an API key in the Authorization header:

```bash
-H "Authorization: Bearer skt_live_your_test_key"      # Test key
-H "Authorization: Bearer skp_live_your_production_key" # Production key
```

### API Key Types (Per PRD FR1)

- **Test Keys** (`skt_`): Configurable credits, 7-day data retention, full functionality for development
- **Production Keys** (`skp_`): Separate credit allocation, configurable billing, production rate limits

### Example Requests

#### Extract Document Data
```bash
curl -X POST http://127.0.0.1:14321/functions/v1/extract \
  -H "Authorization: Bearer skt_live_..." \
  -H "Content-Type: application/json" \
  -d '{
    "document_url": "https://example.com/contract.pdf",
    "agent_id": "contract_analyzer",
    "options": {
      "priority": 5,
      "webhook_url": "https://your-app.com/webhook"
    }
  }'
```

#### List Available Agents
```bash
curl -X GET http://127.0.0.1:14321/functions/v1/agents \
  -H "Authorization: Bearer skt_live_..."
```

#### Clone and Customize Agent
```bash
curl -X POST http://127.0.0.1:14321/functions/v1/agents/clone \
  -H "Authorization: Bearer skt_live_..." \
  -H "Content-Type: application/json" \
  -d '{
    "source_agent_id": "invoice_extractor",
    "name": "Custom Invoice Processor",
    "custom_prompt": "Extract vendor, date, total, and line items..."
  }'
```

---

## 🛠️ Development

### Project Structure

```
idp-platform/
├── supabase/
│   ├── functions/           # Edge Functions (API endpoints)
│   │   ├── extract/        # Document processing
│   │   ├── agents/         # Agent management
│   │   └── validate-api-key/ # Authentication
│   ├── migrations/         # Database schema
│   └── config.toml        # Supabase configuration
├── types/
│   └── database.types.ts   # Auto-generated DB types
├── tests/
│   ├── unit/              # Unit tests
│   ├── integration/       # API integration tests
│   └── manual/           # Manual testing scripts
├── docs/
│   ├── architecture/      # Technical documentation
│   └── product-brief.md   # Product requirements
└── package.json           # Dependencies and scripts
```

### Available Scripts

```bash
# Development
npm run functions:serve     # Start Edge Functions locally
npm run studio             # Open Supabase Studio
npm run db:reset           # Reset database with fresh data

# Database
npm run db:migrate         # Apply pending migrations
npm run db:types          # Generate TypeScript types
npm run db:seed           # Insert sample data

# Testing
npm test                  # Run all tests
npm run test:unit         # Unit tests only
npm run test:integration  # Integration tests only
npm run test:manual       # Interactive manual tests

# Code Quality
npm run lint              # Lint TypeScript code
npm run format            # Format code with Prettier
npm run type-check        # TypeScript validation
```

### Creating a New Edge Function

```bash
# Create function
supabase functions new my-endpoint

# Edit the function
# supabase/functions/my-endpoint/index.ts

# Test locally
supabase functions serve

# Deploy to production
supabase functions deploy my-endpoint
```

### Database Migrations

```bash
# Create new migration
supabase migration new add_feature_table

# Edit the migration file
# supabase/migrations/[timestamp]_add_feature_table.sql

# Apply migration
npm run db:migrate

# Generate updated types
npm run db:types
```

---

## 🧪 Testing

### Running Tests

```bash
# Complete test suite
npm test

# Specific test types
npm run test:unit              # Fast unit tests
npm run test:integration       # API integration tests

# Manual testing (interactive)
npm run test:manual           # Interactive test menu
npm run test:manual:auth      # API authentication tests
npm run test:manual:extract   # Document extraction tests
```

### Manual Testing

Interactive test scripts for debugging and validation:

```bash
# Start interactive test runner
npm run test:manual

# Menu options:
# 1. Test API Authentication
# 2. Test Document Extraction  
# 3. Test Agent Management
# 4. Test Database Operations
# 5. Test End-to-End Workflow
```

### Test Data

Test files are available in `tests/fixtures/`:
- Sample PDFs, images, and documents
- API key examples
- Expected response formats

---

## 🔐 Security

### API Key Management

- **Never store raw API keys** - Always hash with SHA-256 + salt
- **Use environment variables** for sensitive configuration
- **Implement rate limiting** per API key
- **Audit all security events** with correlation IDs

### Input Validation

- **File type validation** - Only allow supported formats
- **Size limits** - Configurable per customer
- **Prompt injection protection** - Multi-layer sanitization
- **Virus scanning** - Optional malware detection

### Example Security Implementation

```typescript
// API key validation
const validateApiKey = async (rawKey: string) => {
  const keyHash = await hashApiKey(rawKey);
  const { data } = await supabase
    .from('api_keys')
    .select('customer_id, credits, rate_limits')
    .eq('key_hash', keyHash)
    .single();
  
  if (!data) throw new Error('Invalid API key');
  return data;
};

// File validation
const validateUpload = async (file: File) => {
  if (file.size > MAX_FILE_SIZE) {
    throw new Error('File too large');
  }
  
  if (!ALLOWED_TYPES.includes(file.type)) {
    throw new Error('Unsupported file type');
  }
};
```

---

## 📊 Monitoring & Analytics

### Built-in Metrics

- **API Usage**: Requests per minute, success rates
- **Cost Tracking**: AI model costs vs revenue
- **Performance**: Response times, processing duration
- **Credits**: Usage and remaining balances per API key

### Logging

All Edge Functions include structured logging:

```typescript
console.log({
  timestamp: new Date().toISOString(),
  level: 'INFO',
  correlationId: 'req_abc123',
  customerId: 'cust_xyz789',
  action: 'document_processed',
  duration: 1200,
  cost: 0.0045
});
```

### Supabase Dashboard

Monitor your platform via Supabase Dashboard:
- **Database**: Query performance, table sizes
- **Edge Functions**: Invocation logs, error rates  
- **Auth**: API key usage patterns
- **Storage**: File upload metrics

---

## 🚀 Deployment

### Production Environment

1. **Create Supabase Project**
   ```bash
   # Link to your production project
   supabase link --project-ref your-project-id
   ```

2. **Environment Variables**
   Set these in your Supabase Dashboard → Settings → API:
   ```bash
   OPENAI_API_KEY=sk-your-production-key
   CLAUDE_API_KEY=sk-ant-your-production-key
   LLAMAPARSE_API_KEY=llx-your-production-key
   JWT_SECRET=your-secure-production-secret
   ```

3. **Deploy Database**
   ```bash
   npm run db:push
   ```

4. **Deploy Edge Functions**
   ```bash
   supabase functions deploy
   ```

### CI/CD Pipeline

Example GitHub Actions workflow:

```yaml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: supabase/setup-cli@v1
    - run: supabase link --project-ref ${{ secrets.SUPABASE_PROJECT_ID }}
    - run: supabase db push
    - run: supabase functions deploy
```

---

## 🤝 Contributing

### Getting Started

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow our coding standards (see `docs/architecture/coding-standards.md`)
4. Write tests for new functionality
5. Submit a pull request

### Code Standards

- **TypeScript**: Strict mode, no `any` types
- **Testing**: Unit tests for all new functions
- **Security**: Follow security checklist for API changes
- **Documentation**: Update relevant docs for API changes

### Pull Request Process

1. **Tests**: All tests must pass (`npm test`)
2. **Linting**: Code must pass linting (`npm run lint`)
3. **Types**: TypeScript must compile cleanly (`npm run type-check`)
4. **Documentation**: Update API docs for endpoint changes

---

## 📖 Documentation

- **[Product Requirements Document (PRD)](docs/prd.md)** - Complete product requirements and epic breakdown
- **[Product Brief](docs/project-brief.md)** - High-level product vision and user journeys
- **[Architecture Guide](docs/architecture/)** - Technical architecture documentation
- **[Coding Standards](docs/architecture/coding-standards.md)** - API-first development best practices
- **[Tech Stack Guide](docs/architecture/tech-stack.md)** - Technology deep dive and performance targets

---

## 🔗 Resources

### API Development
- [Supabase Edge Functions](https://supabase.com/docs/guides/functions)
- [Deno Runtime API](https://deno.land/api)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

### AI Integration
- [OpenAI API Documentation](https://platform.openai.com/docs)
- [Claude API Documentation](https://docs.anthropic.com/claude/reference)
- [LlamaParse Documentation](https://docs.llamaindex.ai/en/stable/llama_cloud/llama_parse/)

### Database
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Supabase Database Guide](https://supabase.com/docs/guides/database)

---

## 📄 License

MIT License - see [LICENSE](LICENSE) file for details.

---

## 🆘 Support

- **Documentation**: Check our [docs/](docs/) directory
- **Issues**: [GitHub Issues](https://github.com/your-org/idp-platform/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/idp-platform/discussions)

---

**Built with ❤️ for developers who need reliable document processing APIs**