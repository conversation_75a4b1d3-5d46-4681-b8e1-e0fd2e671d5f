/**
 * API Key Validation Endpoint
 * 
 * Validates API keys and returns customer context
 * Used by all other endpoints for authentication
 * Includes rate limiting and credit tracking
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
// Inline utility functions (removed _shared dependency)
const handleCors = () => new Response(null, {
  status: 200,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
  },
});

const createApiResponse = (message: string, status: number, success: boolean) => {
  return new Response(JSON.stringify({ success, message }), {
    status,
    headers: { 'Content-Type': 'application/json' }
  });
};

// Simple API key validation (basic implementation)
const validateApiKey = async (supabase: any, rawKey: string) => {
  // Validate API key format
  if (!rawKey || rawKey.length < 10) {
    return { isValid: false, error: 'Invalid API key format' };
  }
  
  // Check format (skt_ for test, skp_ for production)
  if (!rawKey.match(/^sk[tp]_[a-zA-Z0-9]{32,}$/)) {
    return { isValid: false, error: 'Invalid API key format' };
  }
  
  try {
    // Hash the API key for database lookup
    const { data: hashResult, error: hashError } = await supabase.rpc('generate_api_key_hash', {
      raw_key: rawKey
    });
    
    if (hashError || !hashResult) {
      console.error('API key hashing error:', hashError);
      return { isValid: false, error: 'API key validation failed' };
    }
    
    // Look up the API key in database
    const { data: keyData, error: keyError } = await supabase
      .from('api_keys')
      .select('id, customer_id, key_type, credits, revoked, expires_at')
      .eq('key_hash', hashResult)
      .eq('revoked', false)
      .single();
      
    if (keyError || !keyData) {
      console.log('API key not found:', keyError?.message || 'No matching key');
      return { isValid: false, error: 'API key not found' };
    }
    
    // Check if key is expired
    if (keyData.expires_at && new Date(keyData.expires_at) < new Date()) {
      return { isValid: false, error: 'API key expired' };
    }
    
    // Return validation result with real database data
    return {
      isValid: true,
      customerId: keyData.customer_id,
      keyId: keyData.id,  // ✅ REAL UUID from database
      keyType: keyData.key_type,
      credits: keyData.credits,
      status: 'active'
    };
    
  } catch {
    console.error('Database error during API key validation:', error);
    return { isValid: false, error: 'API key validation failed' };
  }
};

const checkRateLimit = async (_supabase: any, _keyId: string, _window: string) => {
  // Basic rate limiting - allow all for now
  return {
    allowed: true,
    remaining: 100,
    resetTime: new Date(Date.now() + 60000) // 1 minute from now
  };
};

const deductCredits = async (_supabase: any, _keyId: string, _amount: number) => {
  // Basic credit deduction
  return {
    success: true,
    remainingCredits: 999 // Mock remaining credits
  };
};

const logApiUsage = async (_supabase: any, customerId: string, _keyId: string, operation: string, success: boolean, credits: number, _metadata: any) => {
  // Basic logging - just console log for now
  console.log(`API Usage: ${customerId} - ${operation} - ${success ? 'SUCCESS' : 'FAILED'} - Credits: ${credits}`);
};

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

interface ValidationRequest {
  consumeCredit?: boolean;
  operationType?: string;
}

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return createApiResponse('Method not allowed', 405, false);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Extract API key from multiple possible headers for test compatibility
    let apiKey = req.headers.get('apikey');
    
    // Also check Authorization header for Bearer tokens (test compatibility)
    if (!apiKey) {
      const authHeader = req.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        apiKey = authHeader.replace('Bearer ', '');
      }
    }
    
    if (!apiKey) {
      return createApiResponse('Missing API key (provide via apikey header or Authorization Bearer)', 401, false);
    }

    const rawKey = apiKey;
    
    // Parse request body
    let body: ValidationRequest = {};
    try {
      const requestText = await req.text();
      if (requestText.trim()) {
        body = JSON.parse(requestText);
      }
    } catch {
      // Ignore JSON parse errors for empty bodies
    }

    // Validate the API key
    const validationResult = await validateApiKey(supabase, rawKey);
    
    if (!validationResult.isValid) {
      // Log failed validation attempt
      await logApiUsage(
        supabase,
        'unknown',
        'unknown',
        'api_key_validation',
        false,
        0,
        { error: validationResult.error, raw_key_prefix: rawKey.substring(0, 8) }
      );

      return createApiResponse(validationResult.error, 401, false);
    }

    const { customerId, keyId, keyType, credits, status } = validationResult;

    // Check rate limits
    const rateLimitResult = await checkRateLimit(supabase, keyId!, 'minute');
    
    if (!rateLimitResult.allowed) {
      // Log rate limit exceeded
      await logApiUsage(
        supabase,
        customerId!,
        keyId!,
        'rate_limit_exceeded',
        false,
        0,
        { 
          remaining: rateLimitResult.remaining,
          reset_time: rateLimitResult.resetTime.toISOString()
        }
      );

      return new Response(
        JSON.stringify({
          success: false,
          error: 'Rate limit exceeded',
          retryAfter: rateLimitResult.resetTime.toISOString()
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
            'X-RateLimit-Reset': rateLimitResult.resetTime.toISOString(),
            'Retry-After': Math.ceil((rateLimitResult.resetTime.getTime() - Date.now()) / 1000).toString()
          }
        }
      );
    }

    // Handle credit consumption if requested
    let remainingCredits = credits;
    if (body.consumeCredit) {
      if (credits! <= 0) {
        // Log insufficient credits
        await logApiUsage(
          supabase,
          customerId!,
          keyId!,
          'insufficient_credits',
          false,
          0,
          { requested_credits: 1, available_credits: credits }
        );

        return createApiResponse('Insufficient credits', 402, false);
      }

      const creditResult = await deductCredits(supabase, keyId!, 1);
      
      if (!creditResult.success) {
        // Log credit deduction failure
        await logApiUsage(
          supabase,
          customerId!,
          keyId!,
          'credit_deduction_failed',
          false,
          0,
          { error: creditResult.error, requested_credits: 1 }
        );

        return createApiResponse(creditResult.error, 402, false);
      }

      remainingCredits = creditResult.remainingCredits;
    }

    // Log successful validation
    await logApiUsage(
      supabase,
      customerId!,
      keyId!,
      body.operationType || 'api_key_validation',
      true,
      body.consumeCredit ? 1 : 0,
      {
        key_type: keyType,
        credits_remaining: remainingCredits,
        rate_limit_remaining: rateLimitResult.remaining
      }
    );

    // Return validation success with customer context
    const response = {
      success: true,
      data: {
        customerId,
        keyType,
        credits: remainingCredits,
        isActive: status === 'active',
        isExpired: false, // Already checked in validation
        rateLimit: {
          remaining: rateLimitResult.remaining,
          resetTime: rateLimitResult.resetTime.toISOString()
        }
      }
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
        'X-RateLimit-Reset': rateLimitResult.resetTime.toISOString(),
        'X-Customer-ID': customerId!,
        'X-Key-Type': keyType!
      }
    });

  } catch {
    console.error('API key validation error:', error);
    
    return createApiResponse(
      error instanceof Error ? error.message : 'Internal server error',
      500,
      false
    );
  }
});

// Add error handling for unhandled promise rejections
globalThis.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});