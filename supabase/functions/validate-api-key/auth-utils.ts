// Authentication utilities for API key validation
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
import type {
  ApiKeyRecord,
  RateLimitInfo,
  AuthResult,
  AuditLogEntry,
  AuthenticationError
} from "../../../types/auth.types.ts";

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// API Key Format Validation
export function validateApiKeyFormat(apiKey: string): boolean {
  if (!apiKey || typeof apiKey !== 'string') {
    return false;
  }

  // FIXED SEC-002: Correct format validation (NO _live_ component)
  // Test format: skt_[32 hex chars]
  // Production format: skp_[32 hex chars]
  const keyPattern = /^sk[tp]_[a-f0-9]{32}$/;
  return keyPattern.test(apiKey);
}

// DEPRECATED: Legacy SHA-256 function - replaced with PBKDF2 in database
// Use database function validate_api_key_auth_secure() instead
export async function hashApiKey(apiKey: string): Promise<string> {
  // This function is deprecated and should not be used for new keys
  // Kept for backward compatibility during migration period
  const encoder = new TextEncoder();
  const _data = encoder.encode(apiKey);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

// Generate correlation ID for audit trail
export function generateCorrelationId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `req_${timestamp}_${random}`;
}

// Get API key record from database
export async function getApiKeyRecord(keyHash: string): Promise<ApiKeyRecord | null> {
  const { data, error } = await supabase
    .from('api_keys')
    .select('*')
    .eq('key_hash', keyHash)
    .eq('revoked', false)
    .single();

  if (error || !data) {
    return null;
  }

  return data as ApiKeyRecord;
}

// Rate limiting implementation
export async function checkRateLimit(apiKeyId: string): Promise<RateLimitInfo> {
  const windowStart = new Date(Date.now() - 60000); // 1 minute window
  const currentTime = Math.floor(Date.now() / 1000);
  const resetTime = currentTime + 60; // Reset in 60 seconds

  // Get current rate limit record
  const { data: rateLimitRecord } = await supabase
    .from('rate_limits')
    .select('*')
    .eq('api_key_id', apiKeyId)
    .gte('window_start', windowStart.toISOString())
    .single();

  let requestCount = 1;
  const defaultLimit = 100; // requests per minute

  if (rateLimitRecord) {
    requestCount = rateLimitRecord.request_count + 1;

    // Update existing record
    await supabase
      .from('rate_limits')
      .update({
        request_count: requestCount,
        updated_at: new Date().toISOString()
      })
      .eq('api_key_id', apiKeyId)
      .eq('window_start', rateLimitRecord.window_start);
  } else {
    // Create new rate limit record
    await supabase
      .from('rate_limits')
      .insert({
        api_key_id: apiKeyId,
        window_start: windowStart.toISOString(),
        request_count: 1,
        updated_at: new Date().toISOString()
      });
  }

  const remaining = Math.max(0, defaultLimit - requestCount);

  if (requestCount > defaultLimit) {
    throw createAuthError(
      'Rate limit exceeded',
      429,
      'RATE_LIMIT_EXCEEDED',
      { retry_after: 60 }
    );
  }

  return {
    limit: defaultLimit,
    remaining,
    reset: resetTime
  };
}

// Create typed authentication error
export function createAuthError(
  message: string,
  status: number,
  code: string,
  details?: Record<string, any>
): AuthenticationError {
  const error = new Error(message) as AuthenticationError;
  error.status = status;
  error.code = code;
  if (details) {
    Object.assign(error, details);
  }
  return error;
}

// Audit logging
export async function logAuthEvent(auditEntry: AuditLogEntry): Promise<void> {
  try {
    await supabase
      .from('audit_logs')
      .insert(auditEntry);
  } catch {
    console.error('Failed to log audit event:', error);
    // Don't throw - logging failures shouldn't break authentication
  }
}

// Extract client information from request
export function extractClientInfo(req: Request): { ipAddress?: string, userAgent?: string } {
  return {
    ipAddress: req.headers.get('X-Forwarded-For') || req.headers.get('X-Real-IP') || 'unknown',
    userAgent: req.headers.get('User-Agent') || 'unknown'
  };
}

// Main API key validation function
export async function validateApiKey(apiKey: string, req: Request): Promise<AuthResult> {
  const correlationId = generateCorrelationId();
  const clientInfo = extractClientInfo(req);

  try {
    // 1. Format validation with FIXED SEC-002 pattern
    if (!validateApiKeyFormat(apiKey)) {
      await logAuthEvent({
        correlation_id: correlationId,
        action: 'api_key_validation',
        status: 'failure',
        error_message: 'Invalid API key format',
        timestamp: new Date().toISOString(),
        ...clientInfo
      });

      throw createAuthError(
        'Invalid API key format',
        400,
        'INVALID_FORMAT'
      );
    }

    // 2. SECURE DATABASE VALIDATION using PBKDF2
    const { data, error } = await supabase.rpc('validate_api_key_auth_secure', {
      raw_key: apiKey
    });

    if (error) {
      console.error('Database validation error:', error);
      await logAuthEvent({
        correlation_id: correlationId,
        action: 'api_key_validation',
        status: 'failure',
        error_message: 'Database validation failed',
        timestamp: new Date().toISOString(),
        ...clientInfo
      });

      throw createAuthError(
        'Internal validation error',
        500,
        'VALIDATION_ERROR'
      );
    }

    if (!data || data.length === 0) {
      await logAuthEvent({
        correlation_id: correlationId,
        action: 'api_key_validation',
        status: 'failure',
        error_message: 'Invalid API key',
        timestamp: new Date().toISOString(),
        ...clientInfo
      });

      throw createAuthError(
        'Invalid API key',
        401,
        'INVALID_KEY'
      );
    }

    const apiKeyRecord = data[0];

    // 3. Check if expired
    if (apiKeyRecord.is_expired) {
      await logAuthEvent({
        correlation_id: correlationId,
        customer_id: apiKeyRecord.customer_id,
        api_key_id: apiKeyRecord.key_id,
        action: 'api_key_validation',
        status: 'failure',
        error_message: 'API key expired',
        timestamp: new Date().toISOString(),
        ...clientInfo
      });

      throw createAuthError(
        'API key expired',
        401,
        'KEY_EXPIRED'
      );
    }

    // 4. Check status
    if (apiKeyRecord.status !== 'active') {
      await logAuthEvent({
        correlation_id: correlationId,
        customer_id: apiKeyRecord.customer_id,
        api_key_id: apiKeyRecord.key_id,
        action: 'api_key_validation',
        status: 'failure',
        error_message: `API key ${apiKeyRecord.status}`,
        timestamp: new Date().toISOString(),
        ...clientInfo
      });

      throw createAuthError(
        `API key ${apiKeyRecord.status}`,
        401,
        'KEY_INACTIVE'
      );
    }

    // 5. Rate limiting
    const _rateLimit = await checkRateLimit(apiKeyRecord.key_id);

    // 6. Update last used timestamp
    await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', apiKeyRecord.key_id);

    // 7. Log successful authentication
    await logAuthEvent({
      correlation_id: correlationId,
      customer_id: apiKeyRecord.customer_id,
      api_key_id: apiKeyRecord.key_id,
      action: 'api_key_validation',
      status: 'success',
      timestamp: new Date().toISOString(),
      ...clientInfo
    });

    // 8. Return authentication result
    return {
      customerId: apiKeyRecord.customer_id,
      keyType: apiKeyRecord.key_type,
      credits: apiKeyRecord.credits,
      apiKeyId: apiKeyRecord.key_id,
      rateLimit
    };

  } catch {
    // Add correlation ID to error for tracing
    if (error instanceof Error) {
      (error as AuthenticationError).correlation_id = correlationId;
    }
    throw error;
  }
}

// JWT validation for admin endpoints
export async function validateJwtToken(token: string): Promise<{ userId: string, role?: string }> {
  try {
    const { data, error } = await supabase.auth.getUser(token);

    if (error || !data.user) {
      throw createAuthError('Invalid JWT token', 401, 'INVALID_JWT');
    }

    return {
      userId: data.user.id,
      role: data.user.user_metadata?.role
    };
  } catch {
    if (error instanceof AuthenticationError) {
      throw error;
    }
    throw createAuthError('JWT token validation failed', 401, 'JWT_VALIDATION_FAILED');
  }
}