/**
 * Expired Data Cleanup Function
 * 
 * Cleans up expired test keys and associated data according to retention policies
 * Should be triggered by cron job or scheduled execution
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
import { createApiResponse, handleCors } from "../../_shared/api-key-utils.ts";

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

interface CleanupResult {
  expiredKeys: number;
  expiredDocuments: number;
  oldRateLimits: number;
  oldUsageLogs: number;
  success: boolean;
  errors: string[];
}

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  // Only allow POST requests (for safety)
  if (req.method !== 'POST') {
    return createApiResponse('Method not allowed. Use POST to trigger cleanup', 405, false);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  const result: CleanupResult = {
    expiredKeys: 0,
    expiredDocuments: 0,
    oldRateLimits: 0,
    oldUsageLogs: 0,
    success: true,
    errors: []
  };

  try {
    console.log('Starting expired data cleanup...');

    // 1. Mark expired test keys as revoked
    await cleanupExpiredKeys(supabase, result);

    // 2. Clean up expired documents
    await cleanupExpiredDocuments(supabase, result);

    // 3. Clean up old rate limit records (older than 24 hours)
    await cleanupOldRateLimits(supabase, result);

    // 4. Clean up old usage logs (older than 1 year for detailed logs)
    await cleanupOldUsageLogs(supabase, result);

    console.log('Cleanup completed:', result);

    return createApiResponse(result, 200, result.success);

  } catch {
    console.error('Cleanup error:', error);
    result.success = false;
    result.errors.push(error instanceof Error ? error.message : 'Unknown error');
    
    return createApiResponse(result, 500, false);
  }
});

/**
 * Mark expired test keys as revoked and update their status
 */
async function cleanupExpiredKeys(supabase: any, result: CleanupResult): Promise<void> {
  try {
    console.log('Cleaning up expired API keys...');

    // Find expired test keys that are still active
    const { data: expiredKeys, error: findError } = await supabase
      .from('api_keys')
      .select('id, customer_id, key_type, expires_at')
      .eq('key_type', 'test')
      .eq('status', 'active')
      .lt('expires_at', new Date().toISOString())
      .eq('revoked', false);

    if (findError) {
      throw new Error(`Failed to find expired keys: ${findError.message}`);
    }

    if (!expiredKeys || expiredKeys.length === 0) {
      console.log('No expired keys found');
      return;
    }

    console.log(`Found ${expiredKeys.length} expired keys`);

    // Update each expired key
    for (const key of expiredKeys) {
      try {
        const { error: updateError } = await supabase.rpc('update_api_key_status', {
          p_key_id: key.id,
          p_new_status: 'revoked',
          p_reason: 'Automatic expiration - 7 day test key retention policy'
        });

        if (updateError) {
          console.error(`Failed to revoke expired key ${key.id}:`, updateError);
          result.errors.push(`Failed to revoke key ${key.id}: ${updateError.message}`);
        } else {
          result.expiredKeys++;
          console.log(`Revoked expired key: ${key.id}`);

          // Log the automatic revocation
          await supabase.from('audit_logs').insert({
            customer_id: key.customer_id,
            api_key_id: key.id,
            action: 'AUTO_EXPIRE',
            resource_type: 'api_key',
            resource_id: key.id,
            success: true,
            metadata: {
              expired_at: key.expires_at,
              cleanup_reason: '7-day retention policy',
              automated: true
            }
          });
        }
      } catch {
        console.error(`Error processing expired key ${key.id}:`, error);
        result.errors.push(`Error processing key ${key.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

  } catch {
    console.error('Error in cleanupExpiredKeys:', error);
    result.errors.push(`Expired keys cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Clean up expired documents according to their retention policies
 */
async function cleanupExpiredDocuments(supabase: any, result: CleanupResult): Promise<void> {
  try {
    console.log('Cleaning up expired documents...');

    // Find documents that have expired
    const { data: expiredDocs, error: findError } = await supabase
      .from('documents')
      .select('id, customer_id, filename, storage_path, expires_at')
      .lt('expires_at', new Date().toISOString())
      .neq('status', 'expired');

    if (findError) {
      throw new Error(`Failed to find expired documents: ${findError.message}`);
    }

    if (!expiredDocs || expiredDocs.length === 0) {
      console.log('No expired documents found');
      return;
    }

    console.log(`Found ${expiredDocs.length} expired documents`);

    // Mark documents as expired (actual file deletion would be handled separately)
    for (const doc of expiredDocs) {
      try {
        const { error: updateError } = await supabase
          .from('documents')
          .update({
            status: 'expired',
            updated_at: new Date().toISOString()
          })
          .eq('id', doc.id);

        if (updateError) {
          console.error(`Failed to mark document ${doc.id} as expired:`, updateError);
          result.errors.push(`Failed to expire document ${doc.id}: ${updateError.message}`);
        } else {
          result.expiredDocuments++;
          console.log(`Marked document as expired: ${doc.id}`);

          // Log the expiration
          await supabase.from('audit_logs').insert({
            customer_id: doc.customer_id,
            action: 'AUTO_EXPIRE',
            resource_type: 'document',
            resource_id: doc.id,
            success: true,
            metadata: {
              filename: doc.filename,
              expired_at: doc.expires_at,
              storage_path: doc.storage_path,
              automated: true
            }
          });
        }
      } catch {
        console.error(`Error processing expired document ${doc.id}:`, error);
        result.errors.push(`Error processing document ${doc.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

  } catch {
    console.error('Error in cleanupExpiredDocuments:', error);
    result.errors.push(`Expired documents cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Clean up old rate limit records (older than 24 hours)
 */
async function cleanupOldRateLimits(supabase: any, result: CleanupResult): Promise<void> {
  try {
    console.log('Cleaning up old rate limit records...');

    const { data, error } = await supabase.rpc('cleanup_old_rate_limits');

    if (error) {
      throw new Error(`Failed to cleanup rate limits: ${error.message}`);
    }

    result.oldRateLimits = data || 0;
    console.log(`Cleaned up ${result.oldRateLimits} old rate limit records`);

  } catch {
    console.error('Error in cleanupOldRateLimits:', error);
    result.errors.push(`Rate limits cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Clean up old usage logs (keep only summary data for logs older than 1 year)
 */
async function cleanupOldUsageLogs(supabase: any, result: CleanupResult): Promise<void> {
  try {
    console.log('Cleaning up old usage logs...');

    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

    // Delete detailed usage logs older than 1 year
    const { error: deleteError, count } = await supabase
      .from('usage_logs')
      .delete()
      .lt('created_at', oneYearAgo.toISOString());

    if (deleteError) {
      throw new Error(`Failed to cleanup old usage logs: ${deleteError.message}`);
    }

    result.oldUsageLogs = count || 0;
    console.log(`Cleaned up ${result.oldUsageLogs} old usage log records`);

  } catch {
    console.error('Error in cleanupOldUsageLogs:', error);
    result.errors.push(`Usage logs cleanup: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Add error handling for unhandled promise rejections
globalThis.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});