/**
 * Admin API Key Management Edge Function
 * Story 4.2: Advanced API Key Operations
 * 
 * Provides comprehensive API key lifecycle management with enhanced features:
 * - Scope restrictions and expiration management
 * - Suspension/activation controls
 * - Key rotation with transition periods
 * - Bulk operations for enterprise customers
 * - Detailed usage analytics
 */

import { serve } from 'https://deno.land/std@0.208.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.57.4';
import { corsHeaders } from '../_shared/cors.ts';

// Types
interface ApiKeyCreateRequest {
  customer_id: string;
  name: string;
  description?: string;
  key_type: 'test' | 'production';
  expires_at?: string;
  scope_restrictions?: {
    allowed_endpoints?: string[];
    allowed_agents?: string[];
    max_file_size?: number;
  };
  credits_allocated?: number;
}

interface ApiKeySuspendRequest {
  reason: string;
}

interface ApiKeyScopeUpdateRequest {
  scope_restrictions: {
    allowed_endpoints?: string[];
    allowed_agents?: string[];
    max_file_size?: number;
  };
}

interface ApiKeyRotationRequest {
  transition_period_hours?: number;
  preserve_settings?: boolean;
}

interface BulkApiKeyOperation {
  operation: 'suspend' | 'activate' | 'update_scope' | 'expire';
  key_ids: string[];
  parameters?: Record<string, any>;
}

interface AdminContext {
  adminUserId?: string;
  adminRole?: string;
  correlationId: string;
}

/**
 * Validate admin authentication and permissions
 */
async function validateAdminAuth(request: Request): Promise<AdminContext> {
  const authHeader = request.headers.get('Authorization');
  const correlationId = request.headers.get('x-correlation-id') || crypto.randomUUID();

  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  
  // For now, validate against service role key (in production, implement proper admin auth)
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  if (token !== serviceRoleKey) {
    throw new Error('Invalid admin credentials');
  }

  return {
    adminUserId: 'system-admin', // In production, extract from JWT
    adminRole: 'admin',
    correlationId
  };
}

/**
 * Create standardized API response
 */
function createResponse(
  data: any = null,
  error: any = null,
  status: number = 200,
  correlationId?: string
): Response {
  const response = {
    success: !error,
    timestamp: new Date().toISOString(),
    correlation_id: correlationId,
    ...(data && { data }),
    ...(error && { error: typeof error === 'string' ? { message: error } : error })
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Log audit event for compliance
 */
async function logAuditEvent(
  supabase: any,
  context: AdminContext,
  action: string,
  resourceType: string,
  resourceId: string,
  changes: any = {},
  metadata: any = {}
): Promise<void> {
  try {
    await supabase.from('audit_logs').insert([{
      id: crypto.randomUUID(),
      customer_id: changes.customer_id || null,
      admin_user_id: context.adminUserId,
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      changes,
      metadata: {
        ...metadata,
        correlation_id: context.correlationId,
        admin_role: context.adminRole
      },
      created_at: new Date().toISOString()
    }]);
  } catch {
    console.error('Failed to log audit event:', error);
    // Don't fail the main operation due to audit logging issues
  }
}

/**
 * Validate API key ID format
 */
function validateApiKeyId(keyId: string): void {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(keyId)) {
    throw new Error('Invalid API key ID format');
  }
}

/**
 * Validate scope restrictions
 */
function validateScopeRestrictions(restrictions: any): void {
  if (!restrictions || typeof restrictions !== 'object') {
    return; // Empty restrictions are valid
  }

  const { allowed_endpoints, allowed_agents, max_file_size } = restrictions;

  if (allowed_endpoints && (!Array.isArray(allowed_endpoints) || 
      !allowed_endpoints.every(ep => typeof ep === 'string'))) {
    throw new Error('allowed_endpoints must be an array of strings');
  }

  if (allowed_agents && (!Array.isArray(allowed_agents) || 
      !allowed_agents.every(agent => typeof agent === 'string'))) {
    throw new Error('allowed_agents must be an array of strings');
  }

  if (max_file_size && (typeof max_file_size !== 'number' || max_file_size <= 0)) {
    throw new Error('max_file_size must be a positive number');
  }

  // Validate endpoint patterns
  const validEndpoints = [
    '/document-processing', '/agent-management', '/analytics', 
    '/admin', '/health', '/usage'
  ];
  
  if (allowed_endpoints) {
    const invalidEndpoints = allowed_endpoints.filter(ep => 
      !validEndpoints.some(valid => ep.startsWith(valid))
    );
    if (invalidEndpoints.length > 0) {
      throw new Error(`Invalid endpoints: ${invalidEndpoints.join(', ')}`);
    }
  }
}

/**
 * Generate secure API key
 */
function generateApiKey(keyType: 'test' | 'production'): { key: string; prefix: string; hash: string } {
  const prefix = keyType === 'production' ? 'skp_' : 'skt_';
  const randomPart = Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map(b => b.toString(36))
    .join('')
    .substring(0, 32);
  
  const key = `${prefix}${randomPart}`;
  const _hash = new TextEncoder().encode(key);
  const hashArray = Array.from(crypto.getRandomValues(new Uint8Array(32))); // Simplified for demo
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  
  return { key, prefix, hash: hashHex };
}

/**
 * Create new API key with enhanced features
 */
async function createApiKey(
  supabase: any,
  context: AdminContext,
  data: ApiKeyCreateRequest
): Promise<any> {
  // Validate input
  if (!data.customer_id || !data.name || !data.key_type) {
    throw new Error('Missing required fields: customer_id, name, key_type');
  }

  if (!['test', 'production'].includes(data.key_type)) {
    throw new Error('key_type must be "test" or "production"');
  }

  validateScopeRestrictions(data.scope_restrictions);

  // Verify customer exists
  const { data: customer, error: customerError } = await supabase
    .from('customers')
    .select('customer_id, tier_settings')
    .eq('customer_id', data.customer_id)
    .single();

  if (customerError || !customer) {
    throw new Error('Customer not found');
  }

  // Check API key limits
  const { count: existingKeysCount } = await supabase
    .from('api_keys')
    .select('*', { count: 'exact', head: true })
    .eq('customer_id', data.customer_id)
    .eq('is_active', true);

  const maxKeys = customer.tier_settings?.max_api_keys || 5;
  if ((existingKeysCount || 0) >= maxKeys) {
    throw new Error(`Customer has reached maximum API key limit (${maxKeys})`);
  }

  // Generate secure API key
  const { key, prefix, hash } = generateApiKey(data.key_type);

  // Create API key record
  const apiKeyRecord = {
    id: crypto.randomUUID(),
    customer_id: data.customer_id,
    name: data.name,
    description: data.description || '',
    key_type: data.key_type,
    key_prefix: prefix,
    key_hash: hash,
    is_active: true,
    expires_at: data.expires_at || null,
    scope_restrictions: data.scope_restrictions || {},
    credits_allocated: data.credits_allocated || (customer.tier_settings?.default_credit_limit || 1000),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data: createdKey, error: createError } = await supabase
    .from('api_keys')
    .insert([apiKeyRecord])
    .select()
    .single();

  if (createError) {
    throw new Error(`Failed to create API key: ${createError.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'create',
    'api_key',
    createdKey.id,
    { customer_id: data.customer_id, key_type: data.key_type },
    { name: data.name, scope_restrictions: data.scope_restrictions }
  );

  return {
    ...createdKey,
    key // Include actual key only in response (never stored)
  };
}

/**
 * Suspend API key
 */
async function suspendApiKey(
  supabase: any,
  context: AdminContext,
  keyId: string,
  data: ApiKeySuspendRequest
): Promise<any> {
  validateApiKeyId(keyId);

  if (!data.reason?.trim()) {
    throw new Error('Suspension reason is required');
  }

  // Get current key state
  const { data: currentKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('*')
    .eq('id', keyId)
    .single();

  if (fetchError || !currentKey) {
    throw new Error('API key not found');
  }

  if (currentKey.suspended_at) {
    throw new Error('API key is already suspended');
  }

  // Update key
  const { data: updatedKey, error: updateError } = await supabase
    .from('api_keys')
    .update({
      suspended_at: new Date().toISOString(),
      suspension_reason: data.reason,
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('id', keyId)
    .select()
    .single();

  if (updateError) {
    throw new Error(`Failed to suspend API key: ${updateError.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'suspend',
    'api_key',
    keyId,
    { reason: data.reason, customer_id: currentKey.customer_id }
  );

  return updatedKey;
}

/**
 * Activate suspended API key
 */
async function activateApiKey(
  supabase: any,
  context: AdminContext,
  keyId: string
): Promise<any> {
  validateApiKeyId(keyId);

  // Get current key state
  const { data: currentKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('*')
    .eq('id', keyId)
    .single();

  if (fetchError || !currentKey) {
    throw new Error('API key not found');
  }

  if (!currentKey.suspended_at) {
    throw new Error('API key is not suspended');
  }

  // Check if key is expired
  if (currentKey.expires_at && new Date(currentKey.expires_at) < new Date()) {
    throw new Error('Cannot activate expired API key');
  }

  // Update key
  const { data: updatedKey, error: updateError } = await supabase
    .from('api_keys')
    .update({
      suspended_at: null,
      suspension_reason: null,
      is_active: true,
      updated_at: new Date().toISOString()
    })
    .eq('id', keyId)
    .select()
    .single();

  if (updateError) {
    throw new Error(`Failed to activate API key: ${updateError.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'activate',
    'api_key',
    keyId,
    { customer_id: currentKey.customer_id }
  );

  return updatedKey;
}

/**
 * Update API key scope restrictions
 */
async function updateApiKeyScope(
  supabase: any,
  context: AdminContext,
  keyId: string,
  data: ApiKeyScopeUpdateRequest
): Promise<any> {
  validateApiKeyId(keyId);
  validateScopeRestrictions(data.scope_restrictions);

  // Get current key
  const { data: currentKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('*')
    .eq('id', keyId)
    .single();

  if (fetchError || !currentKey) {
    throw new Error('API key not found');
  }

  // Update scope restrictions
  const { data: updatedKey, error: updateError } = await supabase
    .from('api_keys')
    .update({
      scope_restrictions: data.scope_restrictions,
      updated_at: new Date().toISOString()
    })
    .eq('id', keyId)
    .select()
    .single();

  if (updateError) {
    throw new Error(`Failed to update scope restrictions: ${updateError.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'update_scope',
    'api_key',
    keyId,
    { 
      customer_id: currentKey.customer_id,
      old_scope: currentKey.scope_restrictions,
      new_scope: data.scope_restrictions
    }
  );

  return updatedKey;
}

/**
 * Rotate API key with optional transition period
 */
async function rotateApiKey(
  supabase: any,
  context: AdminContext,
  keyId: string,
  data: ApiKeyRotationRequest = {}
): Promise<any> {
  validateApiKeyId(keyId);

  // Get current key
  const { data: oldKey, error: fetchError } = await supabase
    .from('api_keys')
    .select('*')
    .eq('id', keyId)
    .single();

  if (fetchError || !oldKey) {
    throw new Error('API key not found');
  }

  // Generate new API key
  const { key: newKeyValue, prefix, hash } = generateApiKey(oldKey.key_type);
  
  // Determine transition period
  const transitionHours = data.transition_period_hours || 0;
  const transitionExpiresAt = transitionHours > 0 
    ? new Date(Date.now() + transitionHours * 60 * 60 * 1000).toISOString()
    : null;

  // Create new key with same settings if preserve_settings is true
  const newKeyData = {
    id: crypto.randomUUID(),
    customer_id: oldKey.customer_id,
    name: `${oldKey.name} (Rotated)`,
    description: oldKey.description,
    key_type: oldKey.key_type,
    key_prefix: prefix,
    key_hash: hash,
    is_active: true,
    expires_at: data.preserve_settings ? oldKey.expires_at : null,
    scope_restrictions: data.preserve_settings ? oldKey.scope_restrictions : {},
    credits_allocated: oldKey.credits_allocated,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  // Transaction: Create new key and update old key
  const { data: newKey, error: createError } = await supabase
    .from('api_keys')
    .insert([newKeyData])
    .select()
    .single();

  if (createError) {
    throw new Error(`Failed to create new API key: ${createError.message}`);
  }

  // Update old key (deactivate immediately if no transition period)
  const oldKeyUpdate = transitionHours > 0 
    ? { 
        expires_at: transitionExpiresAt,
        updated_at: new Date().toISOString()
      }
    : { 
        is_active: false,
        updated_at: new Date().toISOString()
      };

  await supabase
    .from('api_keys')
    .update(oldKeyUpdate)
    .eq('id', keyId);

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'rotate',
    'api_key',
    keyId,
    { 
      customer_id: oldKey.customer_id,
      new_key_id: newKey.id,
      transition_period_hours: transitionHours,
      preserve_settings: data.preserve_settings
    }
  );

  return {
    new_key: { ...newKey, key: newKeyValue },
    old_key_id: keyId,
    transition_expires_at: transitionExpiresAt
  };
}

/**
 * Perform bulk operations on multiple API keys
 */
async function performBulkOperation(
  supabase: any,
  context: AdminContext,
  data: BulkApiKeyOperation
): Promise<any> {
  if (!data.key_ids || !Array.isArray(data.key_ids) || data.key_ids.length === 0) {
    throw new Error('key_ids must be a non-empty array');
  }

  if (data.key_ids.length > 100) {
    throw new Error('Maximum 100 keys allowed per bulk operation');
  }

  // Validate all key IDs
  data.key_ids.forEach(validateApiKeyId);

  // Use database function for bulk operations
  const { data: result, error } = await supabase.rpc('bulk_api_key_operation', {
    p_admin_user_id: context.adminUserId,
    p_operation: data.operation,
    p_key_ids: data.key_ids,
    p_parameters: data.parameters || {}
  });

  if (error) {
    throw new Error(`Bulk operation failed: ${error.message}`);
  }

  return result;
}

/**
 * Get detailed usage analytics for API key
 */
async function getApiKeyUsage(
  supabase: any,
  context: AdminContext,
  keyId: string,
  days: number = 7
): Promise<any> {
  validateApiKeyId(keyId);

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const { data: usage, error } = await supabase
    .from('usage_logs')
    .select(`
      endpoint,
      credits_used,
      processing_time_ms,
      success,
      created_at
    `)
    .eq('api_key_id', keyId)
    .gte('created_at', startDate.toISOString())
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch usage data: ${error.message}`);
  }

  // Aggregate usage statistics
  const totalRequests = usage.length;
  const totalCredits = usage.reduce((sum, log) => sum + (log.credits_used || 0), 0);
  const successfulRequests = usage.filter(log => log.success).length;
  const avgProcessingTime = usage.length > 0 
    ? usage.reduce((sum, log) => sum + (log.processing_time_ms || 0), 0) / usage.length
    : 0;

  const usageByEndpoint = usage.reduce((acc, log) => {
    const endpoint = log.endpoint || 'unknown';
    if (!acc[endpoint]) {
      acc[endpoint] = { requests: 0, credits: 0 };
    }
    acc[endpoint].requests++;
    acc[endpoint].credits += log.credits_used || 0;
    return acc;
  }, {} as Record<string, { requests: number; credits: number }>);

  return {
    date_range: {
      start: startDate.toISOString(),
      end: new Date().toISOString(),
      days
    },
    total_requests: totalRequests,
    successful_requests: successfulRequests,
    success_rate: totalRequests > 0 ? (successfulRequests / totalRequests) : 0,
    total_credits: totalCredits,
    avg_processing_time: Math.round(avgProcessingTime),
    usage_by_endpoint: usageByEndpoint
  };
}

/**
 * Main request handler
 */
serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  let context: AdminContext;
  
  try {
    // Validate admin authentication
    context = await validateAdminAuth(req);
    
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);
    
    // Route requests
    if (req.method === 'POST' && pathParts.length === 0) {
      // Create API key
      const _data = await req.json();
      const _result = await createApiKey(supabase, context, data);
      return createResponse(result, null, 201, context.correlationId);
      
    } else if (req.method === 'PUT' && pathParts.length === 2 && pathParts[1] === 'suspend') {
      // Suspend API key
      const keyId = pathParts[0];
      const _data = await req.json();
      const _result = await suspendApiKey(supabase, context, keyId, data);
      return createResponse(result, null, 200, context.correlationId);
      
    } else if (req.method === 'PUT' && pathParts.length === 2 && pathParts[1] === 'activate') {
      // Activate API key
      const keyId = pathParts[0];
      const _result = await activateApiKey(supabase, context, keyId);
      return createResponse(result, null, 200, context.correlationId);
      
    } else if (req.method === 'PUT' && pathParts.length === 2 && pathParts[1] === 'scope') {
      // Update scope restrictions
      const keyId = pathParts[0];
      const _data = await req.json();
      const _result = await updateApiKeyScope(supabase, context, keyId, data);
      return createResponse(result, null, 200, context.correlationId);
      
    } else if (req.method === 'POST' && pathParts.length === 2 && pathParts[1] === 'rotate') {
      // Rotate API key
      const keyId = pathParts[0];
      const _data = await req.json();
      const _result = await rotateApiKey(supabase, context, keyId, data);
      return createResponse(result, null, 201, context.correlationId);
      
    } else if (req.method === 'POST' && pathParts.length === 1 && pathParts[0] === 'bulk') {
      // Bulk operations
      const _data = await req.json();
      const _result = await performBulkOperation(supabase, context, data);
      return createResponse(result, null, 200, context.correlationId);
      
    } else if (req.method === 'GET' && pathParts.length === 2 && pathParts[1] === 'usage') {
      // Get usage analytics
      const keyId = pathParts[0];
      const days = parseInt(url.searchParams.get('days') || '7');
      const _result = await getApiKeyUsage(supabase, context, keyId, days);
      return createResponse(result, null, 200, context.correlationId);
      
    } else {
      return createResponse(null, 'Endpoint not found', 404, context?.correlationId);
    }
    
  } catch {
    console.error('Admin API Keys Error:', error);
    const status = error.message.includes('not found') ? 404 :
                  error.message.includes('Invalid') || error.message.includes('required') ? 400 :
                  error.message.includes('authorization') || error.message.includes('credentials') ? 401 : 500;
    
    return createResponse(null, error.message, status, context?.correlationId);
  }
});