/**
 * Admin Customer Management Edge Function
 * Story 4.1: Comprehensive Customer Management
 * 
 * Provides CRUD operations for customer lifecycle management
 */

import { serve } from 'https://deno.land/std@0.208.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.57.4';
import { corsHeaders } from '../_shared/cors.ts';

// Types
interface CustomerProfile {
  id?: string;
  customer_id: string;
  name: string;
  email?: string;
  company_name?: string;
  contact_email?: string;
  tier: 'starter' | 'professional' | 'enterprise';
  status?: 'trial' | 'active' | 'suspended' | 'enterprise' | 'cancelled';
  tier_settings?: {
    max_api_keys: number;
    default_credit_limit: number;
    rate_limit_multiplier: number;
  };
  suspension_reason?: string;
  notes?: string;
}

interface CustomerSearchParams {
  limit?: number;
  offset?: number;
  tier?: string;
  status?: string;
  search?: string;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

interface AdminContext {
  adminUserId?: string;
  adminRole?: string;
  correlationId: string;
}

/**
 * Validate admin authentication and permissions
 */
async function validateAdminAuth(request: Request): Promise<AdminContext> {
  const authHeader = request.headers.get('Authorization');
  const adminUserId = request.headers.get('x-admin-user-id');
  const adminRole = request.headers.get('x-admin-role') || 'admin';
  const correlationId = request.headers.get('x-correlation-id') || crypto.randomUUID();

  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  // Basic role validation
  if (adminRole === 'read-only' && request.method !== 'GET') {
    throw new Error('Insufficient permissions for this operation');
  }

  return {
    adminUserId: adminUserId || 'system',
    adminRole,
    correlationId
  };
}

/**
 * Log audit event
 */
async function logAuditEvent(
  supabase: ReturnType<typeof createClient>,
  adminContext: AdminContext,
  action: string,
  resourceType: string,
  resourceId?: string,
  details?: Record<string, unknown>,
  customerId?: string
): Promise<void> {
  try {
    await supabase.from('audit_logs').insert({
      customer_id: customerId || null,
      event_type: `admin_${action}`,
      resource_type: resourceType,
      resource_id: resourceId,
      action,
      actor_id: adminContext.adminUserId,
      actor_type: 'admin',
      details: details || {},
      correlation_id: adminContext.correlationId,
      risk_level: action === 'delete' ? 'high' : 'low'
    });
  } catch {
    console.error('Failed to log audit event:', error);
    // Don't throw - audit logging failures shouldn't break operations
  }
}

/**
 * Validate customer data
 */
function validateCustomerData(data: Partial<CustomerProfile>): string[] {
  const errors: string[] = [];

  if (!data.name?.trim()) {
    errors.push('name is required');
  }

  if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('email format is invalid');
  }

  if (data.contact_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.contact_email)) {
    errors.push('contact_email format is invalid');
  }

  if (data.tier && !['starter', 'professional', 'enterprise'].includes(data.tier)) {
    errors.push('tier must be starter, professional, or enterprise');
  }

  if (data.status && !['trial', 'active', 'suspended', 'enterprise', 'cancelled'].includes(data.status)) {
    errors.push('status must be trial, active, suspended, enterprise, or cancelled');
  }

  if (data.tier_settings) {
    const { max_api_keys, default_credit_limit, rate_limit_multiplier } = data.tier_settings;
    
    if (typeof max_api_keys !== 'number' || max_api_keys < 0) {
      errors.push('tier_settings.max_api_keys must be a non-negative number');
    }
    
    if (typeof default_credit_limit !== 'number' || default_credit_limit < 0) {
      errors.push('tier_settings.default_credit_limit must be a non-negative number');
    }
    
    if (typeof rate_limit_multiplier !== 'number' || rate_limit_multiplier < 1) {
      errors.push('tier_settings.rate_limit_multiplier must be >= 1');
    }
  }

  return errors;
}

/**
 * Create standardized API response
 */
function createResponse(data: unknown, status: number = 200, success: boolean = true): Response {
  const response = {
    success,
    data: success ? data : undefined,
    error: success ? undefined : data,
    timestamp: new Date().toISOString()
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

/**
 * Handle customer creation
 */
async function createCustomer(
  supabase: ReturnType<typeof createClient>,
  adminContext: AdminContext,
  customerData: CustomerProfile
): Promise<Response> {
  // Validation
  const errors = validateCustomerData(customerData);
  if (errors.length > 0) {
    return createResponse(errors.join(', '), 400, false);
  }

  try {
    // Check for existing customer_id
    const { data: existing } = await supabase
      .from('customers')
      .select('id')
      .eq('customer_id', customerData.customer_id)
      .single();

    if (existing) {
      return createResponse('Customer ID already exists', 409, false);
    }

    // Create customer
    const { data: newCustomer, error } = await supabase
      .from('customers')
      .insert({
        customer_id: customerData.customer_id,
        name: customerData.name,
        email: customerData.email || customerData.contact_email,
        company_name: customerData.company_name || customerData.name,
        contact_email: customerData.contact_email || customerData.email,
        tier: customerData.tier,
        status: customerData.status || 'trial',
        tier_settings: customerData.tier_settings,
        notes: customerData.notes
      })
      .select()
      .single();

    if (error) {
      console.error('Customer creation error:', error);
      return createResponse('Failed to create customer', 500, false);
    }

    // Log audit event
    await logAuditEvent(
      supabase,
      adminContext,
      'create',
      'customer',
      newCustomer.id,
      { created_customer: customerData },
      newCustomer.id
    );

    return createResponse(newCustomer, 201);

  } catch {
    console.error('Customer creation error:', error);
    return createResponse('Internal server error', 500, false);
  }
}

/**
 * Handle customer listing with search and pagination
 */
async function listCustomers(
  supabase: any,
  adminContext: AdminContext,
  searchParams: CustomerSearchParams
): Promise<Response> {
  try {
    const {
      limit = 20,
      offset = 0,
      tier,
      status,
      search,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = searchParams;

    // Build query
    let query = supabase
      .from('customers')
      .select(`
        id,
        customer_id,
        name,
        email,
        company_name,
        contact_email,
        tier,
        status,
        tier_settings,
        credits_available,
        suspension_reason,
        suspended_at,
        created_at,
        updated_at,
        last_active_at
      `, { count: 'exact' });

    // Apply filters
    if (tier) {
      query = query.eq('tier', tier);
    }

    if (status) {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(
        `name.ilike.%${search}%,` +
        `company_name.ilike.%${search}%,` +
        `email.ilike.%${search}%,` +
        `contact_email.ilike.%${search}%,` +
        `customer_id.ilike.%${search}%`
      );
    }

    // Exclude soft-deleted customers
    query = query.is('deleted_at', null);

    // Apply sorting
    const ascending = sort_order === 'asc';
    query = query.order(sort_by, { ascending });

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: customers, error, count } = await query;

    if (error) {
      console.error('Customer listing error:', error);
      return createResponse('Failed to list customers', 500, false);
    }

    // Get additional stats for each customer
    const customerIds = customers?.map(c => c.id) || [];
    const { data: apiKeyStats } = await supabase
      .from('api_keys')
      .select('customer_id')
      .in('customer_id', customerIds)
      .eq('is_active', true);

    const apiKeyCount = apiKeyStats?.reduce((acc: Record<string, number>, key: any) => {
      acc[key.customer_id] = (acc[key.customer_id] || 0) + 1;
      return acc;
    }, {}) || {};

    // Enhance customer data
    const enhancedCustomers = customers?.map(customer => ({
      ...customer,
      api_keys_count: apiKeyCount[customer.id] || 0,
      usage_stats: {
        // These would be populated from usage_logs in a real implementation
        total_requests: 0,
        total_credits_used: 0,
        last_activity: customer.last_active_at
      }
    }));

    return createResponse({
      customers: enhancedCustomers,
      pagination: {
        total: count || 0,
        limit,
        offset,
        has_more: (count || 0) > offset + limit
      }
    });

  } catch {
    console.error('Customer listing error:', error);
    return createResponse('Internal server error', 500, false);
  }
}

/**
 * Handle customer details retrieval
 */
async function getCustomer(
  supabase: any,
  adminContext: AdminContext,
  customerId: string
): Promise<Response> {
  try {
    // Get customer details
    const { data: customer, error } = await supabase
      .from('customers')
      .select(`
        id,
        customer_id,
        name,
        email,
        company_name,
        contact_email,
        tier,
        status,
        tier_settings,
        credits_available,
        credits_used,
        suspension_reason,
        suspended_at,
        notes,
        created_at,
        updated_at,
        last_active_at
      `)
      .eq('id', customerId)
      .is('deleted_at', null)
      .single();

    if (error || !customer) {
      return createResponse('Customer not found', 404, false);
    }

    // Get API keys count
    const { count: apiKeysCount } = await supabase
      .from('api_keys')
      .select('id', { count: 'exact', head: true })
      .eq('customer_id', customerId)
      .eq('is_active', true);

    // Get usage statistics (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const { data: usageStats } = await supabase
      .from('usage_logs')
      .select('credits_used, success, created_at')
      .eq('customer_id', customerId)
      .gte('created_at', thirtyDaysAgo.toISOString());

    const usage = usageStats?.reduce((acc, log) => {
      acc.total_requests++;
      acc.total_credits += log.credits_used || 0;
      if (log.success) acc.successful_requests++;
      return acc;
    }, { total_requests: 0, total_credits: 0, successful_requests: 0 }) || 
    { total_requests: 0, total_credits: 0, successful_requests: 0 };

    return createResponse({
      ...customer,
      api_keys_count: apiKeysCount || 0,
      usage_stats: {
        ...usage,
        success_rate: usage.total_requests > 0 ? 
          Math.round((usage.successful_requests / usage.total_requests) * 100) : 0,
        period: '30_days'
      }
    });

  } catch {
    console.error('Customer retrieval error:', error);
    return createResponse('Internal server error', 500, false);
  }
}

/**
 * Handle customer updates
 */
async function updateCustomer(
  supabase: any,
  adminContext: AdminContext,
  customerId: string,
  updateData: Partial<CustomerProfile>
): Promise<Response> {
  try {
    // Validation
    const errors = validateCustomerData(updateData);
    if (errors.length > 0) {
      return createResponse(errors.join(', '), 400, false);
    }

    // Get current customer data
    const { data: currentCustomer } = await supabase
      .from('customers')
      .select('*')
      .eq('id', customerId)
      .is('deleted_at', null)
      .single();

    if (!currentCustomer) {
      return createResponse('Customer not found', 404, false);
    }

    // Prepare update data
    const updates: any = { updated_at: new Date().toISOString() };
    
    if (updateData.name) updates.name = updateData.name;
    if (updateData.email) updates.email = updateData.email;
    if (updateData.company_name) updates.company_name = updateData.company_name;
    if (updateData.contact_email) updates.contact_email = updateData.contact_email;
    if (updateData.tier) updates.tier = updateData.tier;
    if (updateData.status) updates.status = updateData.status;
    if (updateData.tier_settings) updates.tier_settings = updateData.tier_settings;
    if (updateData.notes !== undefined) updates.notes = updateData.notes;

    // Update customer
    const { data: updatedCustomer, error } = await supabase
      .from('customers')
      .update(updates)
      .eq('id', customerId)
      .select()
      .single();

    if (error) {
      console.error('Customer update error:', error);
      return createResponse('Failed to update customer', 500, false);
    }

    // Log audit event
    await logAuditEvent(
      supabase,
      adminContext,
      'update',
      'customer',
      customerId,
      { 
        old_values: currentCustomer,
        new_values: updateData 
      },
      customerId
    );

    return createResponse(updatedCustomer);

  } catch {
    console.error('Customer update error:', error);
    return createResponse('Internal server error', 500, false);
  }
}

/**
 * Handle customer status updates
 */
async function updateCustomerStatus(
  supabase: any,
  adminContext: AdminContext,
  customerId: string,
  statusData: { status: string; reason?: string }
): Promise<Response> {
  try {
    const { status, reason } = statusData;

    // Validate status
    if (!['trial', 'active', 'suspended', 'enterprise', 'cancelled'].includes(status)) {
      return createResponse('Invalid status', 400, false);
    }

    // Get current customer
    const { data: currentCustomer } = await supabase
      .from('customers')
      .select('status')
      .eq('id', customerId)
      .single();

    if (!currentCustomer) {
      return createResponse('Customer not found', 404, false);
    }

    // Validate status transitions
    const invalidTransitions: Record<string, string[]> = {
      'trial': ['enterprise'], // Can't go directly to enterprise
      'cancelled': ['trial', 'active', 'enterprise'] // Can't reactivate cancelled accounts easily
    };

    if (invalidTransitions[currentCustomer.status]?.includes(status)) {
      return createResponse(`Invalid status transition from ${currentCustomer.status} to ${status}`, 400, false);
    }

    // Prepare update
    const updates: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (status === 'suspended') {
      updates.suspended_at = new Date().toISOString();
      updates.suspension_reason = reason || 'No reason provided';
    } else {
      updates.suspended_at = null;
      updates.suspension_reason = null;
    }

    // Update customer
    const { data: updatedCustomer, error } = await supabase
      .from('customers')
      .update(updates)
      .eq('id', customerId)
      .select()
      .single();

    if (error) {
      console.error('Customer status update error:', error);
      return createResponse('Failed to update customer status', 500, false);
    }

    // Log audit event
    await logAuditEvent(
      supabase,
      adminContext,
      'status_change',
      'customer',
      customerId,
      { 
        old_status: currentCustomer.status,
        new_status: status,
        reason 
      },
      customerId
    );

    return createResponse(updatedCustomer);

  } catch {
    console.error('Customer status update error:', error);
    return createResponse('Internal server error', 500, false);
  }
}

/**
 * Handle customer soft deletion
 */
async function deleteCustomer(
  supabase: any,
  adminContext: AdminContext,
  customerId: string
): Promise<Response> {
  try {
    // Get current customer
    const { data: currentCustomer } = await supabase
      .from('customers')
      .select('*')
      .eq('id', customerId)
      .is('deleted_at', null)
      .single();

    if (!currentCustomer) {
      return createResponse('Customer not found', 404, false);
    }

    // Soft delete customer
    const { data: deletedCustomer, error } = await supabase
      .from('customers')
      .update({
        status: 'cancelled',
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', customerId)
      .select()
      .single();

    if (error) {
      console.error('Customer deletion error:', error);
      return createResponse('Failed to delete customer', 500, false);
    }

    // Deactivate all API keys
    await supabase
      .from('api_keys')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('customer_id', customerId);

    // Log audit event
    await logAuditEvent(
      supabase,
      adminContext,
      'delete',
      'customer',
      customerId,
      { deleted_customer: currentCustomer },
      customerId
    );

    return createResponse({ 
      message: 'Customer soft deleted successfully',
      deleted_at: deletedCustomer.deleted_at 
    });

  } catch {
    console.error('Customer deletion error:', error);
    return createResponse('Internal server error', 500, false);
  }
}

/**
 * Main request handler
 */
serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    // Validate admin authentication
    const adminContext = await validateAdminAuth(req);

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? 'http://127.0.0.1:14321';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';
    
    const supabase = createClient(supabaseUrl, supabaseKey
    );

    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);
    const customerId = pathParts[pathParts.length - 1];

    switch (req.method) {
      case 'POST': {
      }
        if (pathParts.includes('status')) {
          // Handle status updates
          const statusData = await req.json();
          return await updateCustomerStatus(supabase, adminContext, pathParts[pathParts.length - 2], statusData);
        } else {
          // Handle customer creation
          const customerData = await req.json();
          return await createCustomer(supabase, adminContext, customerData);
        }
      }

      case 'GET': {
      }
        if (customerId && customerId !== 'admin-customers') {
          // Get specific customer
          return await getCustomer(supabase, adminContext, customerId);
        } else {
          // List customers
          const searchParams: CustomerSearchParams = {
            limit: parseInt(url.searchParams.get('limit') || '20'),
            offset: parseInt(url.searchParams.get('offset') || '0'),
            tier: url.searchParams.get('tier') || undefined,
            status: url.searchParams.get('status') || undefined,
            search: url.searchParams.get('search') || undefined,
            sort_by: url.searchParams.get('sort_by') || 'created_at',
            sort_order: (url.searchParams.get('sort_order') as 'asc' | 'desc') || 'desc'
          };
          return await listCustomers(supabase, adminContext, searchParams);
        }
      }

      case 'PUT': {
      }
        if (pathParts.includes('status')) {
          // Handle status updates
          const statusData = await req.json();
          return await updateCustomerStatus(supabase, adminContext, pathParts[pathParts.length - 2], statusData);
        } else {
          // Handle customer updates
          const updateData = await req.json();
          return await updateCustomer(supabase, adminContext, customerId, updateData);
        }
      }

      case 'DELETE': {
      }
        return await deleteCustomer(supabase, adminContext, customerId);
      }

      default:
        return createResponse('Method not allowed', 405, false);
    }

  } catch {
    console.error('Request processing error:', error);
    
    if (error.message.includes('Missing or invalid authorization')) {
      return createResponse('Unauthorized', 401, false);
    }
    
    if (error.message.includes('Insufficient permissions')) {
      return createResponse('Forbidden', 403, false);
    }
    
    return createResponse('Internal server error', 500, false);
  }
});