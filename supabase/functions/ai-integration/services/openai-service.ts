/**
 * OpenAI Integration Service
 * Handles all OpenAI API interactions
 */

import { parseExtractedData } from '../utils/response-normalizer.ts';

export interface OpenAIConfig {
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  timeout?: number;
}

export interface OpenAIUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
}

export interface OpenAIResponse {
  success: boolean;
  extracted_data: any;
  confidence: number;
  usage: OpenAIUsage;
  model: string;
}

/**
 * Process document with OpenAI GPT models
 */
export async function processWithOpenAI(
  document: string, 
  prompt: string, 
  config: OpenAIConfig
): Promise<OpenAIResponse> {
  const model = config.model || 'gpt-4';
  const temperature = config.temperature || 0.1;
  const maxTokens = config.maxTokens || 1000;
  const timeout = config.timeout || 30000;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages: [
          { 
            role: 'system', 
            content: `${prompt}\n\nRespond with valid JSON in this format: {"extracted_data": {...}, "confidence": 0.0-1.0}` 
          },
          { role: 'user', content: document }
        ],
        temperature,
        max_tokens: maxTokens,
        ...(model.includes('gpt-4') || model.includes('gpt-3.5-turbo') ? { response_format: { type: "json_object" } } : {})
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
    }

    const result = await response.json();
    
    if (!result.choices || !result.choices[0] || !result.choices[0].message) {
      throw new Error('Invalid OpenAI response structure');
    }

    const content = result.choices[0].message.content;
    const { extracted_data, confidence } = parseExtractedData(content);

    return {
      success: true,
      extracted_data,
      confidence,
      usage: result.usage,
      model
    };

  } catch {
    clearTimeout(timeoutId);
    
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error(`OpenAI request timeout after ${timeout}ms`);
    }
    
    throw error;
  }
}

/**
 * Check OpenAI service health
 */
export async function checkOpenAIHealth(apiKey: string): Promise<{ status: string; latency_ms: number; error?: string }> {
  const start = performance.now();
  
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
      signal: AbortSignal.timeout(5000), // 5 second timeout for health check
    });

    const latency_ms = Math.round(performance.now() - start);

    if (!response.ok) {
      return {
        status: 'unhealthy',
        latency_ms,
        error: `HTTP ${response.status}`
      };
    }

    return {
      status: 'healthy',
      latency_ms
    };
  } catch {
    return {
      status: 'unhealthy',
      latency_ms: Math.round(performance.now() - start),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Validate OpenAI configuration
 */
export function validateOpenAIConfig(config: OpenAIConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.apiKey || typeof config.apiKey !== 'string') {
    errors.push('OpenAI API key is required');
  } else if (!config.apiKey.startsWith('sk-')) {
    errors.push('OpenAI API key must start with "sk-"');
  }
  
  if (config.temperature !== undefined && (typeof config.temperature !== 'number' || config.temperature < 0 || config.temperature > 2)) {
    errors.push('Temperature must be a number between 0 and 2');
  }
  
  if (config.maxTokens !== undefined && (typeof config.maxTokens !== 'number' || config.maxTokens < 1 || config.maxTokens > 4096)) {
    errors.push('Max tokens must be a number between 1 and 4096');
  }
  
  if (config.timeout !== undefined && (typeof config.timeout !== 'number' || config.timeout < 1000 || config.timeout > 300000)) {
    errors.push('Timeout must be a number between 1000ms and 300000ms');
  }
  
  return { valid: errors.length === 0, errors };
}

/**
 * Get supported OpenAI models
 */
export function getSupportedModels(): string[] {
  return [
    'gpt-4',
    'gpt-4-turbo',
    'gpt-4o',
    'gpt-4o-mini',
    'gpt-3.5-turbo'
  ];
}

/**
 * Estimate token count for OpenAI request
 */
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  // This is a simplification - real tokenization is more complex
  return Math.ceil(text.length / 4);
}