/**
 * LlamaParse Integration Service
 * Handles LlamaIndex LlamaParse API for complex PDF processing
 */

export interface LlamaParseConfig {
  apiKey: string;
  timeout?: number;
  pollInterval?: number;
  maxPollAttempts?: number;
}

export interface LlamaParseUsage {
  pages: number;
  processing_time_ms: number;
}

export interface LlamaParseResponse {
  success: boolean;
  extracted_data: any;
  confidence: number;
  usage: LlamaParseUsage;
  model: string;
  markdown?: string;
  job_id: string;
}

export interface JobStatus {
  status: 'PENDING' | 'SUCCESS' | 'ERROR';
  result?: any;
  error?: string;
}

/**
 * Process PDF document with LlamaParse
 */
export async function processWithLlamaParse(
  pdfFile: File, 
  config: LlamaParseConfig
): Promise<LlamaParseResponse> {
  const _timeout = config.timeout || 60000; // 60 seconds default
  const pollInterval = config.pollInterval || 2000; // 2 seconds
  const maxPollAttempts = config.maxPollAttempts || 30; // 30 attempts = 60 seconds max

  const startTime = Date.now();

  try {
    // Step 1: Upload PDF file
    const jobId = await uploadFile(pdfFile, config.apiKey);
    
    // Step 2: Poll for results
    const _result = await pollForResults(jobId, config.apiKey, pollInterval, maxPollAttempts);
    
    if (result.status === 'ERROR') {
      throw new Error(`LlamaParse processing failed: ${result.error}`);
    }

    const processingTime = Date.now() - startTime;
    
    // Parse the result and extract structured data
    const { extracted_data, confidence } = parseMarkdownToStructuredData(result.result.markdown);

    return {
      success: true,
      extracted_data,
      confidence,
      usage: {
        pages: result.result.pages || 1,
        processing_time_ms: processingTime
      },
      model: 'llamaparse',
      markdown: result.result.markdown,
      job_id: jobId
    };

  } catch {
    throw new Error(`LlamaParse error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload file to LlamaParse
 */
async function uploadFile(pdfFile: File, apiKey: string): Promise<string> {
  const formData = new FormData();
  formData.append('file', pdfFile);
  formData.append('result_type', 'markdown'); // Get markdown output

  const response = await fetch('https://api.cloud.llamaindex.ai/api/parsing/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${apiKey}`
    },
    body: formData
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(`Upload failed: ${response.status} - ${errorData.error || response.statusText}`);
  }

  const _result = await response.json();
  return result.id || result.job_id;
}

/**
 * Poll for processing results
 */
async function pollForResults(
  jobId: string, 
  apiKey: string, 
  pollInterval: number, 
  maxAttempts: number
): Promise<JobStatus> {
  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      const response = await fetch(`https://api.cloud.llamaindex.ai/api/parsing/job/${jobId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        }
      });

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status}`);
      }

      const status = await response.json();

      if (status.status === 'SUCCESS') {
        // Get the actual result
        const resultResponse = await fetch(`https://api.cloud.llamaindex.ai/api/parsing/job/${jobId}/result/markdown`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`
          }
        });

        if (!resultResponse.ok) {
          throw new Error(`Result fetch failed: ${resultResponse.status}`);
        }

        const resultData = await resultResponse.json();
        return {
          status: 'SUCCESS',
          result: {
            markdown: resultData.markdown || resultData.text,
            pages: resultData.pages || 1
          }
        };
      } else if (status.status === 'ERROR') {
        return {
          status: 'ERROR',
          error: status.error || 'Processing failed'
        };
      }

      // Still pending, wait and try again
      if (attempt < maxAttempts - 1) {
        await new Promise(resolve => setTimeout(resolve, pollInterval));
      }
    } catch {
      if (attempt === maxAttempts - 1) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, pollInterval));
    }
  }

  throw new Error(`Processing timeout after ${maxAttempts} attempts`);
}

/**
 * Parse markdown content to extract structured data
 */
function parseMarkdownToStructuredData(markdown: string): { extracted_data: any; confidence: number } {
  const extracted_data: any = {};
  let confidence = 0.7; // Base confidence for LlamaParse

  // Extract headings
  const headings = markdown.match(/^#+\s+(.+)$/gm);
  if (headings) {
    extracted_data.headings = headings.map(h => h.replace(/^#+\s+/, ''));
  }

  // Extract tables
  const tablePattern = /\|(.+)\|/g;
  const tables = [];
  let match;
  while ((match = tablePattern.exec(markdown)) !== null) {
    const row = match[1].split('|').map(cell => cell.trim());
    tables.push(row);
  }
  if (tables.length > 0) {
    extracted_data.tables = tables;
    confidence += 0.1; // Higher confidence when tables are found
  }

  // Extract amounts/prices
  const amountPattern = /\$[\d,]+\.?\d*/g;
  const amounts = markdown.match(amountPattern);
  if (amounts) {
    extracted_data.amounts = amounts.map(amount => ({
      raw: amount,
      value: parseFloat(amount.replace(/[$,]/g, ''))
    }));
    confidence += 0.1;
  }

  // Extract dates
  const datePattern = /\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b/g;
  const dates = markdown.match(datePattern);
  if (dates) {
    extracted_data.dates = dates;
    confidence += 0.05;
  }

  // Extract emails
  const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
  const emails = markdown.match(emailPattern);
  if (emails) {
    extracted_data.emails = emails;
  }

  // Extract phone numbers
  const phonePattern = /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b|\(\d{3}\)\s*\d{3}[-.]?\d{4}/g;
  const phones = markdown.match(phonePattern);
  if (phones) {
    extracted_data.phone_numbers = phones;
  }

  // Add raw markdown for reference
  extracted_data.raw_markdown = markdown;
  extracted_data.document_type = inferDocumentType(markdown);

  return {
    extracted_data,
    confidence: Math.min(confidence, 1.0)
  };
}

/**
 * Infer document type from content
 */
function inferDocumentType(content: string): string {
  const lowerContent = content.toLowerCase();
  
  if (lowerContent.includes('invoice') || lowerContent.includes('bill to')) {
    return 'invoice';
  } else if (lowerContent.includes('contract') || lowerContent.includes('agreement')) {
    return 'contract';
  } else if (lowerContent.includes('receipt') || lowerContent.includes('purchase')) {
    return 'receipt';
  } else if (lowerContent.includes('policy') || lowerContent.includes('insurance')) {
    return 'insurance';
  } else if (lowerContent.includes('statement') || lowerContent.includes('account')) {
    return 'statement';
  }
  
  return 'document';
}

/**
 * Check LlamaParse service health
 */
export async function checkLlamaParseHealth(apiKey: string): Promise<{ status: string; latency_ms: number; error?: string }> {
  const start = performance.now();
  
  try {
    // Check if we can reach the API by getting account info or limits
    const response = await fetch('https://api.cloud.llamaindex.ai/api/parsing/health', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
      },
      signal: AbortSignal.timeout(5000), // 5 second timeout for health check
    });

    const latency_ms = Math.round(performance.now() - start);

    if (!response.ok) {
      return {
        status: 'unhealthy',
        latency_ms,
        error: `HTTP ${response.status}`
      };
    }

    return {
      status: 'healthy',
      latency_ms
    };
  } catch {
    return {
      status: 'unhealthy',
      latency_ms: Math.round(performance.now() - start),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Validate LlamaParse configuration
 */
export function validateLlamaParseConfig(config: LlamaParseConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.apiKey || typeof config.apiKey !== 'string') {
    errors.push('LlamaParse API key is required');
  } else if (!config.apiKey.startsWith('llx-')) {
    errors.push('LlamaParse API key must start with "llx-"');
  }
  
  if (config.timeout !== undefined && (typeof config.timeout !== 'number' || config.timeout < 5000 || config.timeout > 300000)) {
    errors.push('Timeout must be a number between 5000ms and 300000ms');
  }
  
  if (config.pollInterval !== undefined && (typeof config.pollInterval !== 'number' || config.pollInterval < 1000 || config.pollInterval > 10000)) {
    errors.push('Poll interval must be a number between 1000ms and 10000ms');
  }
  
  if (config.maxPollAttempts !== undefined && (typeof config.maxPollAttempts !== 'number' || config.maxPollAttempts < 5 || config.maxPollAttempts > 100)) {
    errors.push('Max poll attempts must be a number between 5 and 100');
  }
  
  return { valid: errors.length === 0, errors };
}

/**
 * Get supported file types for LlamaParse
 */
export function getSupportedFileTypes(): string[] {
  return [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
    'text/html',
    'text/xml'
  ];
}

/**
 * Validate file for LlamaParse processing
 */
export function validateFileForProcessing(file: File): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const supportedTypes = getSupportedFileTypes();
  
  if (!supportedTypes.includes(file.type)) {
    errors.push(`Unsupported file type: ${file.type}. Supported types: ${supportedTypes.join(', ')}`);
  }
  
  if (file.size > 50 * 1024 * 1024) { // 50MB limit
    errors.push('File size must be less than 50MB');
  }
  
  if (file.size === 0) {
    errors.push('File cannot be empty');
  }
  
  return { valid: errors.length === 0, errors };
}