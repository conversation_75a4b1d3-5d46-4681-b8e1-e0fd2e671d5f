/**
 * Claude Integration Service
 * Handles all Anthropic Claude API interactions
 */

import { parseExtractedData } from '../utils/response-normalizer.ts';

export interface ClaudeConfig {
  apiKey: string;
  model?: string;
  maxTokens?: number;
  timeout?: number;
}

export interface ClaudeUsage {
  input_tokens: number;
  output_tokens: number;
}

export interface ClaudeResponse {
  success: boolean;
  extracted_data: any;
  confidence: number;
  usage: ClaudeUsage;
  model: string;
}

/**
 * Process document with Claude models
 */
export async function processWithClaude(
  document: string, 
  prompt: string, 
  config: ClaudeConfig
): Promise<ClaudeResponse> {
  const model = config.model || 'claude-3-5-sonnet-20240620';
  const maxTokens = config.maxTokens || 1000;
  const timeout = config.timeout || 30000;

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        max_tokens: maxTokens,
        messages: [{
          role: 'user',
          content: `${prompt}\n\nDocument to analyze:\n${document}\n\nRespond with valid JSON in this format: {"extracted_data": {...}, "confidence": 0.0-1.0}`
        }]
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error?.message || response.statusText;
      throw new Error(`Claude API error: ${response.status} - ${errorMessage}`);
    }

    const result = await response.json();
    
    if (!result.content || !result.content[0] || !result.content[0].text) {
      throw new Error('Invalid Claude response structure');
    }

    const content = result.content[0].text;
    const { extracted_data, confidence } = parseExtractedData(content);

    return {
      success: true,
      extracted_data,
      confidence,
      usage: result.usage,
      model
    };

  } catch {
    clearTimeout(timeoutId);
    
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error(`Claude request timeout after ${timeout}ms`);
    }
    
    throw error;
  }
}

/**
 * Check Claude service health
 */
export async function checkClaudeHealth(apiKey: string): Promise<{ status: string; latency_ms: number; error?: string }> {
  const start = performance.now();
  
  try {
    // Claude doesn't have a simple health endpoint, so we make a minimal request
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307', // Use fastest/cheapest model for health check
        max_tokens: 10,
        messages: [{
          role: 'user',
          content: 'Hello'
        }]
      }),
      signal: AbortSignal.timeout(5000), // 5 second timeout for health check
    });

    const latency_ms = Math.round(performance.now() - start);

    if (!response.ok) {
      return {
        status: 'unhealthy',
        latency_ms,
        error: `HTTP ${response.status}`
      };
    }

    return {
      status: 'healthy',
      latency_ms
    };
  } catch {
    return {
      status: 'unhealthy',
      latency_ms: Math.round(performance.now() - start),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Validate Claude configuration
 */
export function validateClaudeConfig(config: ClaudeConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.apiKey || typeof config.apiKey !== 'string') {
    errors.push('Claude API key is required');
  } else if (!config.apiKey.startsWith('sk-ant-')) {
    errors.push('Claude API key must start with "sk-ant-"');
  }
  
  if (config.maxTokens !== undefined && (typeof config.maxTokens !== 'number' || config.maxTokens < 1 || config.maxTokens > 4096)) {
    errors.push('Max tokens must be a number between 1 and 4096');
  }
  
  if (config.timeout !== undefined && (typeof config.timeout !== 'number' || config.timeout < 1000 || config.timeout > 300000)) {
    errors.push('Timeout must be a number between 1000ms and 300000ms');
  }
  
  return { valid: errors.length === 0, errors };
}

/**
 * Get supported Claude models
 */
export function getSupportedModels(): string[] {
  return [
    'claude-3-opus-20240229',
    'claude-3-sonnet-20240229',
    'claude-3-haiku-20240307',
    'claude-3-5-sonnet-20240620'
  ];
}

/**
 * Estimate token count for Claude request
 */
export function estimateTokenCount(text: string): number {
  // Claude uses similar tokenization to OpenAI
  // Rough estimation: 1 token ≈ 4 characters for English text
  return Math.ceil(text.length / 4);
}

/**
 * Format prompt for Claude's expected format
 */
export function formatClaudePrompt(systemPrompt: string, userContent: string): string {
  return `${systemPrompt}

User: ${userContent}`;
}