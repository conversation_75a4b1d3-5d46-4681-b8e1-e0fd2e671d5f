import "jsr:@supabase/functions-js@2.5.0/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";

// Environment variables validation
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY')!;
const CLAUDE_API_KEY = Deno.env.get('CLAUDE_API_KEY')!;
const LLAMAPARSE_API_KEY = Deno.env.get('LLAMAPARSE_API_KEY')!;


// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// Types
interface ExtractedData {
  [key: string]: string | number | boolean | ExtractedData | ExtractedData[];
}

interface DocumentMetadata {
  type?: 'pdf' | 'text' | 'image';
  pages?: number;
  size?: number;
}

interface Usage {
  prompt_tokens?: number;
  completion_tokens?: number;
  input_tokens?: number;
  output_tokens?: number;
  pages?: number;
}

interface ProcessingResult {
  success: boolean;
  model: string;
  extracted_data: ExtractedData;
  confidence: number;
  usage: Usage;
  markdown?: string;
  job_id?: string;
}

interface RequestData {
  customer_id: string;
  api_key_id: string;
  request_id: string;
  document_length: number;
  complexity_score: number;
  processing_time_ms: number;
}

interface LogData {
  type?: string;
  timestamp?: string;
  model?: string;
  cost_usd?: number;
  customer_price_usd?: number;
  profit_margin_percent?: number;
  customer_id: string;
  api_key_id: string;
  request_id: string;
  document_length: number;
  complexity_score: number;
  processing_time_ms: number;
  error?: string;
}

interface StandardizedResponse {
  success: boolean;
  extracted_data: ExtractedData;
  confidence: number;
  model_used: string;
  processing_time_ms: number;
  cost_breakdown: {
    model_cost_usd: number;
    customer_price_usd: number;
    profit_margin_percent: number;
  };
  error?: string;
}

interface CostTracking {
  model: string;
  input_tokens: number;
  output_tokens: number;
  cost_usd: number;
  customer_price_usd: number;
  profit_margin_percent: number;
  timestamp: Date;
}

interface RequestPayload {
  document: string;
  document_type?: string;
  agent_prompt?: string;
  customer_id: string;
  api_key_id: string;
  complexity_override?: number;
}

// Model configuration
const MODEL_TIERS = {
  fast: ['google/gemini-flash-1.5', 'openai/gpt-4o-mini'],
  balanced: ['anthropic/claude-3.5-sonnet', 'openai/gpt-4o'],
  specialized: ['llamaparse']
};

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

/**
 * Calculate document complexity score (0-10)
 */
function calculateComplexity(document: string, metadata?: DocumentMetadata): number {
  let score = 0;
  
  // Length factor (max 3 points)
  score += Math.min(document.length / 1000, 3);
  
  // Structure complexity
  const hasTable = /\||\t|table/i.test(document);
  const hasNumbers = /\d+[.,]\d+|\$\d+/g.test(document);
  const hasMultiColumn = document.split('\n').some(line => line.split(/\s{3,}/).length > 2);
  
  if (hasTable) score += 2;
  if (hasNumbers) score += 1;
  if (hasMultiColumn) score += 2;
  
  // PDF-specific complexity
  if (metadata?.type === 'pdf') {
    score += 2;
    if (metadata.pages && metadata.pages > 5) score += 2;
  }
  
  return Math.min(score, 10);
}

/**
 * Select appropriate model based on document complexity
 */
function selectModel(documentType: string, complexity: number): string {
  if (documentType === 'pdf' && complexity > 8) {
    return MODEL_TIERS.specialized[0]; // llamaparse
  } else if (complexity > 5) {
    return MODEL_TIERS.balanced[0]; // claude-3.5-sonnet
  } else {
    return MODEL_TIERS.fast[0]; // gemini-flash-1.5
  }
}

/**
 * Calculate cost for AI service usage
 */
function calculateCost(model: string, usage: Usage): CostTracking {
  let cost_usd = 0;
  
  if (model === 'gpt-4' || model === 'openai/gpt-4o') {
    // GPT-4 pricing: $0.03/1K prompt tokens, $0.06/1K completion tokens
    cost_usd = ((usage.prompt_tokens ?? 0) / 1000) * 0.03 + ((usage.completion_tokens ?? 0) / 1000) * 0.06;
  } else if (model === 'anthropic/claude-3.5-sonnet') {
    // Claude pricing: $0.003/1K input tokens, $0.015/1K output tokens
    cost_usd = ((usage.input_tokens ?? 0) / 1000) * 0.003 + ((usage.output_tokens ?? 0) / 1000) * 0.015;
  } else if (model === 'google/gemini-flash-1.5') {
    // Gemini Flash pricing: $0.075/1M tokens
    const totalTokens = (usage.prompt_tokens ?? usage.input_tokens ?? 0) + (usage.completion_tokens ?? usage.output_tokens ?? 0);
    cost_usd = (totalTokens / 1000000) * 0.075;
  } else if (model === 'llamaparse') {
    // LlamaParse pricing: $0.003/page
    cost_usd = (usage.pages || 1) * 0.003;
  }

  const markup = 1.5; // 50% markup for 60%+ profit margin
  const customer_price_usd = cost_usd * markup;
  const profit_margin_percent = ((customer_price_usd - cost_usd) / customer_price_usd) * 100;

  return {
    model,
    input_tokens: usage.prompt_tokens || usage.input_tokens || 0,
    output_tokens: usage.completion_tokens || usage.output_tokens || 0,
    cost_usd: Math.round(cost_usd * 10000) / 10000, // Round to 4 decimal places
    customer_price_usd: Math.round(customer_price_usd * 10000) / 10000,
    profit_margin_percent: Math.round(profit_margin_percent * 100) / 100,
    timestamp: new Date()
  };
}

/**
 * Process document with OpenAI
 */
async function processWithOpenAI(document: string, prompt: string): Promise<ProcessingResult> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENAI_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: document }
      ],
      temperature: 0.1,
      response_format: { type: "json_object" }
    })
  });

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
  }

  const _result = await response.json();
  const extractedData = JSON.parse(result.choices[0].message.content);

  return {
    success: true,
    model: 'gpt-4',
    extracted_data: extractedData.extracted_data,
    confidence: extractedData.confidence,
    usage: result.usage
  };
}

/**
 * Process document with Claude
 */
async function processWithClaude(document: string, prompt: string): Promise<ProcessingResult> {
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'x-api-key': CLAUDE_API_KEY,
      'anthropic-version': '2023-06-01',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 1000,
      messages: [{
        role: 'user',
        content: `${prompt}\n\nDocument: ${document}`
      }]
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Claude API error: ${response.status} - ${errorData.error?.message || response.statusText}`);
  }

  const _result = await response.json();
  const extractedData = JSON.parse(result.content[0].text);

  return {
    success: true,
    model: 'claude-3-sonnet',
    extracted_data: extractedData.extracted_data,
    confidence: extractedData.confidence,
    usage: result.usage
  };
}

/**
 * Process document with LlamaParse
 */
async function processWithLlamaParse(pdfFile: File): Promise<ProcessingResult> {
  // Upload PDF
  const formData = new FormData();
  formData.append('file', pdfFile);
  formData.append('result_type', 'json');

  const uploadResponse = await fetch('https://api.cloud.llamaindex.ai/api/parsing/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${LLAMAPARSE_API_KEY}`
    },
    body: formData
  });

  if (!uploadResponse.ok) {
    const error = await uploadResponse.json();
    throw new Error(`LlamaParse error: ${uploadResponse.status} - ${error.error}`);
  }

  const { job_id } = await uploadResponse.json();

  // Poll for results (simplified for demo - should implement proper polling)
  const resultResponse = await fetch(`https://api.cloud.llamaindex.ai/api/parsing/job/${job_id}/result/json`, {
    headers: {
      'Authorization': `Bearer ${LLAMAPARSE_API_KEY}`
    }
  });

  const _result = await resultResponse.json();

  return {
    success: true,
    model: 'llamaparse',
    extracted_data: result.result.extracted_data,
    confidence: 0.8, // Default confidence for LlamaParse
    usage: { pages: 1 } // Simplified usage tracking
  };
}



/**
 * Standardize response format across all models
 */
function standardizeResponse(
  rawResponse: ProcessingResult,
  model: string,
  startTime: number,
  costData: CostTracking
): StandardizedResponse {
  return {
    success: rawResponse.success,
    extracted_data: rawResponse.extracted_data || {},
    confidence: rawResponse.confidence || 0.5,
    model_used: model,
    processing_time_ms: Date.now() - startTime,
    cost_breakdown: {
      model_cost_usd: costData.cost_usd,
      customer_price_usd: costData.customer_price_usd,
      profit_margin_percent: costData.profit_margin_percent
    }
  };
}

/**
 * Standardize error response
 */
function standardizeErrorResponse(
  error: Error,
  model: string,
  startTime: number
): StandardizedResponse {
  return {
    success: false,
    extracted_data: {},
    confidence: 0,
    model_used: model,
    processing_time_ms: Date.now() - startTime,
    cost_breakdown: {
      model_cost_usd: 0,
      customer_price_usd: 0,
      profit_margin_percent: 0
    },
    error: error.message
  };
}

/**
 * Log AI service request
 */
async function logAIRequest(data: LogData): Promise<void> {
  const logEntry = {
    type: 'ai_request',
    timestamp: new Date().toISOString(),
    ...data
  };
  
  console.log(JSON.stringify(logEntry));
  
  // Store in database for audit trail
  await supabase.from('usage_logs').insert({
    customer_id: data.customer_id,
    api_key_id: data.api_key_id,
    model_used: data.model,
    model_cost: data.cost_usd || 0,
    customer_price: data.customer_price_usd || 0,
    profit_margin: data.profit_margin_percent || 0,
    processing_time_ms: data.processing_time_ms || 0,
    request_id: data.request_id,
    metadata: {
      document_length: data.document_length,
      complexity_score: data.complexity_score
    }
  });
}

/**
 * Alert admins for low profit margins
 */
function alertAdmins(message: string): void {
  console.warn(`ADMIN ALERT: ${message}`);
  // In production, this would send to monitoring service
}

/**
 * Track cost and check profit margins
 */
async function trackCostWithMarginCheck(usage: CostTracking, requestData: RequestData): Promise<void> {
  if (usage.profit_margin_percent < 60) {
    alertAdmins(`Low profit margin: ${usage.profit_margin_percent.toFixed(2)}% for ${usage.model}`);
  }

  await logAIRequest({
    model: usage.model,
    cost_usd: usage.cost_usd,
    customer_price_usd: usage.customer_price_usd,
    profit_margin_percent: usage.profit_margin_percent,
    customer_id: requestData.customer_id,
    api_key_id: requestData.api_key_id,
    request_id: requestData.request_id,
    document_length: requestData.document_length,
    complexity_score: requestData.complexity_score,
    processing_time_ms: requestData.processing_time_ms
  });
}

/**
 * Main AI processing function with fallback logic
 */
async function processDocument(payload: RequestPayload): Promise<StandardizedResponse> {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  // Calculate complexity
  const complexity = payload.complexity_override || calculateComplexity(payload.document, {
    type: payload.document_type as 'pdf' | 'text' | 'image' | undefined
  });
  
  // Select model
  const selectedModel = selectModel(payload.document_type || 'text', complexity);
  
  try {
    let result: ProcessingResult;
    let usage: Usage;

    // Process with selected model
    if (selectedModel === 'llamaparse') {
      // Create a mock File object from document string for LlamaParse
      const blob = new Blob([payload.document], { type: 'application/pdf' });
      const file = new File([blob], 'document.pdf', { type: 'application/pdf' });
      result = await processWithLlamaParse(file);
      usage = result.usage;
    } else if (selectedModel.includes('claude')) {
      result = await processWithClaude(payload.document, payload.agent_prompt || 'Extract structured data from this document as JSON');
      usage = result.usage;
    } else {
      // Default to OpenAI/GPT-4
      result = await processWithOpenAI(payload.document, payload.agent_prompt || 'Extract structured data from this document as JSON');
      usage = result.usage;
    }

    // Calculate costs
    const costData = calculateCost(selectedModel, usage);
    
    // Track costs and check margins
    await trackCostWithMarginCheck(costData, {
      customer_id: payload.customer_id,
      api_key_id: payload.api_key_id,
      request_id: requestId,
      document_length: payload.document.length,
      complexity_score: complexity,
      processing_time_ms: Date.now() - startTime
    });

    // Return standardized response
    return standardizeResponse(result, selectedModel, startTime, costData);

  } catch {
    // Log error
    await logAIRequest({
      model: selectedModel,
      error: error instanceof Error ? error.message : 'Unknown error',
      customer_id: payload.customer_id,
      api_key_id: payload.api_key_id,
      request_id: requestId,
      document_length: payload.document.length,
      complexity_score: complexity,
      processing_time_ms: Date.now() - startTime
    });

    return standardizeErrorResponse(
      error instanceof Error ? error : new Error('Unknown error'),
      selectedModel,
      startTime
    );
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  try {
    // Validate method
    if (req.method !== 'POST') {
      return new Response(
        JSON.stringify({ error: 'Method not allowed' }),
        {
          status: 405,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Parse request payload
    const payload = await req.json() as RequestPayload;

    // Validate required fields
    if (!payload.document || !payload.customer_id || !payload.api_key_id) {
      return new Response(
        JSON.stringify({ 
          error: 'Missing required fields: document, customer_id, api_key_id' 
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Process document
    const _result = await processDocument(payload);

    // Return response
    return new Response(
      JSON.stringify(result),
      {
        status: result.success ? 200 : 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );

  } catch {
    console.error('Edge Function error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});