/**
 * AI Service Maintenance Controller
 * Provides manual override capabilities for circuit breakers and service management
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
import { CircuitBreakerFactory } from '../utils/circuit-breaker.ts';
import { getHealthMonitor } from '../utils/service-health-monitor.ts';
import { getMetricsLogger } from '../utils/metrics-logger.ts';

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const ADMIN_API_KEY = Deno.env.get('ADMIN_API_KEY') || 'admin-secret-key';

// Initialize Supabase client
const _supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Admin-Key',
};

/**
 * Authentication middleware for admin operations
 */
function authenticateAdmin(req: Request): boolean {
  const adminKey = req.headers.get('X-Admin-Key') || req.headers.get('Authorization')?.replace('Bearer ', '');
  return adminKey === ADMIN_API_KEY;
}

/**
 * Circuit Breaker Management Operations
 */
interface CircuitBreakerOperation {
  action: 'open' | 'close' | 'reset' | 'status';
  service: string;
  reason?: string;
  operator?: string;
}

async function handleCircuitBreakerOperation(operation: CircuitBreakerOperation): Promise<any> {
  const circuitBreaker = CircuitBreakerFactory.getCircuitBreaker(operation.service);
  const metricsLogger = getMetricsLogger();

  if (!circuitBreaker) {
    throw new Error(`Circuit breaker not found for service: ${operation.service}`);
  }

  const beforeState = circuitBreaker.getState();

  switch (operation.action) {
    case 'open': {
      }
      circuitBreaker.forceOpen();
      metricsLogger?.logOperationalEvent({
        timestamp: new Date(),
        event_type: 'circuit_breaker_manual_open',
        description: `Circuit breaker manually opened for ${operation.service}`,
        operator: operation.operator,
        service: operation.service,
        before_state: beforeState,
        after_state: circuitBreaker.getState(),
        metadata: { reason: operation.reason }
      });
      break;
    }

    case 'close': {
      }
      circuitBreaker.forceClose();
      metricsLogger?.logOperationalEvent({
        timestamp: new Date(),
        event_type: 'circuit_breaker_manual_close',
        description: `Circuit breaker manually closed for ${operation.service}`,
        operator: operation.operator,
        service: operation.service,
        before_state: beforeState,
        after_state: circuitBreaker.getState(),
        metadata: { reason: operation.reason }
      });
      break;
    }

    case 'reset': {
      }
      circuitBreaker.reset();
      metricsLogger?.logOperationalEvent({
        timestamp: new Date(),
        event_type: 'circuit_breaker_reset',
        description: `Circuit breaker reset for ${operation.service}`,
        operator: operation.operator,
        service: operation.service,
        before_state: beforeState,
        after_state: circuitBreaker.getState(),
        metadata: { reason: operation.reason }
      });
      break;
    }

    case 'status': {
      }
      // Just return status, no state change
      break;
    }

    default:
      throw new Error(`Unknown circuit breaker action: ${operation.action}`);
  }

  return {
    service: operation.service,
    action: operation.action,
    before_state: beforeState,
    after_state: circuitBreaker.getState(),
    status_summary: circuitBreaker.getStatusSummary(),
    metrics: circuitBreaker.getMetrics(),
    timestamp: new Date().toISOString()
  };
}

/**
 * Service Health Management Operations
 */
interface HealthOperation {
  action: 'check' | 'status' | 'start_monitoring' | 'stop_monitoring' | 'update_config';
  service?: string;
  config?: any;
}

async function handleHealthOperation(operation: HealthOperation): Promise<any> {
  const healthMonitor = getHealthMonitor();

  if (!healthMonitor) {
    throw new Error('Health monitor not initialized');
  }

  switch (operation.action) {
    case 'check': {
      }
      if (operation.service) {
        const health = await healthMonitor.checkServiceHealth(operation.service);
        return { service: operation.service, health };
      } else {
        const allHealth = healthMonitor.getAllServiceHealth();
        return { all_services: Object.fromEntries(allHealth) };
      }

    case 'status': {
      }
      return {
        monitoring_active: healthMonitor.isActive(),
        availability_summary: healthMonitor.getAvailabilitySummary(),
        detailed_report: healthMonitor.getDetailedHealthReport()
      };

    case 'start_monitoring': {
      }
      healthMonitor.startMonitoring();
      return { message: 'Health monitoring started', active: true };

    case 'stop_monitoring': {
      }
      healthMonitor.stopMonitoring();
      return { message: 'Health monitoring stopped', active: false };

    case 'update_config': {
      }
      if (operation.config) {
        healthMonitor.updateConfig(operation.config);
        return { 
          message: 'Health monitor configuration updated', 
          new_config: healthMonitor.getConfig() 
        };
      } else {
        throw new Error('Config required for update_config action');
      }

    default:
      throw new Error(`Unknown health operation action: ${operation.action}`);
  }
}

/**
 * System Status and Metrics Operations
 */
interface SystemOperation {
  action: 'status' | 'metrics' | 'alerts' | 'reset_all' | 'emergency_stop';
  time_range?: { start: string; end: string };
}

async function handleSystemOperation(operation: SystemOperation): Promise<any> {
  const metricsLogger = getMetricsLogger();

  switch (operation.action) {
    case 'status': { {
    const circuitBreakers = CircuitBreakerFactory.getAllCircuitBreakers();
      const healthMonitor = getHealthMonitor();
      
      return {
        timestamp: new Date().toISOString(),
        circuit_breakers: Array.from(circuitBreakers.values()).map(cb => cb.getStatusSummary()),
        health_status: healthMonitor?.getAvailabilitySummary(),
        metrics_buffer: metricsLogger?.getBufferStatus(),
        system_info: {
          total_services: circuitBreakers.size,
          healthy_services: Array.from(circuitBreakers.values()).filter(cb => cb.isHealthy()).length,
          open_circuits: Array.from(circuitBreakers.values()).filter(cb => cb.getState().state === 'open').length
        }
      };
    }

    case 'metrics': { {
      if (operation.time_range && metricsLogger) {
        const start = new Date(operation.time_range.start);
        const end = new Date(operation.time_range.end);
        return await metricsLogger.getMetricsSummary({ start, end });
      } else {
        return { error: 'Time range required for metrics operation' };
      }
    }

    case 'alerts': { {
      // In a real implementation, this would query recent alerts
      return {
        message: 'Alert history not implemented in this demo',
        recent_alerts: []
      };
    }

    case 'reset_all': { {
      CircuitBreakerFactory.resetAll();
      metricsLogger?.logOperationalEvent({
        timestamp: new Date(),
        event_type: 'system_reset_all',
        description: 'All circuit breakers reset via admin command',
        metadata: { action: 'reset_all' }
      });
      return { message: 'All circuit breakers reset', timestamp: new Date().toISOString() };
    }

    case 'emergency_stop': { {
      // Open all circuit breakers to stop all AI processing
      const allCircuitBreakers = CircuitBreakerFactory.getAllCircuitBreakers();
      allCircuitBreakers.forEach((cb, _service) => {
        cb.forceOpen();
      });
      
      metricsLogger?.logOperationalEvent({
        timestamp: new Date(),
        event_type: 'emergency_stop',
        description: 'Emergency stop activated - all services disabled',
        metadata: { 
          action: 'emergency_stop',
          affected_services: Array.from(allCircuitBreakers.keys())
        }
      });

      // Log critical alert
      metricsLogger?.logAlert({
        timestamp: new Date(),
        severity: 'critical',
        category: 'service_health',
        title: 'Emergency Stop Activated',
        description: 'All AI services have been disabled via emergency stop',
        metadata: {
          trigger: 'admin_command',
          affected_services: Array.from(allCircuitBreakers.keys())
        }
      });

      return { 
        message: 'Emergency stop activated - all AI services disabled', 
        affected_services: Array.from(allCircuitBreakers.keys()),
        timestamp: new Date().toISOString()
      };
    }

    default:
      throw new Error(`Unknown system operation action: ${operation.action}`);
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  try {
    // Authenticate admin request
    if (!authenticateAdmin(req)) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized - Invalid admin key' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const url = new URL(req.url);
    const path = url.pathname.split('/').filter(p => p);

    // Route handling
    if (req.method === 'GET' && path.includes('status')) {
      // GET /status - System overview
      const _result = await handleSystemOperation({ action: 'status' });
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      });
    }

    if (req.method === 'POST') {
      const body = await req.json();

      if (path.includes('circuit-breaker')) {
        // POST /circuit-breaker - Circuit breaker operations
        const _result = await handleCircuitBreakerOperation(body);
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });
      }

      if (path.includes('health')) {
        // POST /health - Health monitoring operations
        const _result = await handleHealthOperation(body);
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });
      }

      if (path.includes('system')) {
        // POST /system - System-wide operations
        const _result = await handleSystemOperation(body);
        return new Response(JSON.stringify(result), {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        });
      }
    }

    // Route not found
    return new Response(
      JSON.stringify({ 
        error: 'Route not found',
        available_endpoints: {
          'GET /status': 'Get system status overview',
          'POST /circuit-breaker': 'Circuit breaker operations (open, close, reset, status)',
          'POST /health': 'Health monitoring operations (check, status, start_monitoring, stop_monitoring)',
          'POST /system': 'System operations (status, metrics, alerts, reset_all, emergency_stop)'
        }
      }),
      {
        status: 404,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch {
    console.error('Admin controller error:', error);
    
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Internal server error',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});

// Export types for testing
export type { CircuitBreakerOperation, HealthOperation, SystemOperation };