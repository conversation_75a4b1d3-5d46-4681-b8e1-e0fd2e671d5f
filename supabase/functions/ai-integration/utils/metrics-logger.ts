/**
 * Comprehensive Metrics Logging and Collection System
 * Tracks circuit breaker events, fallback usage, and service performance
 */

import { createClient } from "jsr:@supabase/supabase-js@2.57.4";

export interface CircuitBreakerEvent {
  timestamp: Date;
  service: string;
  event_type: 'opened' | 'closed' | 'half_open' | 'manual_override';
  failure_count: number;
  success_count: number;
  next_attempt?: Date;
  reason?: string;
  correlation_id?: string;
}

export interface FallbackEvent {
  timestamp: Date;
  request_id: string;
  customer_id: string;
  api_key_id: string;
  primary_service: string;
  fallback_service: string;
  fallback_reason: string;
  cost_impact_percent: number;
  processing_time_ms: number;
  success: boolean;
  error?: string;
}

export interface ServicePerformanceMetric {
  timestamp: Date;
  service: string;
  operation_type: 'health_check' | 'document_processing';
  latency_ms: number;
  success: boolean;
  error_type?: string;
  request_size_bytes?: number;
  response_size_bytes?: number;
  tokens_used?: number;
  cost_usd?: number;
}

export interface SystemHealthSnapshot {
  timestamp: Date;
  overall_status: 'healthy' | 'degraded' | 'critical';
  service_availability: Record<string, 'healthy' | 'degraded' | 'unhealthy'>;
  circuit_breaker_states: Record<string, 'closed' | 'open' | 'half_open'>;
  active_fallbacks: number;
  average_response_time_ms: number;
  success_rate_percent: number;
  cost_efficiency_score: number;
}

export interface AlertEvent {
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'critical';
  category: 'service_health' | 'circuit_breaker' | 'cost_optimization' | 'performance';
  title: string;
  description: string;
  service?: string;
  customer_id?: string;
  metadata?: Record<string, any>;
  auto_resolved?: boolean;
  resolution_time?: Date;
}

/**
 * Metrics Logger Implementation
 */
export class MetricsLogger {
  private supabase: ReturnType<typeof createClient> | null = null;
  private logBuffer: Array<{
    table: string;
    data: any;
  }> = [];
  private flushInterval: number | null = null;
  private isEnabled: boolean;

  constructor(options: {
    supabaseUrl?: string;
    supabaseKey?: string;
    enableDatabaseLogging?: boolean;
    flushIntervalMs?: number;
  } = {}) {
    this.isEnabled = options.enableDatabaseLogging ?? true;

    if (this.isEnabled && options.supabaseUrl && options.supabaseKey) {
      this.supabase = createClient(options.supabaseUrl, options.supabaseKey);
    }

    // Set up periodic flush to database
    const flushInterval = options.flushIntervalMs || 10000; // 10 seconds
    this.flushInterval = setInterval(() => {
      this.flushBuffer();
    }, flushInterval);
  }

  /**
   * Log circuit breaker events
   */
  logCircuitBreakerEvent(event: CircuitBreakerEvent): void {
    const logEntry = {
      timestamp: event.timestamp.toISOString(),
      service: event.service,
      event_type: event.event_type,
      failure_count: event.failure_count,
      success_count: event.success_count,
      next_attempt: event.next_attempt?.toISOString(),
      reason: event.reason,
      correlation_id: event.correlation_id || this.generateCorrelationId(),
      metadata: {
        event_source: 'circuit_breaker'
      }
    };

    console.log(`🔄 Circuit Breaker [${event.service}]: ${event.event_type.toUpperCase()} - failures: ${event.failure_count}, successes: ${event.success_count}`);
    
    this.bufferLog('circuit_breaker_events', logEntry);
  }

  /**
   * Log fallback events
   */
  logFallbackEvent(event: FallbackEvent): void {
    const logEntry = {
      timestamp: event.timestamp.toISOString(),
      request_id: event.request_id,
      customer_id: event.customer_id,
      api_key_id: event.api_key_id,
      primary_service: event.primary_service,
      fallback_service: event.fallback_service,
      fallback_reason: event.fallback_reason,
      cost_impact_percent: event.cost_impact_percent,
      processing_time_ms: event.processing_time_ms,
      success: event.success,
      error: event.error,
      metadata: {
        event_source: 'fallback_system'
      }
    };

    const costImpactSign = event.cost_impact_percent > 0 ? '+' : '';
    console.log(`🔀 Fallback [${event.primary_service} → ${event.fallback_service}]: ${event.success ? '✅' : '❌'} - Cost impact: ${costImpactSign}${event.cost_impact_percent.toFixed(1)}%`);

    this.bufferLog('fallback_events', logEntry);

    // Generate alert for high cost impact
    if (event.cost_impact_percent > 50) {
      this.logAlert({
        timestamp: new Date(),
        severity: 'warning',
        category: 'cost_optimization',
        title: 'High Cost Fallback',
        description: `Fallback from ${event.primary_service} to ${event.fallback_service} increased costs by ${event.cost_impact_percent.toFixed(1)}%`,
        service: event.fallback_service,
        customer_id: event.customer_id,
        metadata: {
          cost_impact_percent: event.cost_impact_percent,
          request_id: event.request_id
        }
      });
    }
  }

  /**
   * Log service performance metrics
   */
  logServicePerformance(metric: ServicePerformanceMetric): void {
    const logEntry = {
      timestamp: metric.timestamp.toISOString(),
      service: metric.service,
      operation_type: metric.operation_type,
      latency_ms: metric.latency_ms,
      success: metric.success,
      error_type: metric.error_type,
      request_size_bytes: metric.request_size_bytes,
      response_size_bytes: metric.response_size_bytes,
      tokens_used: metric.tokens_used,
      cost_usd: metric.cost_usd,
      metadata: {
        event_source: 'service_performance'
      }
    };

    // Only log performance issues or significant events
    if (!metric.success || metric.latency_ms > 5000) {
      const status = metric.success ? '⚠️ SLOW' : '❌ FAILED';
      console.log(`📊 Performance [${metric.service}]: ${status} - ${metric.latency_ms}ms${metric.error_type ? ` (${metric.error_type})` : ''}`);
    }

    this.bufferLog('service_performance_metrics', logEntry);

    // Generate alert for performance issues
    if (metric.latency_ms > 10000) {
      this.logAlert({
        timestamp: new Date(),
        severity: 'warning',
        category: 'performance',
        title: 'Slow Service Response',
        description: `${metric.service} took ${metric.latency_ms}ms to respond`,
        service: metric.service,
        metadata: {
          latency_ms: metric.latency_ms,
          operation_type: metric.operation_type
        }
      });
    }
  }

  /**
   * Log system health snapshots
   */
  logSystemHealth(snapshot: SystemHealthSnapshot): void {
    const logEntry = {
      timestamp: snapshot.timestamp.toISOString(),
      overall_status: snapshot.overall_status,
      service_availability: snapshot.service_availability,
      circuit_breaker_states: snapshot.circuit_breaker_states,
      active_fallbacks: snapshot.active_fallbacks,
      average_response_time_ms: snapshot.average_response_time_ms,
      success_rate_percent: snapshot.success_rate_percent,
      cost_efficiency_score: snapshot.cost_efficiency_score,
      metadata: {
        event_source: 'system_health'
      }
    };

    const healthIcon = snapshot.overall_status === 'healthy' ? '💚' : 
                      snapshot.overall_status === 'degraded' ? '🟡' : '🔴';
    console.log(`${healthIcon} System Health: ${snapshot.overall_status.toUpperCase()} - Success: ${snapshot.success_rate_percent.toFixed(1)}%, Avg Response: ${snapshot.average_response_time_ms}ms`);

    this.bufferLog('system_health_snapshots', logEntry);

    // Generate alert for critical system status
    if (snapshot.overall_status === 'critical') {
      this.logAlert({
        timestamp: new Date(),
        severity: 'critical',
        category: 'service_health',
        title: 'System Critical',
        description: 'All AI services are unavailable',
        metadata: {
          service_availability: snapshot.service_availability,
          active_fallbacks: snapshot.active_fallbacks
        }
      });
    }
  }

  /**
   * Log alerts and notifications
   */
  logAlert(alert: AlertEvent): void {
    const logEntry = {
      timestamp: alert.timestamp.toISOString(),
      severity: alert.severity,
      category: alert.category,
      title: alert.title,
      description: alert.description,
      service: alert.service,
      customer_id: alert.customer_id,
      metadata: alert.metadata || {},
      auto_resolved: alert.auto_resolved || false,
      resolution_time: alert.resolution_time?.toISOString()
    };

    const severityIcon = {
      info: 'ℹ️',
      warning: '⚠️',
      error: '❌',
      critical: '🚨'
    }[alert.severity];

    console.log(`${severityIcon} Alert [${alert.category}]: ${alert.title} - ${alert.description}`);

    this.bufferLog('alerts', logEntry);

    // In production, could send to external alerting system
    if (alert.severity === 'critical') {
      this.sendCriticalAlert(alert);
    }
  }

  /**
   * Log operational events (manual overrides, config changes, etc.)
   */
  logOperationalEvent(event: {
    timestamp: Date;
    event_type: string;
    description: string;
    operator?: string;
    service?: string;
    before_state?: any;
    after_state?: any;
    metadata?: Record<string, any>;
  }): void {
    const logEntry = {
      timestamp: event.timestamp.toISOString(),
      event_type: event.event_type,
      description: event.description,
      operator: event.operator,
      service: event.service,
      before_state: event.before_state,
      after_state: event.after_state,
      metadata: {
        event_source: 'operational',
        ...event.metadata
      }
    };

    console.log(`🔧 Operation [${event.event_type}]: ${event.description}${event.service ? ` (${event.service})` : ''}`);

    this.bufferLog('operational_events', logEntry);
  }

  /**
   * Generate metrics summary for reporting
   */
  async getMetricsSummary(_timeRange: {
    start: Date;
    end: Date;
  }): Promise<{
    circuit_breaker_events: number;
    fallback_events: number;
    service_failures: number;
    average_response_time: number;
    success_rate: number;
    cost_savings_from_optimization: number;
    top_failing_services: Array<{ service: string; failure_count: number }>;
    alert_breakdown: Record<string, number>;
  }> {
    // In a real implementation, this would query the database
    // For now, return mock data structure
    return {
      circuit_breaker_events: 0,
      fallback_events: 0,
      service_failures: 0,
      average_response_time: 0,
      success_rate: 100,
      cost_savings_from_optimization: 0,
      top_failing_services: [],
      alert_breakdown: {}
    };
  }

  /**
   * Buffer log entry for batch processing
   */
  private bufferLog(table: string, data: any): void {
    this.logBuffer.push({ table, data });

    // Immediate flush for critical events
    if (data.severity === 'critical' || this.logBuffer.length > 100) {
      this.flushBuffer();
    }
  }

  /**
   * Flush buffered logs to database
   */
  private async flushBuffer(): Promise<void> {
    if (!this.isEnabled || !this.supabase || this.logBuffer.length === 0) {
      return;
    }

    const logsToFlush = [...this.logBuffer];
    this.logBuffer = [];

    try {
      // Group logs by table
      const logsByTable = logsToFlush.reduce((acc, log) => {
        if (!acc[log.table]) {
          acc[log.table] = [];
        }
        acc[log.table].push(log.data);
        return acc;
      }, {} as Record<string, any[]>);

      // Insert logs for each table
      const insertPromises = Object.entries(logsByTable).map(async ([table, logs]) => {
        try {
          const { error } = await this.supabase!.from(table).insert(logs);
          if (error) {
            console.error(`Failed to insert logs to ${table}:`, error);
          }
        } catch {
          console.error(`Error inserting to ${table}:`, error);
        }
      });

      await Promise.allSettled(insertPromises);
    } catch {
      console.error('Failed to flush metrics buffer:', error);
      // Put logs back in buffer for retry
      this.logBuffer.unshift(...logsToFlush);
    }
  }

  /**
   * Send critical alert to external systems
   */
  private async sendCriticalAlert(alert: AlertEvent): Promise<void> {
    // In production, this would integrate with:
    // - PagerDuty
    // - Slack
    // - Email notifications
    // - SMS alerts
    
    console.error(`🚨 CRITICAL ALERT: ${alert.title} - ${alert.description}`);
    
    // For demo, just log the critical alert
    const alertSummary = {
      timestamp: alert.timestamp,
      title: alert.title,
      description: alert.description,
      service: alert.service,
      customer_id: alert.customer_id,
      metadata: alert.metadata
    };
    
    console.error('Critical Alert Details:', JSON.stringify(alertSummary, null, 2));
  }

  /**
   * Generate correlation ID for tracking related events
   */
  private generateCorrelationId(): string {
    return `cb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Enable or disable logging
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log(`Metrics logging ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get current buffer status
   */
  getBufferStatus(): {
    buffer_size: number;
    is_enabled: boolean;
    database_connected: boolean;
  } {
    return {
      buffer_size: this.logBuffer.length,
      is_enabled: this.isEnabled,
      database_connected: !!this.supabase
    };
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }

    // Final flush
    this.flushBuffer();
    
    console.log('Metrics logger destroyed');
  }
}

/**
 * Global metrics logger instance
 */
let globalMetricsLogger: MetricsLogger | null = null;

/**
 * Initialize global metrics logger
 */
export function initializeMetricsLogger(options?: {
  supabaseUrl?: string;
  supabaseKey?: string;
  enableDatabaseLogging?: boolean;
  flushIntervalMs?: number;
}): MetricsLogger {
  if (globalMetricsLogger) {
    globalMetricsLogger.destroy();
  }
  
  globalMetricsLogger = new MetricsLogger(options);
  return globalMetricsLogger;
}

/**
 * Get global metrics logger
 */
export function getMetricsLogger(): MetricsLogger | null {
  return globalMetricsLogger;
}

/**
 * Convenience functions for common logging operations
 */
export function logCircuitBreakerOpened(service: string, failureCount: number, reason?: string): void {
  globalMetricsLogger?.logCircuitBreakerEvent({
    timestamp: new Date(),
    service,
    event_type: 'opened',
    failure_count: failureCount,
    success_count: 0,
    reason
  });
}

export function logCircuitBreakerClosed(service: string, successCount: number): void {
  globalMetricsLogger?.logCircuitBreakerEvent({
    timestamp: new Date(),
    service,
    event_type: 'closed',
    failure_count: 0,
    success_count: successCount
  });
}

export function logFallbackUsed(
  requestId: string,
  customerId: string,
  apiKeyId: string,
  primary: string,
  fallback: string,
  reason: string,
  costImpact: number,
  success: boolean
): void {
  globalMetricsLogger?.logFallbackEvent({
    timestamp: new Date(),
    request_id: requestId,
    customer_id: customerId,
    api_key_id: apiKeyId,
    primary_service: primary,
    fallback_service: fallback,
    fallback_reason: reason,
    cost_impact_percent: costImpact,
    processing_time_ms: 0, // Will be updated when processing completes
    success
  });
}

export function logServicePerformance(
  service: string,
  latency: number,
  success: boolean,
  errorType?: string
): void {
  globalMetricsLogger?.logServicePerformance({
    timestamp: new Date(),
    service,
    operation_type: 'document_processing',
    latency_ms: latency,
    success,
    error_type: errorType
  });
}