/**
 * Response Standardization Utilities
 * Normalizes responses across different AI models
 */

import { CostTracking } from './cost-calculator.ts';

export interface StandardizedResponse {
  success: boolean;
  extracted_data: Record<string, any>;
  confidence: number;
  model_used: string;
  processing_time_ms: number;
  cost_breakdown: {
    model_cost_usd: number;
    customer_price_usd: number;
    profit_margin_percent: number;
  };
  metadata?: {
    request_id: string;
    timestamp: string;
    complexity_score?: number;
    document_length?: number;
  };
  error?: string;
}

/**
 * Standardize successful response from any AI model
 */
export function standardizeResponse(
  rawResponse: any,
  model: string,
  startTime: number,
  costData: CostTracking,
  metadata: {
    request_id: string;
    complexity_score?: number;
    document_length?: number;
  }
): StandardizedResponse {
  return {
    success: true,
    extracted_data: rawResponse.extracted_data || rawResponse.data || {},
    confidence: rawResponse.confidence || 0.5,
    model_used: model,
    processing_time_ms: Date.now() - startTime,
    cost_breakdown: {
      model_cost_usd: costData.cost_usd,
      customer_price_usd: costData.customer_price_usd,
      profit_margin_percent: costData.profit_margin_percent
    },
    metadata: {
      request_id: metadata.request_id,
      timestamp: new Date().toISOString(),
      complexity_score: metadata.complexity_score,
      document_length: metadata.document_length
    }
  };
}

/**
 * Standardize error response
 */
export function standardizeErrorResponse(
  error: Error,
  model: string,
  startTime: number,
  metadata: {
    request_id: string;
    complexity_score?: number;
    document_length?: number;
  }
): StandardizedResponse {
  return {
    success: false,
    extracted_data: {},
    confidence: 0,
    model_used: model,
    processing_time_ms: Date.now() - startTime,
    cost_breakdown: {
      model_cost_usd: 0,
      customer_price_usd: 0,
      profit_margin_percent: 0
    },
    metadata: {
      request_id: metadata.request_id,
      timestamp: new Date().toISOString(),
      complexity_score: metadata.complexity_score,
      document_length: metadata.document_length
    },
    error: error.message
  };
}

/**
 * Parse and validate extracted data from AI response
 */
export function parseExtractedData(content: string): { extracted_data: any; confidence: number } {
  try {
    const parsed = JSON.parse(content);
    
    // Handle different response formats
    if (parsed.extracted_data && typeof parsed.confidence === 'number') {
      return {
        extracted_data: parsed.extracted_data,
        confidence: Math.min(Math.max(parsed.confidence, 0), 1) // Clamp between 0-1
      };
    } else if (parsed.data) {
      return {
        extracted_data: parsed.data,
        confidence: parsed.confidence || 0.5
      };
    } else {
      // Raw data response
      return {
        extracted_data: parsed,
        confidence: 0.5
      };
    }
  } catch {
    // If parsing fails, return raw content as text
    return {
      extracted_data: { text: content },
      confidence: 0.1 // Low confidence for unparseable responses
    };
  }
}

/**
 * Validate response structure
 */
export function validateResponse(response: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (typeof response !== 'object' || response === null) {
    errors.push('Response must be an object');
    return { valid: false, errors };
  }
  
  if (typeof response.success !== 'boolean') {
    errors.push('Response must have boolean success field');
  }
  
  if (typeof response.extracted_data !== 'object') {
    errors.push('Response must have extracted_data object');
  }
  
  if (typeof response.confidence !== 'number' || response.confidence < 0 || response.confidence > 1) {
    errors.push('Response must have confidence number between 0 and 1');
  }
  
  if (typeof response.model_used !== 'string' || response.model_used.length === 0) {
    errors.push('Response must have non-empty model_used string');
  }
  
  if (typeof response.processing_time_ms !== 'number' || response.processing_time_ms < 0) {
    errors.push('Response must have non-negative processing_time_ms number');
  }
  
  if (!response.cost_breakdown || typeof response.cost_breakdown !== 'object') {
    errors.push('Response must have cost_breakdown object');
  } else {
    const cb = response.cost_breakdown;
    if (typeof cb.model_cost_usd !== 'number' || cb.model_cost_usd < 0) {
      errors.push('cost_breakdown.model_cost_usd must be non-negative number');
    }
    if (typeof cb.customer_price_usd !== 'number' || cb.customer_price_usd < 0) {
      errors.push('cost_breakdown.customer_price_usd must be non-negative number');
    }
    if (typeof cb.profit_margin_percent !== 'number') {
      errors.push('cost_breakdown.profit_margin_percent must be a number');
    }
  }
  
  return { valid: errors.length === 0, errors };
}

/**
 * Format response for different output types
 */
export interface FormattedResponse {
  json: StandardizedResponse;
  summary: string;
  csv: string;
}

export function formatResponse(response: StandardizedResponse): FormattedResponse {
  const summary = response.success 
    ? `Successfully extracted data using ${response.model_used} with ${(response.confidence * 100).toFixed(1)}% confidence in ${response.processing_time_ms}ms. Cost: $${response.cost_breakdown.model_cost_usd} (${response.cost_breakdown.profit_margin_percent.toFixed(1)}% margin).`
    : `Failed to extract data using ${response.model_used}: ${response.error}`;
    
  const csvRow = [
    response.metadata?.timestamp || '',
    response.success ? 'SUCCESS' : 'FAILURE',
    response.model_used,
    response.confidence.toFixed(3),
    response.processing_time_ms.toString(),
    response.cost_breakdown.model_cost_usd.toFixed(4),
    response.cost_breakdown.customer_price_usd.toFixed(4),
    response.cost_breakdown.profit_margin_percent.toFixed(2),
    response.error || ''
  ].join(',');
  
  return {
    json: response,
    summary,
    csv: csvRow
  };
}

/**
 * Create response templates for different document types
 */
export function getResponseTemplate(documentType: string): any {
  const templates: Record<string, any> = {
    invoice: {
      invoice_number: null,
      date: null,
      vendor: null,
      total: null,
      line_items: [],
      tax_amount: null,
      currency: 'USD'
    },
    contract: {
      contract_type: null,
      parties: [],
      effective_date: null,
      expiration_date: null,
      key_terms: [],
      value: null
    },
    receipt: {
      merchant: null,
      date: null,
      total: null,
      items: [],
      payment_method: null
    },
    insurance: {
      policy_number: null,
      policyholder: null,
      coverage_type: null,
      premium: null,
      deductible: null,
      effective_date: null,
      expiration_date: null
    },
    default: {
      document_type: null,
      key_information: {},
      entities: [],
      dates: [],
      amounts: []
    }
  };
  
  return templates[documentType] || templates.default;
}