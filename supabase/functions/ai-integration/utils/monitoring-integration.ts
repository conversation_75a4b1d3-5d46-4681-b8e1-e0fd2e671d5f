/**
 * Production Monitoring Integration for Circuit Breaker System
 * Integrates circuit breaker events with external monitoring services
 */

export interface MonitoringAlert {
  type: 'circuit_breaker_open' | 'circuit_breaker_close' | 'service_degraded' | 'fallback_used';
  service: string;
  severity: 'low' | 'medium' | 'high';
  metadata: any;
  timestamp: Date;
}

/**
 * Send monitoring alerts to external systems
 */
export async function sendMonitoringAlert(alert: MonitoringAlert): Promise<void> {
  const alertPayload = {
    type: alert.type,
    service: alert.service,
    severity: alert.severity,
    timestamp: alert.timestamp.toISOString(),
    metadata: alert.metadata
  };

  // Console logging for all environments
  const severityEmoji = { low: '🟢', medium: '🟡', high: '🔴' };
  console.log(`${severityEmoji[alert.severity]} MONITORING ALERT [${alert.type}]: ${alert.service} - ${JSON.stringify(alert.metadata)}`);

  // Integration with external monitoring services
  try {
    // Send to webhook endpoint if configured  
    const webhookUrl = (typeof Deno !== 'undefined') ? Deno.env.get('MONITORING_WEBHOOK_URL') : process.env.MONITORING_WEBHOOK_URL;
    if (webhookUrl) {
      await sendToWebhook(webhookUrl, alertPayload);
    }

    // Send to Supabase monitoring table if configured
    const supabaseUrl = (typeof Deno !== 'undefined') ? Deno.env.get('SUPABASE_URL') : process.env.SUPABASE_URL;
    const supabaseKey = (typeof Deno !== 'undefined') ? Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') : process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (supabaseUrl && supabaseKey) {
      await sendToSupabaseMonitoring(supabaseUrl, supabaseKey, alertPayload);
    }

    // Send to external services (DataDog, New Relic, etc.)
    await sendToExternalServices(alertPayload);

  } catch {
    console.error('Failed to send monitoring alert:', error);
    // Don't throw - monitoring failures shouldn't break the circuit breaker
  }
}

/**
 * Send alert to webhook endpoint
 */
async function sendToWebhook(url: string, payload: any): Promise<void> {
  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'IDP-Platform-CircuitBreaker/1.0'
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      console.warn(`Webhook alert failed: ${response.status} ${response.statusText}`);
    }
  } catch {
    console.warn('Webhook alert failed:', error);
  }
}

/**
 * Send alert to Supabase monitoring table
 */
async function sendToSupabaseMonitoring(supabaseUrl: string, supabaseKey: string, payload: any): Promise<void> {
  try {
    const response = await fetch(`${supabaseUrl}/rest/v1/monitoring_alerts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseKey}`,
        'apikey': supabaseKey,
        'Prefer': 'return=minimal'
      },
      body: JSON.stringify({
        alert_type: payload.type,
        service: payload.service,
        severity: payload.severity,
        metadata: payload.metadata,
        timestamp: payload.timestamp,
        created_at: new Date().toISOString()
      })
    });

    if (!response.ok) {
      console.warn(`Supabase monitoring alert failed: ${response.status}`);
    }
  } catch {
    console.warn('Supabase monitoring alert failed:', error);
  }
}

/**
 * Send alerts to external monitoring services
 */
async function sendToExternalServices(payload: any): Promise<void> {
  // DataDog integration
  const dataDogApiKey = (typeof Deno !== 'undefined') ? Deno.env.get('DATADOG_API_KEY') : process.env.DATADOG_API_KEY;
  if (dataDogApiKey) {
    await sendToDataDog(dataDogApiKey, payload);
  }

  // New Relic integration
  const newRelicApiKey = (typeof Deno !== 'undefined') ? Deno.env.get('NEW_RELIC_API_KEY') : process.env.NEW_RELIC_API_KEY;
  if (newRelicApiKey) {
    await sendToNewRelic(newRelicApiKey, payload);
  }

  // Slack integration
  const slackWebhook = (typeof Deno !== 'undefined') ? Deno.env.get('SLACK_WEBHOOK_URL') : process.env.SLACK_WEBHOOK_URL;
  if (slackWebhook && payload.severity === 'high') {
    await sendToSlack(slackWebhook, payload);
  }
}

/**
 * Send to DataDog
 */
async function sendToDataDog(apiKey: string, payload: any): Promise<void> {
  try {
    const response = await fetch('https://api.datadoghq.com/api/v1/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'DD-API-KEY': apiKey
      },
      body: JSON.stringify({
        title: `Circuit Breaker: ${payload.type}`,
        text: `Service: ${payload.service}\nSeverity: ${payload.severity}\nMetadata: ${JSON.stringify(payload.metadata)}`,
        tags: [`service:${payload.service}`, `severity:${payload.severity}`, 'system:circuit-breaker'],
        alert_type: payload.severity === 'high' ? 'error' : 'info'
      })
    });

    if (!response.ok) {
      console.warn(`DataDog alert failed: ${response.status}`);
    }
  } catch {
    console.warn('DataDog alert failed:', error);
  }
}

/**
 * Send to New Relic
 */
async function sendToNewRelic(apiKey: string, payload: any): Promise<void> {
  try {
    const response = await fetch('https://insights-collector.newrelic.com/v1/accounts/YOUR_ACCOUNT_ID/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Insert-Key': apiKey
      },
      body: JSON.stringify({
        eventType: 'CircuitBreakerEvent',
        alertType: payload.type,
        service: payload.service,
        severity: payload.severity,
        metadata: JSON.stringify(payload.metadata),
        timestamp: Date.now()
      })
    });

    if (!response.ok) {
      console.warn(`New Relic alert failed: ${response.status}`);
    }
  } catch {
    console.warn('New Relic alert failed:', error);
  }
}

/**
 * Send to Slack for high severity alerts
 */
async function sendToSlack(webhookUrl: string, payload: any): Promise<void> {
  try {
    const severityColor: Record<string, string> = { low: '#36a64f', medium: '#ff9500', high: '#ff0000' };
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        text: `🚨 Circuit Breaker Alert: ${payload.type}`,
        attachments: [{
          color: severityColor[payload.severity as keyof typeof severityColor],
          fields: [
            { title: 'Service', value: payload.service, short: true },
            { title: 'Severity', value: payload.severity.toUpperCase(), short: true },
            { title: 'Type', value: payload.type, short: false },
            { title: 'Details', value: JSON.stringify(payload.metadata, null, 2), short: false }
          ],
          timestamp: Math.floor(Date.now() / 1000)
        }]
      })
    });

    if (!response.ok) {
      console.warn(`Slack alert failed: ${response.status}`);
    }
  } catch {
    console.warn('Slack alert failed:', error);
  }
}

/**
 * Create monitoring alert for circuit breaker events
 */
export function createCircuitBreakerAlert(
  service: string, 
  eventType: 'open' | 'close', 
  metadata: any
): MonitoringAlert {
  return {
    type: eventType === 'open' ? 'circuit_breaker_open' : 'circuit_breaker_close',
    service,
    severity: eventType === 'open' ? 'high' : 'low',
    metadata,
    timestamp: new Date()
  };
}

/**
 * Create monitoring alert for service degradation
 */
export function createServiceDegradationAlert(
  service: string, 
  health: { status: string; latency_ms: number; success_rate: number }
): MonitoringAlert {
  return {
    type: 'service_degraded',
    service,
    severity: health.status === 'unhealthy' ? 'high' : 'medium',
    metadata: {
      status: health.status,
      latency_ms: health.latency_ms,
      success_rate: health.success_rate
    },
    timestamp: new Date()
  };
}

/**
 * Create monitoring alert for fallback usage
 */
export function createFallbackAlert(
  primaryService: string,
  fallbackService: string,
  reason: string,
  costImpact: number
): MonitoringAlert {
  return {
    type: 'fallback_used',
    service: fallbackService,
    severity: costImpact > 50 ? 'high' : costImpact > 20 ? 'medium' : 'low',
    metadata: {
      primary_service: primaryService,
      fallback_service: fallbackService,
      reason,
      cost_impact_percent: costImpact
    },
    timestamp: new Date()
  };
}