/**
 * Model Selection Utilities
 * Implements intelligent model routing for cost optimization and quality
 */

export interface ModelTiers {
  fast: string[];
  balanced: string[];
  specialized: string[];
}

export const MODEL_TIERS: ModelTiers = {
  fast: [
    'google/gemini-flash-1.5',     // $0.075/1M tokens - cost optimized
    'openai/gpt-4o-mini'           // $0.15/1M tokens - balanced
  ],
  balanced: [
    'anthropic/claude-3.5-sonnet', // $3/1M tokens - high quality
    'openai/gpt-4o'                // $2.50/1M tokens - reliable
  ],
  specialized: [
    'llamaparse'                   // $0.003/page - complex PDFs
  ]
};

/**
 * Calculate document complexity score (0-10)
 */
export function calculateComplexity(document: string, metadata?: any): number {
  let score = 0;
  
  // Length factor (max 3 points)
  score += Math.min(document.length / 1000, 3);
  
  // Structure complexity
  const hasTable = /\||\t|table/i.test(document);
  const hasNumbers = /\d+[.,]\d+|\$\d+/g.test(document);
  const hasMultiColumn = document.split('\n').some(line => line.split(/\s{3,}/).length > 2);
  
  if (hasTable) score += 2;
  if (hasNumbers) score += 1;
  if (hasMultiColumn) score += 2;
  
  // PDF-specific complexity
  if (metadata?.type === 'pdf') {
    score += 2;
    if (metadata.pages && metadata.pages > 5) score += 2;
  }
  
  return Math.min(score, 10);
}

/**
 * Select appropriate model based on document complexity and type
 */
export function selectModel(documentType: string, complexity: number): string {
  if (documentType === 'pdf' && complexity > 8) {
    return MODEL_TIERS.specialized[0]; // llamaparse for complex PDFs
  } else if (complexity > 5) {
    return MODEL_TIERS.balanced[0]; // claude for complex documents
  } else {
    return MODEL_TIERS.fast[0]; // gemini for simple documents
  }
}

/**
 * Get model pricing information
 */
export function getModelPricing(model: string): { inputPrice: number; outputPrice: number; unit: string } {
  const pricingMap: Record<string, { inputPrice: number; outputPrice: number; unit: string }> = {
    'gpt-4': { inputPrice: 0.03, outputPrice: 0.06, unit: '1K tokens' },
    'openai/gpt-4o': { inputPrice: 0.0025, outputPrice: 0.01, unit: '1K tokens' },
    'anthropic/claude-3.5-sonnet': { inputPrice: 0.003, outputPrice: 0.015, unit: '1K tokens' },
    'google/gemini-flash-1.5': { inputPrice: 0.000075, outputPrice: 0.0003, unit: '1K tokens' },
    'llamaparse': { inputPrice: 0.003, outputPrice: 0, unit: 'page' }
  };

  return pricingMap[model] || { inputPrice: 0, outputPrice: 0, unit: 'unknown' };
}