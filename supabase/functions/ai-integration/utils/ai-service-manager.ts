/**
 * AI Service Manager with Circuit Breaker and Fallback System
 * Orchestrates document processing across multiple AI services with automatic failover
 */

import { CircuitBreaker, CircuitBreakerFactory, DEFAULT_CIRCUIT_BREAKER_CONFIGS, CircuitBreakerOpenError } from './circuit-breaker.ts';
import { ServiceHealthMonitor, ServiceHealth, initializeHealthMonitor, _getHealthMonitor } from './service-health-monitor.ts';
import { processWithOpenAI, OpenAIConfig } from '../services/openai-service.ts';
import { processWithClaude, ClaudeConfig } from '../services/claude-service.ts';
import { processWithLlamaParse, LlamaParseConfig } from '../services/llamaparse-service.ts';
import { calculateCost } from './cost-calculator.ts';

export interface AIServiceConfig {
  openai?: OpenAIConfig;
  claude?: ClaudeConfig;
  llamaparse?: LlamaParseConfig;
}

export interface ProcessingRequest {
  document: string;
  prompt: string;
  customer_id: string;
  api_key_id: string;
  document_type?: string;
  file?: File; // For LlamaParse
  priority?: 'low' | 'normal' | 'high';
  max_cost_usd?: number; // Budget constraint
  preferred_model?: string; // Customer preference
  fallback_enabled?: boolean; // Allow fallbacks
}

export interface ProcessingResult {
  success: boolean;
  extracted_data: any;
  confidence: number;
  model_used: string;
  processing_time_ms: number;
  cost_breakdown: {
    model_cost_usd: number;
    customer_price_usd: number;
    profit_margin_percent: number;
  };
  fallback_chain?: string[]; // Services attempted
  circuit_breaker_events?: string[]; // CB events during processing
  service_health_snapshot?: Map<string, ServiceHealth>;
  error?: string;
  retry_count?: number;
}

export interface FallbackDecision {
  service: string;
  reason: string;
  available: boolean;
  cost_estimate: number;
  confidence_estimate: number;
}

export interface CostOptimizationResult {
  recommended_service: string;
  cost_savings_percent: number;
  quality_tradeoff: string;
  budget_utilization: number;
}

/**
 * Fallback Strategy Types
 */
export type FallbackStrategy = 
  | 'cost_optimized'    // Prefer cheapest available service
  | 'quality_optimized' // Prefer highest quality service
  | 'speed_optimized'   // Prefer fastest service
  | 'balanced';         // Balance cost, quality, and speed

/**
 * AI Service Manager Implementation
 */
export class AIServiceManager {
  private healthMonitor: ServiceHealthMonitor;
  private serviceConfigs: AIServiceConfig;
  private fallbackStrategy: FallbackStrategy;
  private costOptimizationEnabled: boolean;
  private globalBudgetLimit: number;

  constructor(
    serviceConfigs: AIServiceConfig,
    options: {
      fallbackStrategy?: FallbackStrategy;
      costOptimizationEnabled?: boolean;
      globalBudgetLimit?: number;
      healthMonitorConfig?: any;
    } = {}
  ) {
    this.serviceConfigs = serviceConfigs;
    this.fallbackStrategy = options.fallbackStrategy || 'balanced';
    this.costOptimizationEnabled = options.costOptimizationEnabled ?? true;
    this.globalBudgetLimit = options.globalBudgetLimit || 1000; // $1000 default

    // Initialize circuit breakers for all configured services
    this.initializeCircuitBreakers();

    // Initialize health monitor
    const credentials = {
      openai: serviceConfigs.openai?.apiKey,
      claude: serviceConfigs.claude?.apiKey,
      llamaparse: serviceConfigs.llamaparse?.apiKey
    };

    this.healthMonitor = initializeHealthMonitor(credentials, options.healthMonitorConfig);
    this.healthMonitor.startMonitoring();

    console.log(`AI Service Manager initialized with ${this.fallbackStrategy} strategy`);
  }

  /**
   * Initialize circuit breakers for all configured services
   */
  private initializeCircuitBreakers(): void {
    Object.keys(this.serviceConfigs).forEach(service => {
      if (this.serviceConfigs[service as keyof AIServiceConfig]) {
        CircuitBreakerFactory.getCircuitBreaker(
          service,
          DEFAULT_CIRCUIT_BREAKER_CONFIGS[service]
        );
      }
    });
  }

  /**
   * Main document processing method with fallback system
   */
  async processDocument(request: ProcessingRequest): Promise<ProcessingResult> {
    const startTime = Date.now();
    const fallbackChain: string[] = [];
    const circuitBreakerEvents: string[] = [];
    let retryCount = 0;
    
    // Get current service health snapshot
    const healthSnapshot = this.healthMonitor.getAllServiceHealth();

    // Determine fallback order based on strategy
    const fallbackOrder = this.determineFallbackOrder(request, healthSnapshot);
    
    if (fallbackOrder.length === 0) {
      return {
        success: false,
        extracted_data: {},
        confidence: 0,
        model_used: 'none',
        processing_time_ms: Date.now() - startTime,
        cost_breakdown: { model_cost_usd: 0, customer_price_usd: 0, profit_margin_percent: 0 },
        fallback_chain: [],
        circuit_breaker_events: ['All services unavailable'],
        service_health_snapshot: healthSnapshot,
        error: 'All AI services are currently unavailable'
      };
    }

    // Attempt processing with each service in fallback order
    for (const service of fallbackOrder) {
      fallbackChain.push(service);
      
      try {
        const circuitBreaker = CircuitBreakerFactory.getCircuitBreaker(service);
        
        // Check if we should skip this service due to budget constraints
        if (request.max_cost_usd && this.costOptimizationEnabled) {
          const costEstimate = this.estimateServiceCost(service, request);
          if (costEstimate > request.max_cost_usd) {
            circuitBreakerEvents.push(`Skipped ${service} due to budget constraint (${costEstimate} > ${request.max_cost_usd})`);
            continue;
          }
        }

        // Attempt processing through circuit breaker
        const _result = await circuitBreaker.execute(async () => {
          return await this.processWithService(service, request);
        });

        // Calculate cost breakdown
        const costBreakdown = calculateCost(service, result.usage || {});

        // Log successful processing
        await this.logProcessingSuccess(service, request, costBreakdown);

        return {
          success: true,
          extracted_data: result.extracted_data,
          confidence: result.confidence,
          model_used: service,
          processing_time_ms: Date.now() - startTime,
          cost_breakdown: {
            model_cost_usd: costBreakdown.cost_usd,
            customer_price_usd: costBreakdown.customer_price_usd,
            profit_margin_percent: costBreakdown.profit_margin_percent
          },
          fallback_chain,
          circuit_breaker_events,
          service_health_snapshot: healthSnapshot,
          retry_count: retryCount
        };

      } catch {
        retryCount++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        
        if (error instanceof CircuitBreakerOpenError) {
          circuitBreakerEvents.push(`Circuit breaker open for ${service}`);
        } else {
          circuitBreakerEvents.push(`${service} failed: ${errorMessage}`);
        }

        console.warn(`Service ${service} failed (attempt ${retryCount}): ${errorMessage}`);
        
        // Log processing failure
        await this.logProcessingFailure(service, request, errorMessage);

        // Continue to next service in fallback chain
        if (service === fallbackOrder[fallbackOrder.length - 1]) {
          // Last service in chain failed
          return {
            success: false,
            extracted_data: {},
            confidence: 0,
            model_used: 'none',
            processing_time_ms: Date.now() - startTime,
            cost_breakdown: { model_cost_usd: 0, customer_price_usd: 0, profit_margin_percent: 0 },
            fallback_chain,
            circuit_breaker_events,
            service_health_snapshot: healthSnapshot,
            error: `All services in fallback chain failed. Last error: ${errorMessage}`,
            retry_count: retryCount
          };
        }
      }
    }

    // Should not reach here, but fallback for safety
    return {
      success: false,
      extracted_data: {},
      confidence: 0,
      model_used: 'none',
      processing_time_ms: Date.now() - startTime,
      cost_breakdown: { model_cost_usd: 0, customer_price_usd: 0, profit_margin_percent: 0 },
      fallback_chain,
      circuit_breaker_events,
      service_health_snapshot: healthSnapshot,
      error: 'Unexpected fallback chain completion',
      retry_count: retryCount
    };
  }

  /**
   * Determine fallback order based on strategy and current conditions
   */
  private determineFallbackOrder(
    request: ProcessingRequest, 
    healthSnapshot: Map<string, ServiceHealth>
  ): string[] {
    const availableServices = this.getAvailableServices(healthSnapshot);
    
    if (availableServices.length === 0) {
      return [];
    }

    // Apply customer preference if specified and available
    if (request.preferred_model && availableServices.includes(request.preferred_model)) {
      const preferred = request.preferred_model;
      const others = availableServices.filter(s => s !== preferred);
      return [preferred, ...this.sortServicesByStrategy(others, request)];
    }

    return this.sortServicesByStrategy(availableServices, request);
  }

  /**
   * Get available services based on health and circuit breaker status
   */
  private getAvailableServices(healthSnapshot: Map<string, ServiceHealth>): string[] {
    const configuredServices = Object.keys(this.serviceConfigs).filter(
      service => this.serviceConfigs[service as keyof AIServiceConfig]
    );

    return configuredServices.filter(service => {
      const health = healthSnapshot.get(service);
      const circuitBreaker = CircuitBreakerFactory.getCircuitBreaker(service);
      
      return health && 
             (health.status === 'healthy' || health.status === 'degraded') &&
             circuitBreaker.isHealthy();
    });
  }

  /**
   * Sort services by strategy
   */
  private sortServicesByStrategy(services: string[], request: ProcessingRequest): string[] {
    switch (this.fallbackStrategy) {
      case 'cost_optimized': {
      }
        return this.sortByCost(services);
      
      case 'quality_optimized': {
      }
        return this.sortByQuality(services, request.document_type);
      
      case 'speed_optimized': {
      }
        return this.sortBySpeed(services);
      
      case 'balanced': {
      }
      default:
        return this.sortBalanced(services, request);
    }
  }

  /**
   * Sort services by cost (cheapest first)
   */
  private sortByCost(services: string[]): string[] {
    const costOrder = { openai: 1, claude: 2, llamaparse: 3 };
    return services.sort((a, b) => (costOrder[a as keyof typeof costOrder] || 999) - (costOrder[b as keyof typeof costOrder] || 999));
  }

  /**
   * Sort services by quality (best first)
   */
  private sortByQuality(services: string[], documentType?: string): string[] {
    // For PDFs, LlamaParse might be better; for text, Claude > OpenAI > LlamaParse
    if (documentType === 'pdf') {
      const qualityOrder = { llamaparse: 1, claude: 2, openai: 3 };
      return services.sort((a, b) => (qualityOrder[a as keyof typeof qualityOrder] || 999) - (qualityOrder[b as keyof typeof qualityOrder] || 999));
    } else {
      const qualityOrder = { claude: 1, openai: 2, llamaparse: 3 };
      return services.sort((a, b) => (qualityOrder[a as keyof typeof qualityOrder] || 999) - (qualityOrder[b as keyof typeof qualityOrder] || 999));
    }
  }

  /**
   * Sort services by speed (fastest first)
   */
  private sortBySpeed(services: string[]): string[] {
    const speedOrder = { openai: 1, claude: 2, llamaparse: 3 }; // OpenAI typically fastest
    return services.sort((a, b) => (speedOrder[a as keyof typeof speedOrder] || 999) - (speedOrder[b as keyof typeof speedOrder] || 999));
  }

  /**
   * Sort services with balanced approach
   */
  private sortBalanced(services: string[], request: ProcessingRequest): string[] {
    // Start with cost optimization, but consider document type
    let order = this.sortByCost(services);
    
    // If high priority or PDF, prefer quality
    if (request.priority === 'high' || request.document_type === 'pdf') {
      order = this.sortByQuality(services, request.document_type);
    }
    
    return order;
  }

  /**
   * Estimate cost for a service
   */
  private estimateServiceCost(service: string, request: ProcessingRequest): number {
    const estimatedTokens = Math.ceil(request.document.length / 4); // Rough estimation
    
    switch (service) {
      case 'openai': {
      }
        return (estimatedTokens / 1000) * 0.03; // $0.03 per 1K tokens
      case 'claude': {
      }
        return (estimatedTokens / 1000) * 0.015; // $0.015 per 1K tokens
      case 'llamaparse': {
      }
        return 0.003; // $0.003 per page (assume 1 page)
      default:
        return 0.01; // Default estimate
    }
  }

  /**
   * Process document with specific service
   */
  private async processWithService(service: string, request: ProcessingRequest): Promise<any> {
    switch (service) {
      case 'openai': {
      }
        if (!this.serviceConfigs.openai) {
          throw new Error('OpenAI not configured');
        }
        return await processWithOpenAI(request.document, request.prompt, this.serviceConfigs.openai);

      case 'claude': {
      }
        if (!this.serviceConfigs.claude) {
          throw new Error('Claude not configured');
        }
        return await processWithClaude(request.document, request.prompt, this.serviceConfigs.claude);

      case 'llamaparse': {
      }
        if (!this.serviceConfigs.llamaparse || !request.file) {
          throw new Error('LlamaParse not configured or no file provided');
        }
        return await processWithLlamaParse(request.file, this.serviceConfigs.llamaparse);

      default:
        throw new Error(`Unknown service: ${service}`);
    }
  }

  /**
   * Log successful processing
   */
  private async logProcessingSuccess(
    service: string, 
    request: ProcessingRequest, 
    costBreakdown: any
  ): Promise<void> {
    const _logEntry = {
      type: 'processing_success',
      timestamp: new Date().toISOString(),
      service,
      customer_id: request.customer_id,
      api_key_id: request.api_key_id,
      document_type: request.document_type,
      document_length: request.document.length,
      cost_usd: costBreakdown.cost_usd,
      customer_price_usd: costBreakdown.customer_price_usd,
      profit_margin_percent: costBreakdown.profit_margin_percent,
      fallback_strategy: this.fallbackStrategy
    };

    console.log(`✅ Processing success: ${service} - $${costBreakdown.customer_price_usd} (${costBreakdown.profit_margin_percent.toFixed(1)}% margin)`);
    
    // In production, this would store to database
    // await supabase.from('usage_logs').insert(logEntry);
  }

  /**
   * Log processing failure
   */
  private async logProcessingFailure(
    service: string, 
    request: ProcessingRequest, 
    error: string
  ): Promise<void> {
    const _logEntry = {
      type: 'processing_failure',
      timestamp: new Date().toISOString(),
      service,
      customer_id: request.customer_id,
      api_key_id: request.api_key_id,
      error,
      fallback_strategy: this.fallbackStrategy
    };

    console.warn(`❌ Processing failure: ${service} - ${error}`);
    
    // In production, this would store to database
    // await supabase.from('usage_logs').insert(logEntry);
  }

  /**
   * Get current system status
   */
  getSystemStatus(): {
    overall_health: 'healthy' | 'degraded' | 'critical';
    available_services: string[];
    circuit_breaker_status: Array<ReturnType<CircuitBreaker['getStatusSummary']>>;
    service_health: Map<string, ServiceHealth>;
    fallback_strategy: FallbackStrategy;
    cost_optimization_enabled: boolean;
  } {
    const availableServices = this.getAvailableServices(this.healthMonitor.getAllServiceHealth());
    const circuitBreakers = CircuitBreakerFactory.getAllCircuitBreakers();
    const serviceHealth = this.healthMonitor.getAllServiceHealth();

    let overallHealth: 'healthy' | 'degraded' | 'critical';
    if (availableServices.length === 0) {
      overallHealth = 'critical';
    } else if (availableServices.length < Object.keys(this.serviceConfigs).length) {
      overallHealth = 'degraded';
    } else {
      overallHealth = 'healthy';
    }

    return {
      overall_health: overallHealth,
      available_services: availableServices,
      circuit_breaker_status: Array.from(circuitBreakers.values()).map(cb => cb.getStatusSummary()),
      service_health: serviceHealth,
      fallback_strategy: this.fallbackStrategy,
      cost_optimization_enabled: this.costOptimizationEnabled
    };
  }

  /**
   * Optimize cost for customer
   */
  async optimizeCost(request: ProcessingRequest): Promise<CostOptimizationResult> {
    const availableServices = this.getAvailableServices(this.healthMonitor.getAllServiceHealth());
    
    if (availableServices.length === 0) {
      throw new Error('No services available for cost optimization');
    }

    const costEstimates = availableServices.map(service => ({
      service,
      cost: this.estimateServiceCost(service, request),
      quality_score: this.getQualityScore(service, request.document_type)
    }));

    // Sort by cost
    costEstimates.sort((a, b) => a.cost - b.cost);
    
    const cheapest = costEstimates[0];
    const mostExpensive = costEstimates[costEstimates.length - 1];
    
    const savings = ((mostExpensive.cost - cheapest.cost) / mostExpensive.cost) * 100;
    
    return {
      recommended_service: cheapest.service,
      cost_savings_percent: savings,
      quality_tradeoff: this.getQualityTradeoff(cheapest.quality_score, Math.max(...costEstimates.map(c => c.quality_score))),
      budget_utilization: request.max_cost_usd ? (cheapest.cost / request.max_cost_usd) * 100 : 0
    };
  }

  /**
   * Get quality score for service
   */
  private getQualityScore(service: string, documentType?: string): number {
    const baseScores = { openai: 0.85, claude: 0.90, llamaparse: 0.80 };
    let score = baseScores[service as keyof typeof baseScores] || 0.70;
    
    // Adjust for document type
    if (documentType === 'pdf' && service === 'llamaparse') {
      score += 0.15; // LlamaParse better for PDFs
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Get quality tradeoff description
   */
  private getQualityTradeoff(recommendedQuality: number, bestQuality: number): string {
    const diff = bestQuality - recommendedQuality;
    
    if (diff < 0.05) return 'minimal';
    if (diff < 0.10) return 'low';
    if (diff < 0.20) return 'moderate';
    return 'significant';
  }

  /**
   * Manual service override for maintenance
   */
  setServiceMaintenance(service: string, inMaintenance: boolean): void {
    const circuitBreaker = CircuitBreakerFactory.getCircuitBreaker(service);
    
    if (inMaintenance) {
      circuitBreaker.forceOpen();
      console.log(`🔧 Service ${service} set to maintenance mode`);
    } else {
      circuitBreaker.forceClose();
      console.log(`✅ Service ${service} removed from maintenance mode`);
    }
  }

  /**
   * Update fallback strategy
   */
  setFallbackStrategy(strategy: FallbackStrategy): void {
    this.fallbackStrategy = strategy;
    console.log(`Strategy updated to: ${strategy}`);
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    this.healthMonitor.stopMonitoring();
    CircuitBreakerFactory.resetAll();
    console.log('AI Service Manager destroyed');
  }
}