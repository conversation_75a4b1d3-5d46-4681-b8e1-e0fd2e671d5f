/**
 * Service Health Monitor for AI Services
 * Continuously monitors service health and provides real-time status
 */

import { checkOpenAIHealth } from '../services/openai-service.ts';
import { checkClaudeHealth } from '../services/claude-service.ts';
import { checkLlamaParseHealth } from '../services/llamaparse-service.ts';
import { sendServiceDegradedAlert } from './monitoring-integration.ts';

export interface ServiceHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency_ms: number;
  last_check: Date;
  success_rate: number;
  consecutive_failures: number;
  error?: string;
  metadata?: {
    response_time_trend: number[]; // Last 10 response times
    error_rate_1h: number;
    error_rate_24h: number;
    last_success: Date | null;
    last_failure: Date | null;
  };
}

export interface ServiceHealthConfig {
  checkInterval: number;        // Health check interval in ms
  degradationThreshold: number; // Response time threshold for degraded status
  unhealthyThreshold: number;   // Consecutive failures for unhealthy status
  retainMetricsCount: number;   // Number of metrics to retain
  timeout: number;              // Health check timeout
}

export interface ServiceCredentials {
  openai?: string;
  claude?: string;
  llamaparse?: string;
}

/**
 * Health Check Result
 */
interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency_ms: number;
  error?: string;
  success: boolean;
}

/**
 * Service Health Monitor Implementation
 */
export class ServiceHealthMonitor {
  private healthData = new Map<string, ServiceHealth>();
  private config: ServiceHealthConfig;
  private credentials: ServiceCredentials;
  private monitoringInterval: number | null = null;
  private isMonitoring = false;

  constructor(credentials: ServiceCredentials, config?: Partial<ServiceHealthConfig>) {
    this.credentials = credentials;
    this.config = {
      checkInterval: 30000,      // 30 seconds
      degradationThreshold: 10000, // 10 seconds
      unhealthyThreshold: 3,     // 3 consecutive failures
      retainMetricsCount: 10,    // Keep last 10 metrics
      timeout: 5000,             // 5 second timeout
      ...config
    };

    // Initialize health data for all configured services
    this.initializeHealthData();
  }

  /**
   * Initialize health data for all services
   */
  private initializeHealthData(): void {
    const services = Object.keys(this.credentials) as Array<keyof ServiceCredentials>;
    
    services.forEach(service => {
      if (this.credentials[service]) {
        this.healthData.set(service, {
          service,
          status: 'healthy',
          latency_ms: 0,
          last_check: new Date(),
          success_rate: 1.0,
          consecutive_failures: 0,
          metadata: {
            response_time_trend: [],
            error_rate_1h: 0,
            error_rate_24h: 0,
            last_success: null,
            last_failure: null
          }
        });
      }
    });
  }

  /**
   * Start continuous health monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('Health monitoring already started');
      return;
    }

    this.isMonitoring = true;
    console.log(`Starting health monitoring with ${this.config.checkInterval}ms interval`);

    // Initial health check
    this.performHealthChecks();

    // Set up recurring checks
    this.monitoringInterval = setInterval(() => {
      this.performHealthChecks();
    }, this.config.checkInterval);
  }

  /**
   * Stop health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('Health monitoring stopped');
  }

  /**
   * Perform health checks for all services
   */
  private async performHealthChecks(): Promise<void> {
    const services = Array.from(this.healthData.keys());
    
    const healthCheckPromises = services.map(async (service) => {
      try {
        const _result = await this.checkServiceHealth(service);
        await this.updateServiceHealth(service, result);
      } catch {
        console.error(`Health check failed for ${service}:`, error);
        await this.updateServiceHealth(service, {
          status: 'unhealthy',
          latency_ms: this.config.timeout,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false
        });
      }
    });

    await Promise.allSettled(healthCheckPromises);
    this.logHealthSummary();
  }

  /**
   * Check health of a specific service
   */
  async checkServiceHealth(service: string): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      let healthResult: { status: string; latency_ms: number; error?: string };

      switch (service) {
        case 'openai': {
      }
          if (!this.credentials.openai) {
            throw new Error('OpenAI credentials not configured');
          }
          healthResult = await checkOpenAIHealth(this.credentials.openai);
          break;
    }

        case 'claude': {
      }
          if (!this.credentials.claude) {
            throw new Error('Claude credentials not configured');
          }
          healthResult = await checkClaudeHealth(this.credentials.claude);
          break;
    }

        case 'llamaparse': {
      }
          if (!this.credentials.llamaparse) {
            throw new Error('LlamaParse credentials not configured');
          }
          healthResult = await checkLlamaParseHealth(this.credentials.llamaparse);
          break;
    }

        default:
          throw new Error(`Unknown service: ${service}`);
      }

      const latency = Date.now() - startTime;
      const actualLatency = Math.max(healthResult.latency_ms, latency);

      // Determine status based on latency and response
      let status: 'healthy' | 'degraded' | 'unhealthy';
      if (healthResult.status === 'unhealthy' || healthResult.error) {
        status = 'unhealthy';
      } else if (actualLatency > this.config.degradationThreshold) {
        status = 'degraded';
      } else {
        status = 'healthy';
      }

      return {
        status,
        latency_ms: actualLatency,
        error: healthResult.error,
        success: status !== 'unhealthy'
      };

    } catch {
      const latency = Date.now() - startTime;
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: error instanceof Error ? error.message : 'Unknown error',
        success: false
      };
    }
  }

  /**
   * Update service health data
   */
  private async updateServiceHealth(service: string, result: HealthCheckResult): Promise<void> {
    const currentHealth = this.healthData.get(service);
    if (!currentHealth) {
      console.error(`No health data found for service: ${service}`);
      return;
    }

    const now = new Date();

    // Update consecutive failures
    if (result.success) {
      currentHealth.consecutive_failures = 0;
      if (currentHealth.metadata) {
        currentHealth.metadata.last_success = now;
      }
    } else {
      currentHealth.consecutive_failures++;
      if (currentHealth.metadata) {
        currentHealth.metadata.last_failure = now;
      }
    }

    // Update response time trend
    if (currentHealth.metadata) {
      currentHealth.metadata.response_time_trend.push(result.latency_ms);
      if (currentHealth.metadata.response_time_trend.length > this.config.retainMetricsCount) {
        currentHealth.metadata.response_time_trend.shift();
      }
    }

    // Calculate success rate (simplified - last 10 checks)
    const recentTrend = currentHealth.metadata?.response_time_trend || [];
    const successfulChecks = recentTrend.filter(latency => latency < this.config.degradationThreshold).length;
    currentHealth.success_rate = recentTrend.length > 0 ? successfulChecks / recentTrend.length : 1.0;

    // Override status if too many consecutive failures
    let finalStatus = result.status;
    if (currentHealth.consecutive_failures >= this.config.unhealthyThreshold) {
      finalStatus = 'unhealthy';
    }

    // Update health data
    currentHealth.status = finalStatus;
    currentHealth.latency_ms = result.latency_ms;
    currentHealth.last_check = now;
    currentHealth.error = result.error;

    this.healthData.set(service, currentHealth);

    // Send monitoring alert for service degradation
    if (finalStatus === 'degraded' || finalStatus === 'unhealthy') {
      await sendServiceDegradedAlert(service, result.latency_ms, {
        status: finalStatus,
        consecutive_failures: currentHealth.consecutive_failures,
        success_rate: currentHealth.success_rate,
        error: result.error,
        degradation_threshold: this.config.degradationThreshold
      });
    }
  }

  /**
   * Get health status for a specific service
   */
  getServiceHealth(service: string): ServiceHealth | undefined {
    const health = this.healthData.get(service);
    return health ? { ...health } : undefined;
  }

  /**
   * Get health status for all services
   */
  getAllServiceHealth(): Map<string, ServiceHealth> {
    const _result = new Map<string, ServiceHealth>();
    this.healthData.forEach((health, service) => {
      result.set(service, { ...health });
    });
    return result;
  }

  /**
   * Get healthy services in priority order
   */
  getHealthyServicesInOrder(): string[] {
    const services = Array.from(this.healthData.entries());
    
    // Filter healthy and degraded services, sort by priority
    const availableServices = services
      .filter(([_, health]) => health.status === 'healthy' || health.status === 'degraded')
      .sort(([_a, healthA], [_b, healthB]) => {
        // Priority: healthy > degraded, then by response time
        if (healthA.status !== healthB.status) {
          return healthA.status === 'healthy' ? -1 : 1;
        }
        return healthA.latency_ms - healthB.latency_ms;
      })
      .map(([service, _]) => service);

    return availableServices;
  }

  /**
   * Check if any service is available
   */
  hasAvailableServices(): boolean {
    return this.getHealthyServicesInOrder().length > 0;
  }

  /**
   * Get best available service
   */
  getBestAvailableService(): string | null {
    const availableServices = this.getHealthyServicesInOrder();
    return availableServices.length > 0 ? availableServices[0] : null;
  }

  /**
   * Get service availability summary
   */
  getAvailabilitySummary(): {
    total_services: number;
    healthy_services: number;
    degraded_services: number;
    unhealthy_services: number;
    overall_availability: number;
  } {
    const allHealth = Array.from(this.healthData.values());
    const healthy = allHealth.filter(h => h.status === 'healthy').length;
    const degraded = allHealth.filter(h => h.status === 'degraded').length;
    const unhealthy = allHealth.filter(h => h.status === 'unhealthy').length;
    const total = allHealth.length;

    // Calculate overall availability (healthy + degraded services)
    const available = healthy + degraded;
    const availability = total > 0 ? available / total : 0;

    return {
      total_services: total,
      healthy_services: healthy,
      degraded_services: degraded,
      unhealthy_services: unhealthy,
      overall_availability: availability
    };
  }

  /**
   * Log health summary for monitoring
   */
  private logHealthSummary(): void {
    const summary = this.getAvailabilitySummary();
    const healthyServices = this.getHealthyServicesInOrder();
    
    console.log(`Health Summary: ${summary.healthy_services}H ${summary.degraded_services}D ${summary.unhealthy_services}U (${(summary.overall_availability * 100).toFixed(1)}% available)`);
    
    if (healthyServices.length === 0) {
      console.error('⚠️ NO SERVICES AVAILABLE - All AI services are down!');
    } else {
      console.log(`✅ Available services: ${healthyServices.join(', ')}`);
    }
  }

  /**
   * Force update service health (for testing)
   */
  setServiceHealth(service: string, health: ServiceHealth): void {
    this.healthData.set(service, { ...health });
  }

  /**
   * Get monitoring configuration
   */
  getConfig(): ServiceHealthConfig {
    return { ...this.config };
  }

  /**
   * Update monitoring configuration
   */
  updateConfig(newConfig: Partial<ServiceHealthConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart monitoring with new config if currently monitoring
    if (this.isMonitoring) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * Get detailed health report for debugging
   */
  getDetailedHealthReport(): {
    timestamp: Date;
    monitoring_active: boolean;
    config: ServiceHealthConfig;
    services: Array<ServiceHealth & {
      service_configured: boolean;
      last_check_age_ms: number;
    }>;
    availability_summary: ReturnType<ServiceHealthMonitor['getAvailabilitySummary']>;
  } {
    const now = new Date();
    const services = Array.from(this.healthData.values()).map(health => ({
      ...health,
      service_configured: !!this.credentials[health.service as keyof ServiceCredentials],
      last_check_age_ms: now.getTime() - health.last_check.getTime()
    }));

    return {
      timestamp: now,
      monitoring_active: this.isMonitoring,
      config: this.getConfig(),
      services,
      availability_summary: this.getAvailabilitySummary()
    };
  }

  /**
   * Check if monitoring is active
   */
  isActive(): boolean {
    return this.isMonitoring;
  }

  /**
   * Cleanup and destroy monitor
   */
  destroy(): void {
    this.stopMonitoring();
    this.healthData.clear();
    console.log('Service health monitor destroyed');
  }
}

/**
 * Global health monitor instance for the application
 */
let globalHealthMonitor: ServiceHealthMonitor | null = null;

/**
 * Initialize global health monitor
 */
export function initializeHealthMonitor(
  credentials: ServiceCredentials, 
  config?: Partial<ServiceHealthConfig>
): ServiceHealthMonitor {
  if (globalHealthMonitor) {
    globalHealthMonitor.destroy();
  }
  
  globalHealthMonitor = new ServiceHealthMonitor(credentials, config);
  return globalHealthMonitor;
}

/**
 * Get global health monitor instance
 */
export function getHealthMonitor(): ServiceHealthMonitor | null {
  return globalHealthMonitor;
}

/**
 * Start global health monitoring
 */
export function startGlobalHealthMonitoring(): void {
  if (globalHealthMonitor) {
    globalHealthMonitor.startMonitoring();
  } else {
    console.error('Health monitor not initialized. Call initializeHealthMonitor() first.');
  }
}

/**
 * Stop global health monitoring
 */
export function stopGlobalHealthMonitoring(): void {
  if (globalHealthMonitor) {
    globalHealthMonitor.stopMonitoring();
  }
}