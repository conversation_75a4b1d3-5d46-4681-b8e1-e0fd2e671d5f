/**
 * Cost Calculation Utilities
 * Tracks AI service costs and profit margins
 */

export interface CostTracking {
  model: string;
  input_tokens: number;
  output_tokens: number;
  cost_usd: number;
  customer_price_usd: number;
  profit_margin_percent: number;
  timestamp: Date;
}

export interface Usage {
  prompt_tokens?: number;
  completion_tokens?: number;
  input_tokens?: number;
  output_tokens?: number;
  total_tokens?: number;
  pages?: number;
}

/**
 * Calculate cost for AI service usage with precise pricing
 */
export function calculateCost(model: string, usage: Usage): CostTracking {
  let cost_usd = 0;
  
  if (model === 'gpt-4' || model === 'openai/gpt-4o') {
    // GPT-4 pricing: $0.03/1K prompt tokens, $0.06/1K completion tokens
    const promptTokens = usage.prompt_tokens || usage.input_tokens || 0;
    const completionTokens = usage.completion_tokens || usage.output_tokens || 0;
    cost_usd = (promptTokens / 1000) * 0.03 + (completionTokens / 1000) * 0.06;
  } else if (model === 'anthropic/claude-3.5-sonnet' || model === 'claude-3-sonnet') {
    // Claude pricing: $0.003/1K input tokens, $0.015/1K output tokens
    const inputTokens = usage.input_tokens || usage.prompt_tokens || 0;
    const outputTokens = usage.output_tokens || usage.completion_tokens || 0;
    cost_usd = (inputTokens / 1000) * 0.003 + (outputTokens / 1000) * 0.015;
  } else if (model === 'google/gemini-flash-1.5') {
    // Gemini Flash pricing: $0.075/1M tokens (combined input/output)
    const totalTokens = (usage.prompt_tokens || usage.input_tokens || 0) + 
                       (usage.completion_tokens || usage.output_tokens || 0);
    cost_usd = (totalTokens / 1000000) * 0.075;
  } else if (model === 'llamaparse') {
    // LlamaParse pricing: $0.003/page
    cost_usd = (usage.pages || 1) * 0.003;
  }

  // Apply markup for profit margin (50% markup = 33.33% margin)
  const markup = 1.5;
  const customer_price_usd = cost_usd * markup;
  const profit_margin_percent = ((customer_price_usd - cost_usd) / customer_price_usd) * 100;

  return {
    model,
    input_tokens: usage.prompt_tokens || usage.input_tokens || 0,
    output_tokens: usage.completion_tokens || usage.output_tokens || 0,
    cost_usd: Math.round(cost_usd * 10000) / 10000, // Round to 4 decimal places
    customer_price_usd: Math.round(customer_price_usd * 10000) / 10000,
    profit_margin_percent: Math.round(profit_margin_percent * 100) / 100,
    timestamp: new Date()
  };
}

/**
 * Check if profit margin meets minimum threshold (60%)
 */
export function checkProfitMargin(costTracking: CostTracking): boolean {
  return costTracking.profit_margin_percent >= 60;
}

/**
 * Calculate optimal markup for target profit margin
 */
export function calculateOptimalMarkup(targetMarginPercent: number): number {
  // Formula: markup = 1 / (1 - target_margin_decimal)
  const targetMarginDecimal = targetMarginPercent / 100;
  return 1 / (1 - targetMarginDecimal);
}

/**
 * Estimate cost before making API call
 */
export function estimateCost(model: string, documentLength: number): number {
  // Rough estimation: 1 token ≈ 4 characters
  const estimatedTokens = Math.ceil(documentLength / 4);
  
  // Assume 80% input, 20% output split
  const inputTokens = Math.floor(estimatedTokens * 0.8);
  const outputTokens = Math.floor(estimatedTokens * 0.2);
  
  const usage: Usage = {
    prompt_tokens: inputTokens,
    completion_tokens: outputTokens,
    input_tokens: inputTokens,
    output_tokens: outputTokens
  };
  
  return calculateCost(model, usage).cost_usd;
}

/**
 * Get cost breakdown by service for reporting
 */
export interface CostBreakdown {
  total_cost_usd: number;
  total_revenue_usd: number;
  total_profit_usd: number;
  average_margin_percent: number;
  model_breakdown: Record<string, {
    calls: number;
    cost_usd: number;
    revenue_usd: number;
    profit_usd: number;
    margin_percent: number;
  }>;
}

export function calculateCostBreakdown(costTrackings: CostTracking[]): CostBreakdown {
  const breakdown: CostBreakdown = {
    total_cost_usd: 0,
    total_revenue_usd: 0,
    total_profit_usd: 0,
    average_margin_percent: 0,
    model_breakdown: {}
  };

  for (const tracking of costTrackings) {
    // Update totals
    breakdown.total_cost_usd += tracking.cost_usd;
    breakdown.total_revenue_usd += tracking.customer_price_usd;
    breakdown.total_profit_usd += (tracking.customer_price_usd - tracking.cost_usd);

    // Update model breakdown
    if (!breakdown.model_breakdown[tracking.model]) {
      breakdown.model_breakdown[tracking.model] = {
        calls: 0,
        cost_usd: 0,
        revenue_usd: 0,
        profit_usd: 0,
        margin_percent: 0
      };
    }

    const modelData = breakdown.model_breakdown[tracking.model];
    modelData.calls += 1;
    modelData.cost_usd += tracking.cost_usd;
    modelData.revenue_usd += tracking.customer_price_usd;
    modelData.profit_usd += (tracking.customer_price_usd - tracking.cost_usd);
    modelData.margin_percent = ((modelData.profit_usd / modelData.revenue_usd) * 100);
  }

  // Calculate average margin
  if (breakdown.total_revenue_usd > 0) {
    breakdown.average_margin_percent = (breakdown.total_profit_usd / breakdown.total_revenue_usd) * 100;
  }

  // Round all values
  breakdown.total_cost_usd = Math.round(breakdown.total_cost_usd * 10000) / 10000;
  breakdown.total_revenue_usd = Math.round(breakdown.total_revenue_usd * 10000) / 10000;
  breakdown.total_profit_usd = Math.round(breakdown.total_profit_usd * 10000) / 10000;
  breakdown.average_margin_percent = Math.round(breakdown.average_margin_percent * 100) / 100;

  return breakdown;
}