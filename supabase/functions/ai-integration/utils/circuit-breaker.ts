/**
 * Circuit Breaker Implementation for AI Services
 * Provides automatic failover and service protection
 */

import { sendMonitoringAlert, createCircuitBreakerAlert } from './monitoring-integration.ts';

export interface CircuitBreakerConfig {
  failureThreshold: number;     // Failures before opening circuit
  timeout: number;              // Wait time before retry (ms)
  resetTimeout: number;         // Full reset time (ms)
  successThreshold: number;     // Successes needed to close circuit
  degradationThreshold: number; // Response time considered degraded
}

export interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failures: number;
  successes: number;
  nextAttempt: number;
  lastFailureTime: number;
  lastSuccessTime: number;
}

export interface CircuitBreakerMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  circuitOpenEvents: number;
  circuitCloseEvents: number;
  averageResponseTime: number;
  lastResetTime: number;
}

/**
 * Circuit Breaker Error Types
 */
export class CircuitBreakerOpenError extends Error {
  constructor(serviceName: string, nextAttempt: number) {
    super(`Circuit breaker for ${serviceName} is OPEN. Next attempt at ${new Date(nextAttempt)}`);
    this.name = 'CircuitBreakerOpenError';
  }
}

export class CircuitBreakerTimeoutError extends Error {
  constructor(serviceName: string, timeout: number) {
    super(`Operation timeout for ${serviceName} after ${timeout}ms`);
    this.name = 'CircuitBreakerTimeoutError';
  }
}

/**
 * Determines if an error should count as a circuit breaker failure
 */
function isCircuitBreakerFailure(error: Error): boolean {
  // Network and service errors count as failures
  const networkErrors = [
    'ECONNREFUSED',
    'ENOTFOUND',
    'ETIMEDOUT',
    'ECONNRESET',
    'timeout',
    'network',
    'rate limit',
    'service unavailable',
    'internal server error',
    'bad gateway',
    'gateway timeout'
  ];

  const errorMessage = error.message.toLowerCase();
  return networkErrors.some(pattern => errorMessage.includes(pattern)) ||
         error.name === 'CircuitBreakerTimeoutError' ||
         error.name === 'AbortError';
}

/**
 * Main Circuit Breaker Implementation
 */
export class CircuitBreaker {
  private config: CircuitBreakerConfig;
  private state: CircuitBreakerState;
  private metrics: CircuitBreakerMetrics;
  private serviceName: string;

  constructor(serviceName: string, config: CircuitBreakerConfig) {
    this.validateConfig(config);
    
    this.serviceName = serviceName;
    this.config = { ...config };
    this.state = {
      state: 'closed',
      failures: 0,
      successes: 0,
      nextAttempt: 0,
      lastFailureTime: 0,
      lastSuccessTime: 0
    };
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      circuitOpenEvents: 0,
      circuitCloseEvents: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };
  }

  /**
   * Validate circuit breaker configuration
   */
  private validateConfig(config: CircuitBreakerConfig): void {
    const errors: string[] = [];

    if (!config.failureThreshold || config.failureThreshold <= 0) {
      errors.push('failureThreshold must be greater than 0');
    }

    if (!config.timeout || config.timeout <= 0) {
      errors.push('timeout must be greater than 0');
    }

    if (!config.resetTimeout || config.resetTimeout <= 0) {
      errors.push('resetTimeout must be greater than 0');
    }

    if (!config.successThreshold || config.successThreshold <= 0) {
      errors.push('successThreshold must be greater than 0');
    }

    if (config.degradationThreshold < 0) {
      errors.push('degradationThreshold must be non-negative');
    }

    if (errors.length > 0) {
      throw new Error(`Invalid circuit breaker configuration: ${errors.join(', ')}`);
    }
  }

  /**
   * Execute operation with circuit breaker protection
   */
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    // Check circuit state
    if (this.state.state === 'open') {
      if (Date.now() < this.state.nextAttempt) {
        throw new CircuitBreakerOpenError(this.serviceName, this.state.nextAttempt);
      }
      // Transition to half-open
      this.state.state = 'half-open';
      this.state.successes = 0;
      console.log(`Circuit breaker for ${this.serviceName} transitioning to HALF-OPEN`);
    }

    try {
      // Execute operation with timeout
      const _result = await this.executeWithTimeout(operation);
      
      const responseTime = Date.now() - startTime;
      await this.onSuccess(responseTime);
      
      return result;
    } catch {
      const responseTime = Date.now() - startTime;
      await this.onFailure(error instanceof Error ? error : new Error('Unknown error'), responseTime);
      throw error;
    }
  }

  /**
   * Execute operation with timeout protection
   */
  private async executeWithTimeout<T>(operation: () => Promise<T>): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => {
      controller.abort();
    }, this.config.timeout);

    try {
      // For operations that support AbortSignal, pass it
      const _result = await Promise.race([
        operation(),
        new Promise<never>((_, reject) => {
          controller.signal.addEventListener('abort', () => {
            reject(new CircuitBreakerTimeoutError(this.serviceName, this.config.timeout));
          });
        })
      ]);

      clearTimeout(timeoutId);
      return result;
    } catch {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Handle successful operation
   */
  private async onSuccess(responseTime: number): Promise<void> {
    this.state.failures = 0;
    this.state.lastSuccessTime = Date.now();
    this.metrics.successfulRequests++;
    this.updateAverageResponseTime(responseTime);

    if (this.state.state === 'half-open') {
      this.state.successes++;
      if (this.state.successes >= this.config.successThreshold) {
        await this.closeCircuit();
      }
    }
  }

  /**
   * Handle failed operation
   */
  private async onFailure(error: Error, responseTime: number): Promise<void> {
    if (!isCircuitBreakerFailure(error)) {
      // Don't count application errors as circuit breaker failures
      return;
    }

    this.state.failures++;
    this.state.lastFailureTime = Date.now();
    this.metrics.failedRequests++;
    this.updateAverageResponseTime(responseTime);

    if (this.state.state === 'half-open') {
      // Failure in half-open state immediately opens circuit
      await this.openCircuit();
    } else if (this.state.failures >= this.config.failureThreshold) {
      await this.openCircuit();
    }
  }

  /**
   * Open the circuit breaker
   */
  private async openCircuit(): Promise<void> {
    this.state.state = 'open';
    this.state.nextAttempt = Date.now() + this.config.resetTimeout;
    this.metrics.circuitOpenEvents++;
    
    console.warn(`Circuit breaker for ${this.serviceName} opened. Next attempt at ${new Date(this.state.nextAttempt)}`);
    
    // Send monitoring alert
    await sendMonitoringAlert(createCircuitBreakerAlert(this.serviceName, 'open', {
      nextAttempt: this.state.nextAttempt,
      failures: this.state.failures,
      resetTimeout: this.config.resetTimeout,
      failureThreshold: this.config.failureThreshold
    }));
  }

  /**
   * Close the circuit breaker
   */
  private async closeCircuit(): Promise<void> {
    this.state.state = 'closed';
    this.state.failures = 0;
    this.state.successes = 0;
    this.metrics.circuitCloseEvents++;
    
    console.log(`Circuit breaker for ${this.serviceName} closed - service recovered`);
    
    // Send monitoring alert
    await sendMonitoringAlert(createCircuitBreakerAlert(this.serviceName, 'close', {
      successThreshold: this.config.successThreshold,
      totalEvents: this.metrics.circuitOpenEvents,
      averageResponseTime: this.metrics.averageResponseTime
    }));
  }

  /**
   * Update average response time
   */
  private updateAverageResponseTime(responseTime: number): void {
    const totalResponses = this.metrics.successfulRequests + this.metrics.failedRequests;
    this.metrics.averageResponseTime = 
      ((this.metrics.averageResponseTime * (totalResponses - 1)) + responseTime) / totalResponses;
  }

  /**
   * Get current circuit breaker state
   */
  getState(): CircuitBreakerState {
    return { ...this.state };
  }

  /**
   * Get circuit breaker configuration
   */
  getConfig(): CircuitBreakerConfig {
    return { ...this.config };
  }

  /**
   * Get circuit breaker metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    return { ...this.metrics };
  }

  /**
   * Get service name
   */
  getServiceName(): string {
    return this.serviceName;
  }

  /**
   * Check if circuit is healthy
   */
  isHealthy(): boolean {
    return this.state.state === 'closed' || this.state.state === 'half-open';
  }

  /**
   * Get success rate
   */
  getSuccessRate(): number {
    if (this.metrics.totalRequests === 0) return 1.0;
    return this.metrics.successfulRequests / this.metrics.totalRequests;
  }

  /**
   * Check if service is degraded (slow responses)
   */
  isDegraded(): boolean {
    return this.metrics.averageResponseTime > this.config.degradationThreshold;
  }

  /**
   * Manual circuit breaker controls for maintenance
   */
  forceOpen(): void {
    this.state.state = 'open';
    this.state.nextAttempt = Date.now() + this.config.resetTimeout;
    console.log(`Circuit breaker for ${this.serviceName} manually opened for maintenance`);
  }

  forceClose(): void {
    this.state.state = 'closed';
    this.state.failures = 0;
    this.state.successes = 0;
    console.log(`Circuit breaker for ${this.serviceName} manually closed after maintenance`);
  }

  /**
   * Reset circuit breaker to initial state
   */
  reset(): void {
    this.state = {
      state: 'closed',
      failures: 0,
      successes: 0,
      nextAttempt: 0,
      lastFailureTime: 0,
      lastSuccessTime: 0
    };
    
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      circuitOpenEvents: 0,
      circuitCloseEvents: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };
    
    console.log(`Circuit breaker for ${this.serviceName} reset to initial state`);
  }

  /**
   * Get status summary for monitoring
   */
  getStatusSummary(): {
    service: string;
    state: string;
    isHealthy: boolean;
    isDegraded: boolean;
    successRate: number;
    averageResponseTime: number;
    failures: number;
    nextAttempt?: Date;
  } {
    return {
      service: this.serviceName,
      state: this.state.state,
      isHealthy: this.isHealthy(),
      isDegraded: this.isDegraded(),
      successRate: this.getSuccessRate(),
      averageResponseTime: this.metrics.averageResponseTime,
      failures: this.state.failures,
      nextAttempt: this.state.nextAttempt > 0 ? new Date(this.state.nextAttempt) : undefined
    };
  }
}

/**
 * Circuit Breaker Factory for different services
 */
export class CircuitBreakerFactory {
  private static circuitBreakers = new Map<string, CircuitBreaker>();

  /**
   * Get or create circuit breaker for service
   */
  static getCircuitBreaker(serviceName: string, config?: CircuitBreakerConfig): CircuitBreaker {
    if (!this.circuitBreakers.has(serviceName)) {
      if (!config) {
        // Use default config merged with environment overrides
        const defaultConfig = DEFAULT_CIRCUIT_BREAKER_CONFIGS[serviceName];
        if (!defaultConfig) {
          throw new Error(`No default circuit breaker configuration found for service: ${serviceName}`);
        }
        const envConfig = getConfigFromEnv(serviceName);
        const mergedConfig = { ...defaultConfig, ...envConfig };
        config = mergedConfig;
        
        // Log environment overrides
        const overrideKeys = Object.keys(envConfig);
        if (overrideKeys.length > 0) {
          console.log(`Circuit breaker for ${serviceName} using environment overrides: ${overrideKeys.join(', ')}`);
        }
      }
      this.circuitBreakers.set(serviceName, new CircuitBreaker(serviceName, config));
    }
    
    return this.circuitBreakers.get(serviceName)!;
  }

  /**
   * Get all circuit breakers
   */
  static getAllCircuitBreakers(): Map<string, CircuitBreaker> {
    return new Map(this.circuitBreakers);
  }

  /**
   * Remove circuit breaker for service
   */
  static removeCircuitBreaker(serviceName: string): boolean {
    return this.circuitBreakers.delete(serviceName);
  }

  /**
   * Reset all circuit breakers
   */
  static resetAll(): void {
    this.circuitBreakers.forEach(cb => cb.reset());
  }

  /**
   * Get status summary for all services
   */
  static getAllStatusSummaries(): Array<ReturnType<CircuitBreaker['getStatusSummary']>> {
    return Array.from(this.circuitBreakers.values()).map(cb => cb.getStatusSummary());
  }
}

/**
 * Load configuration from environment variables
 */
function getConfigFromEnv(service: string): Partial<CircuitBreakerConfig> {
  const prefix = `CIRCUIT_BREAKER_${service.toUpperCase()}`;
  
  const envConfig: Partial<CircuitBreakerConfig> = {};
  
  const failureThreshold = Deno.env.get(`${prefix}_FAILURE_THRESHOLD`);
  if (failureThreshold) {
    const parsed = parseInt(failureThreshold);
    if (!isNaN(parsed) && parsed > 0) {
      envConfig.failureThreshold = parsed;
    }
  }
  
  const timeout = Deno.env.get(`${prefix}_TIMEOUT`);
  if (timeout) {
    const parsed = parseInt(timeout);
    if (!isNaN(parsed) && parsed > 0) {
      envConfig.timeout = parsed;
    }
  }
  
  const resetTimeout = Deno.env.get(`${prefix}_RESET_TIMEOUT`);
  if (resetTimeout) {
    const parsed = parseInt(resetTimeout);
    if (!isNaN(parsed) && parsed > 0) {
      envConfig.resetTimeout = parsed;
    }
  }
  
  const successThreshold = Deno.env.get(`${prefix}_SUCCESS_THRESHOLD`);
  if (successThreshold) {
    const parsed = parseInt(successThreshold);
    if (!isNaN(parsed) && parsed > 0) {
      envConfig.successThreshold = parsed;
    }
  }
  
  const degradationThreshold = Deno.env.get(`${prefix}_DEGRADATION_THRESHOLD`);
  if (degradationThreshold) {
    const parsed = parseInt(degradationThreshold);
    if (!isNaN(parsed) && parsed >= 0) {
      envConfig.degradationThreshold = parsed;
    }
  }
  
  return envConfig;
}

/**
 * Default circuit breaker configurations for different AI services
 */
export const DEFAULT_CIRCUIT_BREAKER_CONFIGS: Record<string, CircuitBreakerConfig> = {
  openai: {
    failureThreshold: 5,
    timeout: 30000,        // 30 seconds
    resetTimeout: 300000,  // 5 minutes
    successThreshold: 3,
    degradationThreshold: 10000 // 10 seconds
  },
  claude: {
    failureThreshold: 3,   // More sensitive for secondary service
    timeout: 45000,        // 45 seconds
    resetTimeout: 600000,  // 10 minutes
    successThreshold: 2,
    degradationThreshold: 15000 // 15 seconds
  },
  llamaparse: {
    failureThreshold: 2,   // Most sensitive for tertiary service
    timeout: 60000,        // 60 seconds
    resetTimeout: 900000,  // 15 minutes
    successThreshold: 2,
    degradationThreshold: 20000 // 20 seconds
  }
};