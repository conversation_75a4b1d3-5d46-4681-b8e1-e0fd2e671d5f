/**
 * Admin API Key Management Endpoint
 * 
 * Manages API key lifecycle: activate, suspend, revoke
 * Provides key status and usage information
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
import {
  handleCors,
  createApiResponse,
  logApiUsage
} from "../../_shared/api-key-utils.ts";

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

interface KeyManagementRequest {
  keyId: string;
  action: 'activate' | 'suspend' | 'revoke';
  reason?: string;
}

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/').filter(Boolean);
    
    // Route based on HTTP method and path
    if (req.method === 'PUT') {
      return await handleStatusUpdate(req, supabase);
    } else if (req.method === 'GET') {
      if (pathSegments.includes('usage')) {
        return await handleUsageQuery(req, supabase);
      } else {
        return await handleStatusQuery(req, supabase);
      }
    } else if (req.method === 'DELETE') {
      return await handleKeyRevocation(req, supabase);
    } else {
      return createApiResponse('Method not allowed', 405, false);
    }

  } catch {
    console.error('Manage keys error:', error);
    return createApiResponse(
      error instanceof Error ? error.message : 'Internal server error',
      500,
      false
    );
  }
});

/**
 * Handle key status updates (activate/suspend)
 */
async function handleStatusUpdate(req: Request, supabase: any): Promise<Response> {
  const body = await req.json() as KeyManagementRequest;
  
  if (!body.keyId || !body.action) {
    return createApiResponse('Missing required fields: keyId, action', 400, false);
  }

  if (!['activate', 'suspend'].includes(body.action)) {
    return createApiResponse('Invalid action. Must be "activate" or "suspend"', 400, false);
  }

  // Get key details first
  const { data: keyDetails, error: keyError } = await supabase
    .from('api_keys')
    .select('customer_id, status')
    .eq('id', body.keyId)
    .single();

  if (keyError || !keyDetails) {
    return createApiResponse('API key not found', 404, false);
  }

  // Update key status using database function
  const { data: updateResult, error: updateError } = await supabase.rpc('update_api_key_status', {
    p_key_id: body.keyId,
    p_new_status: body.action === 'activate' ? 'active' : 'suspended',
    p_reason: body.reason
  });

  if (updateError) {
    console.error('Status update error:', updateError);
    return createApiResponse('Failed to update key status', 500, false);
  }

  const _result = updateResult[0];
  
  if (!result.success) {
    return createApiResponse(result.message, 400, false);
  }

  // Log the status change
  await logApiUsage(
    supabase,
    keyDetails.customer_id,
    body.keyId,
    'api_key_status_change',
    true,
    0,
    {
      action: body.action,
      old_status: result.old_status,
      new_status: result.new_status,
      reason: body.reason
    }
  );

  return createApiResponse({
    keyId: body.keyId,
    status: result.new_status,
    previousStatus: result.old_status,
    message: result.message
  });
}

/**
 * Handle key revocation (DELETE)
 */
async function handleKeyRevocation(req: Request, supabase: any): Promise<Response> {
  const body = await req.json() as { keyId: string; reason?: string };
  
  if (!body.keyId) {
    return createApiResponse('Missing required field: keyId', 400, false);
  }

  // Get key details first
  const { data: keyDetails, error: keyError } = await supabase
    .from('api_keys')
    .select('customer_id, status')
    .eq('id', body.keyId)
    .single();

  if (keyError || !keyDetails) {
    return createApiResponse('API key not found', 404, false);
  }

  // Revoke key using database function
  const { data: revokeResult, error: revokeError } = await supabase.rpc('update_api_key_status', {
    p_key_id: body.keyId,
    p_new_status: 'revoked',
    p_reason: body.reason || 'Revoked via admin'
  });

  if (revokeError) {
    console.error('Revocation error:', revokeError);
    return createApiResponse('Failed to revoke key', 500, false);
  }

  const _result = revokeResult[0];
  
  if (!result.success) {
    return createApiResponse(result.message, 400, false);
  }

  // Log the revocation
  await logApiUsage(
    supabase,
    keyDetails.customer_id,
    body.keyId,
    'api_key_revocation',
    true,
    0,
    {
      reason: body.reason,
      old_status: result.old_status
    }
  );

  return createApiResponse({
    keyId: body.keyId,
    status: 'revoked',
    message: 'API key revoked successfully'
  });
}

/**
 * Handle key status queries (GET)
 */
async function handleStatusQuery(req: Request, supabase: any): Promise<Response> {
  const url = new URL(req.url);
  const keyId = url.searchParams.get('keyId');
  
  if (!keyId) {
    return createApiResponse('Missing keyId parameter', 400, false);
  }

  // Get comprehensive key information
  const { data: keyInfo, error: keyError } = await supabase
    .from('api_keys')
    .select(`
      id,
      customer_id,
      key_type,
      key_prefix,
      name,
      credits,
      max_credits,
      rate_limits,
      last_used_at,
      expires_at,
      status,
      revoked,
      revoked_at,
      revoked_reason,
      created_at,
      updated_at
    `)
    .eq('id', keyId)
    .single();

  if (keyError || !keyInfo) {
    return createApiResponse('API key not found', 404, false);
  }

  // Get recent usage statistics
  const { data: usageStats, error: _usageError } = await supabase
    .from('usage_logs')
    .select('operation_type, success, created_at')
    .eq('api_key_id', keyId)
    .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
    .order('created_at', { ascending: false })
    .limit(100);

  // Calculate usage summary
  const usageSummary = usageStats ? {
    totalRequests: usageStats.length,
    successfulRequests: usageStats.filter(u => u.success).length,
    failedRequests: usageStats.filter(u => !u.success).length,
    lastUsed: usageStats[0]?.created_at,
    operationTypes: usageStats.reduce((acc, u) => {
      acc[u.operation_type] = (acc[u.operation_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  } : null;

  return createApiResponse({
    keyInfo: {
      ...keyInfo,
      isExpired: keyInfo.expires_at ? new Date(keyInfo.expires_at) < new Date() : false
    },
    usageSummary
  });
}

/**
 * Handle detailed usage queries
 */
async function handleUsageQuery(req: Request, supabase: any): Promise<Response> {
  const url = new URL(req.url);
  const keyId = url.searchParams.get('keyId');
  const startDate = url.searchParams.get('startDate');
  const endDate = url.searchParams.get('endDate');
  
  if (!keyId) {
    return createApiResponse('Missing keyId parameter', 400, false);
  }

  // Default to last 30 days if no date range provided
  const defaultStartDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
  const defaultEndDate = new Date().toISOString();

  // Get detailed usage logs
  const query = supabase
    .from('usage_logs')
    .select(`
      operation_type,
      success,
      credits_used,
      model_cost,
      customer_price,
      processing_time_ms,
      error_code,
      created_at
    `)
    .eq('api_key_id', keyId)
    .gte('created_at', startDate || defaultStartDate)
    .lte('created_at', endDate || defaultEndDate)
    .order('created_at', { ascending: false });

  const { data: usageLogs, error: logsError } = await query;

  if (logsError) {
    console.error('Usage query error:', logsError);
    return createApiResponse('Failed to retrieve usage data', 500, false);
  }

  // Calculate aggregated statistics
  const stats = usageLogs ? {
    totalRequests: usageLogs.length,
    successfulRequests: usageLogs.filter(u => u.success).length,
    failedRequests: usageLogs.filter(u => !u.success).length,
    totalCreditsUsed: usageLogs.reduce((sum, u) => sum + (u.credits_used || 0), 0),
    totalCost: usageLogs.reduce((sum, u) => sum + (u.model_cost || 0), 0),
    totalRevenue: usageLogs.reduce((sum, u) => sum + (u.customer_price || 0), 0),
    avgProcessingTime: usageLogs.length > 0 
      ? usageLogs.reduce((sum, u) => sum + (u.processing_time_ms || 0), 0) / usageLogs.length 
      : 0,
    operationBreakdown: usageLogs.reduce((acc, u) => {
      acc[u.operation_type] = (acc[u.operation_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    errorBreakdown: usageLogs.filter(u => !u.success).reduce((acc, u) => {
      const error = u.error_code || 'unknown';
      acc[error] = (acc[error] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  } : null;

  return createApiResponse({
    dateRange: {
      start: startDate || defaultStartDate,
      end: endDate || defaultEndDate
    },
    statistics: stats,
    recentLogs: usageLogs?.slice(0, 50) // Return most recent 50 entries
  });
}

// Add error handling for unhandled promise rejections
globalThis.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});