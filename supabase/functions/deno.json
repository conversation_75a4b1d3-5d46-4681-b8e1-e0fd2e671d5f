{"lock": false, "imports": {"@supabase/functions-js": "jsr:@supabase/functions-js@2.5.0", "@supabase/supabase-js": "jsr:@supabase/supabase-js@2.57.4"}, "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false}, "lint": {"include": ["**/*.ts"], "rules": {"tags": ["recommended"], "include": ["no-explicit-any", "no-unused-vars"]}}, "fmt": {"useTabs": false, "lineWidth": 100, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve"}, "tasks": {"serve": "deno run --allow-all --no-lock --watch", "test": "deno test --allow-all --no-lock", "lint": "deno lint --no-lock", "fmt": "deno fmt --no-lock", "check": "deno check --no-lock **/*.ts"}}