/**
 * Document Extraction API Endpoint
 * 
 * POST /api/v1/extract - Process documents with AI extraction
 * 
 * Features:
 * - Secure file upload with comprehensive validation
 * - Multi-model AI processing (OpenAI, Claude, LlamaParse)
 * - API key authentication and rate limiting
 * - Cost tracking and credit management
 * - Temporary storage with automatic cleanup
 */

import "jsr:@supabase/functions-js@2.5.0/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';
import type { Database } from '../../../types/database.types.ts';
import {
  validateApiKey,
  checkRateLimit,
  deductCredits,
  logApiUsage,
  corsHeaders as _corsHeaders,
  createApiResponse,
  handleCors,
  type ApiKeyValidationResult
} from '../_shared/api-key-utils.ts';
import { QueueManager } from '../_shared/queue-manager.ts';
import {
  FileUploadValidator,
  type FileValidationResult
} from '../_shared/file-validation.ts';

// Import processing modules
import { extractTextFromDocument } from './utils/text-extraction.ts';
import { preprocessContent } from './utils/content-preprocessor.ts';
import { processWithAI } from './utils/ai-processor.ts';
import { formatOutput } from './utils/output-formatter.ts';
import { checkDocumentCache, storeInCache } from './utils/document-cache.ts';
import { 
  initializeProcessingStatus, 
  updateProcessingStatus, 
  completeProcessingStage 
} from './utils/status-tracker.ts';

// Type definitions for request/response
interface _ExtractRequest {
  document?: File;
  agentId?: string;
  customPrompt?: string;
  options?: {
    priority?: number;
    webhook?: string;
    maxRetries?: number;
  };
}

interface ExtractResponse {
  success: boolean;
  data?: {
    documentId: string;
    extractedData?: Record<string, unknown>;
    confidence?: number;
    processingTime?: number;
    model?: string;
    status: 'processing' | 'completed' | 'failed' | 'queued';
    creditsUsed: number;
    remainingCredits: number;
    jobId?: string; // For queued jobs
    estimatedCompletion?: string; // For queued jobs
  };
  error?: string;
  timestamp: string;
}

interface ProcessingContext {
  apiKey: ApiKeyValidationResult;
  documentId: string;
  correlationId: string;
  startTime: number;
}

// Environment variables with validation
const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('Missing required environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
}

// Initialize Supabase client and queue manager
const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const queueManager = new QueueManager(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

/**
 * Main handler for the extract endpoint
 */
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  // Only accept POST requests
  if (req.method !== 'POST') {
    return createApiResponse('Method not allowed', 405, false);
  }

  const correlationId = crypto.randomUUID();
  const startTime = performance.now();

  try {
    // Extract and validate API key
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return createApiResponse('Missing or invalid Authorization header', 401, false);
    }

    const rawApiKey = authHeader.replace('Bearer ', '');
    const apiKeyValidation = await validateApiKey(supabase as any, rawApiKey);
    
    if (!apiKeyValidation.isValid) {
      return createApiResponse(apiKeyValidation.error || 'Invalid API key', 401, false);
    }

    // Check rate limits
    const rateLimitCheck = await checkRateLimit(supabase, apiKeyValidation.keyId!, 'minute');
    if (!rateLimitCheck.allowed) {
      return createApiResponse({
        error: 'Rate limit exceeded',
        resetTime: rateLimitCheck.resetTime,
        remaining: rateLimitCheck.remaining
      }, 429, false);
    }

    // Parse multipart form data for file upload
    const formData = await req.formData();
    const file = formData.get('document') as File;
    const agentId = formData.get('agentId') as string;
    console.log('🐛 Debug - Received agentId from formData:', agentId);
    const customPrompt = formData.get('customPrompt') as string;
    const optionsStr = formData.get('options') as string;

    // Validate required fields
    if (!file) {
      return createApiResponse('Missing required field: document', 400, false);
    }

    // Validate file upload
    const fileValidation = await FileUploadValidator.validateUpload(
      file, 
      apiKeyValidation.keyType!
    );

    if (!fileValidation.isValid) {
      // Log failed upload attempt
      await logApiUsage(
        supabase,
        apiKeyValidation.customerId!,
        apiKeyValidation.keyId!,
        'file_upload',
        false,
        0,
        { error: fileValidation.error, fileName: file.name }
      );

      return createApiResponse(fileValidation.error, 400, false);
    }

    // Create processing context
    const context: ProcessingContext = {
      apiKey: apiKeyValidation,
      documentId: crypto.randomUUID(),
      correlationId,
      startTime
    };

    // Store document metadata in database
    console.log('🔍 About to call storeDocumentRecord with agentId:', agentId);
    const documentRecord = await storeDocumentRecord(
      supabase as any,
      context,
      file,
      fileValidation.metadata!,
      agentId
    );

    if (!documentRecord.success) {
      return createApiResponse('Failed to create document record', 500, false);
    }

    // Check if document should be queued based on size and complexity
    const shouldQueue = QueueManager.shouldQueue(file.size, 'medium');
    let processingResult;

    if (shouldQueue) {
      // Queue for asynchronous processing
      processingResult = await queueDocumentProcessing(
        queueManager,
        context,
        file,
        agentId,
        optionsStr ? JSON.parse(optionsStr) : undefined
      );
    } else {
      // Process synchronously
      processingResult = await processDocument(
        supabase as any,
        context,
        file,
        fileValidation.metadata!,
        agentId,
        customPrompt,
        optionsStr ? JSON.parse(optionsStr) : undefined
      );
    }

    // Log successful operation
    await logApiUsage(
      supabase,
      context.apiKey.customerId!,
      context.apiKey.keyId!,
      'document_extraction',
      processingResult.success,
      processingResult.creditsUsed || 1,
      {
        documentId: context.documentId,
        fileName: file.name,
        fileSize: file.size,
        processingTime: performance.now() - startTime
      }
    );

    // Return processing result
    const response: ExtractResponse = {
      success: processingResult.success,
      data: processingResult.success ? {
        documentId: context.documentId,
        extractedData: (processingResult as any).extractedData,
        confidence: (processingResult as any).confidence,
        processingTime: Math.round(performance.now() - startTime),
        model: (processingResult as any).model,
        status: (processingResult.status as 'processing' | 'completed' | 'failed' | 'queued') || 'completed',
        creditsUsed: (processingResult as any).creditsUsed || 1,
        remainingCredits: (processingResult as any).remainingCredits || 0,
        jobId: (processingResult as any).jobId,
        estimatedCompletion: (processingResult as any).estimatedCompletion
      } : undefined,
      error: processingResult.success ? undefined : processingResult.error,
      timestamp: new Date().toISOString()
    };

    return createApiResponse(response.data, processingResult.success ? 200 : 500, processingResult.success);

  } catch (error) {
    console.error('Extract endpoint error:', error);
    
    // Log error for debugging
    await logApiUsage(
      supabase,
      'unknown',
      'unknown',
      'document_extraction',
      false,
      0,
      { 
        error: error instanceof Error ? error.message : 'Unknown error',
        correlationId,
        processingTime: performance.now() - startTime
      }
    );

    return createApiResponse(
      'Internal server error', 
      500, 
      false
    );
  }
});

/**
 * Store document record in database
 */
async function storeDocumentRecord(
  supabase: ReturnType<typeof createClient>,
  context: ProcessingContext,
  file: File,
  metadata: NonNullable<FileValidationResult['metadata']>,
  agentId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log('🔍 Attempting to store document record:', {
      documentId: context.documentId,
      customerId: context.apiKey.customerId,
      filename: file.name,
      fileType: file.type,
      fileSize: file.size,
      agentId: agentId || 'none'
    });

    // Lookup agent UUID by agent_id if agentId is provided
    let agentUuid: string | null = null;
    if (agentId) {
      const { data: agent, error: agentError } = await (supabase.from('agents') as any)
        .select('id')
        .eq('agent_id', agentId)
        .eq('is_default', true)
        .single();

      if (agentError) {
        console.error('🚨 Agent lookup error:', agentError);
        console.error('🔍 Tried to find agent with agent_id:', agentId);
        // List available agents for debugging
        const { data: availableAgents } = await supabase.from('agents').select('agent_id, name').eq('is_default', true);
        console.error('🔍 Available default agents:', availableAgents);
        return { success: false, error: `Agent not found: ${agentId}` };
      }

      agentUuid = agent.id;
      console.log('✅ Agent UUID resolved:', { agentId, agentUuid });
    }

    const { error } = await (supabase.from('documents') as any).insert({
      id: context.documentId,
      customer_id: context.apiKey.customerId,
      document_hash: metadata.hash || '',
      original_filename: file.name,
      file_size: file.size,
      mime_type: file.type,
      agent_id: agentUuid,
      processed_at: new Date().toISOString(),
      storage_path: `temp/${context.correlationId}/${file.name}`,
      status: 'processing'
    });

    if (error) {
      console.error('🚨 Database insert error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      return { success: false, error: `Database error: ${error.message}` };
    }

    console.log('✅ Document record stored successfully');
    return { success: true };

  } catch (error) {
    console.error('🚨 Store document record exception:', error);
    return { success: false, error: 'Database operation failed' };
  }
}

/**
 * Queue document for asynchronous processing
 */
async function queueDocumentProcessing(
  queueManager: QueueManager,
  context: ProcessingContext,
  file: File,
  agentId?: string,
  options?: Record<string, unknown>
): Promise<{
  success: boolean;
  status: string;
  jobId?: string;
  estimatedCompletion?: string;
  creditsUsed?: number;
  remainingCredits?: number;
  error?: string;
}> {
  try {
    console.log(`Queuing large document: ${file.name} (${file.size} bytes)`);

    // Deduct credits for queuing (same as synchronous processing)
    const creditsToUse = 1;
    const creditResult = await deductCredits(supabase, context.apiKey.keyId!, creditsToUse);
    
    if (!creditResult.success) {
      return {
        success: false,
        status: 'failed',
        error: creditResult.error || 'Insufficient credits'
      };
    }

    // Enqueue the job
    const enqueueResult = await queueManager.enqueueJob({
      customerId: context.apiKey.customerId!,
      apiKeyId: context.apiKey.keyId!,
      documentId: context.documentId,
      agentId: agentId || undefined,
      jobType: 'document_processing',
      jobData: {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        options: options || {},
        webhookUrl: options?.webhook as string || undefined
      },
      webhookUrl: options?.webhook as string || undefined,
      maxRetries: (options?.maxRetries as number) || 3
    });

    if (!enqueueResult.success) {
      // Refund credits if queueing fails
      const { data: currentKey } = await supabase.from('api_keys')
        .select('credits')
        .eq('id', context.apiKey.keyId || '')
        .single();
      
      if (currentKey) {
        await supabase.from('api_keys')
          .update({ credits: currentKey.credits + creditsToUse })
          .eq('id', context.apiKey.keyId || '');
      }

      return {
        success: false,
        status: 'failed',
        error: enqueueResult.error || 'Failed to queue job'
      };
    }

    // Update document status to queued
    await (supabase.from('documents') as any).update({
      status: 'processing' // Will be updated to 'queued' by queue trigger
    }).eq('id', context.documentId);

    // Calculate estimated completion time
    const metricsResult = await queueManager.getQueueMetrics();
    const avgProcessingTime = metricsResult.metrics?.avgProcessingTimeSeconds || 60;
    const queueDepth = metricsResult.metrics?.totalQueued || 0;
    const estimatedSeconds = (queueDepth * avgProcessingTime) + avgProcessingTime;
    const estimatedCompletion = new Date(Date.now() + (estimatedSeconds * 1000));

    return {
      success: true,
      status: 'queued',
      jobId: enqueueResult.jobId,
      estimatedCompletion: estimatedCompletion.toISOString(),
      creditsUsed: creditsToUse,
      remainingCredits: creditResult.remainingCredits || 0
    };

  } catch (error) {
    console.error('Queue document processing error:', error);
    return {
      success: false,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Queueing failed'
    };
  }
}

async function processDocument(
  supabase: ReturnType<typeof createClient>,
  context: ProcessingContext,
  file: File,
  metadata: NonNullable<FileValidationResult['metadata']>,
  agentId?: string,
  customPrompt?: string,
  options?: Record<string, unknown>
): Promise<{
  success: boolean;
  extractedData?: Record<string, unknown>;
  confidence?: number;
  model?: string;
  status?: string;
  creditsUsed?: number;
  remainingCredits?: number;
  error?: string;
}> {
  // Processing modules are now imported statically at the top of the file

  try {
    // Initialize status tracking
    await initializeProcessingStatus(context.documentId, {
      fileName: file.name,
      fileSize: file.size,
      agentId: agentId || 'default',
      retryCount: 0,
      customerId: context.apiKey.customerId!,
      apiKeyId: context.apiKey.keyId!,
      correlationId: context.correlationId
    });

    // Update status to processing
    await updateProcessingStatus({
      documentId: context.documentId,
      status: 'processing',
      stage: 'text_extraction'
    });

    // Step 1: Extract text from document
    const extractionResult = await extractTextFromDocument(file, {
      preserveFormatting: true,
      extractImages: false,
      timeout: 30000
    });

    if (!extractionResult.success || !extractionResult.content) {
      await completeProcessingStage(context.documentId, 'text_extraction', false, extractionResult.error);
      throw new Error(`Text extraction failed: ${extractionResult.error}`);
    }

    await completeProcessingStage(context.documentId, 'text_extraction', true);

    // Step 2: Check cache for existing results
    await updateProcessingStatus({
      documentId: context.documentId,
      stage: 'cache_check'
    });

    const cacheResult = await checkDocumentCache(
      metadata.hash || '',
      extractionResult.content.text,
      agentId || 'default',
      {
        enableVectorSimilarity: true,
        similarityThreshold: 0.95,
        maxCacheAge: 24
      }
    );

    if (cacheResult.found) {
      await completeProcessingStage(context.documentId, 'cache_check', true);
      
      // Return cached result
      await updateProcessingStatus({
        documentId: context.documentId,
        status: 'completed',
        progress: 100
      });

      return {
        success: true,
        extractedData: cacheResult.entry!.extractedData,
        confidence: cacheResult.entry!.confidence,
        model: `${cacheResult.entry!.model} (cached)`,
        status: 'completed',
        creditsUsed: 0, // No credits used for cached results
        remainingCredits: await getCurrentCredits(supabase, context.apiKey.keyId!)
      };
    }

    await completeProcessingStage(context.documentId, 'cache_check', true);

    // Step 3: Get agent configuration
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('agent_id', agentId || 'default-invoice')
      .eq('is_default', true)
      .single();

    if (agentError || !agent) {
      throw new Error(`Agent not found: ${agentId}`);
    }

    // Step 4: Preprocess content
    await updateProcessingStatus({
      documentId: context.documentId,
      stage: 'preprocessing'
    });

    const preprocessedContent = await preprocessContent(
      extractionResult.content,
      agent,
      {
        maxLength: 16000,
        preserveFormatting: false,
        removeNoise: true,
        normalizeWhitespace: true,
        enhanceStructure: true,
        includeSchema: true
      }
    );

    await completeProcessingStage(context.documentId, 'preprocessing', true);

    // Step 5: Process with AI
    await updateProcessingStatus({
      documentId: context.documentId,
      stage: 'ai_processing'
    });

    const aiResult = await processWithAI({
      content: preprocessedContent.processedText,
      agent: agent,
      customerId: context.apiKey.customerId!,
      documentType: agent.category,
      correlationId: context.correlationId,
      options: {
        timeout: 45000,
        maxRetries: 3,
        temperature: 0.1,
        maxTokens: 2048,
        ...options
      }
    });

    if (!aiResult.success) {
      await completeProcessingStage(context.documentId, 'ai_processing', false, aiResult.error);
      throw new Error(`AI processing failed: ${aiResult.error}`);
    }

    await completeProcessingStage(context.documentId, 'ai_processing', true);

    // Step 6: Format and validate output
    await updateProcessingStatus({
      documentId: context.documentId,
      stage: 'output_formatting'
    });

    const formattedOutput = await formatOutput(aiResult, agent, {
      strictValidation: true,
      includeRawData: false,
      normalizeTypes: true,
      fillMissingFields: true,
      removeExtraFields: false
    });

    await completeProcessingStage(context.documentId, 'output_formatting', true);

    // Step 7: Calculate credits and deduct
    const creditsToUse = Math.max(1, Math.ceil(aiResult.cost.customerPrice * 10)); // Convert price to credits
    
    const creditResult = await deductCredits(supabase, context.apiKey.keyId!, creditsToUse);
    
    if (!creditResult.success) {
      throw new Error(`Insufficient credits: ${creditResult.error}`);
    }

    // Step 8: Store results and cache
    await updateProcessingStatus({
      documentId: context.documentId,
      stage: 'storage'
    });

    // Update document record with results
    await (supabase.from('documents') as any).update({
      status: 'completed',
      processing_completed_at: new Date().toISOString(),
      extraction_result: formattedOutput.extractedData,
      extraction_confidence: formattedOutput.confidence,
      model_used: aiResult.model,
      processing_cost: aiResult.cost.modelCost,
      customer_price: aiResult.cost.customerPrice
    }).eq('id', context.documentId);

    // Store in cache for future use
    await storeInCache(
      metadata.hash || '',
      extractionResult.content.text,
      (agent as any).agent_id,
      formattedOutput,
      {
        enableVectorSimilarity: true,
        maxCacheAge: 24
      }
    );

    await completeProcessingStage(context.documentId, 'storage', true);

    // Complete processing
    await updateProcessingStatus({
      documentId: context.documentId,
      status: 'completed',
      progress: 100
    });

    return {
      success: true,
      extractedData: formattedOutput.extractedData,
      confidence: formattedOutput.confidence,
      model: aiResult.model,
      status: 'completed',
      creditsUsed: creditsToUse,
      remainingCredits: creditResult.remainingCredits || 0
    };

  } catch (error) {
    console.error('Document processing error:', error);
    
    // Update status to failed
    await updateProcessingStatus({
      documentId: context.documentId,
      status: 'failed',
      error: error instanceof Error ? error.message : 'Processing failed'
    });
    
    // Update document status to failed
    await (supabase.from('documents') as any).update({
      status: 'failed',
      error_message: error instanceof Error ? error.message : 'Processing failed',
      processing_completed_at: new Date().toISOString()
    }).eq('id', context.documentId);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Document processing failed'
    };
  }
}

/**
 * Helper function to get current credits for an API key
 */
async function getCurrentCredits(
  supabase: ReturnType<typeof createClient>,
  apiKeyId: string
): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('api_keys')
      .select('credits')
      .eq('id', apiKeyId)
      .single();

    if (error || !data) {
      console.error('Failed to get current credits:', error);
      return 0;
    }

    return (data as any)?.credits || 0;
  } catch (error) {
    console.error('Error getting current credits:', error);
    return 0;
  }
}