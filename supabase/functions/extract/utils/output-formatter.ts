/**
 * Output Formatter & JSON Schema Validation
 * Standardizes AI responses and validates against agent schemas
 * Issue #10: Basic Document Processing
 */

import type { Agent } from './content-preprocessor.ts';
import type { ProcessingResult } from './ai-processor.ts';

// Type definitions
export interface FormattedOutput {
  success: boolean;
  extractedData: Record<string, unknown>;
  confidence: number;
  validationResults: ValidationResults;
  metadata: OutputMetadata;
  schema: Record<string, unknown>;
  timestamp: string;
}

export interface ValidationResults {
  isValid: boolean;
  score: number;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  missingFields: string[];
  extraFields: string[];
  fieldValidation: Record<string, FieldValidation>;
}

export interface ValidationError {
  field: string;
  message: string;
  expectedType: string;
  actualType: string;
  path: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

export interface FieldValidation {
  present: boolean;
  valid: boolean;
  type: string;
  value: unknown;
  errors: string[];
}

export interface OutputMetadata {
  processingTime: number;
  model: string;
  agentUsed: string;
  tokenUsage: {
    input: number;
    output: number;
    total: number;
  };
  cost: {
    model: number;
    customer: number;
    margin: number;
  };
  qualityIndicators: QualityIndicators;
  rawResponse?: any;
}

export interface QualityIndicators {
  completeness: number;
  accuracy: number;
  consistency: number;
  confidence: number;
  overall: number;
}

export interface FormattingOptions {
  strictValidation?: boolean;
  includeRawData?: boolean;
  normalizeTypes?: boolean;
  fillMissingFields?: boolean;
  removeExtraFields?: boolean;
}

/**
 * Main output formatting function
 */
export async function formatOutput(
  processingResult: ProcessingResult,
  agent: Agent,
  options: FormattingOptions = {}
): Promise<FormattedOutput> {
  
  const opts: Required<FormattingOptions> = {
    strictValidation: true,
    includeRawData: false,
    normalizeTypes: true,
    fillMissingFields: true,
    removeExtraFields: false,
    ...options
  };

  try {
    // Extract and normalize data
    let extractedData = processingResult.extractedData || {};
    
    if (opts.normalizeTypes) {
      extractedData = normalizeDataTypes(extractedData, agent.json_schema);
    }

    // Validate against schema
    const validationResults = await validateAgainstSchema(
      extractedData,
      agent.json_schema,
      opts
    );

    // Apply corrections if needed
    if (opts.fillMissingFields || opts.removeExtraFields) {
      extractedData = applyCorrections(
        extractedData,
        validationResults,
        agent.json_schema,
        opts
      );
    }

    // Calculate quality indicators
    const qualityIndicators = calculateQualityIndicators(
      extractedData,
      validationResults,
      processingResult
    );

    // Build metadata
    const metadata: OutputMetadata = {
      processingTime: processingResult.processingTime,
      model: processingResult.model,
      agentUsed: agent.id,
      tokenUsage: {
        input: processingResult.tokenUsage.inputTokens,
        output: processingResult.tokenUsage.outputTokens,
        total: processingResult.tokenUsage.totalTokens
      },
      cost: {
        model: processingResult.cost.modelCost,
        customer: processingResult.cost.customerPrice,
        margin: processingResult.cost.profitMargin
      },
      qualityIndicators
    };

    // Include raw data if requested
    if (opts.includeRawData) {
      metadata.rawResponse = processingResult.extractedData;
    }

    return {
      success: validationResults.isValid || !opts.strictValidation,
      extractedData,
      confidence: Math.max(processingResult.confidence, qualityIndicators.overall),
      validationResults,
      metadata,
      schema: agent.json_schema,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    return {
      success: false,
      extractedData: {},
      confidence: 0,
      validationResults: {
        isValid: false,
        score: 0,
        errors: [{
          field: 'root',
          message: error instanceof Error ? error.message : 'Formatting failed',
          expectedType: 'object',
          actualType: 'error',
          path: '$'
        }],
        warnings: [],
        missingFields: [],
        extraFields: [],
        fieldValidation: {}
      },
      metadata: {
        processingTime: processingResult.processingTime,
        model: processingResult.model,
        agentUsed: agent.id,
        tokenUsage: { input: 0, output: 0, total: 0 },
        cost: { model: 0, customer: 0, margin: 0 },
        qualityIndicators: { completeness: 0, accuracy: 0, consistency: 0, confidence: 0, overall: 0 }
      },
      schema: agent.json_schema,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Validate extracted data against JSON schema
 */
async function validateAgainstSchema(
  data: Record<string, unknown>,
  schema: Record<string, unknown>,
  options: Required<FormattingOptions>
): Promise<ValidationResults> {
  
  const results: ValidationResults = {
    isValid: true,
    score: 1.0,
    errors: [],
    warnings: [],
    missingFields: [],
    extraFields: [],
    fieldValidation: {}
  };

  try {
    const schemaProps = schema.properties as Record<string, any> || {};
    const requiredFields = (schema.required as string[]) || [];
    const dataKeys = Object.keys(data);
    const schemaKeys = Object.keys(schemaProps);

    // Check required fields
    for (const field of requiredFields) {
      if (!(field in data) || data[field] === null || data[field] === undefined) {
        results.missingFields.push(field);
        results.errors.push({
          field,
          message: `Required field '${field}' is missing`,
          expectedType: schemaProps[field]?.type || 'unknown',
          actualType: 'undefined',
          path: `$.${field}`
        });
      }
    }

    // Check extra fields
    for (const field of dataKeys) {
      if (!(field in schemaProps)) {
        results.extraFields.push(field);
        if (options.strictValidation) {
          results.warnings.push({
            field,
            message: `Extra field '${field}' not defined in schema`,
            suggestion: 'Consider removing or adding to schema'
          });
        }
      }
    }

    // Validate each field
    for (const [field, value] of Object.entries(data)) {
      const fieldSchema = schemaProps[field];
      const validation = validateField(field, value, fieldSchema);
      results.fieldValidation[field] = validation;

      if (!validation.valid) {
        results.errors.push(...validation.errors.map(error => ({
          field,
          message: error,
          expectedType: fieldSchema?.type || 'unknown',
          actualType: typeof value,
          path: `$.${field}`
        })));
      }
    }

    // Calculate overall validity and score
    results.isValid = results.errors.length === 0;
    results.score = calculateValidationScore(results, schemaKeys.length, dataKeys.length);

    return results;

  } catch (error) {
    results.isValid = false;
    results.score = 0;
    results.errors.push({
      field: 'schema',
      message: `Schema validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      expectedType: 'object',
      actualType: 'error',
      path: '$'
    });
    
    return results;
  }
}

/**
 * Validate individual field against schema definition
 */
function validateField(
  fieldName: string,
  value: unknown,
  fieldSchema: any
): FieldValidation {
  
  const validation: FieldValidation = {
    present: value !== undefined && value !== null,
    valid: true,
    type: typeof value,
    value,
    errors: []
  };

  if (!validation.present) {
    validation.valid = false;
    validation.errors.push('Field is missing or null');
    return validation;
  }

  if (!fieldSchema) {
    validation.errors.push('No schema definition found');
    return validation;
  }

  const expectedType = fieldSchema.type;
  const actualType = Array.isArray(value) ? 'array' : typeof value;

  // Type validation
  switch (expectedType) {
    case 'string': {
      }
      if (typeof value !== 'string') {
        validation.valid = false;
        validation.errors.push(`Expected string, got ${actualType}`);
      } else {
        // Additional string validations
        if (fieldSchema.format === 'date' && !isValidDate(value)) {
          validation.errors.push('Invalid date format');
        }
        if (fieldSchema.format === 'email' && !isValidEmail(value)) {
          validation.errors.push('Invalid email format');
        }
        if (fieldSchema.minLength && value.length < fieldSchema.minLength) {
          validation.errors.push(`String too short (min: ${fieldSchema.minLength})`);
        }
        if (fieldSchema.maxLength && value.length > fieldSchema.maxLength) {
          validation.errors.push(`String too long (max: ${fieldSchema.maxLength})`);
        }
      }
      break;
    }

    case 'number': {
      }
      if (typeof value !== 'number' || isNaN(value)) {
        validation.valid = false;
        validation.errors.push(`Expected number, got ${actualType}`);
      } else {
        if (fieldSchema.minimum !== undefined && value < fieldSchema.minimum) {
          validation.errors.push(`Number too small (min: ${fieldSchema.minimum})`);
        }
        if (fieldSchema.maximum !== undefined && value > fieldSchema.maximum) {
          validation.errors.push(`Number too large (max: ${fieldSchema.maximum})`);
        }
      }
      break;
    }

    case 'boolean': {
      }
      if (typeof value !== 'boolean') {
        validation.valid = false;
        validation.errors.push(`Expected boolean, got ${actualType}`);
      }
      break;
    }

    case 'array': {
      }
      if (!Array.isArray(value)) {
        validation.valid = false;
        validation.errors.push(`Expected array, got ${actualType}`);
      } else {
        if (fieldSchema.minItems && value.length < fieldSchema.minItems) {
          validation.errors.push(`Array too short (min: ${fieldSchema.minItems})`);
        }
        if (fieldSchema.maxItems && value.length > fieldSchema.maxItems) {
          validation.errors.push(`Array too long (max: ${fieldSchema.maxItems})`);
        }
      }
      break;
    }

    case 'object': {
      }
      if (typeof value !== 'object' || Array.isArray(value)) {
        validation.valid = false;
        validation.errors.push(`Expected object, got ${actualType}`);
      }
      break;
    }

    default:
      // Unknown type, accept as-is but warn
      validation.errors.push(`Unknown expected type: ${expectedType}`);
  }

  return validation;
}

/**
 * Normalize data types based on schema expectations
 */
function normalizeDataTypes(
  data: Record<string, unknown>,
  schema: Record<string, unknown>
): Record<string, unknown> {
  
  const normalized: Record<string, unknown> = { ...data };
  const schemaProps = schema.properties as Record<string, any> || {};

  for (const [field, value] of Object.entries(normalized)) {
    const fieldSchema = schemaProps[field];
    if (!fieldSchema || value === null || value === undefined) continue;

    const expectedType = fieldSchema.type;
    const currentValue = value;

    try {
      switch (expectedType) {
        case 'string': {
      }
          if (typeof currentValue !== 'string') {
            normalized[field] = String(currentValue);
          }
          break;
    }

        case 'number': {
      }
          if (typeof currentValue === 'string') {
            const parsed = parseFloat(currentValue.replace(/[$,]/g, ''));
            if (!isNaN(parsed)) {
              normalized[field] = parsed;
            }
          } else if (typeof currentValue !== 'number') {
            const parsed = Number(currentValue);
            if (!isNaN(parsed)) {
              normalized[field] = parsed;
            }
          }
          break;
    }

        case 'boolean': {
      }
          if (typeof currentValue === 'string') {
            const lower = currentValue.toLowerCase();
            normalized[field] = lower === 'true' || lower === 'yes' || lower === '1';
          } else if (typeof currentValue !== 'boolean') {
            normalized[field] = Boolean(currentValue);
          }
          break;
    }

        case 'array': {
      }
          if (!Array.isArray(currentValue)) {
            // Try to convert to array
            if (typeof currentValue === 'string') {
              // Split on common delimiters
              normalized[field] = currentValue.split(/[,;|]/).map(s => s.trim()).filter(s => s);
            } else {
              normalized[field] = [currentValue];
            }
          }
          break;
    }
      }
    } catch {
      // Keep original value if normalization fails
      console.warn(`Failed to normalize ${field}:`, error);
    }
  }

  return normalized;
}

/**
 * Apply corrections to data based on validation results
 */
function applyCorrections(
  data: Record<string, unknown>,
  validation: ValidationResults,
  schema: Record<string, unknown>,
  options: Required<FormattingOptions>
): Record<string, unknown> {
  
  const corrected: Record<string, unknown> = { ...data };
  const schemaProps = schema.properties as Record<string, any> || {};

  // Fill missing required fields with defaults
  if (options.fillMissingFields) {
    for (const field of validation.missingFields) {
      const fieldSchema = schemaProps[field];
      if (fieldSchema) {
        corrected[field] = getDefaultValue(fieldSchema);
      }
    }
  }

  // Remove extra fields
  if (options.removeExtraFields) {
    for (const field of validation.extraFields) {
      delete corrected[field];
    }
  }

  return corrected;
}

/**
 * Get default value for a schema field
 */
function getDefaultValue(fieldSchema: any): unknown {
  if (fieldSchema.default !== undefined) {
    return fieldSchema.default;
  }

  switch (fieldSchema.type) {
    case 'string': {
      }
      return '';
    }
    case 'number': {
      }
      return 0;
    }
    case 'boolean': {
      }
      return false;
    }
    case 'array': {
      }
      return [];
    }
    case 'object': {
      }
      return {};
    }
    default:
      return null;
  }
}

/**
 * Calculate validation score (0-1)
 */
function calculateValidationScore(
  results: ValidationResults,
  expectedFields: number,
  actualFields: number
): number {
  
  if (expectedFields === 0) return 1.0;

  let score = 1.0;

  // Penalize missing required fields
  score -= (results.missingFields.length / expectedFields) * 0.5;

  // Penalize validation errors
  score -= (results.errors.length / Math.max(actualFields, 1)) * 0.3;

  // Small penalty for extra fields
  score -= (results.extraFields.length / Math.max(actualFields, 1)) * 0.1;

  return Math.max(0, Math.min(1, score));
}

/**
 * Calculate quality indicators
 */
function calculateQualityIndicators(
  data: Record<string, unknown>,
  validation: ValidationResults,
  processingResult: ProcessingResult
): QualityIndicators {
  
  // Completeness: how much of expected data is present
  const completeness = validation.score;

  // Accuracy: based on validation errors and data quality
  const accuracy = Math.max(0, 1 - (validation.errors.length * 0.2));

  // Consistency: based on type consistency and format
  const consistency = Object.values(validation.fieldValidation)
    .reduce((sum, field) => sum + (field.valid ? 1 : 0), 0) / 
    Math.max(Object.keys(validation.fieldValidation).length, 1);

  // Confidence: from AI processing result
  const confidence = processingResult.confidence;

  // Overall quality score
  const overall = (completeness * 0.3 + accuracy * 0.3 + consistency * 0.2 + confidence * 0.2);

  return {
    completeness: Math.round(completeness * 100) / 100,
    accuracy: Math.round(accuracy * 100) / 100,
    consistency: Math.round(consistency * 100) / 100,
    confidence: Math.round(confidence * 100) / 100,
    overall: Math.round(overall * 100) / 100
  };
}

/**
 * Validation helper functions
 */
function isValidDate(value: string): boolean {
  const date = new Date(value);
  return !isNaN(date.getTime()) && date.getFullYear() > 1900 && date.getFullYear() < 2100;
}

function isValidEmail(value: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

/**
 * Export schema validation for external use
 */
export function validateSchema(schema: Record<string, unknown>): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!schema.type || schema.type !== 'object') {
    errors.push('Schema must be of type "object"');
  }

  if (!schema.properties || typeof schema.properties !== 'object') {
    errors.push('Schema must have "properties" field');
  }

  if (schema.required && !Array.isArray(schema.required)) {
    errors.push('Schema "required" field must be an array');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}