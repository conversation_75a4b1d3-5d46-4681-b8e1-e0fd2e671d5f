/**
 * Processing Status Tracking Utility
 * Tracks document processing lifecycle and provides real-time updates
 * Issue #10: Basic Document Processing
 */

import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';

// Type definitions
export interface ProcessingStatus {
  documentId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  stage: ProcessingStage;
  progress: number; // 0-100
  startedAt?: Date;
  completedAt?: Date;
  processingTime?: number;
  error?: string;
  metadata: StatusMetadata;
}

export interface ProcessingStage {
  current: string;
  stages: StageInfo[];
  currentIndex: number;
  totalStages: number;
}

export interface StageInfo {
  name: string;
  description: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  duration?: number;
  error?: string;
}

export interface StatusMetadata {
  fileName: string;
  fileSize: number;
  agentId?: string;
  model?: string;
  retryCount: number;
  customerId: string;
  apiKeyId: string;
  correlationId: string;
}

export interface StatusUpdate {
  documentId: string;
  status?: ProcessingStatus['status'];
  stage?: string;
  progress?: number;
  error?: string;
  metadata?: Partial<StatusMetadata>;
}

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

// Processing stages configuration
const PROCESSING_STAGES: Omit<StageInfo, 'status' | 'startedAt' | 'completedAt' | 'duration'>[] = [
  {
    name: 'validation',
    description: 'Validating file upload and API key'
  },
  {
    name: 'text_extraction',
    description: 'Extracting text from document'
  },
  {
    name: 'cache_check',
    description: 'Checking cache for existing results'
  },
  {
    name: 'preprocessing',
    description: 'Cleaning and preparing content for AI'
  },
  {
    name: 'ai_processing',
    description: 'Processing with AI models'
  },
  {
    name: 'output_formatting',
    description: 'Formatting and validating results'
  },
  {
    name: 'storage',
    description: 'Storing results and updating cache'
  }
];

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

/**
 * Initialize processing status tracking
 */
export async function initializeProcessingStatus(
  documentId: string,
  metadata: StatusMetadata
): Promise<{ success: boolean; error?: string }> {
  
  try {
    const initialStages: StageInfo[] = PROCESSING_STAGES.map(stage => ({
      ...stage,
      status: 'pending' as const
    }));

    const status: ProcessingStatus = {
      documentId,
      status: 'queued',
      stage: {
        current: 'validation',
        stages: initialStages,
        currentIndex: 0,
        totalStages: initialStages.length
      },
      progress: 0,
      metadata
    };

    // Store initial status in database
    const { error } = await supabase
      .from('processing_status')
      .insert({
        document_id: documentId,
        status: status.status,
        current_stage: status.stage.current,
        stage_index: status.stage.currentIndex,
        progress: status.progress,
        stages: status.stage.stages,
        metadata: status.metadata,
        created_at: new Date().toISOString()
      });

    if (error) {
      throw error;
    }

    // Send real-time update
    await broadcastStatusUpdate(status);

    return { success: true };

  } catch (error) {
    console.error('Failed to initialize processing status:', error);
    // Gracefully handle missing table - don't fail the entire process
    if (error?.code === 'PGRST205') {
      console.log('Processing status table not found - continuing without status tracking');
      return { success: true };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Status initialization failed'
    };
  }
}

/**
 * Update processing status
 */
export async function updateProcessingStatus(
  update: StatusUpdate
): Promise<{ success: boolean; error?: string }> {
  
  try {
    // Get current status
    const { data: currentData, error: fetchError } = await supabase
      .from('processing_status')
      .select('*')
      .eq('document_id', update.documentId)
      .single();

    if (fetchError || !currentData) {
      throw fetchError || new Error('Processing status not found');
    }

    const currentStatus: ProcessingStatus = {
      documentId: currentData.document_id,
      status: currentData.status,
      stage: {
        current: currentData.current_stage,
        stages: currentData.stages,
        currentIndex: currentData.stage_index,
        totalStages: currentData.stages.length
      },
      progress: currentData.progress,
      startedAt: currentData.started_at ? new Date(currentData.started_at) : undefined,
      completedAt: currentData.completed_at ? new Date(currentData.completed_at) : undefined,
      processingTime: currentData.processing_time,
      error: currentData.error_message,
      metadata: currentData.metadata
    };

    // Apply updates
    if (update.status) {
      currentStatus.status = update.status;
      
      if (update.status === 'processing' && !currentStatus.startedAt) {
        currentStatus.startedAt = new Date();
      }
      
      if (update.status === 'completed' || update.status === 'failed') {
        currentStatus.completedAt = new Date();
        if (currentStatus.startedAt) {
          currentStatus.processingTime = currentStatus.completedAt.getTime() - currentStatus.startedAt.getTime();
        }
      }
    }

    if (update.stage) {
      const stageIndex = PROCESSING_STAGES.findIndex(s => s.name === update.stage);
      if (stageIndex !== -1) {
        // Mark previous stages as completed
        for (let i = 0; i < stageIndex; i++) {
          if (currentStatus.stage.stages[i].status === 'running') {
            currentStatus.stage.stages[i].status = 'completed';
            currentStatus.stage.stages[i].completedAt = new Date();
            if (currentStatus.stage.stages[i].startedAt) {
              const duration = new Date().getTime() - currentStatus.stage.stages[i].startedAt!.getTime();
              currentStatus.stage.stages[i].duration = duration;
            }
          }
        }

        // Update current stage
        currentStatus.stage.current = update.stage;
        currentStatus.stage.currentIndex = stageIndex;
        currentStatus.stage.stages[stageIndex].status = 'running';
        currentStatus.stage.stages[stageIndex].startedAt = new Date();
      }
    }

    if (update.progress !== undefined) {
      currentStatus.progress = Math.max(0, Math.min(100, update.progress));
    }

    if (update.error) {
      currentStatus.error = update.error;
      // Mark current stage as failed
      const currentStageIndex = currentStatus.stage.currentIndex;
      if (currentStageIndex < currentStatus.stage.stages.length) {
        currentStatus.stage.stages[currentStageIndex].status = 'failed';
        currentStatus.stage.stages[currentStageIndex].error = update.error;
        currentStatus.stage.stages[currentStageIndex].completedAt = new Date();
      }
    }

    if (update.metadata) {
      currentStatus.metadata = { ...currentStatus.metadata, ...update.metadata };
    }

    // Update database
    const { error: updateError } = await supabase
      .from('processing_status')
      .update({
        status: currentStatus.status,
        current_stage: currentStatus.stage.current,
        stage_index: currentStatus.stage.currentIndex,
        progress: currentStatus.progress,
        stages: currentStatus.stage.stages,
        started_at: currentStatus.startedAt?.toISOString(),
        completed_at: currentStatus.completedAt?.toISOString(),
        processing_time: currentStatus.processingTime,
        error_message: currentStatus.error,
        metadata: currentStatus.metadata,
        updated_at: new Date().toISOString()
      })
      .eq('document_id', update.documentId);

    if (updateError) {
      throw updateError;
    }

    // Send real-time update
    await broadcastStatusUpdate(currentStatus);

    return { success: true };

  } catch (error) {
    console.error('Failed to update processing status:', error);
    // Gracefully handle missing table - don't fail the entire process
    if (error?.code === 'PGRST205') {
      console.log('Processing status table not found - continuing without status tracking');
      return { success: true };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Status update failed'
    };
  }
}

/**
 * Complete processing stage
 */
export async function completeProcessingStage(
  documentId: string,
  stageName: string,
  success: boolean = true,
  error?: string
): Promise<{ success: boolean; error?: string }> {
  
  const stageIndex = PROCESSING_STAGES.findIndex(s => s.name === stageName);
  if (stageIndex === -1) {
    return { success: false, error: `Unknown stage: ${stageName}` };
  }

  try {
    const { data, error: fetchError } = await supabase
      .from('processing_status')
      .select('stages, stage_index')
      .eq('document_id', documentId)
      .single();

    if (fetchError || !data) {
      throw fetchError || new Error('Processing status not found');
    }

    const stages = [...data.stages];
    if (stageIndex < stages.length) {
      stages[stageIndex].status = success ? 'completed' : 'failed';
      stages[stageIndex].completedAt = new Date();
      if (error) {
        stages[stageIndex].error = error;
      }
      if (stages[stageIndex].startedAt) {
        stages[stageIndex].duration = new Date().getTime() - new Date(stages[stageIndex].startedAt!).getTime();
      }
    }

    // Calculate progress
    const completedStages = stages.filter(s => s.status === 'completed').length;
    const progress = Math.round((completedStages / stages.length) * 100);

    // Determine next stage
    const nextStageIndex = stageIndex + 1;
    const nextStageName = nextStageIndex < stages.length ? PROCESSING_STAGES[nextStageIndex].name : null;

    const updateData: any = {
      stages,
      progress,
      updated_at: new Date().toISOString()
    };

    if (nextStageName && success) {
      updateData.current_stage = nextStageName;
      updateData.stage_index = nextStageIndex;
    }

    if (!success) {
      updateData.status = 'failed';
      updateData.error_message = error;
    }

    const { error: updateError } = await supabase
      .from('processing_status')
      .update(updateData)
      .eq('document_id', documentId);

    if (updateError) {
      throw updateError;
    }

    return { success: true };

  } catch (error) {
    console.error('Failed to complete processing stage:', error);
    // Gracefully handle missing table - don't fail the entire process
    if (error?.code === 'PGRST205') {
      console.log('Processing status table not found - continuing without status tracking');
      return { success: true };
    }
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Stage completion failed'
    };
  }
}

/**
 * Get current processing status
 */
export async function getProcessingStatus(documentId: string): Promise<ProcessingStatus | null> {
  try {
    const { data, error } = await supabase
      .from('processing_status')
      .select('*')
      .eq('document_id', documentId)
      .single();

    if (error || !data) {
      return null;
    }

    return {
      documentId: data.document_id,
      status: data.status,
      stage: {
        current: data.current_stage,
        stages: data.stages,
        currentIndex: data.stage_index,
        totalStages: data.stages.length
      },
      progress: data.progress,
      startedAt: data.started_at ? new Date(data.started_at) : undefined,
      completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
      processingTime: data.processing_time,
      error: data.error_message,
      metadata: data.metadata
    };

  } catch {
    console.error('Failed to get processing status:', error);
    return null;
  }
}

/**
 * Broadcast status update via real-time channels
 */
async function broadcastStatusUpdate(status: ProcessingStatus): Promise<void> {
  try {
    // Broadcast to customer-specific channel
    await supabase.channel(`processing:${status.metadata.customerId}`)
      .send({
        type: 'broadcast',
        event: 'status_update',
        payload: {
          documentId: status.documentId,
          status: status.status,
          stage: status.stage.current,
          progress: status.progress,
          error: status.error,
          timestamp: new Date().toISOString()
        }
      });

    // Also broadcast to document-specific channel
    await supabase.channel(`document:${status.documentId}`)
      .send({
        type: 'broadcast',
        event: 'status_update',
        payload: status
      });

  } catch {
    console.error('Failed to broadcast status update:', error);
    // Non-critical error, don't throw
  }
}

/**
 * Clean up old processing status records
 */
export async function cleanupOldStatusRecords(olderThanHours: number = 24): Promise<{ deleted: number; error?: string }> {
  try {
    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - olderThanHours);

    const { data, error } = await supabase
      .from('processing_status')
      .delete()
      .lt('created_at', cutoffTime.toISOString())
      .in('status', ['completed', 'failed']);

    if (error) {
      throw error;
    }

    return {
      deleted: data ? (data as any)?.length || 0 : 0
    };

  } catch {
    return {
      deleted: 0,
      error: error instanceof Error ? error.message : 'Cleanup failed'
    };
  }
}

/**
 * Get processing statistics
 */
export async function getProcessingStats(): Promise<{
  totalProcessed: number;
  successRate: number;
  averageProcessingTime: number;
  stageStats: Record<string, { success: number; failure: number; avgDuration: number }>;
}> {
  try {
    const { data, error } = await supabase.rpc('get_processing_statistics');

    if (error || !data) {
      throw error || new Error('No processing statistics available');
    }

    return data;

  } catch {
    console.error('Failed to get processing stats:', error);
    return {
      totalProcessed: 0,
      successRate: 0,
      averageProcessingTime: 0,
      stageStats: {}
    };
  }
}