/**
 * Document Caching System with Vector Similarity
 * Prevents redundant processing of similar documents
 * Issue #10: Basic Document Processing
 */

import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';
import type { FormattedOutput } from './output-formatter.ts';

// Type definitions
export interface CacheEntry {
  id: string;
  documentHash: string;
  contentHash: string;
  embedding?: number[];
  extractedData: Record<string, unknown>;
  confidence: number;
  model: string;
  agentId: string;
  processingTime: number;
  createdAt: string;
  expiresAt: string;
  hitCount: number;
}

export interface CacheResult {
  found: boolean;
  type: 'exact' | 'similar' | 'none';
  entry?: CacheEntry;
  similarity?: number;
  processingTime: number;
}

export interface CacheOptions {
  enableVectorSimilarity?: boolean;
  similarityThreshold?: number;
  maxCacheAge?: number; // hours
  enableHashCache?: boolean;
}

export interface SimilarityResult {
  id: string;
  similarity: number;
  entry: CacheEntry;
}

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

// Constants
const DEFAULT_SIMILARITY_THRESHOLD = 0.95;
const DEFAULT_CACHE_HOURS = 24;
const _EMBEDDING_DIMENSION = 1536; // OpenAI embedding dimension
const MAX_SIMILAR_RESULTS = 5;

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

/**
 * Check cache for existing processing results
 */
export async function checkDocumentCache(
  documentHash: string,
  contentText: string,
  agentId: string,
  options: CacheOptions = {}
): Promise<CacheResult> {
  const startTime = performance.now();
  
  const opts: Required<CacheOptions> = {
    enableVectorSimilarity: true,
    similarityThreshold: DEFAULT_SIMILARITY_THRESHOLD,
    maxCacheAge: DEFAULT_CACHE_HOURS,
    enableHashCache: true,
    ...options
  };

  try {
    // 1. Check for exact hash match first (fastest)
    if (opts.enableHashCache) {
      const exactMatch = await checkExactMatch(documentHash, agentId, opts.maxCacheAge);
      if (exactMatch) {
        await incrementHitCount(exactMatch.id);
        return {
          found: true,
          type: 'exact',
          entry: exactMatch,
          processingTime: performance.now() - startTime
        };
      }
    }

    // 2. Check for vector similarity (if enabled)
    if (opts.enableVectorSimilarity) {
      const embedding = await generateEmbedding(contentText);
      const similarMatch = await checkSimilarDocuments(
        embedding,
        agentId,
        opts.similarityThreshold,
        opts.maxCacheAge
      );
      
      if (similarMatch) {
        await incrementHitCount(similarMatch.entry.id);
        return {
          found: true,
          type: 'similar',
          entry: similarMatch.entry,
          similarity: similarMatch.similarity,
          processingTime: performance.now() - startTime
        };
      }
    }

    // No cache hit
    return {
      found: false,
      type: 'none',
      processingTime: performance.now() - startTime
    };

  } catch (error) {
    console.error('Cache check failed:', error);
    return {
      found: false,
      type: 'none',
      processingTime: performance.now() - startTime
    };
  }
}

/**
 * Store processing results in cache
 */
export async function storeInCache(
  documentHash: string,
  contentText: string,
  agentId: string,
  result: FormattedOutput,
  options: CacheOptions = {}
): Promise<{ success: boolean; cacheId?: string; error?: string }> {
  const opts: Required<CacheOptions> = {
    enableVectorSimilarity: true,
    similarityThreshold: DEFAULT_SIMILARITY_THRESHOLD,
    maxCacheAge: DEFAULT_CACHE_HOURS,
    enableHashCache: true,
    ...options
  };

  try {
    // Generate content hash for deduplication
    const contentHash = await generateContentHash(contentText);
    
    // Generate embedding if vector similarity is enabled
    let embedding: number[] | undefined;
    if (opts.enableVectorSimilarity) {
      embedding = await generateEmbedding(contentText);
    }

    // Calculate expiration
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + opts.maxCacheAge);

    // Store in database
    const { data, error } = await supabase
      .from('document_cache')
      .insert({
        document_hash: documentHash,
        content_hash: contentHash,
        embedding: embedding,
        extracted_data: result.extractedData,
        confidence: result.confidence,
        model: result.metadata.model,
        agent_id: agentId,
        processing_time: result.metadata.processingTime,
        expires_at: expiresAt.toISOString(),
        hit_count: 0,
        metadata: {
          validation_score: result.validationResults.score,
          quality_indicators: result.metadata.qualityIndicators,
          timestamp: result.timestamp
        }
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    return {
      success: true,
      cacheId: data.id
    };

  } catch (error) {
    console.error('Cache storage failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown cache error'
    };
  }
}

/**
 * Check for exact document hash match
 */
async function checkExactMatch(
  documentHash: string,
  agentId: string,
  maxAgeHours: number
): Promise<CacheEntry | null> {
  
  const cutoffTime = new Date();
  cutoffTime.setHours(cutoffTime.getHours() - maxAgeHours);

  const { data, error } = await supabase
    .from('document_cache')
    .select('*')
    .eq('document_hash', documentHash)
    .eq('agent_id', agentId)
    .gte('created_at', cutoffTime.toISOString())
    .lt('expires_at', new Date().toISOString())
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (error || !data) {
    return null;
  }

  return {
    id: data.id,
    documentHash: data.document_hash,
    contentHash: data.content_hash,
    embedding: data.embedding,
    extractedData: data.extracted_data,
    confidence: data.confidence,
    model: data.model,
    agentId: data.agent_id,
    processingTime: data.processing_time,
    createdAt: data.created_at,
    expiresAt: data.expires_at,
    hitCount: data.hit_count
  };
}

/**
 * Check for similar documents using vector similarity
 */
async function checkSimilarDocuments(
  queryEmbedding: number[],
  agentId: string,
  threshold: number,
  maxAgeHours: number
): Promise<SimilarityResult | null> {
  
  const cutoffTime = new Date();
  cutoffTime.setHours(cutoffTime.getHours() - maxAgeHours);

  try {
    // Use pgvector similarity search
    const { data, error } = await supabase.rpc('find_similar_documents', {
      query_embedding: queryEmbedding,
      agent_id: agentId,
      similarity_threshold: threshold,
      max_results: MAX_SIMILAR_RESULTS,
      cutoff_time: cutoffTime.toISOString()
    });

    if (error || !data || data.length === 0) {
      return null;
    }

    // Return the most similar result
    const best = data[0];
    
    return {
      id: best.id,
      similarity: best.similarity,
      entry: {
        id: best.id,
        documentHash: best.document_hash,
        contentHash: best.content_hash,
        embedding: best.embedding,
        extractedData: best.extracted_data,
        confidence: best.confidence,
        model: best.model,
        agentId: best.agent_id,
        processingTime: best.processing_time,
        createdAt: best.created_at,
        expiresAt: best.expires_at,
        hitCount: best.hit_count
      }
    };

  } catch (error) {
    console.error('Vector similarity search failed:', error);
    return null;
  }
}

/**
 * Generate embedding for text content
 */
async function generateEmbedding(text: string): Promise<number[]> {
  const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
  
  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key required for embeddings');
  }

  // Truncate text if too long (OpenAI has token limits)
  const truncatedText = text.length > 8000 ? text.substring(0, 8000) : text;

  const response = await fetch('https://api.openai.com/v1/embeddings', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'text-embedding-3-small',
      input: truncatedText,
      encoding_format: 'float'
    })
  });

  if (!response.ok) {
    throw new Error(`OpenAI embeddings API error: ${response.status}`);
  }

  const result = await response.json();

  if (!result.data || result.data.length === 0) {
    throw new Error('No embedding data returned');
  }

  return result.data[0].embedding;
}

/**
 * Generate content hash for deduplication
 */
async function generateContentHash(content: string): Promise<string> {
  // Normalize content for hashing
  const normalized = content
    .toLowerCase()
    .replace(/\s+/g, ' ')
    .trim();

  const encoder = new TextEncoder();
  const data = encoder.encode(normalized);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Increment cache hit count
 */
async function incrementHitCount(cacheId: string): Promise<void> {
  try {
    await supabase.rpc('increment_cache_hit_count', {
      cache_id: cacheId
    });
  } catch (error) {
    console.error('Failed to increment hit count:', error);
    // Non-critical error, don't throw
  }
}

/**
 * Clean up expired cache entries
 */
export async function cleanupExpiredCache(): Promise<{ deleted: number; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('document_cache')
      .delete()
      .lt('expires_at', new Date().toISOString());

    if (error) {
      throw error;
    }

    return {
      deleted: data ? (data as any)?.length || 0 : 0
    };

  } catch (error) {
    return {
      deleted: 0,
      error: error instanceof Error ? error.message : 'Cleanup failed'
    };
  }
}

/**
 * Get cache statistics
 */
export async function getCacheStats(): Promise<{
  totalEntries: number;
  totalHits: number;
  averageHitCount: number;
  cacheHitRate: number;
  oldestEntry?: string;
  newestEntry?: string;
}> {
  try {
    const { data, error } = await supabase.rpc('get_cache_statistics');

    if (error || !data) {
      throw error || new Error('No cache statistics available');
    }

    return data;

  } catch (error) {
    console.error('Failed to get cache stats:', error);
    return {
      totalEntries: 0,
      totalHits: 0,
      averageHitCount: 0,
      cacheHitRate: 0
    };
  }
}

/**
 * Invalidate cache entries for specific agent
 */
export async function invalidateAgentCache(agentId: string): Promise<{ deleted: number; error?: string }> {
  try {
    const { data, error } = await supabase
      .from('document_cache')
      .delete()
      .eq('agent_id', agentId);

    if (error) {
      throw error;
    }

    return {
      deleted: data ? (data as any)?.length || 0 : 0
    };

  } catch (error) {
    return {
      deleted: 0,
      error: error instanceof Error ? error.message : 'Cache invalidation failed'
    };
  }
}

/**
 * Get recent cache entries for debugging
 */
export async function getRecentCacheEntries(limit: number = 10): Promise<CacheEntry[]> {
  try {
    const { data, error } = await supabase
      .from('document_cache')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error || !data) {
      return [];
    }

    return data.map(item => ({
      id: item.id,
      documentHash: item.document_hash,
      contentHash: item.content_hash,
      embedding: item.embedding,
      extractedData: item.extracted_data,
      confidence: item.confidence,
      model: item.model,
      agentId: item.agent_id,
      processingTime: item.processing_time,
      createdAt: item.created_at,
      expiresAt: item.expires_at,
      hitCount: item.hit_count
    }));

  } catch (error) {
    console.error('Failed to get recent cache entries:', error);
    return [];
  }
}