/**
 * Content Preprocessing Module
 * Cleans and prepares document content for AI processing
 * Issue #10: Basic Document Processing
 */

import type { ExtractedContent } from './text-extraction.ts';

// Type definitions
export interface Agent {
  id: string;
  name: string;
  prompt: string;
  json_schema: Record<string, unknown>;
  category: string;
}

export interface PreprocessingOptions {
  maxLength?: number;
  preserveFormatting?: boolean;
  removeNoise?: boolean;
  normalizeWhitespace?: boolean;
  enhanceStructure?: boolean;
  includeSchema?: boolean;
}

export interface PreprocessedContent {
  processedText: string;
  originalLength: number;
  processedLength: number;
  qualityScore: number;
  preservedElements: string[];
  removedElements: string[];
  structureAnalysis: DocumentStructure;
  promptTokens: number;
  truncated: boolean;
}

export interface DocumentStructure {
  hasHeader: boolean;
  hasSections: boolean;
  hasTable: boolean;
  hasList: boolean;
  keyValuePairs: Record<string, string>;
  amounts: string[];
  dates: string[];
  sections: string[];
}

export interface QualityMetrics {
  readability: number;
  completeness: number;
  structure: number;
  confidence: number;
  overall: number;
}

// Constants
const MAX_PROMPT_LENGTH = 16000; // Conservative limit for AI models
const _MIN_QUALITY_THRESHOLD = 0.3;
const STRUCTURE_PATTERNS = {
  header: /^[A-Z\s]{5,}$/m,
  section: /^(Section|Chapter|\d+\.)\s+[A-Z]/m,
  table: /\s{2,}|\t|\|/,
  list: /^[\s]*[-•*]\s+/m,
  keyValue: /^([^:]+):\s*(.+)$/m,
  amount: /\$[\d,]+\.?\d*|\d+\.\d{2}/g,
  date: /\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}-\d{2}-\d{2}\b/g
};

/**
 * Main preprocessing function
 */
export async function preprocessContent(
  content: ExtractedContent,
  agent: Agent,
  options: PreprocessingOptions = {}
): Promise<PreprocessedContent> {
  const _startTime = performance.now();
  
  try {
    // Set default options
    const opts: Required<PreprocessingOptions> = {
      maxLength: MAX_PROMPT_LENGTH,
      preserveFormatting: false,
      removeNoise: true,
      normalizeWhitespace: true,
      enhanceStructure: true,
      includeSchema: true,
      ...options
    };

    // Analyze document structure
    const structure = analyzeDocumentStructure(content.text);
    
    // Clean and normalize text
    let processedText = content.text;
    const removedElements: string[] = [];
    const preservedElements: string[] = ['original_text'];

    if (opts.removeNoise) {
      processedText = removeNoise(processedText);
      removedElements.push('noise', 'artifacts');
    }

    if (opts.normalizeWhitespace) {
      processedText = normalizeWhitespace(processedText);
      removedElements.push('excessive_whitespace');
    }

    if (opts.enhanceStructure) {
      processedText = enhanceStructure(processedText, structure);
      preservedElements.push('structure', 'formatting');
    }

    // Calculate quality metrics
    const quality = calculateQualityMetrics(processedText, structure, content.metadata);

    // Prepare AI prompt
    const prompt = preparePromptForAgent(processedText, agent, structure, opts);
    
    // Handle length constraints
    const { finalPrompt, wasTruncated } = handleLengthConstraints(prompt, opts.maxLength);

    // Calculate token estimate
    const tokenCount = estimateTokenCount(finalPrompt);

    return {
      processedText: finalPrompt,
      originalLength: content.text.length,
      processedLength: finalPrompt.length,
      qualityScore: quality.overall,
      preservedElements,
      removedElements,
      structureAnalysis: structure,
      promptTokens: tokenCount,
      truncated: wasTruncated
    };

  } catch (error) {
    throw new Error(`Content preprocessing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Analyze document structure and extract key elements
 */
function analyzeDocumentStructure(text: string): DocumentStructure {
  const structure: DocumentStructure = {
    hasHeader: false,
    hasSections: false,
    hasTable: false,
    hasList: false,
    keyValuePairs: {},
    amounts: [],
    dates: [],
    sections: []
  };

  // Detect header
  structure.hasHeader = STRUCTURE_PATTERNS.header.test(text);

  // Detect sections
  structure.hasSections = STRUCTURE_PATTERNS.section.test(text);
  const sectionMatches = text.match(/^(Section|Chapter|\d+\.)\s+(.+)$/gm);
  if (sectionMatches) {
    structure.sections = sectionMatches.map(match => match.trim());
  }

  // Detect table structure
  structure.hasTable = STRUCTURE_PATTERNS.table.test(text);

  // Detect lists
  structure.hasList = STRUCTURE_PATTERNS.list.test(text);

  // Extract key-value pairs
  const lines = text.split('\n');
  for (const line of lines) {
    const match = line.match(STRUCTURE_PATTERNS.keyValue);
    if (match) {
      const key = match[1].trim();
      const value = match[2].trim();
      if (key.length < 50 && value.length < 200) { // Reasonable limits
        structure.keyValuePairs[key] = value;
      }
    }
  }

  // Extract amounts
  const amountMatches = text.match(STRUCTURE_PATTERNS.amount);
  if (amountMatches) {
    structure.amounts = [...new Set(amountMatches)]; // Remove duplicates
  }

  // Extract dates
  const dateMatches = text.match(STRUCTURE_PATTERNS.date);
  if (dateMatches) {
    structure.dates = [...new Set(dateMatches)]; // Remove duplicates
  }

  return structure;
}

/**
 * Remove noise and artifacts from text
 */
function removeNoise(text: string): string {
  let cleaned = text;

  // Remove non-printable characters except newlines and tabs
  cleaned = cleaned.replace(/[^\t\n\r\x20-\x7E]/g, '');

  // Fix common OCR mistakes
  cleaned = cleaned.replace(/\b1\b/g, 'l'); // 1 -> l
  cleaned = cleaned.replace(/\b0(?=[a-zA-Z])/g, 'o'); // 0 -> o when before letters
  cleaned = cleaned.replace(/\b5(?=[a-zA-Z])/g, 's'); // 5 -> s
  cleaned = cleaned.replace(/\b8(?=[a-zA-Z])/g, 'b'); // 8 -> b

  // Remove excessive punctuation
  cleaned = cleaned.replace(/[!?]{3,}/g, '!');
  cleaned = cleaned.replace(/\.{4,}/g, '...');

  // Remove standalone special characters
  cleaned = cleaned.replace(/\n[^\w\s]\n/g, '\n');

  return cleaned;
}

/**
 * Normalize whitespace while preserving structure
 */
function normalizeWhitespace(text: string): string {
  let normalized = text;

  // Convert different types of spaces to regular spaces
  normalized = normalized.replace(/[\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000]/g, ' ');

  // Normalize line endings
  normalized = normalized.replace(/\r\n|\r/g, '\n');

  // Remove trailing whitespace from lines
  normalized = normalized.replace(/ +$/gm, '');

  // Reduce multiple spaces to single space (except at line starts for indentation)
  normalized = normalized.replace(/(?<!^) {2,}/gm, ' ');

  // Reduce multiple newlines but preserve paragraph breaks
  normalized = normalized.replace(/\n{4,}/g, '\n\n\n');

  return normalized;
}

/**
 * Enhance document structure for better AI processing
 */
function enhanceStructure(text: string, structure: DocumentStructure): string {
  let enhanced = text;

  // Add section markers if sections detected
  if (structure.hasSections && structure.sections.length > 0) {
    for (const section of structure.sections) {
      enhanced = enhanced.replace(section, `\n--- ${section} ---\n`);
    }
  }

  // Enhance table formatting if detected
  if (structure.hasTable) {
    enhanced = enhanceTableFormatting(enhanced);
  }

  // Highlight key-value pairs
  if (Object.keys(structure.keyValuePairs).length > 0) {
    for (const [key, value] of Object.entries(structure.keyValuePairs)) {
      const pattern = new RegExp(`^${escapeRegex(key)}:\\s*${escapeRegex(value)}$`, 'gm');
      enhanced = enhanced.replace(pattern, `**${key}**: ${value}`);
    }
  }

  // Highlight amounts
  if (structure.amounts.length > 0) {
    for (const amount of structure.amounts) {
      enhanced = enhanced.replace(new RegExp(escapeRegex(amount), 'g'), `**${amount}**`);
    }
  }

  return enhanced;
}

/**
 * Enhance table formatting for better AI understanding
 */
function enhanceTableFormatting(text: string): string {
  const lines = text.split('\n');
  const enhancedLines: string[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Check if line looks like a table row (multiple columns separated by spaces/tabs)
    const columns = line.split(/\s{2,}|\t/).filter(col => col.trim());
    
    if (columns.length >= 2 && line.trim()) {
      // Format as clear table row
      enhancedLines.push(`| ${columns.join(' | ')} |`);
    } else {
      enhancedLines.push(line);
    }
  }
  
  return enhancedLines.join('\n');
}

/**
 * Prepare final prompt for AI agent
 */
function preparePromptForAgent(
  text: string,
  agent: Agent,
  structure: DocumentStructure,
  options: Required<PreprocessingOptions>
): string {
  let prompt = '';

  // Add system prompt
  prompt += agent.prompt + '\n\n';

  // Add schema information if requested
  if (options.includeSchema && agent.json_schema) {
    prompt += 'Expected JSON Schema:\n';
    prompt += JSON.stringify(agent.json_schema, null, 2) + '\n\n';
  }

  // Add structure hints based on analysis
  if (structure.hasTable || structure.hasList) {
    prompt += 'Note: This document contains structured data (tables/lists).\n';
  }
  
  if (structure.amounts.length > 0) {
    prompt += `Note: Key amounts found: ${structure.amounts.slice(0, 3).join(', ')}\n`;
  }
  
  if (structure.dates.length > 0) {
    prompt += `Note: Key dates found: ${structure.dates.slice(0, 3).join(', ')}\n`;
  }

  // Add document content
  prompt += 'Document Content:\n';
  prompt += '---\n';
  prompt += text;
  prompt += '\n---\n\n';

  // Add extraction instruction
  prompt += 'Please extract the structured data as JSON according to the schema above.';

  return prompt;
}

/**
 * Handle length constraints with intelligent truncation
 */
function handleLengthConstraints(text: string, maxLength: number): { finalPrompt: string; wasTruncated: boolean } {
  if (text.length <= maxLength) {
    return { finalPrompt: text, wasTruncated: false };
  }

  // Find document content section
  const contentStart = text.indexOf('Document Content:\n---\n');
  const contentEnd = text.indexOf('\n---\n\n', contentStart);
  
  if (contentStart === -1 || contentEnd === -1) {
    // Simple truncation if structure not found
    return { 
      finalPrompt: text.substring(0, maxLength - 100) + '\n...\n[Content truncated]',
      wasTruncated: true 
    };
  }

  const beforeContent = text.substring(0, contentStart + 21); // Include "Document Content:\n---\n"
  const afterContent = text.substring(contentEnd);
  const documentContent = text.substring(contentStart + 21, contentEnd);

  const availableSpace = maxLength - beforeContent.length - afterContent.length - 50; // Buffer

  if (availableSpace < 100) {
    // Not enough space, use minimal content
    return {
      finalPrompt: beforeContent + '[Document too large to process]' + afterContent,
      wasTruncated: true
    };
  }

  // Intelligent truncation of document content
  const truncatedContent = truncateIntelligently(documentContent, availableSpace);
  
  return {
    finalPrompt: beforeContent + truncatedContent + afterContent,
    wasTruncated: true
  };
}

/**
 * Intelligently truncate content while preserving important parts
 */
function truncateIntelligently(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }

  // Split into paragraphs/sections
  const sections = text.split(/\n\s*\n/);
  
  // Prioritize sections with important content
  const prioritizedSections = sections.map(section => ({
    content: section,
    priority: calculateSectionPriority(section),
    length: section.length
  })).sort((a, b) => b.priority - a.priority);

  let result = '';
  let remainingLength = maxLength - 50; // Reserve space for truncation notice

  for (const section of prioritizedSections) {
    if (remainingLength <= 0) break;
    
    if (section.length <= remainingLength) {
      result += section.content + '\n\n';
      remainingLength -= section.length + 2;
    } else if (remainingLength > 100) {
      // Include partial section if there's reasonable space
      result += section.content.substring(0, remainingLength - 20) + '...';
      break;
    }
  }

  return result.trim() + '\n\n[Content truncated to fit size limits]';
}

/**
 * Calculate priority score for text sections
 */
function calculateSectionPriority(text: string): number {
  let priority = 0;

  // Headers and titles
  if (/^[A-Z\s]{5,}$/m.test(text)) priority += 10;
  
  // Key-value pairs
  if (/^[^:]+:\s*.+$/m.test(text)) priority += 8;
  
  // Amounts and numbers
  if (/\$[\d,]+\.?\d*/.test(text)) priority += 7;
  
  // Dates
  if (/\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b/.test(text)) priority += 6;
  
  // Tables (multiple columns)
  if (/\s{3,}|\t/.test(text)) priority += 5;
  
  // Lists
  if (/^[\s]*[-•*]\s+/m.test(text)) priority += 4;
  
  // Short, dense content (likely important)
  if (text.length < 200 && text.split(/\s+/).length > 5) priority += 3;
  
  return priority;
}

/**
 * Calculate quality metrics for processed content
 */
function calculateQualityMetrics(
  text: string,
  structure: DocumentStructure,
  metadata: Record<string, unknown>
): QualityMetrics {
  const metrics: QualityMetrics = {
    readability: 0,
    completeness: 0,
    structure: 0,
    confidence: 0,
    overall: 0
  };

  // Readability (character distribution)
  const totalChars = text.length;
  if (totalChars > 0) {
    const alphaCount = (text.match(/[a-zA-Z]/g) || []).length;
    const digitCount = (text.match(/[0-9]/g) || []).length;
    const _spaceCount = (text.match(/\s/g) || []).length;
    
    metrics.readability = Math.min(1, (alphaCount / totalChars) * 1.2 + (digitCount / totalChars) * 0.8);
  }

  // Completeness (presence of key elements)
  let completenessScore = 0;
  if (structure.amounts.length > 0) completenessScore += 0.3;
  if (structure.dates.length > 0) completenessScore += 0.2;
  if (Object.keys(structure.keyValuePairs).length > 0) completenessScore += 0.3;
  if (text.length > 50) completenessScore += 0.2;
  metrics.completeness = completenessScore;

  // Structure (organization and formatting)
  let structureScore = 0.3; // Base score
  if (structure.hasHeader) structureScore += 0.2;
  if (structure.hasSections) structureScore += 0.2;
  if (structure.hasTable || structure.hasList) structureScore += 0.2;
  if (structure.amounts.length > 0 || structure.dates.length > 0) structureScore += 0.1;
  metrics.structure = Math.min(1, structureScore);

  // Confidence (from metadata if available)
  metrics.confidence = (metadata.confidence as number) || 0.8;

  // Overall score
  metrics.overall = (
    metrics.readability * 0.3 +
    metrics.completeness * 0.3 +
    metrics.structure * 0.2 +
    metrics.confidence * 0.2
  );

  return metrics;
}

/**
 * Estimate token count for API usage
 */
function estimateTokenCount(text: string): number {
  // Rough estimate: ~4 characters per token for English text
  return Math.ceil(text.length / 4);
}

/**
 * Escape regex special characters
 */
function escapeRegex(text: string): string {
  return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Validate preprocessing options
 */
export function validatePreprocessingOptions(options: PreprocessingOptions): string[] {
  const errors: string[] = [];

  if (options.maxLength && options.maxLength < 100) {
    errors.push('maxLength must be at least 100 characters');
  }

  if (options.maxLength && options.maxLength > 50000) {
    errors.push('maxLength must not exceed 50000 characters');
  }

  return errors;
}