/**
 * Admin Rate Limiting Management Edge Function
 * Epic 4 Story 4.4: Advanced Rate Limiting System
 * 
 * Provides comprehensive rate limiting configuration and monitoring:
 * - Dynamic rate limit configuration per customer/API key
 * - Multi-algorithm support (sliding window, token bucket, leaky bucket, fixed window)
 * - Geographic and endpoint-specific rate limiting
 * - Burst handling and intelligent traffic shaping
 * - Real-time monitoring and violation tracking
 * - Emergency whitelist management
 */

import { serve } from 'https://deno.land/std@0.208.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.57.4';
import { corsHeaders } from '../_shared/cors.ts';

// Types
interface RateLimitCreateRequest {
  customer_id?: string;
  api_key_id?: string;
  limit_type: 'per_minute' | 'per_hour' | 'per_day' | 'per_month';
  limit_value: number;
  burst_allowance?: number;
  algorithm_type?: 'sliding_window' | 'token_bucket' | 'leaky_bucket' | 'fixed_window';
  geographic_regions?: string[];
  endpoint_patterns?: string[];
  priority_level?: number;
}

interface RateLimitUpdateRequest {
  limit_value?: number;
  burst_allowance?: number;
  algorithm_type?: 'sliding_window' | 'token_bucket' | 'leaky_bucket' | 'fixed_window';
  geographic_regions?: string[];
  endpoint_patterns?: string[];
  priority_level?: number;
  is_active?: boolean;
}

interface RateLimitCheckRequest {
  customer_id?: string;
  api_key_id?: string;
  endpoint: string;
  requests?: number;
  region?: string;
  ip_address?: string;
  user_agent?: string;
}

interface WhitelistCreateRequest {
  customer_id?: string;
  api_key_id?: string;
  whitelist_type: 'emergency' | 'enterprise' | 'testing' | 'migration';
  bypass_all_limits?: boolean;
  specific_endpoints?: string[];
  valid_until: string;
  approval_reason: string;
  requested_by: string;
}

interface BulkRateLimitOperation {
  operation: 'create' | 'update' | 'delete';
  customer_ids?: string[];
  api_key_ids?: string[];
  rate_limit_config?: RateLimitCreateRequest;
  updates?: RateLimitUpdateRequest;
}

interface AdminContext {
  adminUserId?: string;
  adminRole?: string;
  correlationId: string;
}

/**
 * Validate admin authentication and permissions
 */
async function validateAdminAuth(request: Request): Promise<AdminContext> {
  const authHeader = request.headers.get('Authorization');
  const correlationId = request.headers.get('x-correlation-id') || crypto.randomUUID();

  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  
  // For now, validate against service role key (in production, implement proper admin auth)
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  if (token !== serviceRoleKey) {
    throw new Error('Invalid admin credentials');
  }

  return {
    adminUserId: 'system-admin', // In production, extract from JWT
    adminRole: 'admin',
    correlationId
  };
}

/**
 * Create standardized API response
 */
function createResponse(
  data: any = null,
  error: any = null,
  status: number = 200,
  correlationId?: string
): Response {
  const response = {
    success: !error,
    timestamp: new Date().toISOString(),
    correlation_id: correlationId,
    ...(data && { data }),
    ...(error && { error: typeof error === 'string' ? { message: error } : error })
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Log audit events for compliance and tracking
 */
async function logAuditEvent(
  supabase: any,
  context: AdminContext,
  action: string,
  resourceType: string,
  resourceId: string,
  customerId?: string,
  details?: any
): Promise<void> {
  try {
    await supabase.from('audit_logs').insert({
      admin_user_id: context.adminUserId,
      customer_id: customerId,
      resource_type: resourceType,
      resource_id: resourceId,
      action,
      details: details || {},
      correlation_id: context.correlationId,
      created_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Failed to log audit event:', error);
  }
}

/**
 * Validate rate limit configuration
 */
function validateRateLimitConfig(config: RateLimitCreateRequest): void {
  // Validate scope - either customer_id OR api_key_id, not both
  if (config.customer_id && config.api_key_id) {
    throw new Error('Cannot specify both customer_id and api_key_id');
  }
  
  if (!config.customer_id && !config.api_key_id) {
    throw new Error('Must specify either customer_id or api_key_id');
  }

  // Validate limit values
  if (config.limit_value <= 0) {
    throw new Error('limit_value must be positive');
  }

  if (config.burst_allowance && config.burst_allowance < 0) {
    throw new Error('burst_allowance cannot be negative');
  }

  // Validate limit type
  const validTypes = ['per_minute', 'per_hour', 'per_day', 'per_month'];
  if (!validTypes.includes(config.limit_type)) {
    throw new Error(`Invalid limit_type. Must be one of: ${validTypes.join(', ')}`);
  }

  // Validate algorithm type
  const validAlgorithms = ['sliding_window', 'token_bucket', 'leaky_bucket', 'fixed_window'];
  if (config.algorithm_type && !validAlgorithms.includes(config.algorithm_type)) {
    throw new Error(`Invalid algorithm_type. Must be one of: ${validAlgorithms.join(', ')}`);
  }

  // Validate priority level
  if (config.priority_level && (config.priority_level < 1 || config.priority_level > 10)) {
    throw new Error('priority_level must be between 1 and 10');
  }
}

/**
 * Calculate next reset time based on limit type
 */
function calculateResetTime(limitType: string): string {
  const now = new Date();
  
  switch (limitType) {
    case 'per_minute': {
      }
      return new Date(Math.ceil(now.getTime() / 60000) * 60000).toISOString();
    }
    case 'per_hour': {
      }
      return new Date(Math.ceil(now.getTime() / 3600000) * 3600000).toISOString();
    }
    case 'per_day': {
      }
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      return tomorrow.toISOString();
    }
    case 'per_month': {
      }
      const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      return nextMonth.toISOString();
    }
    default:
      throw new Error(`Invalid limit type: ${limitType}`);
  }
}

/**
 * Create rate limit configuration
 */
async function createRateLimit(
  supabase: any,
  context: AdminContext,
  config: RateLimitCreateRequest
): Promise<any> {
  validateRateLimitConfig(config);

  // Check for existing rate limit with same scope and type
  let query = supabase
    .from('rate_limits')
    .select('id')
    .eq('limit_type', config.limit_type)
    .eq('algorithm_type', config.algorithm_type || 'sliding_window')
    .eq('is_active', true);

  if (config.customer_id) {
    query = query.eq('customer_id', config.customer_id).is('api_key_id', null);
  } else {
    query = query.eq('api_key_id', config.api_key_id).is('customer_id', null);
  }

  const { data: existing } = await query.single();
  
  if (existing) {
    throw new Error(`Rate limit already exists for this scope and type`);
  }

  // Create rate limit record
  const rateLimitData = {
    customer_id: config.customer_id || null,
    api_key_id: config.api_key_id || null,
    limit_type: config.limit_type,
    limit_value: config.limit_value,
    burst_allowance: config.burst_allowance || 0,
    algorithm_type: config.algorithm_type || 'sliding_window',
    geographic_regions: config.geographic_regions || null,
    endpoint_patterns: config.endpoint_patterns || null,
    priority_level: config.priority_level || 1,
    current_usage: 0,
    burst_usage: 0,
    window_start: new Date().toISOString(),
    reset_at: calculateResetTime(config.limit_type),
    is_active: true,
    created_by: context.adminUserId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data: rateLimit, error } = await supabase
    .from('rate_limits')
    .insert(rateLimitData)
    .select()
    .single();

  if (error) throw error;

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'CREATE',
    'rate_limit',
    rateLimit.id,
    config.customer_id,
    { rate_limit_config: config }
  );

  return rateLimit;
}

/**
 * Update rate limit configuration
 */
async function updateRateLimit(
  supabase: any,
  context: AdminContext,
  rateLimitId: string,
  updates: RateLimitUpdateRequest
): Promise<any> {
  // Get existing rate limit
  const { data: existing, error: fetchError } = await supabase
    .from('rate_limits')
    .select('*')
    .eq('id', rateLimitId)
    .single();

  if (fetchError || !existing) {
    throw new Error('Rate limit not found');
  }

  // Prepare update data
  const updateData: any = {
    updated_at: new Date().toISOString()
  };

  if (updates.limit_value !== undefined) {
    if (updates.limit_value <= 0) {
      throw new Error('limit_value must be positive');
    }
    updateData.limit_value = updates.limit_value;
  }

  if (updates.burst_allowance !== undefined) {
    if (updates.burst_allowance < 0) {
      throw new Error('burst_allowance cannot be negative');
    }
    updateData.burst_allowance = updates.burst_allowance;
  }

  if (updates.algorithm_type !== undefined) {
    const validAlgorithms = ['sliding_window', 'token_bucket', 'leaky_bucket', 'fixed_window'];
    if (!validAlgorithms.includes(updates.algorithm_type)) {
      throw new Error(`Invalid algorithm_type. Must be one of: ${validAlgorithms.join(', ')}`);
    }
    updateData.algorithm_type = updates.algorithm_type;
  }

  if (updates.geographic_regions !== undefined) {
    updateData.geographic_regions = updates.geographic_regions;
  }

  if (updates.endpoint_patterns !== undefined) {
    updateData.endpoint_patterns = updates.endpoint_patterns;
  }

  if (updates.priority_level !== undefined) {
    if (updates.priority_level < 1 || updates.priority_level > 10) {
      throw new Error('priority_level must be between 1 and 10');
    }
    updateData.priority_level = updates.priority_level;
  }

  if (updates.is_active !== undefined) {
    updateData.is_active = updates.is_active;
  }

  // Update rate limit
  const { data: updated, error } = await supabase
    .from('rate_limits')
    .update(updateData)
    .eq('id', rateLimitId)
    .select()
    .single();

  if (error) throw error;

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'UPDATE',
    'rate_limit',
    rateLimitId,
    existing.customer_id,
    { 
      old_config: existing,
      updates: updates
    }
  );

  return updated;
}

/**
 * Delete rate limit configuration
 */
async function deleteRateLimit(
  supabase: any,
  context: AdminContext,
  rateLimitId: string
): Promise<void> {
  // Get existing rate limit for audit
  const { data: existing } = await supabase
    .from('rate_limits')
    .select('*')
    .eq('id', rateLimitId)
    .single();

  if (!existing) {
    throw new Error('Rate limit not found');
  }

  const { error } = await supabase
    .from('rate_limits')
    .delete()
    .eq('id', rateLimitId);

  if (error) throw error;

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'DELETE',
    'rate_limit',
    rateLimitId,
    existing.customer_id,
    { deleted_config: existing }
  );
}

/**
 * Check rate limit for request
 */
async function checkRateLimit(
  supabase: any,
  context: AdminContext,
  checkRequest: RateLimitCheckRequest
): Promise<any> {
  const { customer_id, api_key_id, endpoint, requests = 1, region, ip_address, user_agent } = checkRequest;

  if (!customer_id && !api_key_id) {
    throw new Error('Must specify either customer_id or api_key_id');
  }

  // Use the database function for rate limit checking
  const { data: result, error } = await supabase.rpc('check_rate_limit', {
    p_customer_id: customer_id || null,
    p_api_key_id: api_key_id || null,
    p_endpoint: endpoint,
    p_requests_to_add: requests
  });

  if (error) throw error;

  // If request was blocked, return 429 status
  if (!result.allowed) {
    return createResponse(result, null, 429, context.correlationId);
  }

  return result;
}

/**
 * Create whitelist entry
 */
async function createWhitelist(
  supabase: any,
  context: AdminContext,
  config: WhitelistCreateRequest
): Promise<any> {
  // Validate scope
  if (config.customer_id && config.api_key_id) {
    throw new Error('Cannot specify both customer_id and api_key_id');
  }
  
  if (!config.customer_id && !config.api_key_id) {
    throw new Error('Must specify either customer_id or api_key_id');
  }

  // Validate valid_until is in the future
  const validUntil = new Date(config.valid_until);
  if (validUntil <= new Date()) {
    throw new Error('valid_until must be in the future');
  }

  const whitelistData = {
    customer_id: config.customer_id || null,
    api_key_id: config.api_key_id || null,
    whitelist_type: config.whitelist_type,
    bypass_all_limits: config.bypass_all_limits || false,
    specific_endpoints: config.specific_endpoints || null,
    valid_from: new Date().toISOString(),
    valid_until: config.valid_until,
    requested_by: config.requested_by,
    approved_by: context.adminUserId,
    approval_reason: config.approval_reason,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data: whitelist, error } = await supabase
    .from('rate_limit_whitelist')
    .insert(whitelistData)
    .select()
    .single();

  if (error) throw error;

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'CREATE',
    'rate_limit_whitelist',
    whitelist.id,
    config.customer_id,
    { whitelist_config: config }
  );

  return whitelist;
}

/**
 * Get rate limit violations
 */
async function getRateLimitViolations(
  supabase: any,
  context: AdminContext,
  params: URLSearchParams
): Promise<any> {
  const customerId = params.get('customer_id');
  const apiKeyId = params.get('api_key_id');
  const period = params.get('period') || 'last_24h';
  const limit = parseInt(params.get('limit') || '100');
  const offset = parseInt(params.get('offset') || '0');

  let query = supabase
    .from('rate_limit_violations')
    .select(`
      *,
      rate_limits(limit_type, limit_value),
      customers(name, customer_id),
      api_keys(name, key_prefix)
    `)
    .order('violation_time', { ascending: false })
    .range(offset, offset + limit - 1);

  if (customerId) {
    query = query.eq('customer_id', customerId);
  }

  if (apiKeyId) {
    query = query.eq('api_key_id', apiKeyId);
  }

  // Add time filter based on period
  let timeFilter: Date;
  switch (period) {
    case 'last_1h': {
      }
      timeFilter = new Date(Date.now() - 60 * 60 * 1000);
      break;
    }
    case 'last_24h': {
      }
      timeFilter = new Date(Date.now() - 24 * 60 * 60 * 1000);
      break;
    }
    case 'last_7d': {
      }
      timeFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    }
    case 'last_30d': {
      }
      timeFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      break;
    }
    default:
      timeFilter = new Date(Date.now() - 24 * 60 * 60 * 1000);
  }

  query = query.gte('violation_time', timeFilter.toISOString());

  const { data: violations, error } = await query;

  if (error) throw error;

  return {
    violations,
    period,
    total_count: violations?.length || 0
  };
}

/**
 * Get rate limit analytics
 */
async function getRateLimitAnalytics(
  supabase: any,
  context: AdminContext,
  params: URLSearchParams
): Promise<any> {
  const customerId = params.get('customer_id');
  const apiKeyId = params.get('api_key_id');
  const windowType = params.get('window_type') || 'hour';
  const limit = parseInt(params.get('limit') || '24');

  let query = supabase
    .from('rate_limit_analytics')
    .select('*')
    .eq('window_type', windowType)
    .order('window_start', { ascending: false })
    .limit(limit);

  if (customerId) {
    query = query.eq('customer_id', customerId);
  }

  if (apiKeyId) {
    query = query.eq('api_key_id', apiKeyId);
  }

  const { data: analytics, error } = await query;

  if (error) throw error;

  // Calculate summary metrics
  const summary = analytics?.reduce((acc, record) => {
    acc.total_requests += record.total_requests || 0;
    acc.blocked_requests += record.blocked_requests || 0;
    acc.burst_requests += record.burst_requests || 0;
    return acc;
  }, {
    total_requests: 0,
    blocked_requests: 0,
    burst_requests: 0
  });

  return {
    analytics,
    summary,
    window_type: windowType,
    period_count: analytics?.length || 0
  };
}

/**
 * Get rate limit dashboard data
 */
async function getRateLimitDashboard(
  supabase: any,
  context: AdminContext,
  params: URLSearchParams
): Promise<any> {
  const period = params.get('period') || 'last_7d';

  // Get summary statistics
  const { data: summary } = await supabase.rpc('get_rate_limit_summary', {
    period_days: period === 'last_7d' ? 7 : period === 'last_30d' ? 30 : 1
  });

  // Get top violators
  const { data: topViolators } = await supabase
    .from('rate_limit_violations')
    .select(`
      customer_id,
      customers(name, customer_id),
      count(*)
    `)
    .gte('violation_time', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    .group('customer_id, customers.name, customers.customer_id')
    .order('count', { ascending: false })
    .limit(10);

  // Get trending patterns (simplified)
  const { data: trends } = await supabase
    .from('rate_limit_analytics')
    .select('window_start, total_requests, blocked_requests')
    .eq('window_type', 'hour')
    .gte('window_start', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
    .order('window_start', { ascending: true });

  return {
    summary: summary?.[0] || {
      total_requests: 0,
      blocked_requests: 0,
      active_rate_limits: 0,
      active_whitelists: 0
    },
    top_violators: topViolators || [],
    trending_patterns: trends || []
  };
}

/**
 * Perform bulk rate limit operations
 */
async function performBulkOperation(
  supabase: any,
  context: AdminContext,
  operation: BulkRateLimitOperation
): Promise<any> {
  const results = {
    created_count: 0,
    updated_count: 0,
    deleted_count: 0,
    failed_count: 0,
    failures: [] as any[]
  };

  const targets = operation.customer_ids || operation.api_key_ids || [];

  for (const targetId of targets) {
    try {
      switch (operation.operation) {
        case 'create': {
      }
          if (!operation.rate_limit_config) {
            throw new Error('rate_limit_config required for create operation');
          }
          
          const config = {
            ...operation.rate_limit_config,
            customer_id: operation.customer_ids ? targetId : undefined,
            api_key_id: operation.api_key_ids ? targetId : undefined
          };
          
          await createRateLimit(supabase, context, config);
          results.created_count++;
          break;
    }

        case 'update': {
      }
          if (!operation.updates) {
            throw new Error('updates required for update operation');
          }
          
          // Find rate limits for target
          let query = supabase.from('rate_limits').select('id');
          if (operation.customer_ids) {
            query = query.eq('customer_id', targetId);
          } else {
            query = query.eq('api_key_id', targetId);
          }
          
          const { data: rateLimits } = await query;
          
          for (const rateLimit of rateLimits || []) {
            await updateRateLimit(supabase, context, rateLimit.id, operation.updates);
            results.updated_count++;
          }
          break;
    }

        case 'delete': {
      }
          // Find and delete rate limits for target
          let deleteQuery = supabase.from('rate_limits').select('id');
          if (operation.customer_ids) {
            deleteQuery = deleteQuery.eq('customer_id', targetId);
          } else {
            deleteQuery = deleteQuery.eq('api_key_id', targetId);
          }
          
          const { data: toDelete } = await deleteQuery;
          
          for (const rateLimit of toDelete || []) {
            await deleteRateLimit(supabase, context, rateLimit.id);
            results.deleted_count++;
          }
          break;
    }

        default:
          throw new Error(`Unknown operation: ${operation.operation}`);
      }
    } catch (error) {
      results.failed_count++;
      results.failures.push({
        target_id: targetId,
        error: error.message
      });
    }
  }

  // Log bulk operation
  await logAuditEvent(
    supabase,
    context,
    'BULK_OPERATION',
    'rate_limits',
    'bulk',
    undefined,
    { operation, results }
  );

  return results;
}

/**
 * Main request handler
 */
serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Validate admin authentication
    const context = await validateAdminAuth(req);

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Parse URL and method
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);
    const method = req.method;

    // Route handling
    if (method === 'POST' && pathParts.length === 1) {
      // Create rate limit: POST /admin-rate-limits
      const body = await req.json();
      const _rateLimit = await createRateLimit(supabase, context, body);
      return createResponse(rateLimit, null, 201, context.correlationId);
    }

    if (method === 'PUT' && pathParts.length === 2) {
      // Update rate limit: PUT /admin-rate-limits/{id}
      const rateLimitId = pathParts[1];
      const body = await req.json();
      const _rateLimit = await updateRateLimit(supabase, context, rateLimitId, body);
      return createResponse(rateLimit, null, 200, context.correlationId);
    }

    if (method === 'DELETE' && pathParts.length === 2) {
      // Delete rate limit: DELETE /admin-rate-limits/{id}
      const rateLimitId = pathParts[1];
      await deleteRateLimit(supabase, context, rateLimitId);
      return createResponse({ deleted: true }, null, 200, context.correlationId);
    }

    if (method === 'POST' && pathParts[1] === 'check') {
      // Check rate limit: POST /admin-rate-limits/check
      const body = await req.json();
      const result = await checkRateLimit(supabase, context, body);
      return createResponse(result, null, 200, context.correlationId);
    }

    if (method === 'POST' && pathParts[1] === 'whitelist') {
      // Create whitelist: POST /admin-rate-limits/whitelist
      const body = await req.json();
      const whitelist = await createWhitelist(supabase, context, body);
      return createResponse(whitelist, null, 201, context.correlationId);
    }

    if (method === 'DELETE' && pathParts[1] === 'whitelist' && pathParts.length === 3) {
      // Delete whitelist: DELETE /admin-rate-limits/whitelist/{id}
      const whitelistId = pathParts[2];
      
      const { error } = await supabase
        .from('rate_limit_whitelist')
        .update({ is_active: false, updated_at: new Date().toISOString() })
        .eq('id', whitelistId);

      if (error) throw error;

      return createResponse({ deactivated: true }, null, 200, context.correlationId);
    }

    if (method === 'GET' && pathParts[1] === 'violations') {
      // Get violations: GET /admin-rate-limits/violations
      const result = await getRateLimitViolations(supabase, context, url.searchParams);
      return createResponse(result, null, 200, context.correlationId);
    }

    if (method === 'GET' && pathParts[1] === 'analytics') {
      // Get analytics: GET /admin-rate-limits/analytics
      const result = await getRateLimitAnalytics(supabase, context, url.searchParams);
      return createResponse(result, null, 200, context.correlationId);
    }

    if (method === 'GET' && pathParts[1] === 'dashboard') {
      // Get dashboard: GET /admin-rate-limits/dashboard
      const result = await getRateLimitDashboard(supabase, context, url.searchParams);
      return createResponse(result, null, 200, context.correlationId);
    }

    if (method === 'POST' && pathParts[1] === 'bulk') {
      // Bulk operations: POST /admin-rate-limits/bulk
      const body = await req.json();
      const result = await performBulkOperation(supabase, context, body);
      return createResponse(result, null, 200, context.correlationId);
    }

    if (method === 'GET' && pathParts[1] === 'status') {
      // Get rate limit status: GET /admin-rate-limits/status
      const customerId = url.searchParams.get('customer_id');
      const apiKeyId = url.searchParams.get('api_key_id');

      let query = supabase
        .from('rate_limits')
        .select('*')
        .eq('is_active', true);

      if (customerId) {
        query = query.eq('customer_id', customerId);
      }

      if (apiKeyId) {
        query = query.eq('api_key_id', apiKeyId);
      }

      const { data: rateLimits, error } = await query;
      if (error) throw error;

      // Calculate overall utilization
      const overallUtilization = rateLimits?.reduce((acc, limit) => {
        const utilization = ((limit.current_usage + limit.burst_usage) / (limit.limit_value + limit.burst_allowance)) * 100;
        return acc + utilization;
      }, 0) / (rateLimits?.length || 1);

      // Get next reset times
      const nextResetTimes = rateLimits?.map(limit => ({
        limit_type: limit.limit_type,
        reset_at: limit.reset_at
      }));

      const result = {
        rate_limits: rateLimits,
        overall_utilization: Math.round(overallUtilization * 100) / 100,
        next_reset_times: nextResetTimes
      };

      return createResponse(result, null, 200, context.correlationId);
    }

    // Default: Method not allowed
    return createResponse(null, 'Method not allowed', 405, context.correlationId);

  } catch (error) {
    console.error('Rate limit management error:', error);
    return createResponse(
      null,
      {
        message: error.message || 'Internal server error',
        code: 'RATE_LIMIT_ERROR'
      },
      error.message?.includes('not found') ? 404 :
      error.message?.includes('validation') || error.message?.includes('Invalid') ? 400 :
      error.message?.includes('authorization') || error.message?.includes('credentials') ? 401 : 500
    );
  }
});