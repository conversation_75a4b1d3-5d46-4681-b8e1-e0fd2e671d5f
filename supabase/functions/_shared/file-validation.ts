/**
 * File Upload Validation and Security Scanning
 * Handles file type validation, size limits, and security checks
 */

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  metadata?: FileMetadata;
}

export interface FileMetadata {
  hash: string;
  mimeType: string;
  size: number;
  dimensions?: {
    width: number;
    height: number;
  };
  pages?: number;
  textContent?: string;
  language?: string;
  encoding?: string;
  createdAt?: Date;
  modifiedAt?: Date;
}

export interface FileValidationOptions {
  maxSizeBytes?: number;
  allowedMimeTypes?: string[];
  enableVirusScan?: boolean;
  extractMetadata?: boolean;
  validateHeaders?: boolean;
}

/**
 * File Upload Validator with security scanning and metadata extraction
 */
export class FileUploadValidator {
  /**
   * Validate uploaded file against security and business rules
   */
  static async validateUpload(
    file: File,
    keyType: 'test' | 'production',
    options?: FileValidationOptions
  ): Promise<FileValidationResult> {
    try {
      const opts = {
        maxSizeBytes: keyType === 'test' ? 10 * 1024 * 1024 : 50 * 1024 * 1024, // 10MB test, 50MB prod
        allowedMimeTypes: [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // DOCX
          'application/msword', // DOC
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // XLSX
          'application/vnd.ms-excel', // XLS
          'application/vnd.openxmlformats-officedocument.presentationml.presentation', // PPTX
          'application/vnd.ms-powerpoint', // PPT
          'application/vnd.oasis.opendocument.text', // ODT
          'application/vnd.oasis.opendocument.spreadsheet', // ODS
          'application/vnd.oasis.opendocument.presentation', // ODP
          'application/rtf', // RTF
          'text/plain', // TXT
          'text/csv', // CSV
          'image/jpeg', // JPEG
          'image/png', // PNG
          'image/tiff', // TIFF
          'image/gif', // GIF
          'image/bmp', // BMP
          'image/webp' // WEBP
        ],
        enableVirusScan: false, // Disabled by default for performance
        extractMetadata: true,
        validateHeaders: true,
        ...options
      };

      // Basic validation
      if (!file || !file.name) {
        return {
          isValid: false,
          error: 'No file provided'
        };
      }

      // File size validation
      if (file.size > opts.maxSizeBytes) {
        return {
          isValid: false,
          error: `File too large. Maximum size: ${Math.round(opts.maxSizeBytes / 1024 / 1024)}MB`
        };
      }

      if (file.size === 0) {
        return {
          isValid: false,
          error: 'File is empty'
        };
      }

      // MIME type validation - handle charset suffixes
      const baseMimeType = file.type.split(';')[0].trim();
      if (!opts.allowedMimeTypes.includes(baseMimeType)) {
        return {
          isValid: false,
          error: `Unsupported file type: ${file.type}. Supported: ${opts.allowedMimeTypes.join(', ')}`
        };
      }

      // File name validation
      const fileNameValidation = this.validateFileName(file.name);
      if (!fileNameValidation.isValid) {
        return fileNameValidation;
      }

      // Header validation (magic numbers)
      if (opts.validateHeaders) {
        const headerValidation = await this.validateFileHeaders(file);
        if (!headerValidation.isValid) {
          return headerValidation;
        }
      }

      // Extract metadata
      let metadata: FileMetadata | undefined;
      if (opts.extractMetadata) {
        metadata = await this.extractFileMetadata(file);
      }

      // Virus scanning (if enabled)
      if (opts.enableVirusScan) {
        const virusScanResult = await this.scanForMalware(file);
        if (!virusScanResult.isValid) {
          return virusScanResult;
        }
      }

      return {
        isValid: true,
        metadata
      };

    } catch (error) {
      console.error('File validation error:', error);
      return {
        isValid: false,
        error: 'File validation failed'
      };
    }
  }

  /**
   * Validate file name for security issues
   */
  private static validateFileName(fileName: string): FileValidationResult {
    // Check for dangerous characters
    const dangerousChars = /[<>:"|?*]/;
    if (dangerousChars.test(fileName)) {
      return {
        isValid: false,
        error: 'File name contains invalid characters'
      };
    }

    // Check for path traversal attempts
    if (fileName.includes('..') || fileName.includes('/') || fileName.includes('\\')) {
      return {
        isValid: false,
        error: 'File name contains path traversal characters'
      };
    }

    // Check length
    if (fileName.length > 255) {
      return {
        isValid: false,
        error: 'File name too long (max 255 characters)'
      };
    }

    // Check for reserved names (Windows)
    const reservedNames = /^(CON|PRN|AUX|NUL|COM[1-9]|LPT[1-9])(\.|$)/i;
    if (reservedNames.test(fileName)) {
      return {
        isValid: false,
        error: 'File name uses reserved system name'
      };
    }

    return { isValid: true };
  }

  /**
   * Validate file headers (magic numbers) to ensure file type matches MIME type
   */
  private static async validateFileHeaders(file: File): Promise<FileValidationResult> {
    try {
      const buffer = await file.slice(0, 16).arrayBuffer();
      const bytes = new Uint8Array(buffer);

      const signatures: Record<string, number[]> = {
        'application/pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
        'image/jpeg': [0xFF, 0xD8, 0xFF],
        'image/png': [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
        'image/gif': [0x47, 0x49, 0x46],
        'image/bmp': [0x42, 0x4D],
        'image/tiff': [0x49, 0x49, 0x2A, 0x00], // TIFF little-endian
        'image/webp': [0x52, 0x49, 0x46, 0x46], // RIFF header (WebP)
        // Office documents (ZIP-based)
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [0x50, 0x4B, 0x03, 0x04],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [0x50, 0x4B, 0x03, 0x04],
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': [0x50, 0x4B, 0x03, 0x04],
        // Legacy Office documents
        'application/msword': [0xD0, 0xCF, 0x11, 0xE0], // OLE Compound Document
        'application/vnd.ms-excel': [0xD0, 0xCF, 0x11, 0xE0],
        'application/vnd.ms-powerpoint': [0xD0, 0xCF, 0x11, 0xE0],
        // OpenDocument formats (ZIP-based)
        'application/vnd.oasis.opendocument.text': [0x50, 0x4B, 0x03, 0x04],
        'application/vnd.oasis.opendocument.spreadsheet': [0x50, 0x4B, 0x03, 0x04],
        'application/vnd.oasis.opendocument.presentation': [0x50, 0x4B, 0x03, 0x04],
        // RTF
        'application/rtf': [0x7B, 0x5C, 0x72, 0x74, 0x66], // {\rtf
      };

      const expectedSignature = signatures[file.type];
      if (expectedSignature) {
        for (let i = 0; i < expectedSignature.length; i++) {
          if (bytes[i] !== expectedSignature[i]) {
            return {
              isValid: false,
              error: `File header mismatch for ${file.type} - possible file type spoofing`
            };
          }
        }
      }

      return { isValid: true };

    } catch (error) {
      console.error('Header validation error:', error);
      return {
        isValid: false,
        error: 'Failed to validate file headers'
      };
    }
  }

  /**
   * Extract file metadata including hash, dimensions, etc.
   */
  private static async extractFileMetadata(file: File): Promise<FileMetadata> {
    try {
      // Generate hash
      const buffer = await file.arrayBuffer();
      const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

      const metadata: FileMetadata = {
        hash,
        mimeType: file.type,
        size: file.size,
        createdAt: file.lastModified ? new Date(file.lastModified) : undefined
      };

      // Extract image dimensions for image files
      if (file.type.startsWith('image/')) {
        try {
          const dimensions = await this.getImageDimensions(file);
          metadata.dimensions = dimensions;
        } catch (error) {
          console.warn('Failed to extract image dimensions:', error);
        }
      }

      // Extract text content for text files
      if (file.type === 'text/plain' || file.type === 'text/csv') {
        try {
          const textContent = await file.text();
          metadata.textContent = textContent.substring(0, 1000); // First 1000 chars
          metadata.encoding = 'utf-8';
        } catch (error) {
          console.warn('Failed to extract text content:', error);
        }
      }

      return metadata;

    } catch (error) {
      console.error('Metadata extraction error:', error);
      return {
        hash: '',
        mimeType: file.type,
        size: file.size
      };
    }
  }

  /**
   * Get image dimensions
   */
  private static async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);

      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        });
      };

      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };

      img.src = url;
    });
  }

  /**
   * Scan file for malware (placeholder implementation)
   */
  private static async scanForMalware(file: File): Promise<FileValidationResult> {
    try {
      // In a real implementation, this would integrate with:
      // - ClamAV
      // - VirusTotal API
      // - AWS GuardDuty
      // - Azure Security Center

      const virusScanEnabled = Deno.env.get('VIRUS_SCAN_ENABLED') === 'true';
      if (!virusScanEnabled) {
        return { isValid: true };
      }

      // Placeholder: Check file size and type for suspicious patterns
      if (file.size > 100 * 1024 * 1024) { // > 100MB
        console.warn(`Large file detected: ${file.name} (${file.size} bytes)`);
      }

      // Check for suspicious file patterns
      const suspiciousExtensions = /\.(exe|bat|cmd|scr|pif|vbs|js|jar|com|pif)$/i;
      if (suspiciousExtensions.test(file.name)) {
        return {
          isValid: false,
          error: 'File type not allowed for security reasons'
        };
      }

      return { isValid: true };

    } catch (error) {
      console.error('Malware scan error:', error);
      // Fail open - don't block uploads on scan errors
      return { isValid: true };
    }
  }

  /**
   * Get maximum file size for key type
   */
  static getMaxFileSize(keyType: 'test' | 'production'): number {
    return keyType === 'test' ? 10 * 1024 * 1024 : 50 * 1024 * 1024;
  }

  /**
   * Get supported MIME types
   */
  static getSupportedMimeTypes(): string[] {
    return [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint',
      'application/vnd.oasis.opendocument.text',
      'application/vnd.oasis.opendocument.spreadsheet',
      'application/vnd.oasis.opendocument.presentation',
      'application/rtf',
      'text/plain',
      'text/csv',
      'image/jpeg',
      'image/png',
      'image/tiff',
      'image/gif',
      'image/bmp',
      'image/webp'
    ];
  }
}