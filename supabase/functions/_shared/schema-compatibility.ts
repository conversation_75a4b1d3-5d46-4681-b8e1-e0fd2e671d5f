import { 
  JSONSchema, 
  CompatibilityResult, 
  CompatibilityIssue,
  ValidationErrorCode 
} from '../../../types/schema.ts';

/**
 * Schema Compatibility Checker
 * Analyzes schema changes to detect breaking changes and compatibility issues
 */
export class SchemaCompatibilityChecker {
  
  /**
   * Check if new schema is backward compatible with old schema
   */
  checkCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityResult {
    const issues: CompatibilityIssue[] = [];

    try {
      // Check required fields changes
      const requiredFieldIssues = this.checkRequiredFields(oldSchema, newSchema);
      issues.push(...requiredFieldIssues);

      // Check field type changes
      const typeChanges = this.checkTypeCompatibility(oldSchema, newSchema);
      issues.push(...typeChanges);

      // Check enum value changes
      const enumChanges = this.checkEnumCompatibility(oldSchema, newSchema);
      issues.push(...enumChanges);

      // Check property additions/removals
      const propertyChanges = this.checkPropertyChanges(oldSchema, newSchema);
      issues.push(...propertyChanges);

      // Check constraint changes (min/max, length, etc.)
      const constraintChanges = this.checkConstraintCompatibility(oldSchema, newSchema);
      issues.push(...constraintChanges);

      // Check format changes
      const formatChanges = this.checkFormatCompatibility(oldSchema, newSchema);
      issues.push(...formatChanges);

      return {
        compatible: !issues.some(i => i.type === 'breaking'),
        issues: issues
      };
    } catch {
      throw new Error(`${ValidationErrorCode.COMPATIBILITY_CHECK_FAILED}: ${error.message}`);
    }
  }

  /**
   * Check changes in required fields
   */
  private checkRequiredFields(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldRequired = oldSchema.required || [];
    const newRequired = newSchema.required || [];

    // Breaking change: new required fields
    for (const field of newRequired) {
      if (!oldRequired.includes(field)) {
        issues.push({
          type: 'breaking',
          field: field,
          message: `New required field '${field}' added`,
          impact: 'Existing data may fail validation'
        });
      }
    }

    // Non-breaking change: removed required fields (now optional)
    for (const field of oldRequired) {
      if (!newRequired.includes(field)) {
        issues.push({
          type: 'warning',
          field: field,
          message: `Required field '${field}' is now optional`,
          impact: 'May affect data consistency expectations'
        });
      }
    }

    return issues;
  }

  /**
   * Check field type compatibility
   */
  private checkTypeCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldProps = oldSchema.properties || {};
    const newProps = newSchema.properties || {};

    for (const [fieldName, oldProp] of Object.entries(oldProps)) {
      const newProp = newProps[fieldName];
      
      if (newProp && oldProp.type !== newProp.type) {
        const compatible = this.areTypesCompatible(oldProp.type, newProp.type);

        issues.push({
          type: compatible ? 'warning' : 'breaking',
          field: fieldName,
          message: `Field type changed from ${oldProp.type} to ${newProp.type}`,
          impact: compatible
            ? 'May require data conversion'
            : 'Will break existing data structures'
        });
      }
    }

    return issues;
  }

  /**
   * Check enum value compatibility
   */
  private checkEnumCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldProps = oldSchema.properties || {};
    const newProps = newSchema.properties || {};

    for (const [fieldName, oldProp] of Object.entries(oldProps)) {
      const newProp = newProps[fieldName];
      
      if (oldProp.enum && newProp?.enum) {
        const oldValues = new Set(oldProp.enum);
        const newValues = new Set(newProp.enum);

        // Check for removed enum values (breaking)
        for (const value of oldValues) {
          if (!newValues.has(value)) {
            issues.push({
              type: 'breaking',
              field: fieldName,
              message: `Enum value '${value}' removed from field '${fieldName}'`,
              impact: 'Existing data with this value will fail validation'
            });
          }
        }

        // Check for added enum values (non-breaking)
        for (const value of newValues) {
          if (!oldValues.has(value)) {
            issues.push({
              type: 'info',
              field: fieldName,
              message: `Enum value '${value}' added to field '${fieldName}'`,
              impact: 'Expanded options available'
            });
          }
        }
      } else if (oldProp.enum && !newProp?.enum) {
        // Enum constraint removed - generally non-breaking
        issues.push({
          type: 'warning',
          field: fieldName,
          message: `Enum constraint removed from field '${fieldName}'`,
          impact: 'Field now accepts any value of the base type'
        });
      } else if (!oldProp.enum && newProp?.enum) {
        // Enum constraint added - potentially breaking
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Enum constraint added to field '${fieldName}'`,
          impact: 'Existing data may not match new allowed values'
        });
      }
    }

    return issues;
  }

  /**
   * Check property additions and removals
   */
  private checkPropertyChanges(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldProps = Object.keys(oldSchema.properties || {});
    const newProps = Object.keys(newSchema.properties || {});

    // Properties removed from schema
    for (const prop of oldProps) {
      if (!newProps.includes(prop)) {
        const wasRequired = (oldSchema.required || []).includes(prop);
        issues.push({
          type: wasRequired ? 'breaking' : 'warning',
          field: prop,
          message: `Property '${prop}' removed from schema`,
          impact: wasRequired 
            ? 'Required field no longer accepted'
            : 'Optional field no longer accepted'
        });
      }
    }

    // New properties added to schema (generally non-breaking)
    for (const prop of newProps) {
      if (!oldProps.includes(prop)) {
        const isRequired = (newSchema.required || []).includes(prop);
        if (!isRequired) {
          issues.push({
            type: 'info',
            field: prop,
            message: `New optional property '${prop}' added to schema`,
            impact: 'Additional data can now be captured'
          });
        }
        // Required property additions are already caught in checkRequiredFields
      }
    }

    return issues;
  }

  /**
   * Check constraint compatibility (min/max, length, etc.)
   */
  private checkConstraintCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldProps = oldSchema.properties || {};
    const newProps = newSchema.properties || {};

    for (const [fieldName, oldProp] of Object.entries(oldProps)) {
      const newProp = newProps[fieldName];
      if (!newProp) continue;

      // Check numeric constraints
      this.checkNumericConstraints(fieldName, oldProp, newProp, issues);
      
      // Check string length constraints
      this.checkStringConstraints(fieldName, oldProp, newProp, issues);
      
      // Check array constraints
      this.checkArrayConstraints(fieldName, oldProp, newProp, issues);
    }

    return issues;
  }

  /**
   * Check numeric constraint changes
   */
  private checkNumericConstraints(
    fieldName: string, 
    oldProp: JSONSchema, 
    newProp: JSONSchema, 
    issues: CompatibilityIssue[]
  ): void {
    // Minimum value changes
    if (oldProp.minimum !== undefined && newProp.minimum !== undefined) {
      if (newProp.minimum > oldProp.minimum) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Minimum value increased from ${oldProp.minimum} to ${newProp.minimum}`,
          impact: 'Some existing values may no longer be valid'
        });
      } else if (newProp.minimum < oldProp.minimum) {
        issues.push({
          type: 'info',
          field: fieldName,
          message: `Minimum value decreased from ${oldProp.minimum} to ${newProp.minimum}`,
          impact: 'More values now accepted'
        });
      }
    }

    // Maximum value changes
    if (oldProp.maximum !== undefined && newProp.maximum !== undefined) {
      if (newProp.maximum < oldProp.maximum) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Maximum value decreased from ${oldProp.maximum} to ${newProp.maximum}`,
          impact: 'Some existing values may no longer be valid'
        });
      } else if (newProp.maximum > oldProp.maximum) {
        issues.push({
          type: 'info',
          field: fieldName,
          message: `Maximum value increased from ${oldProp.maximum} to ${newProp.maximum}`,
          impact: 'More values now accepted'
        });
      }
    }
  }

  /**
   * Check string constraint changes
   */
  private checkStringConstraints(
    fieldName: string, 
    oldProp: JSONSchema, 
    newProp: JSONSchema, 
    issues: CompatibilityIssue[]
  ): void {
    // Min length changes
    if (oldProp.minLength !== undefined && newProp.minLength !== undefined) {
      if (newProp.minLength > oldProp.minLength) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Minimum length increased from ${oldProp.minLength} to ${newProp.minLength}`,
          impact: 'Some existing strings may be too short'
        });
      }
    }

    // Max length changes
    if (oldProp.maxLength !== undefined && newProp.maxLength !== undefined) {
      if (newProp.maxLength < oldProp.maxLength) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Maximum length decreased from ${oldProp.maxLength} to ${newProp.maxLength}`,
          impact: 'Some existing strings may be too long'
        });
      }
    }

    // Pattern changes
    if (oldProp.pattern !== newProp.pattern) {
      if (oldProp.pattern && newProp.pattern) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Pattern constraint changed`,
          impact: 'Existing strings may not match new pattern'
        });
      } else if (oldProp.pattern && !newProp.pattern) {
        issues.push({
          type: 'warning',
          field: fieldName,
          message: `Pattern constraint removed`,
          impact: 'Field now accepts any string format'
        });
      } else if (!oldProp.pattern && newProp.pattern) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Pattern constraint added`,
          impact: 'Existing strings may not match new pattern'
        });
      }
    }
  }

  /**
   * Check array constraint changes
   */
  private checkArrayConstraints(
    fieldName: string, 
    oldProp: JSONSchema, 
    newProp: JSONSchema, 
    issues: CompatibilityIssue[]
  ): void {
    // Min items changes
    if (oldProp.minItems !== undefined && newProp.minItems !== undefined) {
      if (newProp.minItems > oldProp.minItems) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Minimum items increased from ${oldProp.minItems} to ${newProp.minItems}`,
          impact: 'Some existing arrays may have too few items'
        });
      }
    }

    // Max items changes
    if (oldProp.maxItems !== undefined && newProp.maxItems !== undefined) {
      if (newProp.maxItems < oldProp.maxItems) {
        issues.push({
          type: 'breaking',
          field: fieldName,
          message: `Maximum items decreased from ${oldProp.maxItems} to ${newProp.maxItems}`,
          impact: 'Some existing arrays may have too many items'
        });
      }
    }
  }

  /**
   * Check format compatibility
   */
  private checkFormatCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldProps = oldSchema.properties || {};
    const newProps = newSchema.properties || {};

    for (const [fieldName, oldProp] of Object.entries(oldProps)) {
      const newProp = newProps[fieldName];
      if (!newProp) continue;

      if (oldProp.format !== newProp.format) {
        if (oldProp.format && newProp.format) {
          const compatible = this.areFormatsCompatible(oldProp.format, newProp.format);
          issues.push({
            type: compatible ? 'warning' : 'breaking',
            field: fieldName,
            message: `Format changed from ${oldProp.format} to ${newProp.format}`,
            impact: compatible 
              ? 'Format change may require data conversion'
              : 'Existing data may not match new format'
          });
        } else if (oldProp.format && !newProp.format) {
          issues.push({
            type: 'warning',
            field: fieldName,
            message: `Format constraint removed`,
            impact: 'Field now accepts any string format'
          });
        } else if (!oldProp.format && newProp.format) {
          issues.push({
            type: 'breaking',
            field: fieldName,
            message: `Format constraint added: ${newProp.format}`,
            impact: 'Existing strings may not match new format'
          });
        }
      }
    }

    return issues;
  }

  /**
   * Check if type changes are compatible
   */
  private areTypesCompatible(oldType: string, newType: string): boolean {
    // Define compatible type transitions
    const compatibleTransitions: Record<string, string[]> = {
      'integer': ['number'],           // integer -> number is safe
      'string': ['string'],            // string -> string (but format may change)
      'number': [],                    // number -> other types generally not safe
      'boolean': [],                   // boolean -> other types not safe
      'array': [],                     // array structure changes need careful review
      'object': []                     // object structure changes need careful review
    };

    return compatibleTransitions[oldType]?.includes(newType) || false;
  }

  /**
   * Check if format changes are compatible
   */
  private areFormatsCompatible(oldFormat: string, newFormat: string): boolean {
    // Define compatible format transitions
    const compatibleFormats: Record<string, string[]> = {
      'date': ['date-time'],           // date -> date-time adds time component
      'email': [],                     // email format is quite specific
      'uri': ['uri-reference'],        // uri -> uri-reference is more permissive
      'currency': [],                  // currency codes are specific
      'phone': []                      // phone formats are specific
    };

    return compatibleFormats[oldFormat]?.includes(newFormat) || false;
  }

  /**
   * Get summary of compatibility check
   */
  getSummary(result: CompatibilityResult): string {
    const breakingCount = result.issues.filter(i => i.type === 'breaking').length;
    const warningCount = result.issues.filter(i => i.type === 'warning').length;
    const infoCount = result.issues.filter(i => i.type === 'info').length;

    if (result.compatible) {
      if (result.issues.length === 0) {
        return 'Schema is fully backward compatible with no changes detected.';
      } else {
        return `Schema is backward compatible. Found ${warningCount} warnings and ${infoCount} informational items.`;
      }
    } else {
      return `Schema has ${breakingCount} breaking changes that will affect backward compatibility.`;
    }
  }
}