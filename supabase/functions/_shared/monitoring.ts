// ================================================================================
// PERFORMANCE MONITORING AND ALERTING SYSTEM
// ================================================================================

import { supabase } from "./supabase.ts";

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  endpoint: string;
  method: string;
  status_code: number;
  response_time_ms: number;
  db_query_time_ms?: number;
  ai_service_time_ms?: number;
  queue_wait_time_ms?: number;
  customer_id?: string;
  api_key_id?: string;
  correlation_id?: string;
  ip_address?: string;
  user_agent?: string;
  request_size_bytes?: number;
  response_size_bytes?: number;
  timestamp: string;
}

/**
 * Alert threshold configuration
 */
export interface AlertThreshold {
  id?: string;
  metric_name: string;
  threshold_type: 'response_time' | 'error_rate' | 'availability' | 'security_events' | 'throughput';
  threshold_value: number;
  duration_minutes: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  webhook_url?: string;
  email_recipients?: string[];
}

/**
 * Alert notification interface
 */
export interface AlertNotification {
  threshold_id: string;
  metric_name: string;
  threshold_value: number;
  actual_value: number;
  severity: string;
  duration_minutes: number;
  correlation_id: string;
  timestamp: string;
  details: Record<string, any>;
}

/**
 * Performance metrics collector with batching
 */
export class MetricsCollector {
  private static instance: MetricsCollector;
  private metrics: PerformanceMetrics[] = [];
  private batchSize = 50;
  private flushInterval = 30000; // 30 seconds
  private lastFlush = Date.now();
  
  private constructor() {
    // Set up periodic flush
    setInterval(() => this.flush(), this.flushInterval);
  }
  
  static getInstance(): MetricsCollector {
    if (!MetricsCollector.instance) {
      MetricsCollector.instance = new MetricsCollector();
    }
    return MetricsCollector.instance;
  }
  
  /**
   * Record performance metrics
   */
  async recordMetrics(metrics: PerformanceMetrics): Promise<void> {
    try {
      this.metrics.push({
        ...metrics,
        timestamp: new Date().toISOString(),
      });
      
      // Flush if batch is full or if it's been too long
      if (this.metrics.length >= this.batchSize || 
          Date.now() - this.lastFlush > this.flushInterval) {
        await this.flush();
      }
    } catch (error) {
      console.error('Failed to record metrics:', error);
    }
  }
  
  /**
   * Flush metrics to database
   */
  async flush(): Promise<void> {
    if (this.metrics.length === 0) return;
    
    try {
      const metricsToFlush = [...this.metrics];
      this.metrics = [];
      this.lastFlush = Date.now();
      
      const { error } = await supabase
        .from('performance_metrics')
        .insert(metricsToFlush);
      
      if (error) {
        console.error('Failed to flush metrics to database:', error);
        // Put metrics back if flush failed
        this.metrics.unshift(...metricsToFlush);
      }
    } catch (error) {
      console.error('Error flushing metrics:', error);
    }
  }
  
  /**
   * Get current metrics buffer size
   */
  getBufferSize(): number {
    return this.metrics.length;
  }
}

/**
 * Real-time performance monitor
 */
export class PerformanceMonitor {
  private static thresholds: Map<string, AlertThreshold> = new Map();
  private static lastCheck = Date.now();
  private static checkInterval = 60000; // 1 minute
  
  /**
   * Initialize performance monitoring
   */
  static async initialize(): Promise<void> {
    try {
      // Load alert thresholds from database
      await this.loadAlertThresholds();
      
      // Set up periodic threshold checking
      setInterval(() => this.checkAlertThresholds(), this.checkInterval);
      
      console.log('Performance monitoring initialized');
    } catch (error) {
      console.error('Failed to initialize performance monitoring:', error);
    }
  }
  
  /**
   * Load alert thresholds from database
   */
  private static async loadAlertThresholds(): Promise<void> {
    try {
      const { data: thresholds, error } = await supabase
        .from('alert_thresholds')
        .select('*')
        .eq('enabled', true);
      
      if (error) {
        console.error('Failed to load alert thresholds:', error);
        return;
      }
      
      this.thresholds.clear();
      for (const threshold of thresholds || []) {
        this.thresholds.set(threshold.id, threshold);
      }
    } catch (error) {
      console.error('Error loading alert thresholds:', error);
    }
  }
  
  /**
   * Check all alert thresholds
   */
  private static async checkAlertThresholds(): Promise<void> {
    try {
      const now = Date.now();
      const timeSinceLastCheck = now - this.lastCheck;
      this.lastCheck = now;
      
      for (const threshold of this.thresholds.values()) {
        await this.checkThreshold(threshold, timeSinceLastCheck);
      }
    } catch (error) {
      console.error('Error checking alert thresholds:', error);
    }
  }
  
  /**
   * Check individual threshold
   */
  private static async checkThreshold(
    threshold: AlertThreshold,
    timeSinceLastCheck: number
  ): Promise<void> {
    try {
      const startTime = new Date(Date.now() - threshold.duration_minutes * 60 * 1000);
      let shouldAlert = false;
      let actualValue = 0;
      let additionalDetails: Record<string, any> = {};
      
      switch (threshold.threshold_type) {
        case 'response_time':
          const responseTimeResult = await this.checkResponseTime(startTime, threshold.threshold_value);
          shouldAlert = responseTimeResult.shouldAlert;
          actualValue = responseTimeResult.avgTime;
          additionalDetails = responseTimeResult.details;
          break;
          
        case 'error_rate':
          const errorRateResult = await this.checkErrorRate(startTime, threshold.threshold_value);
          shouldAlert = errorRateResult.shouldAlert;
          actualValue = errorRateResult.errorRate;
          additionalDetails = errorRateResult.details;
          break;
          
        case 'security_events':
          const securityResult = await this.checkSecurityEvents(startTime, threshold.threshold_value);
          shouldAlert = securityResult.shouldAlert;
          actualValue = securityResult.eventCount;
          additionalDetails = securityResult.details;
          break;
          
        case 'throughput':
          const throughputResult = await this.checkThroughput(startTime, threshold.threshold_value);
          shouldAlert = throughputResult.shouldAlert;
          actualValue = throughputResult.requestCount;
          additionalDetails = throughputResult.details;
          break;
          
        case 'availability':
          const availabilityResult = await this.checkAvailability(startTime, threshold.threshold_value);
          shouldAlert = availabilityResult.shouldAlert;
          actualValue = availabilityResult.availability;
          additionalDetails = availabilityResult.details;
          break;
      }
      
      if (shouldAlert) {
        await this.sendAlert({
          threshold_id: threshold.id!,
          metric_name: threshold.metric_name,
          threshold_value: threshold.threshold_value,
          actual_value: actualValue,
          severity: threshold.severity,
          duration_minutes: threshold.duration_minutes,
          correlation_id: crypto.randomUUID(),
          timestamp: new Date().toISOString(),
          details: additionalDetails,
        });
      }
    } catch (error) {
      console.error(`Error checking threshold ${threshold.metric_name}:`, error);
    }
  }
  
  /**
   * Check response time threshold
   */
  private static async checkResponseTime(
    startTime: Date,
    thresholdMs: number
  ): Promise<{ shouldAlert: boolean; avgTime: number; details: Record<string, any> }> {
    const { data, error } = await supabase
      .from('performance_metrics')
      .select('response_time_ms, endpoint')
      .gte('created_at', startTime.toISOString())
      .gt('response_time_ms', thresholdMs);
    
    if (error || !data) {
      return { shouldAlert: false, avgTime: 0, details: { error: error?.message } };
    }
    
    if (data.length === 0) {
      return { shouldAlert: false, avgTime: 0, details: {} };
    }
    
    const avgTime = data.reduce((sum, row) => sum + row.response_time_ms, 0) / data.length;
    const slowEndpoints = data.reduce((acc: Record<string, number>, row) => {
      acc[row.endpoint] = (acc[row.endpoint] || 0) + 1;
      return acc;
    }, {});
    
    return {
      shouldAlert: data.length > 0,
      avgTime,
      details: {
        slow_requests_count: data.length,
        avg_response_time_ms: avgTime,
        slow_endpoints: slowEndpoints,
      },
    };
  }
  
  /**
   * Check error rate threshold
   */
  private static async checkErrorRate(
    startTime: Date,
    thresholdRate: number
  ): Promise<{ shouldAlert: boolean; errorRate: number; details: Record<string, any> }> {
    const { data, error } = await supabase
      .from('performance_metrics')
      .select('status_code, endpoint')
      .gte('created_at', startTime.toISOString());
    
    if (error || !data || data.length === 0) {
      return { shouldAlert: false, errorRate: 0, details: { error: error?.message } };
    }
    
    const totalRequests = data.length;
    const errorRequests = data.filter(row => row.status_code >= 400);
    const errorRate = errorRequests.length / totalRequests;
    
    const errorsByEndpoint = errorRequests.reduce((acc: Record<string, number>, row) => {
      acc[row.endpoint] = (acc[row.endpoint] || 0) + 1;
      return acc;
    }, {});
    
    const errorsByStatus = errorRequests.reduce((acc: Record<string, number>, row) => {
      acc[row.status_code] = (acc[row.status_code] || 0) + 1;
      return acc;
    }, {});
    
    return {
      shouldAlert: errorRate > thresholdRate,
      errorRate,
      details: {
        total_requests: totalRequests,
        error_requests: errorRequests.length,
        error_rate: errorRate,
        errors_by_endpoint: errorsByEndpoint,
        errors_by_status: errorsByStatus,
      },
    };
  }
  
  /**
   * Check security events threshold
   */
  private static async checkSecurityEvents(
    startTime: Date,
    thresholdCount: number
  ): Promise<{ shouldAlert: boolean; eventCount: number; details: Record<string, any> }> {
    const { data, error } = await supabase
      .from('security_events')
      .select('event_type, severity, ip_address')
      .gte('created_at', startTime.toISOString())
      .in('severity', ['high', 'critical']);
    
    if (error || !data) {
      return { shouldAlert: false, eventCount: 0, details: { error: error?.message } };
    }
    
    const eventsByType = data.reduce((acc: Record<string, number>, row) => {
      acc[row.event_type] = (acc[row.event_type] || 0) + 1;
      return acc;
    }, {});
    
    const eventsBySeverity = data.reduce((acc: Record<string, number>, row) => {
      acc[row.severity] = (acc[row.severity] || 0) + 1;
      return acc;
    }, {});
    
    const topIPs = Object.entries(
      data.reduce((acc: Record<string, number>, row) => {
        acc[row.ip_address] = (acc[row.ip_address] || 0) + 1;
        return acc;
      }, {})
    ).sort(([,a], [,b]) => b - a).slice(0, 5);
    
    return {
      shouldAlert: data.length > thresholdCount,
      eventCount: data.length,
      details: {
        events_by_type: eventsByType,
        events_by_severity: eventsBySeverity,
        top_threat_ips: topIPs,
      },
    };
  }
  
  /**
   * Check throughput threshold
   */
  private static async checkThroughput(
    startTime: Date,
    thresholdCount: number
  ): Promise<{ shouldAlert: boolean; requestCount: number; details: Record<string, any> }> {
    const { data, error } = await supabase
      .from('performance_metrics')
      .select('endpoint, method')
      .gte('created_at', startTime.toISOString());
    
    if (error || !data) {
      return { shouldAlert: false, requestCount: 0, details: { error: error?.message } };
    }
    
    const requestCount = data.length;
    const requestsByEndpoint = data.reduce((acc: Record<string, number>, row) => {
      const key = `${row.method} ${row.endpoint}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {});
    
    return {
      shouldAlert: requestCount < thresholdCount,
      requestCount,
      details: {
        total_requests: requestCount,
        requests_by_endpoint: requestsByEndpoint,
      },
    };
  }
  
  /**
   * Check availability threshold
   */
  private static async checkAvailability(
    startTime: Date,
    thresholdPercent: number
  ): Promise<{ shouldAlert: boolean; availability: number; details: Record<string, any> }> {
    const { data, error } = await supabase
      .from('performance_metrics')
      .select('status_code, endpoint')
      .gte('created_at', startTime.toISOString());
    
    if (error || !data || data.length === 0) {
      return { shouldAlert: true, availability: 0, details: { error: error?.message || 'No data' } };
    }
    
    const totalRequests = data.length;
    const successfulRequests = data.filter(row => row.status_code < 500).length;
    const availability = successfulRequests / totalRequests;
    
    const downtime = data.filter(row => row.status_code >= 500);
    const downtimeByEndpoint = downtime.reduce((acc: Record<string, number>, row) => {
      acc[row.endpoint] = (acc[row.endpoint] || 0) + 1;
      return acc;
    }, {});
    
    return {
      shouldAlert: availability < thresholdPercent,
      availability,
      details: {
        total_requests: totalRequests,
        successful_requests: successfulRequests,
        availability_percent: availability * 100,
        downtime_events: downtime.length,
        downtime_by_endpoint: downtimeByEndpoint,
      },
    };
  }
  
  /**
   * Send alert notification
   */
  private static async sendAlert(alert: AlertNotification): Promise<void> {
    try {
      // Log the alert
      await supabase.from('audit_logs').insert({
        action: 'alert_threshold_breached',
        event_category: 'monitoring',
        status: 'warning',
        details: {
          alert_type: 'performance_threshold',
          ...alert,
        },
        correlation_id: alert.correlation_id,
        created_at: new Date().toISOString(),
      });
      
      // Get threshold configuration for webhook/email
      const threshold = this.thresholds.get(alert.threshold_id);
      if (!threshold) return;
      
      // Send webhook notification
      if (threshold.webhook_url) {
        await this.sendWebhookAlert(threshold.webhook_url, alert);
      }
      
      // Send email notifications (if configured)
      if (threshold.email_recipients && threshold.email_recipients.length > 0) {
        await this.sendEmailAlert(threshold.email_recipients, alert);
      }
      
      // Log high-severity alerts to security events
      if (alert.severity === 'critical' || alert.severity === 'high') {
        await supabase.from('security_events').insert({
          event_type: 'suspicious_input', // Generic type for monitoring alerts
          threat_details: {
            alert_type: 'performance_threshold',
            metric_name: alert.metric_name,
            threshold_value: alert.threshold_value,
            actual_value: alert.actual_value,
            severity: alert.severity,
          },
          ip_address: 'system',
          user_agent: 'monitoring_system',
          severity: alert.severity,
          correlation_id: alert.correlation_id,
          created_at: new Date().toISOString(),
        });
      }
      
    } catch (error) {
      console.error('Failed to send alert:', error);
    }
  }
  
  /**
   * Send webhook alert
   */
  private static async sendWebhookAlert(webhookUrl: string, alert: AlertNotification): Promise<void> {
    try {
      const payload = {
        type: 'performance_alert',
        alert,
        timestamp: new Date().toISOString(),
        platform: 'IDP Platform',
      };
      
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'IDP-Platform-Monitor/1.0',
        },
        body: JSON.stringify(payload),
      });
      
      if (!response.ok) {
        console.error(`Webhook alert failed: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('Error sending webhook alert:', error);
    }
  }
  
  /**
   * Send email alert (placeholder - implement with your email service)
   */
  private static async sendEmailAlert(recipients: string[], alert: AlertNotification): Promise<void> {
    try {
      // This would integrate with your email service (SendGrid, AWS SES, etc.)
      console.log('Email alert would be sent to:', recipients, 'Alert:', alert);
      
      // Example implementation with a webhook to email service
      const emailServiceUrl = Deno.env.get('EMAIL_SERVICE_WEBHOOK_URL');
      if (emailServiceUrl) {
        await fetch(emailServiceUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            to: recipients,
            subject: `[${alert.severity.toUpperCase()}] IDP Platform Alert: ${alert.metric_name}`,
            body: this.formatEmailBody(alert),
          }),
        });
      }
    } catch (error) {
      console.error('Error sending email alert:', error);
    }
  }
  
  /**
   * Format email body for alerts
   */
  private static formatEmailBody(alert: AlertNotification): string {
    return `
Alert Details:
- Metric: ${alert.metric_name}
- Severity: ${alert.severity.toUpperCase()}
- Threshold: ${alert.threshold_value}
- Actual Value: ${alert.actual_value}
- Duration: ${alert.duration_minutes} minutes
- Time: ${alert.timestamp}
- Correlation ID: ${alert.correlation_id}

Additional Details:
${JSON.stringify(alert.details, null, 2)}

Please investigate and take appropriate action.

---
IDP Platform Monitoring System
    `.trim();
  }
}

/**
 * Performance analytics and insights
 */
export class PerformanceAnalytics {
  /**
   * Get performance summary for a time period
   */
  static async getPerformanceSummary(
    startDate: string,
    endDate: string,
    customerId?: string
  ): Promise<any> {
    try {
      let query = supabase
        .from('performance_metrics')
        .select('*')
        .gte('created_at', startDate)
        .lte('created_at', endDate);
      
      if (customerId) {
        query = query.eq('customer_id', customerId);
      }
      
      const { data, error } = await query;
      
      if (error || !data) {
        return { error: error?.message || 'No data found' };
      }
      
      const totalRequests = data.length;
      const avgResponseTime = data.reduce((sum, row) => sum + row.response_time_ms, 0) / totalRequests;
      const errorRate = data.filter(row => row.status_code >= 400).length / totalRequests;
      
      const p95ResponseTime = this.calculatePercentile(
        data.map(row => row.response_time_ms).sort((a, b) => a - b),
        95
      );
      
      const requestsByEndpoint = data.reduce((acc: Record<string, number>, row) => {
        const key = `${row.method} ${row.endpoint}`;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {});
      
      const requestsByHour = data.reduce((acc: Record<string, number>, row) => {
        const hour = new Date(row.created_at).toISOString().slice(0, 13);
        acc[hour] = (acc[hour] || 0) + 1;
        return acc;
      }, {});
      
      return {
        period: { start: startDate, end: endDate },
        total_requests: totalRequests,
        avg_response_time_ms: Math.round(avgResponseTime),
        p95_response_time_ms: p95ResponseTime,
        error_rate: Math.round(errorRate * 10000) / 100, // As percentage
        requests_by_endpoint: requestsByEndpoint,
        requests_by_hour: requestsByHour,
      };
    } catch (error) {
      console.error('Error generating performance summary:', error);
      return { error: 'Failed to generate performance summary' };
    }
  }
  
  /**
   * Calculate percentile for array of numbers
   */
  private static calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }
  
  /**
   * Detect performance anomalies
   */
  static async detectAnomalies(lookbackHours: number = 24): Promise<any[]> {
    try {
      const startTime = new Date(Date.now() - lookbackHours * 60 * 60 * 1000);
      
      const { data, error } = await supabase
        .from('performance_metrics')
        .select('*')
        .gte('created_at', startTime.toISOString());
      
      if (error || !data) {
        return [];
      }
      
      const anomalies: any[] = [];
      
      // Check for response time anomalies
      const responseTimes = data.map(row => row.response_time_ms);
      const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const stdDev = Math.sqrt(
        responseTimes.reduce((sum, time) => sum + Math.pow(time - avgResponseTime, 2), 0) / 
        responseTimes.length
      );
      
      const responseTimeThreshold = avgResponseTime + (2 * stdDev); // 2 standard deviations
      const slowRequests = data.filter(row => row.response_time_ms > responseTimeThreshold);
      
      if (slowRequests.length > 0) {
        anomalies.push({
          type: 'slow_response_time',
          count: slowRequests.length,
          threshold_ms: Math.round(responseTimeThreshold),
          avg_response_time_ms: Math.round(avgResponseTime),
          details: slowRequests.slice(0, 5), // First 5 examples
        });
      }
      
      // Check for error spikes
      const errorsByHour = data.reduce((acc: Record<string, number>, row) => {
        const hour = new Date(row.created_at).toISOString().slice(0, 13);
        if (row.status_code >= 400) {
          acc[hour] = (acc[hour] || 0) + 1;
        }
        return acc;
      }, {});
      
      const hourlyErrorCounts = Object.values(errorsByHour);
      if (hourlyErrorCounts.length > 0) {
        const avgErrorsPerHour = hourlyErrorCounts.reduce((a, b) => a + b, 0) / hourlyErrorCounts.length;
        const errorSpikes = Object.entries(errorsByHour).filter(([, count]) => count > avgErrorsPerHour * 2);
        
        if (errorSpikes.length > 0) {
          anomalies.push({
            type: 'error_spike',
            avg_errors_per_hour: Math.round(avgErrorsPerHour),
            spike_hours: errorSpikes.map(([hour, count]) => ({ hour, error_count: count })),
          });
        }
      }
      
      return anomalies;
    } catch (error) {
      console.error('Error detecting anomalies:', error);
      return [];
    }
  }
}

/**
 * Request performance middleware for Edge Functions
 */
export function createPerformanceMiddleware() {
  return async (
    req: Request,
    handler: (req: Request) => Promise<Response>
  ): Promise<Response> => {
    const startTime = Date.now();
    const correlationId = req.headers.get('x-correlation-id') || crypto.randomUUID();
    const url = new URL(req.url);
    
    let response: Response;
    let dbTime = 0;
    let aiTime = 0;
    
    try {
      // Execute the actual handler
      response = await handler(req);
      
    } catch (error) {
      // Create error response
      response = new Response(
        JSON.stringify({ error: 'Internal server error' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Record performance metrics
    const metricsCollector = MetricsCollector.getInstance();
    await metricsCollector.recordMetrics({
      endpoint: url.pathname,
      method: req.method,
      status_code: response.status,
      response_time_ms: responseTime,
      db_query_time_ms: dbTime > 0 ? dbTime : undefined,
      ai_service_time_ms: aiTime > 0 ? aiTime : undefined,
      correlation_id: correlationId,
      ip_address: req.headers.get('x-forwarded-for') || req.headers.get('cf-connecting-ip') || undefined,
      user_agent: req.headers.get('user-agent') || undefined,
      request_size_bytes: parseInt(req.headers.get('content-length') || '0'),
      response_size_bytes: response.headers.get('content-length') ? 
        parseInt(response.headers.get('content-length')!) : undefined,
      timestamp: new Date().toISOString(),
    });
    
    // Add performance headers to response
    const headers = new Headers(response.headers);
    headers.set('X-Response-Time', `${responseTime}ms`);
    headers.set('X-Correlation-ID', correlationId);
    
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers,
    });
  };
}