/**
 * Performance Analyzer Utility
 * 
 * Advanced data analysis and insights for agent performance
 * Provides statistical analysis, trend detection, and predictive insights
 */

import type { SupabaseClient } from 'jsr:@supabase/supabase-js@2.57.4';
import type { Database } from '../../../types/database.types.ts';
import type {
  _AgentPerformanceMetrics,
  _AgentSummary,
  _PerformanceAlert
} from '../../../types/agent-performance.types.ts';

export interface PerformanceAnalysis {
  agent_id: string;
  analysis_date: Date;
  trends: {
    processing_time: TrendAnalysis;
    accuracy: TrendAnalysis;
    volume: TrendAnalysis;
    cost: TrendAnalysis;
  };
  anomalies: AnomalyDetection[];
  predictions: PerformancePrediction[];
  recommendations: PerformanceRecommendation[];
  health_score: number;
}

export interface TrendAnalysis {
  direction: 'improving' | 'declining' | 'stable';
  confidence: number;
  rate_of_change: number;
  significance: 'high' | 'medium' | 'low';
  data_points: Array<{ date: string; value: number }>;
}

export interface AnomalyDetection {
  metric: string;
  detected_at: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  expected_value: number;
  actual_value: number;
  deviation_percent: number;
}

export interface PerformancePrediction {
  metric: string;
  prediction_horizon_days: number;
  predicted_value: number;
  confidence_interval: { lower: number; upper: number };
  methodology: string;
}

export interface PerformanceRecommendation {
  category: 'optimization' | 'scaling' | 'maintenance' | 'alerting';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  expected_impact: string;
  implementation_effort: 'low' | 'medium' | 'high';
  actions: string[];
}

/**
 * Advanced performance analyzer with statistical methods
 */
export class PerformanceAnalyzer {
  constructor(private supabase: SupabaseClient<Database>) {}

  /**
   * Perform comprehensive performance analysis for an agent
   */
  async analyzeAgentPerformance(
    agentId: string, 
    analysisWindow: number = 30
  ): Promise<PerformanceAnalysis> {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - analysisWindow * 24 * 60 * 60 * 1000);

    // Get raw performance data
    const { data: rawData, error } = await this.supabase
      .from('agent_performance_logs')
      .select('*')
      .eq('agent_id', agentId)
      .gte('timestamp', startDate.toISOString())
      .order('timestamp', { ascending: true });

    if (error || !rawData || rawData.length === 0) {
      return this.getEmptyAnalysis(agentId);
    }

    // Get daily aggregations for trend analysis
    const { data: dailyData } = await this.supabase
      .from('agent_performance_daily')
      .select('*')
      .eq('agent_id', agentId)
      .gte('date', startDate.toISOString().split('T')[0])
      .order('date', { ascending: true });

    return {
      agent_id: agentId,
      analysis_date: new Date(),
      trends: this.analyzeTrends(dailyData || []),
      anomalies: this.detectAnomalies(rawData),
      predictions: this.generatePredictions(dailyData || []),
      recommendations: this.generateRecommendations(rawData, dailyData || []),
      health_score: this.calculateHealthScore(rawData, dailyData || [])
    };
  }

  /**
   * Analyze performance trends using statistical methods
   */
  private analyzeTrends(dailyData: any[]): PerformanceAnalysis['trends'] {
    return {
      processing_time: this.analyzeTrend(
        dailyData.map(d => ({ date: d.date, value: d.avg_processing_time_ms })),
        'processing_time'
      ),
      accuracy: this.analyzeTrend(
        dailyData.map(d => ({ date: d.date, value: d.avg_accuracy_score })),
        'accuracy'
      ),
      volume: this.analyzeTrend(
        dailyData.map(d => ({ date: d.date, value: d.total_requests })),
        'volume'
      ),
      cost: this.analyzeTrend(
        dailyData.map(d => ({ date: d.date, value: d.total_cost_usd })),
        'cost'
      )
    };
  }

  /**
   * Analyze individual trend using linear regression
   */
  private analyzeTrend(
    data: Array<{ date: string; value: number }>,
    metric: string
  ): TrendAnalysis {
    if (data.length < 3) {
      return {
        direction: 'stable',
        confidence: 0,
        rate_of_change: 0,
        significance: 'low',
        data_points: data
      };
    }

    // Simple linear regression
    const n = data.length;
    const x = data.map((_, i) => i); // Time index
    const y = data.map(d => d.value);

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Calculate R-squared for confidence
    const yMean = sumY / n;
    const ssTotal = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
    const ssResidual = y.reduce((sum, yi, i) => {
      const predicted = slope * x[i] + intercept;
      return sum + Math.pow(yi - predicted, 2);
    }, 0);
    const rSquared = ssTotal === 0 ? 0 : 1 - ssResidual / ssTotal;

    // Determine direction and significance
    const direction = Math.abs(slope) < 0.01 ? 'stable' 
                    : slope > 0 ? 'improving' 
                    : 'declining';

    const significance = rSquared > 0.7 ? 'high'
                       : rSquared > 0.4 ? 'medium'
                       : 'low';

    return {
      direction,
      confidence: Math.round(rSquared * 100),
      rate_of_change: parseFloat(slope.toFixed(4)),
      significance,
      data_points: data
    };
  }

  /**
   * Detect performance anomalies using statistical methods
   */
  private detectAnomalies(rawData: any[]): AnomalyDetection[] {
    const anomalies: AnomalyDetection[] = [];

    // Group by metric type for anomaly detection
    const metrics = {
      processing_time: rawData.map(d => d.processing_time_ms),
      accuracy: rawData.map(d => d.accuracy_score).filter(v => v !== null),
      confidence: rawData.map(d => d.confidence_score).filter(v => v !== null)
    };

    Object.entries(metrics).forEach(([metric, values]) => {
      if (values.length < 10) return; // Need sufficient data

      const mean = values.reduce((a, b) => a + b, 0) / values.length;
      const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
      const stdDev = Math.sqrt(variance);

      // Find outliers (values beyond 2 standard deviations)
      rawData.forEach((dataPoint, _index) => {
        const value = metric === 'processing_time' ? dataPoint.processing_time_ms
                    : metric === 'accuracy' ? dataPoint.accuracy_score
                    : dataPoint.confidence_score;

        if (value === null || value === undefined) return;

        const deviationFromMean = Math.abs(value - mean);
        const standardDeviations = deviationFromMean / stdDev;

        if (standardDeviations > 2) {
          const severity = standardDeviations > 3 ? 'critical'
                         : standardDeviations > 2.5 ? 'high'
                         : 'medium';

          anomalies.push({
            metric,
            detected_at: new Date(dataPoint.timestamp),
            severity,
            description: `${metric} value significantly deviates from normal range`,
            expected_value: parseFloat(mean.toFixed(2)),
            actual_value: value,
            deviation_percent: parseFloat(((deviationFromMean / mean) * 100).toFixed(1))
          });
        }
      });
    });

    return anomalies.slice(0, 10); // Limit to most recent/severe anomalies
  }

  /**
   * Generate performance predictions using trend analysis
   */
  private generatePredictions(dailyData: any[]): PerformancePrediction[] {
    if (dailyData.length < 7) return []; // Need at least a week of data

    const predictions: PerformancePrediction[] = [];

    // Predict processing time trend
    const processingTimes = dailyData.map(d => d.avg_processing_time_ms);
    const timeProjection = this.projectTrend(processingTimes, 7);
    
    if (timeProjection) {
      predictions.push({
        metric: 'processing_time_ms',
        prediction_horizon_days: 7,
        predicted_value: timeProjection.predicted,
        confidence_interval: timeProjection.confidence_interval,
        methodology: 'Linear trend projection'
      });
    }

    // Predict accuracy trend
    const accuracyScores = dailyData.map(d => d.avg_accuracy_score);
    const accuracyProjection = this.projectTrend(accuracyScores, 7);
    
    if (accuracyProjection) {
      predictions.push({
        metric: 'accuracy_score',
        prediction_horizon_days: 7,
        predicted_value: accuracyProjection.predicted,
        confidence_interval: accuracyProjection.confidence_interval,
        methodology: 'Linear trend projection'
      });
    }

    return predictions;
  }

  /**
   * Project trend into the future
   */
  private projectTrend(values: number[], horizonDays: number): {
    predicted: number;
    confidence_interval: { lower: number; upper: number };
  } | null {
    if (values.length < 3) return null;

    // Simple linear regression for projection
    const n = values.length;
    const x = values.map((_, i) => i);
    const y = values;

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    // Project to future point
    const futureX = n + horizonDays - 1;
    const predicted = slope * futureX + intercept;

    // Calculate prediction interval (simplified)
    const residuals = y.map((yi, i) => yi - (slope * x[i] + intercept));
    const mse = residuals.reduce((sum, r) => sum + r * r, 0) / (n - 2);
    const standardError = Math.sqrt(mse);

    return {
      predicted: parseFloat(predicted.toFixed(2)),
      confidence_interval: {
        lower: parseFloat((predicted - 1.96 * standardError).toFixed(2)),
        upper: parseFloat((predicted + 1.96 * standardError).toFixed(2))
      }
    };
  }

  /**
   * Generate performance recommendations based on analysis
   */
  private generateRecommendations(
    rawData: any[],
    dailyData: any[]
  ): PerformanceRecommendation[] {
    const recommendations: PerformanceRecommendation[] = [];

    // Analyze recent performance
    const recentData = rawData.slice(-100); // Last 100 requests
    const avgProcessingTime = recentData.reduce((sum, d) => sum + d.processing_time_ms, 0) / recentData.length;
    const avgAccuracy = recentData.reduce((sum, d) => sum + (d.accuracy_score || 0), 0) / recentData.length;
    const errorRate = recentData.filter(d => !d.success).length / recentData.length;

    // Processing time recommendations
    if (avgProcessingTime > 5000) {
      recommendations.push({
        category: 'optimization',
        priority: 'high',
        title: 'Optimize Agent Processing Time',
        description: `Average processing time of ${avgProcessingTime}ms exceeds optimal range`,
        expected_impact: 'Reduce processing time by 30-50%',
        implementation_effort: 'medium',
        actions: [
          'Review prompt complexity and reduce unnecessary instructions',
          'Consider using a more efficient AI model',
          'Implement response caching for similar documents',
          'Optimize schema validation rules'
        ]
      });
    }

    // Accuracy recommendations
    if (avgAccuracy < 0.85) {
      recommendations.push({
        category: 'optimization',
        priority: 'high',
        title: 'Improve Agent Accuracy',
        description: `Average accuracy of ${(avgAccuracy * 100).toFixed(1)}% is below target`,
        expected_impact: 'Increase accuracy by 10-15%',
        implementation_effort: 'high',
        actions: [
          'Enhance system prompt with more specific instructions',
          'Add few-shot examples to the prompt',
          'Refine output schema validation',
          'Implement multi-model consensus for critical fields'
        ]
      });
    }

    // Error rate recommendations
    if (errorRate > 0.05) {
      recommendations.push({
        category: 'maintenance',
        priority: 'high',
        title: 'Reduce Error Rate',
        description: `Error rate of ${(errorRate * 100).toFixed(1)}% is concerning`,
        expected_impact: 'Reduce errors by 70-80%',
        implementation_effort: 'medium',
        actions: [
          'Implement input validation and preprocessing',
          'Add fallback models for error scenarios',
          'Enhance error handling and retry logic',
          'Monitor and alert on error patterns'
        ]
      });
    }

    // Volume-based scaling recommendations
    const totalDailyRequests = dailyData.reduce((sum, d) => sum + d.total_requests, 0);
    const avgDailyRequests = totalDailyRequests / dailyData.length;

    if (avgDailyRequests > 1000) {
      recommendations.push({
        category: 'scaling',
        priority: 'medium',
        title: 'Consider Performance Optimizations',
        description: `High request volume (${avgDailyRequests.toFixed(0)} requests/day) may benefit from optimizations`,
        expected_impact: 'Improve system capacity by 50-100%',
        implementation_effort: 'medium',
        actions: [
          'Implement async processing for non-critical operations',
          'Add request queuing and rate limiting',
          'Consider horizontal scaling strategies',
          'Implement performance monitoring and alerting'
        ]
      });
    }

    return recommendations;
  }

  /**
   * Calculate overall health score for the agent
   */
  private calculateHealthScore(rawData: any[], dailyData: any[]): number {
    let score = 100;

    // Recent performance (last 100 requests)
    const recentData = rawData.slice(-100);
    if (recentData.length === 0) return 0;

    const avgProcessingTime = recentData.reduce((sum, d) => sum + d.processing_time_ms, 0) / recentData.length;
    const avgAccuracy = recentData.reduce((sum, d) => sum + (d.accuracy_score || 0), 0) / recentData.length;
    const successRate = recentData.filter(d => d.success).length / recentData.length;

    // Deduct points for poor performance
    if (avgProcessingTime > 10000) score -= 20; // Very slow
    else if (avgProcessingTime > 5000) score -= 10; // Slow

    if (avgAccuracy < 0.7) score -= 30; // Poor accuracy
    else if (avgAccuracy < 0.85) score -= 15; // Below target

    if (successRate < 0.9) score -= 25; // High error rate
    else if (successRate < 0.95) score -= 10; // Moderate errors

    // Bonus points for excellent performance
    if (avgAccuracy > 0.95 && avgProcessingTime < 3000 && successRate > 0.98) {
      score += 5;
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Get empty analysis for agents with no data
   */
  private getEmptyAnalysis(agentId: string): PerformanceAnalysis {
    return {
      agent_id: agentId,
      analysis_date: new Date(),
      trends: {
        processing_time: {
          direction: 'stable',
          confidence: 0,
          rate_of_change: 0,
          significance: 'low',
          data_points: []
        },
        accuracy: {
          direction: 'stable',
          confidence: 0,
          rate_of_change: 0,
          significance: 'low',
          data_points: []
        },
        volume: {
          direction: 'stable',
          confidence: 0,
          rate_of_change: 0,
          significance: 'low',
          data_points: []
        },
        cost: {
          direction: 'stable',
          confidence: 0,
          rate_of_change: 0,
          significance: 'low',
          data_points: []
        }
      },
      anomalies: [],
      predictions: [],
      recommendations: [{
        category: 'alerting',
        priority: 'low',
        title: 'Insufficient Data',
        description: 'No performance data available for analysis',
        expected_impact: 'Begin collecting metrics for insights',
        implementation_effort: 'low',
        actions: ['Process documents to generate performance data']
      }],
      health_score: 0
    };
  }
}

/**
 * Factory function for creating PerformanceAnalyzer
 */
export function createPerformanceAnalyzer(supabase: SupabaseClient<Database>): PerformanceAnalyzer {
  return new PerformanceAnalyzer(supabase);
}