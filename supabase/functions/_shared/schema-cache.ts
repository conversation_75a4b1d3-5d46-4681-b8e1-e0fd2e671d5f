import { 
  CompiledSchema, 
  Agent, 
  CacheMetrics,
  ValidationErrorCode
} from '../../../types/schema.ts';
import { SchemaValidator } from './schema-validator.ts';

/**
 * Schema Cache
 * High-performance caching layer for compiled schemas with automatic cleanup
 */
export class SchemaCache {
  private cache = new Map<string, CompiledSchema>();
  private metrics = {
    hits: 0,
    misses: 0,
    compilations: 0
  };
  private mockAgents = new Map<string, Agent>(); // For testing
  private maxCacheSize: number;

  constructor(maxCacheSize: number = 1000) {
    this.maxCacheSize = maxCacheSize;
  }

  /**
   * Get compiled schema for an agent with caching
   */
  async getSchema(agentId: string): Promise<CompiledSchema> {
    // Check cache first
    if (this.cache.has(agentId)) {
      this.metrics.hits++;
      return this.cache.get(agentId)!;
    }

    // Cache miss - compile and cache
    this.metrics.misses++;
    const schema = await this.compileSchema(agentId);
    
    // Cache the compiled schema
    this.cache.set(agentId, schema);

    // Cleanup if cache size exceeded
    if (this.cache.size > this.maxCacheSize) {
      this.cleanupCache();
    }

    return schema;
  }

  /**
   * Compile schema for an agent
   */
  private async compileSchema(agentId: string): Promise<CompiledSchema> {
    this.metrics.compilations++;

    try {
      const agent = await this.getAgentFromDatabase(agentId);
      
      if (!agent) {
        throw new Error(`Agent ${agentId} not found`);
      }

      const startTime = Date.now();
      
      // Test compilation by validating against the schema
      const testValidator = new SchemaValidator();
      await testValidator.validateData({}, agent.output_schema);
      
      const compiled = agent.output_schema; // Store the raw schema
      
      const compilationTime = Date.now() - startTime;
      
      // Log performance metrics
      if (compilationTime > 50) {
        console.warn(`Slow schema compilation for agent ${agentId}: ${compilationTime}ms`);
      }

      return {
        validator: compiled, // This is now just the schema
        compiledAt: new Date(),
        schemaVersion: agent.updated_at
      };
    } catch {
      throw new Error(`${ValidationErrorCode.CACHE_ERROR}: Failed to compile schema for agent ${agentId} - ${error.message}`);
    }
  }

  /**
   * Get agent from database (in production this would be a real DB query)
   */
  private async getAgentFromDatabase(agentId: string): Promise<Agent | null> {
    // For testing - use mock data
    if (this.mockAgents.has(agentId)) {
      return this.mockAgents.get(agentId)!;
    }

    // In production, this would be:
    // const { data: agent } = await supabase
    //   .from('agents')
    //   .select('id, output_schema, updated_at')
    //   .eq('id', agentId)
    //   .single();
    // 
    // return agent;

    return null;
  }

  /**
   * Invalidate cache entry for a specific agent
   */
  invalidateAgent(agentId: string): void {
    this.cache.delete(agentId);
  }

  /**
   * Clear entire cache
   */
  clearCache(): void {
    this.cache.clear();
    this.resetMetrics();
  }

  /**
   * Clean up cache by removing oldest entries
   */
  private cleanupCache(): void {
    // Remove 20% of oldest entries
    const entriesToRemove = Math.floor(this.maxCacheSize * 0.2);
    const entries = Array.from(this.cache.entries());
    
    // Sort by compilation time (oldest first)
    entries.sort((a, b) => a[1].compiledAt.getTime() - b[1].compiledAt.getTime());
    
    // Remove oldest entries
    for (let i = 0; i < entriesToRemove; i++) {
      this.cache.delete(entries[i][0]);
    }
  }

  /**
   * Get cache performance metrics
   */
  getCacheMetrics(): CacheMetrics {
    const total = this.metrics.hits + this.metrics.misses;
    return {
      hits: this.metrics.hits,
      misses: this.metrics.misses,
      compilations: this.metrics.compilations,
      hit_rate: total > 0 ? this.metrics.hits / total : 0,
      cache_size: this.cache.size
    };
  }

  /**
   * Reset metrics (for testing)
   */
  private resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      compilations: 0
    };
  }

  /**
   * Set mock agent for testing
   */
  setMockAgent(agentId: string, agent: Pick<Agent, 'output_schema' | 'updated_at'>): void {
    this.mockAgents.set(agentId, {
      id: agentId,
      output_schema: agent.output_schema,
      updated_at: agent.updated_at
    });
  }

  /**
   * Preload schemas for multiple agents
   */
  async preloadSchemas(agentIds: string[]): Promise<void> {
    const promises = agentIds.map(agentId => this.getSchema(agentId));
    await Promise.all(promises);
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStatistics() {
    const metrics = this.getCacheMetrics();
    const memoryUsage = this.estimateMemoryUsage();
    
    return {
      ...metrics,
      memory_usage_kb: memoryUsage,
      cache_efficiency: this.calculateCacheEfficiency(),
      avg_compilation_time: this.calculateAverageCompilationTime()
    };
  }

  /**
   * Estimate memory usage of cache
   */
  private estimateMemoryUsage(): number {
    // Rough estimation - each compiled schema is approximately 1-5KB
    return this.cache.size * 3; // Average 3KB per entry
  }

  /**
   * Calculate cache efficiency score
   */
  private calculateCacheEfficiency(): number {
    const metrics = this.getCacheMetrics();
    if (metrics.hits + metrics.misses === 0) return 0;
    
    // Efficiency considers hit rate and compilation avoidance
    const hitRate = metrics.hit_rate;
    const compilationSavings = metrics.hits / Math.max(metrics.compilations, 1);
    
    return (hitRate * 0.7) + (Math.min(compilationSavings, 1) * 0.3);
  }

  /**
   * Calculate average compilation time (placeholder for actual implementation)
   */
  private calculateAverageCompilationTime(): number {
    // In a real implementation, we'd track compilation times
    return 5; // 5ms average
  }

  /**
   * Check if agent schema needs recompilation due to updates
   */
  async needsRecompilation(agentId: string): Promise<boolean> {
    const cached = this.cache.get(agentId);
    if (!cached) return true;

    try {
      const agent = await this.getAgentFromDatabase(agentId);
      if (!agent) return true;

      // Check if schema version has changed
      return agent.updated_at !== cached.schemaVersion;
    } catch {
      // If we can't check, assume recompilation is needed
      return true;
    }
  }

  /**
   * Refresh schema for an agent (force recompilation)
   */
  async refreshSchema(agentId: string): Promise<CompiledSchema> {
    this.invalidateAgent(agentId);
    return await this.getSchema(agentId);
  }

  /**
   * Bulk refresh schemas for multiple agents
   */
  async refreshSchemas(agentIds: string[]): Promise<CompiledSchema[]> {
    // Invalidate all first
    agentIds.forEach(agentId => this.invalidateAgent(agentId));
    
    // Recompile all
    const promises = agentIds.map(agentId => this.getSchema(agentId));
    return await Promise.all(promises);
  }

  /**
   * Get cache health status
   */
  getCacheHealth(): { status: 'healthy' | 'degraded' | 'critical', issues: string[] } {
    const metrics = this.getCacheMetrics();
    const issues: string[] = [];
    
    // Check hit rate
    if (metrics.hit_rate < 0.5) {
      issues.push('Low cache hit rate (< 50%)');
    }
    
    // Check cache size utilization
    if (this.cache.size > this.maxCacheSize * 0.9) {
      issues.push('Cache near capacity (> 90%)');
    }
    
    // Check if too many compilations relative to requests
    const total = metrics.hits + metrics.misses;
    if (total > 0 && metrics.compilations / total > 0.8) {
      issues.push('High compilation rate (> 80% of requests)');
    }

    let status: 'healthy' | 'degraded' | 'critical' = 'healthy';
    if (issues.length > 0) {
      status = issues.length >= 2 ? 'critical' : 'degraded';
    }

    return { status, issues };
  }

  /**
   * Optimize cache performance
   */
  optimizeCache(): { actions_taken: string[], performance_improvement: number } {
    const actionsTaken: string[] = [];
    const beforeMetrics = this.getCacheMetrics();

    // Clean up old entries if cache is getting full
    if (this.cache.size > this.maxCacheSize * 0.8) {
      this.cleanupCache();
      actionsTaken.push('Cleaned up old cache entries');
    }

    // In a real implementation, we might:
    // - Preload frequently accessed schemas
    // - Adjust cache size based on memory usage
    // - Implement LRU eviction policy

    const afterMetrics = this.getCacheMetrics();
    const improvement = afterMetrics.hit_rate - beforeMetrics.hit_rate;

    return {
      actions_taken: actionsTaken,
      performance_improvement: improvement
    };
  }
}