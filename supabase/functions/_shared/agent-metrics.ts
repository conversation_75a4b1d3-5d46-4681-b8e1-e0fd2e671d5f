/**
 * Agent Metrics Utility
 * 
 * Core performance tracking logic and utilities
 * Provides high-level interfaces for metric collection and analysis
 */

import type { SupabaseClient } from 'jsr:@supabase/supabase-js@2.57.4';
import type { Database } from '../../../types/database.types.ts';
import {
  AgentPerformanceTracker,
  AgentBenchmarker,
  CustomizationAnalyzer,
  __PerformanceAlerter
} from './agent-performance.ts';
import type {
  __AgentPerformanceMetrics,
  __AgentSummary,
  __BenchmarkReport,
  CustomizationInsights,
  __PerformanceAlert
} from '../../../types/agent-performance.types.ts';

/**
 * Unified agent metrics interface
 * Provides a simplified API for all performance tracking needs
 */
export class AgentMetrics {
  private tracker: AgentPerformanceTracker;
  private benchmarker: AgentBenchmarker;
  private analyzer: CustomizationAnalyzer;
  private alerter: _PerformanceAlerter;

  constructor(private supabase: SupabaseClient<Database>) {
    this.tracker = new AgentPerformanceTracker(supabase);
    this.benchmarker = new AgentBenchmarker(supabase);
    this.analyzer = new CustomizationAnalyzer(supabase);
    this.alerter = new _PerformanceAlerter(supabase);
  }

  /**
   * Record agent performance metrics
   */
  async recordMetrics(metrics: AgentPerformanceMetrics): Promise<{
    success: boolean;
    alerts: _PerformanceAlert[];
    error?: string;
  }> {
    try {
      const _result = await this.tracker.recordPerformance(metrics);
      const alerts = await this.alerter.check_PerformanceAlerts(metrics);

      return {
        success: result.success,
        alerts: alerts
      };
    } catch {
      return {
        success: false,
        alerts: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Get comprehensive agent performance summary
   */
  async getAgentPerformance(agentId: string): Promise<{
    summary: AgentSummary;
    benchmark?: BenchmarkReport;
    alerts: _PerformanceAlert[];
  }> {
    const [summary, alerts] = await Promise.all([
      this.tracker.getAgentSummary(agentId),
      this.getRecentAlerts(agentId)
    ]);

    // Get benchmark if this is a custom agent
    let benchmark: BenchmarkReport | undefined;
    try {
      benchmark = await this.benchmarker.benchmarkAgentPerformance(agentId);
    } catch {
      // Not a custom agent or insufficient data
      benchmark = undefined;
    }

    return {
      summary,
      benchmark,
      alerts
    };
  }

  /**
   * Get platform-wide customization insights
   */
  async getCustomizationInsights(): Promise<CustomizationInsights> {
    return await this.analyzer.analyzeCustomizationPatterns();
  }

  /**
   * Get recent alerts for an agent
   */
  async getRecentAlerts(agentId: string, hours: number = 24): Promise<_PerformanceAlert[]> {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);

    const { data: alertData, error } = await this.supabase
      .from('performance_alerts')
      .select('*')
      .eq('agent_id', agentId)
      .gte('created_at', since.toISOString())
      .order('created_at', { ascending: false });

    if (error || !alertData) {
      return [];
    }

    return alertData.map(alert => ({
      type: alert.alert_type as any,
      severity: alert.severity as any,
      agent_id: alert.agent_id,
      customer_id: alert.customer_id,
      message: alert.message,
      timestamp: new Date(alert.created_at),
      threshold_value: alert.threshold_value,
      actual_value: alert.actual_value,
      metadata: alert.metadata
    }));
  }

  /**
   * Batch record multiple metrics efficiently
   */
  async recordBatchMetrics(metrics: AgentPerformanceMetrics[]): Promise<{
    successCount: number;
    errorCount: number;
    totalAlerts: _PerformanceAlert[];
  }> {
    const results = await Promise.allSettled(
      metrics.map(_metric => this.recordMetrics(_metric))
    );

    let successCount = 0;
    let errorCount = 0;
    const allAlerts: _PerformanceAlert[] = [];

    results.forEach(result => {
      if (result.status === 'fulfilled' && result.value.success) {
        successCount++;
        allAlerts.push(...result.value.alerts);
      } else {
        errorCount++;
      }
    });

    return {
      successCount,
      errorCount,
      totalAlerts: allAlerts
    };
  }

  /**
   * Get agent performance trends
   */
  async getPerformanceTrends(agentId: string, days: number = 30): Promise<{
    processingTime: Array<{ date: string; avgTime: number }>;
    accuracy: Array<{ date: string; avgAccuracy: number }>;
    requestVolume: Array<{ date: string; count: number }>;
  }> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    const { data: dailyData, error } = await this.supabase
      .from('agent_performance_daily')
      .select('date, avg_processing_time_ms, avg_accuracy_score, total_requests')
      .eq('agent_id', agentId)
      .gte('date', startDate.toISOString().split('T')[0])
      .order('date', { ascending: true });

    if (error || !dailyData) {
      return {
        processingTime: [],
        accuracy: [],
        requestVolume: []
      };
    }

    return {
      processingTime: dailyData.map(d => ({
        date: d.date,
        avgTime: d.avg_processing_time_ms
      })),
      accuracy: dailyData.map(d => ({
        date: d.date,
        avgAccuracy: d.avg_accuracy_score
      })),
      requestVolume: dailyData.map(d => ({
        date: d.date,
        count: d.total_requests
      }))
    };
  }

  /**
   * Health check for the metrics system
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, boolean>;
    message: string;
  }> {
    const checks = {
      database: false,
      aggregation: false,
      alerting: false
    };

    try {
      // Test database connectivity
      const { error: dbError } = await this.supabase
        .from('agent_performance_logs')
        .select('id')
        .limit(1);
      checks.database = !dbError;

      // Test aggregation system
      const { error: aggError } = await this.supabase
        .from('agent_performance_daily')
        .select('id')
        .limit(1);
      checks.aggregation = !aggError;

      // Test alerting system
      const { error: alertError } = await this.supabase
        .from('performance_alerts')
        .select('id')
        .limit(1);
      checks.alerting = !alertError;

      const healthyCount = Object.values(checks).filter(Boolean).length;
      const status = healthyCount === 3 ? 'healthy' 
                  : healthyCount >= 2 ? 'degraded' 
                  : 'unhealthy';

      return {
        status,
        checks,
        message: `${healthyCount}/3 systems operational`
      };

    } catch {
      return {
        status: 'unhealthy',
        checks,
        message: `Health check failed: ${error}`
      };
    }
  }
}

/**
 * Factory function for creating AgentMetrics instance
 */
export function createAgentMetrics(supabase: SupabaseClient<Database>): AgentMetrics {
  return new AgentMetrics(supabase);
}

/**
 * Utility functions for metric calculations
 */
export const MetricsUtils = {
  /**
   * Calculate percentage improvement
   */
  calculateImprovement(baseline: number, current: number, type: 'higher_better' | 'lower_better'): number {
    if (baseline === 0) {
      return current === 0 ? 0 : (type === 'lower_better' ? -100 : 100);
    }

    if (type === 'higher_better') {
      return parseFloat(((current - baseline) / baseline * 100).toFixed(1));
    } else {
      return parseFloat(((baseline - current) / baseline * 100).toFixed(1));
    }
  },

  /**
   * Calculate success rate
   */
  calculateSuccessRate(successful: number, total: number): number {
    return total === 0 ? 0 : parseFloat((successful / total).toFixed(3));
  },

  /**
   * Calculate average with proper rounding
   */
  calculateAverage(values: number[]): number {
    if (values.length === 0) return 0;
    return Math.round(values.reduce((sum, val) => sum + val, 0) / values.length);
  },

  /**
   * Generate correlation ID for tracking
   */
  generateCorrelationId(): string {
    return `perf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
};