// ================================================================================
// ENTERPRISE ENCRYPTION AND DATA PROTECTION
// ================================================================================

/**
 * AES-256-GCM encryption for sensitive data at rest
 */
export class DataEncryption {
  private static readonly ALGORITHM = 'AES-GCM';
  private static readonly KEY_LENGTH = 32; // 256 bits
  private static readonly IV_LENGTH = 12; // 96 bits for GCM
  private static readonly TAG_LENGTH = 16; // 128 bits
  
  private static encryptionKey: CryptoKey | null = null;
  
  /**
   * Initialize encryption with the master key
   */
  static async initialize(): Promise<void> {
    try {
      const keyString = Deno.env.get('ENCRYPTION_MASTER_KEY');
      if (!keyString) {
        throw new Error('ENCRYPTION_MASTER_KEY environment variable not set');
      }
      
      // Convert base64 key to bytes
      const keyBytes = new Uint8Array(
        atob(keyString).split('').map(char => char.charCodeAt(0))
      );
      
      if (keyBytes.length !== this.KEY_LENGTH) {
        throw new Error(`Invalid key length: expected ${this.KEY_LENGTH}, got ${keyBytes.length}`);
      }
      
      this.encryptionKey = await crypto.subtle.importKey(
        'raw',
        keyBytes,
        { name: this.ALGORITHM },
        false,
        ['encrypt', 'decrypt']
      );
      
      console.log('✅ Data encryption initialized');
    } catch (error) {
      console.error('Failed to initialize encryption:', error);
      throw error;
    }
  }
  
  /**
   * Encrypt sensitive data using AES-256-GCM
   */
  static async encrypt(data: string): Promise<string> {
    if (!this.encryptionKey) {
      await this.initialize();
    }
    
    try {
      const iv = crypto.getRandomValues(new Uint8Array(this.IV_LENGTH));
      const encodedData = new TextEncoder().encode(data);
      
      const encrypted = await crypto.subtle.encrypt(
        { name: this.ALGORITHM, iv },
        this.encryptionKey!,
        encodedData
      );
      
      // Combine IV + encrypted data + tag
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);
      
      // Return base64 encoded result
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }
  
  /**
   * Decrypt sensitive data using AES-256-GCM
   */
  static async decrypt(encryptedData: string): Promise<string> {
    if (!this.encryptionKey) {
      await this.initialize();
    }
    
    try {
      // Decode from base64
      const combined = new Uint8Array(
        atob(encryptedData).split('').map(char => char.charCodeAt(0))
      );
      
      // Extract IV and encrypted data
      const iv = combined.slice(0, this.IV_LENGTH);
      const encrypted = combined.slice(this.IV_LENGTH);
      
      const decrypted = await crypto.subtle.decrypt(
        { name: this.ALGORITHM, iv },
        this.encryptionKey!,
        encrypted
      );
      
      return new TextDecoder().decode(decrypted);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }
  
  /**
   * Generate a new master key (for key rotation)
   */
  static generateMasterKey(): string {
    const key = crypto.getRandomValues(new Uint8Array(this.KEY_LENGTH));
    return btoa(String.fromCharCode(...key));
  }
  
  /**
   * Hash sensitive data for comparison (one-way)
   */
  static async hash(data: string, salt?: string): Promise<string> {
    const saltBytes = salt 
      ? new TextEncoder().encode(salt)
      : crypto.getRandomValues(new Uint8Array(16));
    
    const dataBytes = new TextEncoder().encode(data);
    const combined = new Uint8Array(saltBytes.length + dataBytes.length);
    combined.set(saltBytes);
    combined.set(dataBytes, saltBytes.length);
    
    const hashBuffer = await crypto.subtle.digest('SHA-256', combined);
    const hashArray = new Uint8Array(hashBuffer);
    
    // Combine salt + hash for storage
    const result = new Uint8Array(saltBytes.length + hashArray.length);
    result.set(saltBytes);
    result.set(hashArray, saltBytes.length);
    
    return btoa(String.fromCharCode(...result));
  }
  
  /**
   * Verify hashed data
   */
  static async verifyHash(data: string, hashedData: string): Promise<boolean> {
    try {
      // Decode the stored hash
      const stored = new Uint8Array(
        atob(hashedData).split('').map(char => char.charCodeAt(0))
      );
      
      // Extract salt (first 16 bytes)
      const salt = stored.slice(0, 16);
      const storedHash = stored.slice(16);
      
      // Hash the input data with the same salt
      const dataBytes = new TextEncoder().encode(data);
      const combined = new Uint8Array(salt.length + dataBytes.length);
      combined.set(salt);
      combined.set(dataBytes, salt.length);
      
      const hashBuffer = await crypto.subtle.digest('SHA-256', combined);
      const computedHash = new Uint8Array(hashBuffer);
      
      // Compare hashes
      if (computedHash.length !== storedHash.length) {
        return false;
      }
      
      for (let i = 0; i < computedHash.length; i++) {
        if (computedHash[i] !== storedHash[i]) {
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Hash verification failed:', error);
      return false;
    }
  }
}

/**
 * PII (Personally Identifiable Information) protection utilities
 */
export class PIIProtection {
  private static readonly PII_PATTERNS = [
    // Email addresses
    /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    // Phone numbers (various formats)
    /(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}/g,
    // SSN (XXX-XX-XXXX)
    /\b\d{3}-\d{2}-\d{4}\b/g,
    // Credit card numbers (simple pattern)
    /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g,
    // IP addresses
    /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
  ];
  
  /**
   * Mask PII in text for logging/display
   */
  static maskPII(text: string): string {
    let masked = text;
    
    for (const pattern of this.PII_PATTERNS) {
      masked = masked.replace(pattern, (match) => {
        if (match.length <= 4) return '***';
        return match.slice(0, 2) + '*'.repeat(match.length - 4) + match.slice(-2);
      });
    }
    
    return masked;
  }
  
  /**
   * Detect if text contains potential PII
   */
  static containsPII(text: string): boolean {
    return this.PII_PATTERNS.some(pattern => pattern.test(text));
  }
  
  /**
   * Extract and classify PII from text
   */
  static extractPII(text: string): Array<{ type: string; value: string; masked: string }> {
    const found: Array<{ type: string; value: string; masked: string }> = [];
    
    const patterns = [
      { type: 'email', pattern: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g },
      { type: 'phone', pattern: /(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}/g },
      { type: 'ssn', pattern: /\b\d{3}-\d{2}-\d{4}\b/g },
      { type: 'credit_card', pattern: /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g },
      { type: 'ip_address', pattern: /\b(?:\d{1,3}\.){3}\d{1,3}\b/g },
    ];
    
    for (const { type, pattern } of patterns) {
      const matches = text.match(pattern);
      if (matches) {
        for (const match of matches) {
          found.push({
            type,
            value: match,
            masked: this.maskPII(match),
          });
        }
      }
    }
    
    return found;
  }
}

/**
 * Key management and rotation utilities
 */
export class KeyManagement {
  private static readonly KEY_ROTATION_INTERVAL = 90 * 24 * 60 * 60 * 1000; // 90 days
  
  /**
   * Check if key rotation is needed
   */
  static isKeyRotationNeeded(keyCreatedAt: Date): boolean {
    const now = Date.now();
    const keyAge = now - keyCreatedAt.getTime();
    return keyAge > this.KEY_ROTATION_INTERVAL;
  }
  
  /**
   * Generate secure API key with embedded metadata
   */
  static generateApiKey(
    keyType: 'skt' | 'skp', // skt = test, skp = production
    customerId: string
  ): { key: string; hash: string; metadata: any } {
    // Generate random bytes for the key
    const keyBytes = crypto.getRandomValues(new Uint8Array(32));
    const keyString = btoa(String.fromCharCode(...keyBytes))
      .replace(/[+/=]/g, '') // Remove special characters
      .substring(0, 32); // Ensure consistent length
    
    const key = `${keyType}_live_${keyString}`;
    
    // Create metadata
    const metadata = {
      key_type: keyType,
      customer_id: customerId,
      created_at: new Date().toISOString(),
      version: '1',
      algorithm: 'sha256',
    };
    
    // Generate hash for storage
    const hash = this.hashApiKey(key);
    
    return { key, hash, metadata };
  }
  
  /**
   * Hash API key for secure storage
   */
  static hashApiKey(apiKey: string): string {
    const salt = Deno.env.get('API_KEY_SALT') || 'default-salt-change-in-production';
    const encoder = new TextEncoder();
    const data = encoder.encode(apiKey + salt);
    
    // Create a simple hash (in production, use more sophisticated hashing)
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      hash = ((hash << 5) - hash) + data[i];
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(16);
  }
  
  /**
   * Validate API key format
   */
  static validateApiKeyFormat(apiKey: string): boolean {
    // Check format: sk[t|p]_live_[32 chars]
    const pattern = /^sk[tp]_live_[a-zA-Z0-9]{32}$/;
    return pattern.test(apiKey);
  }
  
  /**
   * Extract key type from API key
   */
  static extractKeyType(apiKey: string): 'test' | 'production' | null {
    if (apiKey.startsWith('skt_live_')) return 'test';
    if (apiKey.startsWith('skp_live_')) return 'production';
    return null;
  }
}

/**
 * Secure configuration management
 */
export class SecureConfig {
  private static config: Map<string, any> = new Map();
  private static initialized = false;
  
  /**
   * Initialize secure configuration
   */
  static async initialize(): Promise<void> {
    if (this.initialized) return;
    
    try {
      // Load configuration from environment variables
      const requiredEnvVars = [
        'ENCRYPTION_MASTER_KEY',
        'API_KEY_SALT', 
        'JWT_SECRET',
        'SUPABASE_SERVICE_ROLE_KEY',
      ];
      
      for (const envVar of requiredEnvVars) {
        const value = Deno.env.get(envVar);
        if (!value) {
          throw new Error(`Required environment variable ${envVar} not set`);
        }
        this.config.set(envVar, value);
      }
      
      // Validate configuration
      await this.validateConfiguration();
      
      this.initialized = true;
      console.log('✅ Secure configuration initialized');
    } catch (error) {
      console.error('Failed to initialize secure configuration:', error);
      throw error;
    }
  }
  
  /**
   * Get configuration value
   */
  static get(key: string): string | undefined {
    if (!this.initialized) {
      throw new Error('SecureConfig not initialized. Call initialize() first.');
    }
    return this.config.get(key);
  }
  
  /**
   * Validate configuration values
   */
  private static async validateConfiguration(): Promise<void> {
    // Validate encryption key
    const encryptionKey = this.config.get('ENCRYPTION_MASTER_KEY');
    if (encryptionKey && encryptionKey.length < 32) {
      throw new Error('ENCRYPTION_MASTER_KEY must be at least 32 characters');
    }
    
    // Validate JWT secret
    const jwtSecret = this.config.get('JWT_SECRET');
    if (jwtSecret && jwtSecret.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters');
    }
    
    // Validate API key salt
    const apiKeySalt = this.config.get('API_KEY_SALT');
    if (apiKeySalt && apiKeySalt.length < 16) {
      throw new Error('API_KEY_SALT must be at least 16 characters');
    }
  }
  
  /**
   * Check if running in production environment
   */
  static isProduction(): boolean {
    return Deno.env.get('ENVIRONMENT') === 'production';
  }
  
  /**
   * Get security headers for production
   */
  static getSecurityHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': "default-src 'self'; script-src 'self'; object-src 'none';",
    };
    
    if (this.isProduction()) {
      headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload';
    }
    
    return headers;
  }
}

/**
 * Data retention and cleanup utilities
 */
export class DataRetention {
  /**
   * Retention policies by data type
   */
  private static readonly RETENTION_POLICIES = {
    audit_logs: 2555, // 7 years (compliance requirement)
    performance_metrics: 365, // 1 year
    error_logs: 90, // 3 months
    security_events: 1095, // 3 years
    test_data: 7, // 1 week (for skt_ keys)
    session_data: 30, // 1 month
  };
  
  /**
   * Clean up expired data based on retention policies
   */
  static async cleanupExpiredData(): Promise<void> {
    try {
      for (const [dataType, retentionDays] of Object.entries(this.RETENTION_POLICIES)) {
        const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000);
        await this.cleanupDataType(dataType, cutoffDate);
      }
      
      console.log('✅ Data retention cleanup completed');
    } catch (error) {
      console.error('Data retention cleanup failed:', error);
    }
  }
  
  /**
   * Clean up specific data type
   */
  private static async cleanupDataType(dataType: string, cutoffDate: Date): Promise<void> {
    // This would implement actual cleanup logic for each data type
    console.log(`Cleaning up ${dataType} data older than ${cutoffDate.toISOString()}`);
  }
  
  /**
   * Archive old data before deletion
   */
  static async archiveData(dataType: string, cutoffDate: Date): Promise<void> {
    // This would implement data archival logic
    console.log(`Archiving ${dataType} data older than ${cutoffDate.toISOString()}`);
  }
}