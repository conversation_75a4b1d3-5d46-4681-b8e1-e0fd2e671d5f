import { 
  JSONSchema, 
  FieldDefinition,
  ValidationErrorCode 
} from '../../../types/schema.ts';

/**
 * Schema Generator
 * Generates JSON Schema from simplified field definitions
 */
export class SchemaGenerator {
  
  /**
   * Generate JSON Schema from field definitions
   */
  generateFromFields(fields: FieldDefinition[]): JSONSchema {
    try {
      const properties: Record<string, JSONSchema> = {};
      const required: string[] = [];

      for (const field of fields) {
        properties[field.name] = this.fieldToSchemaProperty(field);

        if (field.required) {
          required.push(field.name);
        }
      }

      return {
        type: 'object',
        properties: properties,
        required: required.length > 0 ? required : undefined,
        additionalProperties: false,
        $schema: 'http://json-schema.org/draft-07/schema#'
      };
    } catch {
      throw new Error(`${ValidationErrorCode.SCHEMA_COMPILATION_FAILED}: Failed to generate schema - ${error.message}`);
    }
  }

  /**
   * Convert field definition to JSON Schema property
   */
  private fieldToSchemaProperty(field: FieldDefinition): JSONSchema {
    const property: JSONSchema = {
      type: field.type,
      description: field.description
    };

    // Add type-specific properties
    switch (field.type) {
      case 'string': {
      }
        this.addStringProperties(field, property);
        break;
    }

      case 'number':
      case 'integer': {
      }
        this.addNumericProperties(field, property);
        break;
      }

      case 'array': {
      }
        this.addArrayProperties(field, property);
        break;
    }

      case 'object': {
      }
        this.addObjectProperties(field, property);
        break;
    }

      case 'boolean': {
      }
        // Boolean type has no additional properties typically
        break;
    }

      default:
        throw new Error(`Unsupported field type: ${field.type}`);
    }

    // Add default value if specified
    if (field.default !== undefined) {
      property.default = field.default;
    }

    return property;
  }

  /**
   * Add string-specific properties
   */
  private addStringProperties(field: FieldDefinition, property: JSONSchema): void {
    if (field.format) {
      property.format = field.format;
    }

    if (field.pattern) {
      property.pattern = field.pattern;
    }

    if (field.minLength !== undefined) {
      property.minLength = field.minLength;
    }

    if (field.maxLength !== undefined) {
      property.maxLength = field.maxLength;
    }

    if (field.enum && field.enum.length > 0) {
      property.enum = field.enum;
    }
  }

  /**
   * Add numeric properties (number/integer)
   */
  private addNumericProperties(field: FieldDefinition, property: JSONSchema): void {
    if (field.minimum !== undefined) {
      property.minimum = field.minimum;
    }

    if (field.maximum !== undefined) {
      property.maximum = field.maximum;
    }

    // For numbers, we can also add multipleOf if needed in the future
  }

  /**
   * Add array-specific properties
   */
  private addArrayProperties(field: FieldDefinition, property: JSONSchema): void {
    if (field.items) {
      property.items = this.fieldToSchemaProperty(field.items);
    }

    if (field.minItems !== undefined) {
      property.minItems = field.minItems;
    }

    if (field.maxItems !== undefined) {
      property.maxItems = field.maxItems;
    }

    // Could add uniqueItems: true if needed
  }

  /**
   * Add object-specific properties
   */
  private addObjectProperties(field: FieldDefinition, property: JSONSchema): void {
    if (field.properties) {
      property.properties = {};
      const required: string[] = [];

      for (const [propName, propDef] of Object.entries(field.properties)) {
        property.properties[propName] = this.fieldToSchemaProperty(propDef);
        
        if (propDef.required) {
          required.push(propName);
        }
      }

      if (required.length > 0) {
        property.required = required;
      }

      // Objects typically don't allow additional properties for strict validation
      property.additionalProperties = false;
    }
  }

  /**
   * Generate schema for common business document types
   */
  generateInvoiceSchema(): JSONSchema {
    const fields: FieldDefinition[] = [
      {
        name: 'invoice_number',
        type: 'string',
        required: true,
        description: 'Unique invoice identifier',
        pattern: '^(INV|INVOICE)[-_]?\\d+$'
      },
      {
        name: 'invoice_date',
        type: 'string',
        required: true,
        description: 'Date the invoice was issued',
        format: 'date'
      },
      {
        name: 'due_date',
        type: 'string',
        required: false,
        description: 'Payment due date',
        format: 'date'
      },
      {
        name: 'vendor_name',
        type: 'string',
        required: true,
        description: 'Name of the vendor/company',
        minLength: 1,
        maxLength: 200
      },
      {
        name: 'vendor_address',
        type: 'object',
        required: false,
        description: 'Vendor address information',
        properties: {
          street: { name: 'street', type: 'string', required: false },
          city: { name: 'city', type: 'string', required: false },
          state: { name: 'state', type: 'string', required: false },
          zip_code: { name: 'zip_code', type: 'string', required: false },
          country: { name: 'country', type: 'string', required: false }
        }
      },
      {
        name: 'total_amount',
        type: 'number',
        required: true,
        description: 'Total invoice amount',
        minimum: 0
      },
      {
        name: 'currency',
        type: 'string',
        required: true,
        description: 'Currency code',
        format: 'currency',
        default: 'USD'
      },
      {
        name: 'tax_amount',
        type: 'number',
        required: false,
        description: 'Total tax amount',
        minimum: 0
      },
      {
        name: 'line_items',
        type: 'array',
        required: true,
        description: 'Invoice line items',
        minItems: 1,
        items: {
          name: 'line_item',
          type: 'object',
          required: true,
          properties: {
            description: { 
              name: 'description', 
              type: 'string', 
              required: true,
              minLength: 1,
              maxLength: 500
            },
            quantity: { 
              name: 'quantity', 
              type: 'integer', 
              required: true,
              minimum: 1
            },
            unit_price: { 
              name: 'unit_price', 
              type: 'number', 
              required: true,
              minimum: 0
            },
            total_price: { 
              name: 'total_price', 
              type: 'number', 
              required: false,
              minimum: 0
            }
          }
        }
      },
      {
        name: 'confidence_score',
        type: 'number',
        required: false,
        description: 'AI extraction confidence score',
        format: 'confidence',
        minimum: 0,
        maximum: 1
      }
    ];

    return this.generateFromFields(fields);
  }

  /**
   * Generate schema for contract documents
   */
  generateContractSchema(): JSONSchema {
    const fields: FieldDefinition[] = [
      {
        name: 'contract_title',
        type: 'string',
        required: true,
        description: 'Title or subject of the contract',
        minLength: 1,
        maxLength: 300
      },
      {
        name: 'contract_number',
        type: 'string',
        required: false,
        description: 'Contract identifier or reference number'
      },
      {
        name: 'effective_date',
        type: 'string',
        required: true,
        description: 'Date the contract becomes effective',
        format: 'date'
      },
      {
        name: 'expiration_date',
        type: 'string',
        required: false,
        description: 'Date the contract expires',
        format: 'date'
      },
      {
        name: 'parties',
        type: 'array',
        required: true,
        description: 'Parties involved in the contract',
        minItems: 2,
        items: {
          name: 'party',
          type: 'object',
          required: true,
          properties: {
            name: { 
              name: 'name', 
              type: 'string', 
              required: true,
              minLength: 1
            },
            role: { 
              name: 'role', 
              type: 'string', 
              required: false,
              enum: ['client', 'vendor', 'contractor', 'partner', 'other']
            },
            address: { 
              name: 'address', 
              type: 'string', 
              required: false
            }
          }
        }
      },
      {
        name: 'contract_value',
        type: 'number',
        required: false,
        description: 'Total value of the contract',
        minimum: 0
      },
      {
        name: 'currency',
        type: 'string',
        required: false,
        description: 'Currency for contract value',
        format: 'currency'
      },
      {
        name: 'key_terms',
        type: 'array',
        required: false,
        description: 'Key terms and conditions',
        items: {
          name: 'term',
          type: 'string',
          required: true
        }
      },
      {
        name: 'renewal_terms',
        type: 'string',
        required: false,
        description: 'Contract renewal conditions'
      },
      {
        name: 'termination_clause',
        type: 'string',
        required: false,
        description: 'Contract termination conditions'
      }
    ];

    return this.generateFromFields(fields);
  }

  /**
   * Generate schema for receipt documents
   */
  generateReceiptSchema(): JSONSchema {
    const fields: FieldDefinition[] = [
      {
        name: 'receipt_number',
        type: 'string',
        required: false,
        description: 'Receipt identifier',
        pattern: '^(REC|RECEIPT)[-_]?\\d*$'
      },
      {
        name: 'merchant_name',
        type: 'string',
        required: true,
        description: 'Name of the merchant/store',
        minLength: 1,
        maxLength: 200
      },
      {
        name: 'transaction_date',
        type: 'string',
        required: true,
        description: 'Date of transaction',
        format: 'date'
      },
      {
        name: 'transaction_time',
        type: 'string',
        required: false,
        description: 'Time of transaction',
        format: 'time'
      },
      {
        name: 'total_amount',
        type: 'number',
        required: true,
        description: 'Total transaction amount',
        minimum: 0
      },
      {
        name: 'currency',
        type: 'string',
        required: false,
        description: 'Currency code',
        format: 'currency',
        default: 'USD'
      },
      {
        name: 'tax_amount',
        type: 'number',
        required: false,
        description: 'Tax amount',
        minimum: 0
      },
      {
        name: 'payment_method',
        type: 'string',
        required: false,
        description: 'Method of payment',
        enum: ['cash', 'credit_card', 'debit_card', 'check', 'mobile_payment', 'other']
      },
      {
        name: 'items',
        type: 'array',
        required: false,
        description: 'Items purchased',
        items: {
          name: 'item',
          type: 'object',
          required: true,
          properties: {
            description: { 
              name: 'description', 
              type: 'string', 
              required: true,
              minLength: 1
            },
            quantity: { 
              name: 'quantity', 
              type: 'integer', 
              required: false,
              minimum: 1,
              default: 1
            },
            unit_price: { 
              name: 'unit_price', 
              type: 'number', 
              required: false,
              minimum: 0
            },
            total_price: { 
              name: 'total_price', 
              type: 'number', 
              required: false,
              minimum: 0
            }
          }
        }
      }
    ];

    return this.generateFromFields(fields);
  }

  /**
   * Generate schema for general document processing
   */
  generateGeneralDocumentSchema(): JSONSchema {
    const fields: FieldDefinition[] = [
      {
        name: 'document_type',
        type: 'string',
        required: false,
        description: 'Type of document identified',
        enum: ['invoice', 'receipt', 'contract', 'report', 'letter', 'form', 'other']
      },
      {
        name: 'title',
        type: 'string',
        required: false,
        description: 'Document title or subject'
      },
      {
        name: 'date_created',
        type: 'string',
        required: false,
        description: 'Date the document was created',
        format: 'date'
      },
      {
        name: 'author',
        type: 'string',
        required: false,
        description: 'Document author or creator'
      },
      {
        name: 'key_information',
        type: 'object',
        required: false,
        description: 'Key information extracted from document',
        properties: {
          names: {
            name: 'names',
            type: 'array',
            required: false,
            items: { name: 'name', type: 'string', required: true }
          },
          dates: {
            name: 'dates',
            type: 'array',
            required: false,
            items: { name: 'date', type: 'string', required: true, format: 'date' }
          },
          amounts: {
            name: 'amounts',
            type: 'array',
            required: false,
            items: { name: 'amount', type: 'number', required: true, minimum: 0 }
          },
          addresses: {
            name: 'addresses',
            type: 'array',
            required: false,
            items: { name: 'address', type: 'string', required: true }
          }
        }
      },
      {
        name: 'summary',
        type: 'string',
        required: false,
        description: 'Brief summary of document content',
        maxLength: 1000
      },
      {
        name: 'confidence_score',
        type: 'number',
        required: false,
        description: 'AI extraction confidence score',
        format: 'confidence'
      }
    ];

    return this.generateFromFields(fields);
  }

  /**
   * Validate generated schema
   */
  validateGeneratedSchema(schema: JSONSchema): boolean {
    try {
      // Basic validation checks
      if (!schema.type || schema.type !== 'object') {
        throw new Error('Schema must be of type object');
      }

      if (!schema.properties || Object.keys(schema.properties).length === 0) {
        throw new Error('Schema must have at least one property');
      }

      if (!schema.$schema) {
        throw new Error('Schema must include $schema property');
      }

      // Validate each property recursively
      for (const [propName, propSchema] of Object.entries(schema.properties)) {
        this.validateProperty(propName, propSchema);
      }

      return true;
    } catch {
      console.error('Schema validation failed:', error.message);
      return false;
    }
  }

  /**
   * Validate individual property schema
   */
  private validateProperty(propName: string, propSchema: JSONSchema): void {
    if (!propSchema.type) {
      throw new Error(`Property ${propName} must have a type`);
    }

    // Validate type-specific constraints
    switch (propSchema.type) {
      case 'string': {
      }
        if (propSchema.minLength !== undefined && propSchema.minLength < 0) {
          throw new Error(`Property ${propName}: minLength cannot be negative`);
        }
        if (propSchema.maxLength !== undefined && propSchema.maxLength < 0) {
          throw new Error(`Property ${propName}: maxLength cannot be negative`);
        }
        if (propSchema.minLength !== undefined && propSchema.maxLength !== undefined && 
            propSchema.minLength > propSchema.maxLength) {
          throw new Error(`Property ${propName}: minLength cannot be greater than maxLength`);
        }
        break;
    }

      case 'number':
      case 'integer': {
      }
        if (propSchema.minimum !== undefined && propSchema.maximum !== undefined && 
            propSchema.minimum > propSchema.maximum) {
          throw new Error(`Property ${propName}: minimum cannot be greater than maximum`);
        }
        break;
      }

      case 'array': {
      }
        if (propSchema.minItems !== undefined && propSchema.minItems < 0) {
          throw new Error(`Property ${propName}: minItems cannot be negative`);
        }
        if (propSchema.maxItems !== undefined && propSchema.maxItems < 0) {
          throw new Error(`Property ${propName}: maxItems cannot be negative`);
        }
        if (propSchema.minItems !== undefined && propSchema.maxItems !== undefined && 
            propSchema.minItems > propSchema.maxItems) {
          throw new Error(`Property ${propName}: minItems cannot be greater than maxItems`);
        }
        break;
    }
    }
  }
}