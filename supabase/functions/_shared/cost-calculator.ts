export interface CostConfig {
  model: string;
  inputTokenCost: number;
  outputTokenCost: number;
  markupMultiplier: number;
}

export interface CostCalculation {
  inputTokens: number;
  outputTokens: number;
  model: string;
  providerCost: number;
  customerPrice: number;
  markup: number;
  creditsUsed: number;
}

const MODEL_COSTS: Record<string, CostConfig> = {
  'gpt-4o': {
    model: 'gpt-4o',
    inputTokenCost: 0.005 / 1000,
    outputTokenCost: 0.015 / 1000,
    markupMultiplier: 2.5
  },
  'gpt-4o-mini': {
    model: 'gpt-4o-mini',
    inputTokenCost: 0.00015 / 1000,
    outputTokenCost: 0.0006 / 1000,
    markupMultiplier: 3.0
  },
  'claude-3-haiku': {
    model: 'claude-3-haiku',
    inputTokenCost: 0.00025 / 1000,
    outputTokenCost: 0.00125 / 1000,
    markupMultiplier: 2.8
  },
  'claude-3-sonnet': {
    model: 'claude-3-sonnet',
    inputTokenCost: 0.003 / 1000,
    outputTokenCost: 0.015 / 1000,
    markupMultiplier: 2.5
  },
  'llamaparse': {
    model: 'llamaparse',
    inputTokenCost: 0.003 / 1000,
    outputTokenCost: 0.003 / 1000,
    markupMultiplier: 2.0
  }
};

export function calculateCost(
  inputTokens: number,
  outputTokens: number,
  model: string
): CostCalculation {
  const config = MODEL_COSTS[model] || MODEL_COSTS['gpt-4o-mini'];
  
  const providerCost = (inputTokens * config.inputTokenCost) + (outputTokens * config.outputTokenCost);
  const customerPrice = providerCost * config.markupMultiplier;
  const markup = customerPrice - providerCost;
  
  // Convert to credits (1 credit = $0.001)
  const creditsUsed = Math.ceil(customerPrice * 1000);
  
  return {
    inputTokens,
    outputTokens,
    model: config.model,
    providerCost: Math.round(providerCost * 1000000) / 1000000, // 6 decimal precision
    customerPrice: Math.round(customerPrice * 1000000) / 1000000,
    markup: Math.round(markup * 1000000) / 1000000,
    creditsUsed
  };
}

export function estimateCostForDocument(
  documentSizeBytes: number,
  model: string = 'gpt-4o-mini'
): CostCalculation {
  // Rough estimation: 1 token ~= 4 characters for English text
  // For documents, assume 70% text extraction efficiency
  const estimatedChars = documentSizeBytes * 0.7;
  const estimatedInputTokens = Math.ceil(estimatedChars / 4);
  
  // Output tokens typically 10-20% of input for extraction tasks
  const estimatedOutputTokens = Math.ceil(estimatedInputTokens * 0.15);
  
  return calculateCost(estimatedInputTokens, estimatedOutputTokens, model);
}

export function getModelPricing(): Record<string, CostConfig> {
  return { ...MODEL_COSTS };
}

export function addCustomModelPricing(model: string, config: CostConfig): void {
  MODEL_COSTS[model] = config;
}