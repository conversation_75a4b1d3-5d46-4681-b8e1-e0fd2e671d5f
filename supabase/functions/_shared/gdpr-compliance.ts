// ================================================================================
// GDPR COMPLIANCE AND DATA PROTECTION UTILITIES
// ================================================================================

import { supabase } from "./supabase.ts";
import { DataEncryption, PIIProtection } from "./encryption.ts";
import { AuditLogger } from "./audit.ts";

/**
 * GDPR consent types
 */
export enum ConsentType {
  NECESSARY = 'necessary',
  FUNCTIONAL = 'functional', 
  ANALYTICS = 'analytics',
  MARKETING = 'marketing',
}

/**
 * GDPR legal basis
 */
export enum LegalBasis {
  CONSENT = 'consent',
  CONTRACT = 'contract',
  LEGAL_OBLIGATION = 'legal_obligation',
  VITAL_INTERESTS = 'vital_interests',
  PUBLIC_TASK = 'public_task',
  LEGITIMATE_INTERESTS = 'legitimate_interests',
}

/**
 * Data categories for GDPR classification
 */
export enum DataCategory {
  IDENTITY = 'identity',
  CONTACT = 'contact',
  FINANCIAL = 'financial',
  TECHNICAL = 'technical',
  USAGE = 'usage',
  CONTENT = 'content',
  SPECIAL = 'special', // Special category data (sensitive)
}

/**
 * GDPR data subject rights
 */
export enum DataSubjectRight {
  ACCESS = 'access',           // Right to access
  RECTIFICATION = 'rectification', // Right to rectification
  ERASURE = 'erasure',         // Right to erasure (right to be forgotten)
  RESTRICT = 'restrict',       // Right to restrict processing
  PORTABILITY = 'portability', // Right to data portability
  OBJECT = 'object',          // Right to object
  AUTOMATED = 'automated',    // Rights related to automated decision making
}

/**
 * GDPR data processing record
 */
export interface DataProcessingRecord {
  id?: string;
  customer_id: string;
  data_subject_id: string;
  legal_basis: LegalBasis;
  purpose: string;
  data_categories: DataCategory[];
  retention_period: string; // ISO 8601 duration
  third_party_sharing: boolean;
  cross_border_transfer: boolean;
  automated_decision_making: boolean;
  consent_given?: boolean;
  consent_date?: string;
  consent_withdrawn?: boolean;
  consent_withdrawal_date?: string;
  created_at: string;
  updated_at: string;
}

/**
 * GDPR data subject request
 */
export interface DataSubjectRequest {
  id?: string;
  customer_id: string;
  data_subject_id: string;
  request_type: DataSubjectRight;
  status: 'pending' | 'in_progress' | 'completed' | 'denied';
  request_details: Record<string, any>;
  requester_email: string;
  verification_status: 'pending' | 'verified' | 'failed';
  response_data?: Record<string, any>;
  completion_date?: string;
  denial_reason?: string;
  created_at: string;
  updated_at: string;
}

/**
 * GDPR compliance manager
 */
export class GDPRCompliance {
  /**
   * Record data processing activity
   */
  static async recordProcessingActivity(
    customerId: string,
    dataSubjectId: string,
    activity: {
      legal_basis: LegalBasis;
      purpose: string;
      data_categories: DataCategory[];
      retention_period: string;
      third_party_sharing?: boolean;
      cross_border_transfer?: boolean;
      automated_decision_making?: boolean;
    }
  ): Promise<void> {
    try {
      const record: DataProcessingRecord = {
        customer_id: customerId,
        data_subject_id: dataSubjectId,
        legal_basis: activity.legal_basis,
        purpose: activity.purpose,
        data_categories: activity.data_categories,
        retention_period: activity.retention_period,
        third_party_sharing: activity.third_party_sharing || false,
        cross_border_transfer: activity.cross_border_transfer || false,
        automated_decision_making: activity.automated_decision_making || false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
      
      const { error } = await supabase
        .from('compliance_events')
        .insert({
          compliance_type: 'gdpr',
          event_type: 'data_processing',
          customer_id: customerId,
          data_subject_id: dataSubjectId,
          legal_basis: activity.legal_basis,
          data_categories: activity.data_categories,
          purpose: activity.purpose,
          retention_period: activity.retention_period,
          automated_decision: activity.automated_decision_making || false,
          third_party_sharing: activity.third_party_sharing || false,
          cross_border_transfer: activity.cross_border_transfer || false,
          audit_trail: record,
          created_at: new Date().toISOString(),
        });
      
      if (error) {
        throw new Error(`Failed to record processing activity: ${error.message}`);
      }
      
      // Log for audit trail
      await AuditLogger.log({
        customer_id: customerId,
        action: 'gdpr_processing_recorded',
        event_category: 'compliance',
        status: 'success',
        details: {
          data_subject_id: dataSubjectId,
          legal_basis: activity.legal_basis,
          purpose: activity.purpose,
          data_categories: activity.data_categories,
        },
        ip_address: 'system',
        user_agent: 'gdpr_compliance_system',
        correlation_id: crypto.randomUUID(),
        compliance_tags: ['gdpr', 'processing_record'],
      });
      
    } catch (error) {
      console.error('Failed to record GDPR processing activity:', error);
      throw error;
    }
  }
  
  /**
   * Handle data subject access request (Right to Access)
   */
  static async handleAccessRequest(
    customerId: string,
    dataSubjectId: string,
    requesterEmail: string
  ): Promise<{ request_id: string; status: string }> {
    try {
      // Create the request record
      const { data: requestData, error: requestError } = await supabase
        .from('data_subject_requests')
        .insert({
          customer_id: customerId,
          data_subject_id: dataSubjectId,
          request_type: DataSubjectRight.ACCESS,
          status: 'pending',
          request_details: {
            requested_data_categories: 'all',
            format: 'json',
          },
          requester_email: requesterEmail,
          verification_status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select('id')
        .single();
      
      if (requestError || !requestData) {
        throw new Error(`Failed to create access request: ${requestError?.message}`);
      }
      
      // Start processing the request
      const responseData = await this.gatherUserData(customerId, dataSubjectId);
      
      // Update request with response data
      const { error: updateError } = await supabase
        .from('data_subject_requests')
        .update({
          status: 'completed',
          response_data: responseData,
          completion_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', requestData.id);
      
      if (updateError) {
        throw new Error(`Failed to update access request: ${updateError.message}`);
      }
      
      // Log the access request
      await AuditLogger.logDataAccess(
        'gdpr_access_request',
        'user_data',
        dataSubjectId,
        customerId,
        {
          request_id: requestData.id,
          requester_email: requesterEmail,
          data_categories_accessed: Object.keys(responseData),
        },
        { headers: { get: () => 'system' } } as unknown as Request
      );
      
      return {
        request_id: requestData.id,
        status: 'completed',
      };
      
    } catch (error) {
      console.error('Failed to handle GDPR access request:', error);
      throw error;
    }
  }
  
  /**
   * Handle data subject erasure request (Right to be Forgotten)
   */
  static async handleErasureRequest(
    customerId: string,
    dataSubjectId: string,
    requesterEmail: string,
    justification?: string
  ): Promise<{ request_id: string; status: string }> {
    try {
      // Create the request record
      const { data: requestData, error: requestError } = await supabase
        .from('data_subject_requests')
        .insert({
          customer_id: customerId,
          data_subject_id: dataSubjectId,
          request_type: DataSubjectRight.ERASURE,
          status: 'pending',
          request_details: {
            justification: justification || 'No longer necessary for original purpose',
            erasure_scope: 'all_personal_data',
          },
          requester_email: requesterEmail,
          verification_status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select('id')
        .single();
      
      if (requestError || !requestData) {
        throw new Error(`Failed to create erasure request: ${requestError?.message}`);
      }
      
      // Check if erasure is legally possible
      const canErase = await this.checkErasureFeasibility(customerId, dataSubjectId);
      
      if (!canErase.possible) {
        // Update request with denial
        await supabase
          .from('data_subject_requests')
          .update({
            status: 'denied',
            denial_reason: canErase.reason,
            updated_at: new Date().toISOString(),
          })
          .eq('id', requestData.id);
        
        return {
          request_id: requestData.id,
          status: 'denied',
        };
      }
      
      // Perform data erasure
      await this.performDataErasure(customerId, dataSubjectId);
      
      // Update request as completed
      const { error: updateError } = await supabase
        .from('data_subject_requests')
        .update({
          status: 'completed',
          completion_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', requestData.id);
      
      if (updateError) {
        throw new Error(`Failed to update erasure request: ${updateError.message}`);
      }
      
      // Log the erasure
      await AuditLogger.log({
        customer_id: customerId,
        action: 'gdpr_data_erased',
        event_category: 'compliance',
        status: 'success',
        details: {
          request_id: requestData.id,
          data_subject_id: dataSubjectId,
          requester_email: requesterEmail,
          justification,
        },
        ip_address: 'system',
        user_agent: 'gdpr_compliance_system',
        correlation_id: crypto.randomUUID(),
        compliance_tags: ['gdpr', 'right_to_erasure'],
      });
      
      return {
        request_id: requestData.id,
        status: 'completed',
      };
      
    } catch (error) {
      console.error('Failed to handle GDPR erasure request:', error);
      throw error;
    }
  }
  
  /**
   * Handle data portability request
   */
  static async handlePortabilityRequest(
    customerId: string,
    dataSubjectId: string,
    requesterEmail: string,
    format: 'json' | 'csv' | 'xml' = 'json'
  ): Promise<{ request_id: string; download_url: string }> {
    try {
      // Gather user data
      const userData = await this.gatherUserData(customerId, dataSubjectId);
      
      // Format data for portability
      const portableData = await this.formatDataForPortability(userData, format);
      
      // Store data temporarily for download (encrypt for security)
      const encryptedData = await DataEncryption.encrypt(JSON.stringify(portableData));
      const downloadToken = crypto.randomUUID();
      
      // Create temporary download record (expires in 7 days)
      const expiryDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
      
      const { data: downloadRecord, error: downloadError } = await supabase
        .from('data_downloads')
        .insert({
          download_token: downloadToken,
          customer_id: customerId,
          data_subject_id: dataSubjectId,
          encrypted_data: encryptedData,
          format,
          expires_at: expiryDate.toISOString(),
          created_at: new Date().toISOString(),
        })
        .select('id')
        .single();
      
      if (downloadError || !downloadRecord) {
        throw new Error(`Failed to create download record: ${downloadError?.message}`);
      }
      
      // Create the request record
      const { data: requestData, error: requestError } = await supabase
        .from('data_subject_requests')
        .insert({
          customer_id: customerId,
          data_subject_id: dataSubjectId,
          request_type: DataSubjectRight.PORTABILITY,
          status: 'completed',
          request_details: {
            format,
            download_token: downloadToken,
            expires_at: expiryDate.toISOString(),
          },
          requester_email: requesterEmail,
          verification_status: 'verified',
          completion_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select('id')
        .single();
      
      if (requestError || !requestData) {
        throw new Error(`Failed to create portability request: ${requestError?.message}`);
      }
      
      // Generate download URL
      const downloadUrl = `${Deno.env.get('SUPABASE_URL')}/functions/v1/gdpr-download?token=${downloadToken}`;
      
      // Log the portability request
      await AuditLogger.log({
        customer_id: customerId,
        action: 'gdpr_data_export',
        event_category: 'compliance',
        status: 'success',
        details: {
          request_id: requestData.id,
          data_subject_id: dataSubjectId,
          requester_email: requesterEmail,
          format,
          download_token: downloadToken,
        },
        ip_address: 'system',
        user_agent: 'gdpr_compliance_system',
        correlation_id: crypto.randomUUID(),
        compliance_tags: ['gdpr', 'data_portability'],
      });
      
      return {
        request_id: requestData.id,
        download_url: downloadUrl,
      };
      
    } catch (error) {
      console.error('Failed to handle GDPR portability request:', error);
      throw error;
    }
  }
  
  /**
   * Gather all user data for access/portability requests
   */
  private static async gatherUserData(customerId: string, dataSubjectId: string): Promise<Record<string, any>> {
    try {
      const userData: Record<string, any> = {};
      
      // Customer profile data
      const { data: customer } = await supabase
        .from('customers')
        .select('*')
        .eq('id', customerId)
        .single();
      
      if (customer) {
        userData.profile = {
          customer_id: customer.id,
          company_name: customer.company_name,
          contact_email: customer.contact_email,
          tier: customer.tier,
          created_at: customer.created_at,
        };
      }
      
      // API keys (without sensitive data)
      const { data: apiKeys } = await supabase
        .from('api_keys')
        .select('id, key_type, credits, rate_limits, created_at, last_used_at')
        .eq('customer_id', customerId);
      
      if (apiKeys) {
        userData.api_keys = apiKeys;
      }
      
      // Usage logs
      const { data: usageLogs } = await supabase
        .from('usage_logs')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })
        .limit(1000); // Limit to recent 1000 records
      
      if (usageLogs) {
        userData.usage_history = usageLogs;
      }
      
      // Documents processed (metadata only, not content)
      const { data: documents } = await supabase
        .from('documents')
        .select('id, file_name, file_size, mime_type, created_at')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })
        .limit(500);
      
      if (documents) {
        userData.processed_documents = documents;
      }
      
      // Audit logs related to this customer
      const { data: auditLogs } = await supabase
        .from('audit_logs')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false })
        .limit(500);
      
      if (auditLogs) {
        userData.audit_trail = auditLogs;
      }
      
      // Compliance events
      const { data: complianceEvents } = await supabase
        .from('compliance_events')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false });
      
      if (complianceEvents) {
        userData.compliance_history = complianceEvents;
      }
      
      return userData;
      
    } catch (error) {
      console.error('Failed to gather user data:', error);
      throw error;
    }
  }
  
  /**
   * Check if data erasure is legally feasible
   */
  private static async checkErasureFeasibility(
    customerId: string,
    dataSubjectId: string
  ): Promise<{ possible: boolean; reason?: string }> {
    try {
      // Check if there are legal obligations preventing erasure
      const { data: legalObligations } = await supabase
        .from('compliance_events')
        .select('*')
        .eq('customer_id', customerId)
        .eq('legal_basis', 'legal_obligation');
      
      if (legalObligations && legalObligations.length > 0) {
        return {
          possible: false,
          reason: 'Data retention required for legal compliance obligations',
        };
      }
      
      // Check for ongoing contractual obligations
      const { data: customer } = await supabase
        .from('customers')
        .select('status, tier')
        .eq('id', customerId)
        .single();
      
      if (customer && customer.status === 'active') {
        return {
          possible: false,
          reason: 'Cannot erase data while contract is active. Please cancel service first.',
        };
      }
      
      // Check for pending financial obligations
      const { data: pendingCharges } = await supabase
        .from('usage_logs')
        .select('*')
        .eq('customer_id', customerId)
        .eq('status', 'pending')
        .limit(1);
      
      if (pendingCharges && pendingCharges.length > 0) {
        return {
          possible: false,
          reason: 'Cannot erase data while there are pending financial obligations',
        };
      }
      
      return { possible: true };
      
    } catch (error) {
      console.error('Failed to check erasure feasibility:', error);
      return {
        possible: false,
        reason: 'Unable to verify erasure feasibility due to system error',
      };
    }
  }
  
  /**
   * Perform actual data erasure
   */
  private static async performDataErasure(customerId: string, dataSubjectId: string): Promise<void> {
    try {
      // Mark customer as deleted
      await supabase
        .from('customers')
        .update({
          status: 'deleted',
          company_name: 'DELETED',
          contact_email: '<EMAIL>',
          deletion_requested_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', customerId);
      
      // Anonymize API keys
      await supabase
        .from('api_keys')
        .update({
          key_hash: 'DELETED_' + crypto.randomUUID(),
          revoked: true,
          revoked_at: new Date().toISOString(),
        })
        .eq('customer_id', customerId);
      
      // Delete sensitive document content but retain metadata for legal compliance
      const { data: documents } = await supabase
        .from('documents')
        .select('id')
        .eq('customer_id', customerId);
      
      if (documents) {
        for (const doc of documents) {
          await supabase
            .from('documents')
            .update({
              file_content: null,
              file_url: null,
              customer_id: null, // Anonymize
              updated_at: new Date().toISOString(),
            })
            .eq('id', doc.id);
        }
      }
      
      // Anonymize usage logs but retain for aggregate analytics
      await supabase
        .from('usage_logs')
        .update({
          customer_id: null,
          api_key_id: null,
        })
        .eq('customer_id', customerId);
      
      // Keep audit logs for compliance but anonymize customer references
      await supabase
        .from('audit_logs')
        .update({
          customer_id: null,
          api_key_id: null,
        })
        .eq('customer_id', customerId);
      
      // Record the deletion in compliance events
      await supabase
        .from('compliance_events')
        .insert({
          compliance_type: 'gdpr',
          event_type: 'data_erasure_completed',
          customer_id: null, // Already anonymized
          data_subject_id: dataSubjectId,
          legal_basis: 'consent',
          deletion_completed_at: new Date().toISOString(),
          audit_trail: {
            original_customer_id: customerId,
            deletion_date: new Date().toISOString(),
            erasure_scope: 'complete',
          },
          created_at: new Date().toISOString(),
        });
      
    } catch (error) {
      console.error('Failed to perform data erasure:', error);
      throw error;
    }
  }
  
  /**
   * Format data for portability
   */
  private static async formatDataForPortability(
    userData: Record<string, any>,
    format: 'json' | 'csv' | 'xml'
  ): Promise<any> {
    try {
      switch (format) {
        case 'json':
          return {
            export_date: new Date().toISOString(),
            data_format: 'JSON',
            data: userData,
          };
          
        case 'csv':
          // Convert to CSV format (simplified)
          return this.convertToCSV(userData);
          
        case 'xml':
          // Convert to XML format (simplified)
          return this.convertToXML(userData);
          
        default:
          throw new Error(`Unsupported format: ${format}`);
      }
    } catch (error) {
      console.error('Failed to format data for portability:', error);
      throw error;
    }
  }
  
  /**
   * Convert data to CSV format
   */
  private static convertToCSV(data: Record<string, any>): string {
    const lines: string[] = [];
    
    for (const [section, sectionData] of Object.entries(data)) {
      if (Array.isArray(sectionData)) {
        if (sectionData.length > 0) {
          const headers = Object.keys(sectionData[0]);
          lines.push(`\n[${section.toUpperCase()}]`);
          lines.push(headers.join(','));
          
          for (const row of sectionData) {
            const values = headers.map(header => 
              JSON.stringify(row[header] || '').replace(/"/g, '""')
            );
            lines.push(values.join(','));
          }
        }
      } else if (typeof sectionData === 'object') {
        lines.push(`\n[${section.toUpperCase()}]`);
        for (const [key, value] of Object.entries(sectionData)) {
          lines.push(`${key},${JSON.stringify(value).replace(/"/g, '""')}`);
        }
      }
    }
    
    return lines.join('\n');
  }
  
  /**
   * Convert data to XML format
   */
  private static convertToXML(data: Record<string, any>): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<gdpr_export>\n';
    xml += `  <export_date>${new Date().toISOString()}</export_date>\n`;
    
    for (const [section, sectionData] of Object.entries(data)) {
      xml += `  <${section}>\n`;
      
      if (Array.isArray(sectionData)) {
        for (const item of sectionData) {
          xml += '    <item>\n';
          for (const [key, value] of Object.entries(item)) {
            xml += `      <${key}>${this.escapeXML(String(value))}</${key}>\n`;
          }
          xml += '    </item>\n';
        }
      } else if (typeof sectionData === 'object') {
        for (const [key, value] of Object.entries(sectionData)) {
          xml += `    <${key}>${this.escapeXML(String(value))}</${key}>\n`;
        }
      }
      
      xml += `  </${section}>\n`;
    }
    
    xml += '</gdpr_export>';
    return xml;
  }
  
  /**
   * Escape XML special characters
   */
  private static escapeXML(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }
  
  /**
   * Get GDPR compliance status for a customer
   */
  static async getComplianceStatus(customerId: string): Promise<Record<string, any>> {
    try {
      const { data: complianceEvents } = await supabase
        .from('compliance_events')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false });
      
      const { data: dataRequests } = await supabase
        .from('data_subject_requests')
        .select('*')
        .eq('customer_id', customerId)
        .order('created_at', { ascending: false });
      
      return {
        customer_id: customerId,
        total_processing_events: complianceEvents?.length || 0,
        total_data_requests: dataRequests?.length || 0,
        recent_requests: dataRequests?.slice(0, 10) || [],
        compliance_score: this.calculateComplianceScore(complianceEvents || [], dataRequests || []),
        last_audit_date: complianceEvents?.[0]?.created_at || null,
      };
      
    } catch (error) {
      console.error('Failed to get compliance status:', error);
      throw error;
    }
  }
  
  /**
   * Calculate compliance score (0-100)
   */
  private static calculateComplianceScore(
    events: any[],
    requests: any[]
  ): number {
    let score = 100;
    
    // Deduct points for overdue requests
    const overdueRequests = requests.filter(req => {
      const created = new Date(req.created_at);
      const daysSinceCreated = (Date.now() - created.getTime()) / (1000 * 60 * 60 * 24);
      return req.status === 'pending' && daysSinceCreated > 30; // GDPR requires response within 30 days
    });
    
    score -= overdueRequests.length * 20; // 20 points per overdue request
    
    // Deduct points for denied requests without proper justification
    const unjustifiedDenials = requests.filter(req => 
      req.status === 'denied' && (!req.denial_reason || req.denial_reason.length < 10)
    );
    
    score -= unjustifiedDenials.length * 15; // 15 points per unjustified denial
    
    return Math.max(0, Math.min(100, score));
  }
}