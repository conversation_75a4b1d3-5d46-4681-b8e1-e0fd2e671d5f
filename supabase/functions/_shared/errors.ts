// ================================================================================
// STANDARDIZED ERROR HANDLING SYSTEM
// ================================================================================

import { supabase } from "./supabase.ts";

/**
 * Standardized error codes for the API
 */
export enum ErrorCode {
  // Authentication errors (401)
  AUTH_INVALID_KEY = 'AUTH_INVALID_KEY',
  AUTH_EXPIRED_KEY = 'AUTH_EXPIRED_KEY',
  AUTH_MALFORMED_KEY = 'AUTH_MALFORMED_KEY',
  AUTH_REVOKED_KEY = 'AUTH_REVOKED_KEY',
  
  // Authorization errors (403)
  PERM_INSUFFICIENT_CREDITS = 'PERM_INSUFFICIENT_CREDITS',
  PERM_SUSPENDED_KEY = 'PERM_SUSPENDED_KEY',
  PERM_TIER_RESTRICTION = 'PERM_TIER_RESTRICTION',
  PERM_FEATURE_DISABLED = 'PERM_FEATURE_DISABLED',
  
  // Rate limiting (429)
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  RATE_BURST_DETECTED = 'RATE_BURST_DETECTED',
  RATE_QUOTA_EXCEEDED = 'RATE_QUOTA_EXCEEDED',
  
  // Validation errors (400)
  VALID_FILE_TOO_LARGE = 'VALID_FILE_TOO_LARGE',
  VALID_UNSUPPORTED_FORMAT = 'VALID_UNSUPPORTED_FORMAT',
  VALID_INVALID_SCHEMA = 'VALID_INVALID_SCHEMA',
  VALID_MISSING_FIELD = 'VALID_MISSING_FIELD',
  VALID_MALFORMED_JSON = 'VALID_MALFORMED_JSON',
  
  // Security errors (400/403)
  SECURITY_PROMPT_INJECTION = 'SECURITY_PROMPT_INJECTION',
  SECURITY_MALICIOUS_INPUT = 'SECURITY_MALICIOUS_INPUT',
  SECURITY_UNSAFE_FILE = 'SECURITY_UNSAFE_FILE',
  SECURITY_IP_BLOCKED = 'SECURITY_IP_BLOCKED',
  
  // Processing errors (422)
  PROC_AI_SERVICE_FAILED = 'PROC_AI_SERVICE_FAILED',
  PROC_TIMEOUT = 'PROC_TIMEOUT',
  PROC_INVALID_RESPONSE = 'PROC_INVALID_RESPONSE',
  PROC_EXTRACTION_FAILED = 'PROC_EXTRACTION_FAILED',
  PROC_QUEUE_FULL = 'PROC_QUEUE_FULL',
  
  // System errors (500)
  SYS_DATABASE_ERROR = 'SYS_DATABASE_ERROR',
  SYS_AI_SERVICE_UNAVAILABLE = 'SYS_AI_SERVICE_UNAVAILABLE',
  SYS_INTERNAL_ERROR = 'SYS_INTERNAL_ERROR',
  SYS_CONFIGURATION_ERROR = 'SYS_CONFIGURATION_ERROR',
  SYS_DEPENDENCY_FAILURE = 'SYS_DEPENDENCY_FAILURE',
  
  // Network/Infrastructure errors (502/503/504)
  INFRA_SERVICE_UNAVAILABLE = 'INFRA_SERVICE_UNAVAILABLE',
  INFRA_GATEWAY_TIMEOUT = 'INFRA_GATEWAY_TIMEOUT',
  INFRA_CIRCUIT_BREAKER_OPEN = 'INFRA_CIRCUIT_BREAKER_OPEN',
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  error: {
    code: ErrorCode;
    message: string;
    details?: Record<string, any>;
    correlation_id: string;
    timestamp: string;
    documentation_url?: string;
    retry_after?: number;
    support_reference?: string;
  };
}

/**
 * Custom API Error class with structured error handling
 */
export class APIError extends Error {
  constructor(
    public code: ErrorCode,
    public statusCode: number,
    message: string,
    public details?: Record<string, any>,
    public retryAfter?: number,
    public isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'APIError';
  }
  
  /**
   * Convert to standardized error response
   */
  toResponse(correlationId: string): ErrorResponse {
    return {
      error: {
        code: this.code,
        message: this.message,
        details: this.details,
        correlation_id: correlationId,
        timestamp: new Date().toISOString(),
        documentation_url: `https://docs.idp-platform.com/errors/${this.code.toLowerCase()}`,
        retry_after: this.retryAfter,
        support_reference: this.generateSupportReference(correlationId),
      }
    };
  }
  
  /**
   * Generate support reference for customer service
   */
  private generateSupportReference(correlationId: string): string {
    const timestamp = Date.now().toString(36);
    const errorPrefix = this.code.split('_')[0].toLowerCase();
    return `${errorPrefix}-${timestamp}-${correlationId.slice(-8)}`;
  }
}

/**
 * Error severity levels
 */
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium', 
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * Enhanced error logging interface
 */
export interface ErrorLogEntry {
  error_code: ErrorCode;
  error_message: string;
  stack_trace?: string;
  endpoint?: string;
  method?: string;
  customer_id?: string;
  api_key_id?: string;
  correlation_id: string;
  ip_address?: string;
  user_agent?: string;
  request_data?: Record<string, any>;
  context_data?: Record<string, any>;
  severity: ErrorSeverity;
  resolved?: boolean;
  resolution_notes?: string;
}

/**
 * Global error handler for Edge Functions
 */
export function createErrorHandler() {
  return async (req: Request, error: unknown): Promise<Response> => {
    const correlationId = req.headers.get('x-correlation-id') || crypto.randomUUID();
    const startTime = Date.now();
    
    // Determine error details
    let apiError: APIError;
    let severity: ErrorSeverity = ErrorSeverity.MEDIUM;
    
    if (error instanceof APIError) {
      apiError = error;
      severity = determineSeverity(apiError.code);
    } else if (error instanceof Error) {
      // Convert known error types
      apiError = convertKnownError(error);
      severity = ErrorSeverity.HIGH;
    } else {
      // Unknown error type
      apiError = new APIError(
        ErrorCode.SYS_INTERNAL_ERROR,
        500,
        'An unexpected error occurred',
        { original_error: String(error) }
      );
      severity = ErrorSeverity.CRITICAL;
    }
    
    // Log the error
    await logError({
      error_code: apiError.code,
      error_message: apiError.message,
      stack_trace: error instanceof Error ? error.stack : undefined,
      endpoint: new URL(req.url).pathname,
      method: req.method,
      correlation_id: correlationId,
      ip_address: req.headers.get('x-forwarded-for') || req.headers.get('cf-connecting-ip') || undefined,
      user_agent: req.headers.get('user-agent') || undefined,
      request_data: await safelyParseRequestData(req),
      context_data: apiError.details,
      severity,
    });
    
    // Return standardized error response
    const response = new Response(
      JSON.stringify(apiError.toResponse(correlationId)),
      {
        status: apiError.statusCode,
        headers: {
          'Content-Type': 'application/json',
          'X-Correlation-ID': correlationId,
          'X-Response-Time': `${Date.now() - startTime}ms`,
          ...(apiError.retryAfter && { 'Retry-After': apiError.retryAfter.toString() }),
          // Security headers
          'X-Content-Type-Options': 'nosniff',
          'X-Frame-Options': 'DENY',
        }
      }
    );
    
    return response;
  };
}

/**
 * Determine error severity based on error code
 */
function determineSeverity(code: ErrorCode): ErrorSeverity {
  if (code.startsWith('SYS_') || code.startsWith('INFRA_')) {
    return ErrorSeverity.CRITICAL;
  }
  if (code.startsWith('SECURITY_') || code.startsWith('PROC_')) {
    return ErrorSeverity.HIGH;
  }
  if (code.startsWith('AUTH_') || code.startsWith('PERM_')) {
    return ErrorSeverity.MEDIUM;
  }
  return ErrorSeverity.LOW;
}

/**
 * Convert known error types to APIError
 */
function convertKnownError(error: Error): APIError {
  // Database connection errors
  if (error.message.includes('connection') || error.message.includes('timeout')) {
    return new APIError(
      ErrorCode.SYS_DATABASE_ERROR,
      500,
      'Database connection error',
      { original_message: error.message },
      30, // retry after 30 seconds
      true
    );
  }
  
  // JSON parsing errors
  if (error.message.includes('JSON') || error.message.includes('parse')) {
    return new APIError(
      ErrorCode.VALID_MALFORMED_JSON,
      400,
      'Invalid JSON in request body',
      { original_message: error.message }
    );
  }
  
  // Network errors
  if (error.message.includes('fetch') || error.message.includes('network')) {
    return new APIError(
      ErrorCode.INFRA_SERVICE_UNAVAILABLE,
      503,
      'External service unavailable',
      { original_message: error.message },
      60,
      true
    );
  }
  
  // Default conversion
  return new APIError(
    ErrorCode.SYS_INTERNAL_ERROR,
    500,
    'Internal server error',
    { original_message: error.message }
  );
}

/**
 * Safely parse request data for logging (without sensitive info)
 */
async function safelyParseRequestData(req: Request): Promise<Record<string, any> | undefined> {
  try {
    if (req.method === 'GET') {
      const url = new URL(req.url);
      return Object.fromEntries(url.searchParams.entries());
    }
    
    const clonedReq = req.clone();
    const contentType = clonedReq.headers.get('content-type');
    
    if (contentType?.includes('application/json')) {
      const data = await clonedReq.json();
      // Remove sensitive fields before logging
      return sanitizeForLogging(data);
    }
    
    if (contentType?.includes('multipart/form-data')) {
      return { content_type: 'multipart/form-data', message: 'Form data not logged for privacy' };
    }
    
    return { content_type: contentType || 'unknown' };
  } catch {
    return undefined;
  }
}

/**
 * Remove sensitive data before logging
 */
function sanitizeForLogging(data: any): any {
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  const sensitiveFields = [
    'password', 'token', 'key', 'secret', 'auth', 'credential',
    'api_key', 'access_token', 'refresh_token', 'jwt', 'bearer'
  ];
  
  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }
  
  // Recursively sanitize nested objects
  for (const [key, value] of Object.entries(sanitized)) {
    if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeForLogging(value);
    }
  }
  
  return sanitized;
}

/**
 * Log error to database
 */
async function logError(errorLog: ErrorLogEntry): Promise<void> {
  try {
    const { error } = await supabase.from('error_logs').insert({
      ...errorLog,
      created_at: new Date().toISOString(),
    });
    
    if (error) {
      console.error('Failed to log error to database:', error);
    }
    
    // Also log to console for immediate debugging
    console.error('API Error:', {
      code: errorLog.error_code,
      message: errorLog.error_message,
      correlation_id: errorLog.correlation_id,
      endpoint: errorLog.endpoint,
      severity: errorLog.severity,
    });
  } catch (loggingError) {
    // Never let error logging break the main flow
    console.error('Error logging failed:', loggingError);
  }
}

/**
 * Retry manager with exponential backoff
 */
export class RetryManager {
  /**
   * Execute operation with automatic retry and exponential backoff
   */
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
    backoffMultiplier: number = 2,
    maxDelay: number = 30000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) break;
        
        // Don't retry on non-retryable errors
        if (error instanceof APIError && !error.isRetryable) {
          break;
        }
        
        // Calculate delay with exponential backoff and jitter
        const exponentialDelay = Math.min(
          baseDelay * Math.pow(backoffMultiplier, attempt),
          maxDelay
        );
        const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
        const delay = exponentialDelay + jitter;
        
        console.warn(`Retry attempt ${attempt + 1}/${maxRetries} after ${delay}ms`, {
          error: error instanceof Error ? error.message : String(error),
          attempt,
          delay,
        });
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  }
  
  /**
   * Check if an error is retryable
   */
  static isRetryable(error: unknown): boolean {
    if (error instanceof APIError) {
      return error.isRetryable;
    }
    
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      // Network/timeout errors are generally retryable
      return message.includes('timeout') || 
             message.includes('network') || 
             message.includes('connection') ||
             message.includes('502') ||
             message.includes('503') ||
             message.includes('504');
    }
    
    return false;
  }
}

/**
 * Circuit breaker for external service calls
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  
  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000, // 1 minute
    private successThreshold: number = 2
  ) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() - this.lastFailureTime < this.recoveryTimeout) {
        throw new APIError(
          ErrorCode.INFRA_CIRCUIT_BREAKER_OPEN,
          503,
          'Circuit breaker is open',
          { 
            retry_after: Math.ceil((this.recoveryTimeout - (Date.now() - this.lastFailureTime)) / 1000),
            failures: this.failures 
          },
          Math.ceil((this.recoveryTimeout - (Date.now() - this.lastFailureTime)) / 1000),
          true
        );
      } else {
        this.state = 'half-open';
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
  
  private onSuccess(): void {
    if (this.state === 'half-open') {
      this.state = 'closed';
    }
    this.failures = 0;
  }
  
  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'open';
    }
  }
  
  getState(): { state: string; failures: number; lastFailureTime: number } {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime,
    };
  }
}