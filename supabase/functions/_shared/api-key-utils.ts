/**
 * Shared API Key Utilities for Edge Functions
 * Handles authentication, rate limiting, and usage tracking
 */

import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';

export interface ApiKeyValidationResult {
  isValid: boolean;
  customerId?: string;
  keyId?: string;
  keyType?: 'test' | 'production';
  credits?: number;
  status?: string;
  error?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  limit: number;
}

export interface CreditDeductionResult {
  success: boolean;
  remainingCredits?: number;
  error?: string;
}

/**
 * CORS headers for API responses
 */
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey',
  'Access-Control-Max-Age': '86400',
};

/**
 * Validate API key and return customer information
 */
export async function validateApiKey(
  supabase: ReturnType<typeof createClient>,
  rawKey: string
): Promise<ApiKeyValidationResult> {
  try {
    if (!rawKey || rawKey.length < 10) {
      return {
        isValid: false,
        error: 'Invalid API key format'
      };
    }

    // Hash the API key for database lookup
    const keyHash = await hashApiKey(supabase, rawKey);

    // Query database for API key
    const { data: apiKeyData, error: keyError } = await supabase
      .from('api_keys')
      .select('id, customer_id, key_type, credits_allocated, is_active')
      .eq('key_hash', keyHash)
      .eq('is_active', true)
      .single();

    if (keyError || !apiKeyData) {
      return {
        isValid: false,
        error: 'API key not found or inactive'
      };
    }

    return {
      isValid: true,
      customerId: apiKeyData.customer_id,
      keyId: apiKeyData.id,
      keyType: apiKeyData.key_type as 'test' | 'production',
      credits: apiKeyData.credits_allocated,
      status: 'active'
    };

  } catch (error) {
    console.error('API key validation error:', error);
    return {
      isValid: false,
      error: 'Validation failed'
    };
  }
}

/**
 * Hash API key using same method as registration
 */
async function hashApiKey(supabase: ReturnType<typeof createClient>, rawKey: string): Promise<string> {
  const { data: hashResult, error: hashError } = await supabase.rpc('generate_api_key_hash', {
    raw_key: rawKey
  });
  
  if (hashError || !hashResult) {
    console.error('API key hashing error:', hashError);
    throw new Error('Failed to hash API key');
  }
  
  return hashResult;
}

/**
 * Check rate limits for an API key
 */
export async function checkRateLimit(
  supabase: ReturnType<typeof createClient>,
  apiKeyId: string,
  windowType: 'minute' | 'hour' | 'day' = 'minute'
): Promise<RateLimitResult> {
  try {
    const now = new Date();
    let windowStart: Date;
    let defaultLimit: number;

    switch (windowType) {
      case 'minute': {
      }
        windowStart = new Date(now.getTime() - 60000); // 1 minute ago
        defaultLimit = 100;
        break;
    }
      case 'hour': {
      }
        windowStart = new Date(now.getTime() - 3600000); // 1 hour ago
        defaultLimit = 1000;
        break;
    }
      case 'day': {
      }
        windowStart = new Date(now.getTime() - 86400000); // 1 day ago
        defaultLimit = 10000;
        break;
    }
    }

    // Count requests in the current window
    const { count, error } = await supabase
      .from('api_usage_logs')
      .select('*', { count: 'exact', head: true })
      .eq('api_key_id', apiKeyId)
      .gte('created_at', windowStart.toISOString());

    if (error) {
      console.error('Rate limit check error:', error);
      // Allow request on error (fail open)
      return {
        allowed: true,
        remaining: defaultLimit,
        resetTime: new Date(now.getTime() + 60000),
        limit: defaultLimit
      };
    }

    const requestCount = count || 0;
    const remaining = Math.max(0, defaultLimit - requestCount);

    // Calculate reset time (next window)
    let resetTime: Date;
    switch (windowType) {
      case 'minute': {
      }
        resetTime = new Date(Math.ceil(now.getTime() / 60000) * 60000);
        break;
    }
      case 'hour': {
      }
        resetTime = new Date(Math.ceil(now.getTime() / 3600000) * 3600000);
        break;
    }
      case 'day': {
      }
        resetTime = new Date(Math.ceil(now.getTime() / 86400000) * 86400000);
        break;
    }
    }

    return {
      allowed: requestCount < defaultLimit,
      remaining,
      resetTime,
      limit: defaultLimit
    };

  } catch (error) {
    console.error('Rate limit check error:', error);
    // Allow request on error (fail open)
    return {
      allowed: true,
      remaining: 100,
      resetTime: new Date(Date.now() + 60000),
      limit: 100
    };
  }
}

/**
 * Deduct credits from API key
 */
export async function deductCredits(
  supabase: ReturnType<typeof createClient>,
  apiKeyId: string,
  creditsToDeduct: number
): Promise<CreditDeductionResult> {
  try {
    // Get current credits
    const { data: currentKey, error: fetchError } = await supabase
      .from('api_keys')
      .select('credits_allocated')
      .eq('id', apiKeyId)
      .single();

    if (fetchError || !currentKey) {
      return {
        success: false,
        error: 'Failed to fetch current credits'
      };
    }

    const currentCredits = currentKey.credits_allocated;
    
    if (currentCredits < creditsToDeduct) {
      return {
        success: false,
        error: 'Insufficient credits',
        remainingCredits: currentCredits
      };
    }

    // Deduct credits
    const newCredits = currentCredits - creditsToDeduct;
    const { error: updateError } = await supabase
      .from('api_keys')
      .update({ credits_allocated: newCredits })
      .eq('id', apiKeyId);

    if (updateError) {
      return {
        success: false,
        error: 'Failed to deduct credits'
      };
    }

    return {
      success: true,
      remainingCredits: newCredits
    };

  } catch (error) {
    console.error('Credit deduction error:', error);
    return {
      success: false,
      error: 'Credit deduction failed'
    };
  }
}

/**
 * Log API usage for tracking and billing
 */
export async function logApiUsage(
  supabase: ReturnType<typeof createClient>,
  customerId: string,
  apiKeyId: string,
  operationType: string,
  success: boolean,
  creditsUsed: number,
  metadata?: Record<string, unknown>
): Promise<void> {
  try {
    await supabase.from('api_usage_logs').insert({
      customer_id: customerId,
      api_key_id: apiKeyId,
      operation_type: operationType,
      success,
      credits_used: creditsUsed,
      metadata: metadata || {},
      created_at: new Date().toISOString()
    });
  } catch (error) {
    console.error('Failed to log API usage:', error);
    // Don't throw - logging failures shouldn't break the main operation
  }
}

/**
 * Create standardized API response
 */
export function createApiResponse(
  data: unknown,
  status: number = 200,
  success: boolean = true
): Response {
  const response = {
    success,
    data: success ? data : undefined,
    error: success ? undefined : data,
    timestamp: new Date().toISOString()
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders
    }
  });
}

/**
 * Handle CORS preflight requests
 */
export function handleCors(): Response {
  return new Response(null, {
    status: 200,
    headers: corsHeaders
  });
}