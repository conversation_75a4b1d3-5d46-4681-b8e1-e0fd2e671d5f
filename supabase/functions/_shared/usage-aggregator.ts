import type { SupabaseClient } from 'jsr:@supabase/supabase-js@2.57.4';

export interface UsageMetrics {
  customerId: string;
  apiKeyId: string;
  operation: string;
  inputTokens?: number;
  outputTokens?: number;
  model?: string;
  cost: number;
  customerPrice: number;
  creditsUsed: number;
  processingTimeMs: number;
  timestamp: string;
}

export async function aggregateUsage(
  supabaseClient: SupabaseClient,
  metrics: UsageMetrics
): Promise<void> {
  try {
    const { error } = await supabaseClient
      .from('usage_logs')
      .insert({
        customer_id: metrics.customerId,
        api_key_id: metrics.apiKeyId,
        operation: metrics.operation,
        input_tokens: metrics.inputTokens || 0,
        output_tokens: metrics.outputTokens || 0,
        model: metrics.model || 'unknown',
        cost: metrics.cost,
        customer_price: metrics.customerPrice,
        credits_used: metrics.creditsUsed,
        processing_time_ms: metrics.processingTimeMs,
        timestamp: metrics.timestamp
      });

    if (error) {
      console.error('Failed to aggregate usage:', error);
      throw error;
    }
  } catch {
    console.error('Usage aggregation error:', error);
    throw error;
  }
}

export async function getUsageSummary(
  supabaseClient: SupabaseClient,
  customerId: string,
  period: 'day' | 'week' | 'month' = 'day'
): Promise<unknown> {
  try {
    let dateFilter = '';
    const now = new Date();

    switch (period) {
      case 'day': {
      }
        dateFilter = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString();
        break;
    }
      case 'week': {
      }
        dateFilter = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();
        break;
    }
      case 'month': {
      }
        dateFilter = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString();
        break;
    }
    }

    const { data, error } = await supabaseClient
      .from('usage_logs')
      .select('*')
      .eq('customer_id', customerId)
      .gte('timestamp', dateFilter)
      .order('timestamp', { ascending: false });

    if (error) {
      throw error;
    }

    return data;
  } catch {
    console.error('Failed to get usage summary:', error);
    throw error;
  }
}