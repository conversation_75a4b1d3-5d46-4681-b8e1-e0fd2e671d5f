import Ajv, { ValidateFunction, ErrorObject } from 'ajv';
import addFormats from 'ajv-formats';
import { 
  JSONSchema, 
  SchemaValidationResult, 
  ValidationError, 
  ProcessedOutput,
  ValidationConfig,
  ValidationErrorCode,
  // FormatValidator
} from '../../../types/schema.ts';

/**
 * Core JSON Schema Validator
 * Handles validation of agent outputs against defined schemas with comprehensive error reporting
 */
export class SchemaValidator {
  private ajv: Ajv;
  private compiledSchemas = new Map<string, ValidateFunction>();
  private agentSchemas = new Map<string, JSONSchema>(); // For testing - TODO: Remove in production
  private config: ValidationConfig;

  constructor(config: ValidationConfig = {}) {
    this.config = {
      enableCaching: true,
      cacheSize: 1000,
      performanceMode: false,
      strictMode: true,
      bypassValidation: false,
      ...config
    };

    this.ajv = new Ajv({
      allErrors: true,           // Collect all validation errors
      removeAdditional: true,    // Remove properties not in schema
      useDefaults: true,         // Apply default values
      coerceTypes: true,         // Type coercion (string "123" -> number 123)
      verbose: true,             // Include schema and data in errors
      strict: this.config.strictMode
    });

    // Add format validation (date, email, etc.)
    addFormats(this.ajv);

    // Add custom formats for business data
    this.addCustomFormats();
  }

  /**
   * Validate data against a JSON schema
   */
  async validateData(data: any, schema: JSONSchema): Promise<SchemaValidationResult> {
    if (this.config.bypassValidation) {
      return { valid: true, data };
    }

    try {
      const validate = await this.getCompiledValidator(schema);
      const startTime = Date.now();
      
      // Create a deep copy to avoid modifying original data
      const dataCopy = JSON.parse(JSON.stringify(data));
      const valid = validate(dataCopy);
      
      const validationTime = Date.now() - startTime;
      
      // Log performance metrics for monitoring
      if (validationTime > 100) {
        console.warn(`Slow validation detected: ${validationTime}ms for ${JSON.stringify(schema).substring(0, 100)}...`);
      }

      if (valid) {
        return {
          valid: true,
          data: dataCopy // May be modified by removeAdditional/useDefaults
        };
      }

      const errors = this.formatValidationErrors(validate.errors || []);
      return {
        valid: false,
        errors: errors,
        data: dataCopy
      };
    } catch {
      throw new Error(`${ValidationErrorCode.VALIDATION_FAILED}: ${error.message}`);
    }
  }

  /**
   * Validate agent output against agent's defined schema
   */
  async validateAgentOutput(agentId: string, data: any): Promise<SchemaValidationResult> {
    try {
      const schema = await this.getAgentSchema(agentId);
      return this.validateData(data, schema);
    } catch {
      if (error.message.includes('not found')) {
        throw new Error(`${ValidationErrorCode.AGENT_NOT_FOUND}: Agent ${agentId} not found`);
      }
      throw error;
    }
  }

  /**
   * Validate and format output for document processing integration
   */
  async validateAndFormatOutput(agentId: string, rawOutput: any): Promise<ProcessedOutput> {
    try {
      const validation = await this.validateAgentOutput(agentId, rawOutput);

      if (validation.valid) {
        return {
          success: true,
          data: validation.data,
          validation_passed: true
        };
      } else {
        // Return validation errors but don't fail completely
        return {
          success: true,
          data: rawOutput, // Return original data
          validation_passed: false,
          validation_errors: validation.errors,
          warning: 'Output does not match expected schema'
        };
      }
    } catch {
      // Schema validation system error
      console.error('Schema validation error:', error);
      return {
        success: true,
        data: rawOutput,
        validation_passed: false,
        validation_errors: [{
          field: 'system',
          message: 'Schema validation system error',
          value: null,
          expected: 'valid schema',
          severity: 'error' as const
        }]
      };
    }
  }

  /**
   * Get compiled validator with caching
   */
  private async getCompiledValidator(schema: JSONSchema): Promise<ValidateFunction> {
    const schemaKey = this.getSchemaKey(schema);

    if (this.config.enableCaching && this.compiledSchemas.has(schemaKey)) {
      return this.compiledSchemas.get(schemaKey)!;
    }

    try {
      const startTime = Date.now();
      const validate = this.ajv.compile(schema);
      const compilationTime = Date.now() - startTime;
      
      // Log slow compilation for performance monitoring
      if (compilationTime > 50) {
        console.warn(`Slow schema compilation: ${compilationTime}ms`);
      }

      if (this.config.enableCaching) {
        // Cache cleanup if size exceeded
        if (this.compiledSchemas.size >= this.config.cacheSize!) {
          this.cleanupCache();
        }
        this.compiledSchemas.set(schemaKey, validate);
      }

      return validate;
    } catch {
      throw new Error(`${ValidationErrorCode.SCHEMA_COMPILATION_FAILED}: ${error.message}`);
    }
  }

  /**
   * Get agent schema (in production, this would query the database)
   */
  private async getAgentSchema(agentId: string): Promise<JSONSchema> {
    // For testing - use mock data
    if (this.agentSchemas.has(agentId)) {
      return this.agentSchemas.get(agentId)!;
    }

    // In production, this would be:
    // const { data: agent } = await supabase
    //   .from('agents')
    //   .select('output_schema')
    //   .eq('id', agentId)
    //   .single();
    
    throw new Error(`Agent ${agentId} not found`);
  }

  /**
   * Format AJV errors into user-friendly format
   */
  private formatValidationErrors(ajvErrors: ErrorObject[]): ValidationError[] {
    return ajvErrors.map(error => ({
      field: this.getFieldPath(error),
      message: this.getHumanReadableError(error),
      value: error.data,
      expected: this.getExpectedValue(error),
      severity: this.getErrorSeverity(error)
    }));
  }

  /**
   * Extract field path from AJV error
   */
  private getFieldPath(error: ErrorObject): string {
    if (error.instancePath) {
      return error.instancePath.replace(/^\//, '').replace(/\//g, '.');
    }
    
    if (error.keyword === 'required' && error.params?.missingProperty) {
      return error.params.missingProperty;
    }

    return error.schemaPath.split('/').pop() || 'root';
  }

  /**
   * Generate human-readable error messages
   */
  private getHumanReadableError(error: ErrorObject): string {
    const field = this.getFieldPath(error);

    switch (error.keyword) {
      case 'required': {
      }
        return `Required field '${error.params.missingProperty}' is missing`;
      }
      case 'type': {
      }
        return `Field '${field}' must be of type ${error.params.type}`;
      }
      case 'format': {
      }
        return `Field '${field}' must be in ${error.params.format} format`;
      }
      case 'minimum': {
      }
        return `Field '${field}' must be at least ${error.params.limit}`;
      }
      case 'maximum': {
      }
        return `Field '${field}' must be at most ${error.params.limit}`;
      }
      case 'minLength': {
      }
        return `Field '${field}' must be at least ${error.params.limit} characters`;
      }
      case 'maxLength': {
      }
        return `Field '${field}' must be at most ${error.params.limit} characters`;
      }
      case 'pattern': {
      }
        return `Field '${field}' does not match required pattern`;
      }
      case 'enum': {
      }
        return `Field '${field}' must be one of: ${error.params.allowedValues.join(', ')}`;
      }
      case 'minItems': {
      }
        return `Field '${field}' must have at least ${error.params.limit} items`;
      }
      case 'maxItems': {
      }
        return `Field '${field}' must have at most ${error.params.limit} items`;
      }
      case 'additionalProperties': {
      }
        return `Field '${field}' contains unexpected property '${error.params.additionalProperty}'`;
      }
      default:
        return error.message || 'Validation failed';
    }
  }

  /**
   * Get expected value description for error
   */
  private getExpectedValue(error: ErrorObject): string {
    switch (error.keyword) {
      case 'type': {
      }
        return error.params.type;
      }
      case 'format': {
      }
        return `${error.params.format} format`;
      }
      case 'enum': {
      }
        return error.params.allowedValues.join(' | ');
      }
      case 'minimum': {
      }
        return `>= ${error.params.limit}`;
      }
      case 'maximum': {
      }
        return `<= ${error.params.limit}`;
      }
      default:
        return 'valid value';
    }
  }

  /**
   * Determine error severity
   */
  private getErrorSeverity(_error: ErrorObject): 'error' | 'warning' {
    // Most validation failures are errors
    // Could be enhanced to have warnings for certain cases
    return 'error';
  }

  /**
   * Add custom format validators for business data
   */
  private addCustomFormats(): void {
    // Currency format (e.g., "USD", "EUR")
    this.ajv.addFormat('currency', /^[A-Z]{3}$/);

    // Phone number format (international)
    this.ajv.addFormat('phone', /^\+?[\d\s\-()]{10,}$/);

    // Business identifier format
    this.ajv.addFormat('business-id', /^[A-Z0-9-]{5,20}$/);

    // Confidence score (0.0 to 1.0)
    this.ajv.addFormat('confidence', {
      type: 'number',
      validate: (data: number) => {
        return typeof data === 'number' && data >= 0 && data <= 1;
      }
    });

    // Date formats commonly used in business documents
    this.ajv.addFormat('date-mmddyyyy', /^(0[1-9]|1[0-2])\/(0[1-9]|[12]\d|3[01])\/\d{4}$/);
    this.ajv.addFormat('date-ddmmyyyy', /^(0[1-9]|[12]\d|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/);

    // Invoice/Receipt number patterns
    this.ajv.addFormat('invoice-number', /^(INV|INVOICE)[-_]?\d+$/i);
    this.ajv.addFormat('receipt-number', /^(REC|RECEIPT)[-_]?\d+$/i);

    // Tax ID formats (US)
    this.ajv.addFormat('ein', /^\d{2}-\d{7}$/);
    this.ajv.addFormat('ssn', /^\d{3}-\d{2}-\d{4}$/);

    // Credit card number format (basic validation)
    this.ajv.addFormat('credit-card', /^\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}$/);
  }

  /**
   * Generate cache key for schema
   */
  private getSchemaKey(schema: JSONSchema): string {
    return JSON.stringify(schema);
  }

  /**
   * Clean up cache by removing oldest entries
   */
  private cleanupCache(): void {
    // Simple cleanup - remove 20% of entries
    const keysToRemove = Array.from(this.compiledSchemas.keys()).slice(0, Math.floor(this.config.cacheSize! * 0.2));
    keysToRemove.forEach(key => this.compiledSchemas.delete(key));
  }

  /**
   * Set agent schema for testing
   */
  public setAgentSchema(agentId: string, schema: JSONSchema): void {
    this.agentSchemas.set(agentId, schema);
  }

  /**
   * Clear all caches (for testing)
   */
  public clearCaches(): void {
    this.compiledSchemas.clear();
    this.agentSchemas.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats() {
    return {
      compiled_schemas: this.compiledSchemas.size,
      agent_schemas: this.agentSchemas.size
    };
  }
}