// ================================================================================
// SUPABASE CLIENT CONFIGURATION
// ================================================================================

import { createClient } from "jsr:@supabase/supabase-js@2.57.4";

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

// Create Supabase client with service role key for backend operations
export const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);