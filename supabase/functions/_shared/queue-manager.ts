/**
 * Queue Manager for Document Processing
 * Handles asynchronous processing of large documents
 */

import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';

export interface QueueJob {
  customerId: string;
  apiKeyId: string;
  documentId: string;
  agentId?: string;
  jobType: 'document_processing' | 'batch_processing' | 'webhook_delivery';
  jobData: Record<string, unknown>;
  webhookUrl?: string;
  maxRetries?: number;
  priority?: 'low' | 'medium' | 'high';
}

export interface EnqueueResult {
  success: boolean;
  jobId?: string;
  error?: string;
}

export interface QueueMetrics {
  totalQueued: number;
  totalProcessing: number;
  totalCompleted: number;
  totalFailed: number;
  avgProcessingTimeSeconds: number;
  oldestQueuedJob?: Date;
}

export interface QueueMetricsResult {
  success: boolean;
  metrics?: QueueMetrics;
  error?: string;
}

/**
 * Queue Manager for handling asynchronous document processing
 */
export class QueueManager {
  private supabase: ReturnType<typeof createClient>;

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
  }

  /**
   * Determine if a document should be queued based on size and complexity
   */
  static shouldQueue(
    fileSize: number, 
    priority: 'low' | 'medium' | 'high' = 'medium'
  ): boolean {
    // Queue thresholds based on file size and priority
    const queueThresholds = {
      low: 5 * 1024 * 1024,    // 5MB
      medium: 10 * 1024 * 1024, // 10MB  
      high: 25 * 1024 * 1024    // 25MB
    };

    return fileSize > queueThresholds[priority];
  }

  /**
   * Enqueue a job for processing
   */
  async enqueueJob(job: QueueJob): Promise<EnqueueResult> {
    try {
      const jobId = crypto.randomUUID();
      const priority = this.calculatePriority(job);

      const { error } = await this.supabase.from('processing_queue').insert({
        id: jobId,
        customer_id: job.customerId,
        api_key_id: job.apiKeyId,
        document_id: job.documentId,
        agent_id: job.agentId,
        job_type: job.jobType,
        job_data: job.jobData,
        webhook_url: job.webhookUrl,
        max_retries: job.maxRetries || 3,
        retry_count: 0,
        priority,
        status: 'queued',
        created_at: new Date().toISOString(),
        scheduled_for: new Date().toISOString() // Process immediately
      });

      if (error) {
        console.error('Failed to enqueue job:', error);
        return {
          success: false,
          error: 'Failed to enqueue job'
        };
      }

      console.log(`Job ${jobId} enqueued successfully for document ${job.documentId}`);

      return {
        success: true,
        jobId
      };

    } catch {
      console.error('Enqueue job error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get queue metrics for monitoring and estimation
   */
  async getQueueMetrics(): Promise<QueueMetricsResult> {
    try {
      // Get queue status counts
      const { data: queueStats, error: statsError } = await this.supabase
        .from('processing_queue')
        .select('status, created_at, started_at, completed_at')
        .order('created_at', { ascending: false });

      if (statsError) {
        console.error('Failed to fetch queue stats:', statsError);
        return {
          success: false,
          error: 'Failed to fetch queue metrics'
        };
      }

      if (!queueStats || queueStats.length === 0) {
        return {
          success: true,
          metrics: {
            totalQueued: 0,
            totalProcessing: 0,
            totalCompleted: 0,
            totalFailed: 0,
            avgProcessingTimeSeconds: 60
          }
        };
      }

      // Calculate metrics
      const totalQueued = queueStats.filter(job => job.status === 'queued').length;
      const totalProcessing = queueStats.filter(job => job.status === 'processing').length;
      const totalCompleted = queueStats.filter(job => job.status === 'completed').length;
      const totalFailed = queueStats.filter(job => job.status === 'failed').length;

      // Calculate average processing time from completed jobs
      const completedJobs = queueStats.filter(job => 
        job.status === 'completed' && job.started_at && job.completed_at
      );

      let avgProcessingTimeSeconds = 60; // Default fallback
      if (completedJobs.length > 0) {
        const totalProcessingTime = completedJobs.reduce((total, job) => {
          const startTime = new Date(job.started_at!).getTime();
          const endTime = new Date(job.completed_at!).getTime();
          return total + (endTime - startTime);
        }, 0);
        
        avgProcessingTimeSeconds = Math.ceil(totalProcessingTime / completedJobs.length / 1000);
      }

      // Find oldest queued job
      const queuedJobs = queueStats.filter(job => job.status === 'queued');
      const oldestQueuedJob = queuedJobs.length > 0 
        ? new Date(queuedJobs[queuedJobs.length - 1].created_at)
        : undefined;

      return {
        success: true,
        metrics: {
          totalQueued,
          totalProcessing,
          totalCompleted,
          totalFailed,
          avgProcessingTimeSeconds,
          oldestQueuedJob
        }
      };

    } catch {
      console.error('Queue metrics error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get job status by ID
   */
  async getJobStatus(jobId: string): Promise<{
    success: boolean;
    status?: 'queued' | 'processing' | 'completed' | 'failed';
    progress?: number;
    error?: string;
    result?: Record<string, unknown>;
  }> {
    try {
      const { data: job, error } = await this.supabase
        .from('processing_queue')
        .select('status, progress, error_message, result_data')
        .eq('id', jobId)
        .single();

      if (error || !job) {
        return {
          success: false,
          error: 'Job not found'
        };
      }

      return {
        success: true,
        status: job.status as 'queued' | 'processing' | 'completed' | 'failed',
        progress: job.progress || 0,
        error: job.error_message || undefined,
        result: job.result_data || undefined
      };

    } catch {
      console.error('Get job status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Update job progress
   */
  async updateJobProgress(
    jobId: string,
    progress: number,
    status?: 'processing' | 'completed' | 'failed',
    errorMessage?: string,
    resultData?: Record<string, unknown>
  ): Promise<boolean> {
    try {
      const updates: Record<string, unknown> = {
        progress,
        updated_at: new Date().toISOString()
      };

      if (status) {
        updates.status = status;
        
        if (status === 'processing' && !updates.started_at) {
          updates.started_at = new Date().toISOString();
        }
        
        if (status === 'completed' || status === 'failed') {
          updates.completed_at = new Date().toISOString();
        }
      }

      if (errorMessage) {
        updates.error_message = errorMessage;
      }

      if (resultData) {
        updates.result_data = resultData;
      }

      const { error } = await this.supabase
        .from('processing_queue')
        .update(updates)
        .eq('id', jobId);

      if (error) {
        console.error('Failed to update job progress:', error);
        return false;
      }

      return true;

    } catch {
      console.error('Update job progress error:', error);
      return false;
    }
  }

  /**
   * Calculate job priority based on customer tier, file size, and urgency
   */
  private calculatePriority(job: QueueJob): number {
    let priority = 5; // Default priority

    // Adjust based on job type
    if (job.jobType === 'webhook_delivery') {
      priority += 2; // Higher priority for webhooks
    }

    // Adjust based on explicit priority
    if (job.priority) {
      switch (job.priority) {
        case 'high': {
      }
          priority += 3;
          break;
    }
        case 'medium': {
      }
          priority += 1;
          break;
    }
        case 'low': {
      }
          priority -= 1;
          break;
    }
      }
    }

    // Adjust based on file size (smaller files get higher priority)
    const fileSize = (job.jobData.fileSize as number) || 0;
    if (fileSize < 1024 * 1024) { // < 1MB
      priority += 1;
    } else if (fileSize > 10 * 1024 * 1024) { // > 10MB
      priority -= 1;
    }

    return Math.max(1, Math.min(10, priority)); // Clamp between 1-10
  }

  /**
   * Clean up old completed and failed jobs
   */
  async cleanupOldJobs(olderThanDays: number = 7): Promise<boolean> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const { error } = await this.supabase
        .from('processing_queue')
        .delete()
        .in('status', ['completed', 'failed'])
        .lt('completed_at', cutoffDate.toISOString());

      if (error) {
        console.error('Failed to cleanup old jobs:', error);
        return false;
      }

      return true;

    } catch {
      console.error('Cleanup old jobs error:', error);
      return false;
    }
  }
}