export interface CreditResult {
  success: boolean;
  remainingCredits: number;
  error?: string;
}

export interface CreditDeduction {
  apiKeyId: string;
  creditsToDeduct: number;
  operation: string;
  metadata?: Record<string, any>;
}

export async function checkCreditsAvailable(
  supabaseClient: any,
  apiKeyId: string,
  requiredCredits: number
): Promise<CreditResult> {
  try {
    const { data: apiKey, error } = await supabaseClient
      .from('api_keys')
      .select('credits_remaining, credits_limit')
      .eq('id', apiKeyId)
      .single();

    if (error) {
      return {
        success: false,
        remainingCredits: 0,
        error: `Failed to fetch API key credits: ${error.message}`
      };
    }

    if (!apiKey) {
      return {
        success: false,
        remainingCredits: 0,
        error: 'API key not found'
      };
    }

    const available = apiKey.credits_remaining;
    
    if (available < requiredCredits) {
      return {
        success: false,
        remainingCredits: available,
        error: `Insufficient credits. Required: ${requiredCredits}, Available: ${available}`
      };
    }

    return {
      success: true,
      remainingCredits: available
    };
  } catch {
    return {
      success: false,
      remainingCredits: 0,
      error: `Credit check failed: ${error.message}`
    };
  }
}

export async function deductCredits(
  supabaseClient: any,
  deduction: CreditDeduction
): Promise<CreditResult> {
  try {
    // First check if credits are available
    const creditCheck = await checkCreditsAvailable(
      supabaseClient, 
      deduction.apiKeyId, 
      deduction.creditsToDeduct
    );

    if (!creditCheck.success) {
      return creditCheck;
    }

    // Deduct credits atomically
    const { data, error } = await supabaseClient
      .from('api_keys')
      .update({
        credits_remaining: creditCheck.remainingCredits - deduction.creditsToDeduct,
        last_used_at: new Date().toISOString()
      })
      .eq('id', deduction.apiKeyId)
      .select('credits_remaining')
      .single();

    if (error) {
      return {
        success: false,
        remainingCredits: creditCheck.remainingCredits,
        error: `Failed to deduct credits: ${error.message}`
      };
    }

    return {
      success: true,
      remainingCredits: data.credits_remaining
    };

  } catch {
    return {
      success: false,
      remainingCredits: 0,
      error: `Credit deduction failed: ${error.message}`
    };
  }
}

export async function addCredits(
  supabaseClient: any,
  apiKeyId: string,
  creditsToAdd: number,
  _reason: string = 'manual_addition'
): Promise<CreditResult> {
  try {
    const { data, error } = await supabaseClient
      .from('api_keys')
      .update({
        credits_remaining: supabaseClient.raw(`credits_remaining + ${creditsToAdd}`)
      })
      .eq('id', apiKeyId)
      .select('credits_remaining')
      .single();

    if (error) {
      return {
        success: false,
        remainingCredits: 0,
        error: `Failed to add credits: ${error.message}`
      };
    }

    return {
      success: true,
      remainingCredits: data.credits_remaining
    };

  } catch {
    return {
      success: false,
      remainingCredits: 0,
      error: `Credit addition failed: ${error.message}`
    };
  }
}

export async function getCreditBalance(
  supabaseClient: any,
  apiKeyId: string
): Promise<CreditResult> {
  try {
    const { data, error } = await supabaseClient
      .from('api_keys')
      .select('credits_remaining, credits_limit')
      .eq('id', apiKeyId)
      .single();

    if (error) {
      return {
        success: false,
        remainingCredits: 0,
        error: `Failed to get credit balance: ${error.message}`
      };
    }

    return {
      success: true,
      remainingCredits: data.credits_remaining
    };

  } catch {
    return {
      success: false,
      remainingCredits: 0,
      error: `Get credit balance failed: ${error.message}`
    };
  }
}