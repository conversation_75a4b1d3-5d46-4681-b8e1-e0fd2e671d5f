// ================================================================================
// COMPREHENSIVE AUDIT LOGGING SYSTEM
// ================================================================================

import { supabase } from "./supabase.ts";

/**
 * Event categories for audit logging
 */
export type AuditEventCategory = 
  | 'api_call'
  | 'authentication'
  | 'authorization'
  | 'data_access'
  | 'data_modification'
  | 'admin_action'
  | 'security'
  | 'compliance'
  | 'system'
  | 'monitoring';

/**
 * Audit event status
 */
export type AuditEventStatus = 'success' | 'error' | 'warning' | 'info';

/**
 * Comprehensive audit event interface
 */
export interface AuditEvent {
  customer_id?: string;
  api_key_id?: string;
  action: string;
  event_category: AuditEventCategory;
  status: AuditEventStatus;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  correlation_id: string;
  session_id?: string;
  processing_time_ms?: number;
  resource_id?: string;
  resource_type?: string;
  before_state?: Record<string, any>;
  after_state?: Record<string, any>;
  compliance_tags?: string[];
}

/**
 * Enhanced audit logger with compliance features
 */
export class AuditLogger {
  /**
   * Log a comprehensive audit event
   */
  static async log(event: AuditEvent): Promise<void> {
    try {
      // Validate required fields
      if (!event.action || !event.event_category || !event.correlation_id) {
        console.error('Invalid audit event: missing required fields', event);
        return;
      }
      
      // Sanitize sensitive data in details
      const sanitizedDetails = this.sanitizeAuditData(event.details);
      const sanitizedBefore = event.before_state ? this.sanitizeAuditData(event.before_state) : undefined;
      const sanitizedAfter = event.after_state ? this.sanitizeAuditData(event.after_state) : undefined;
      
      // Insert audit log
      const { error } = await supabase.from('audit_logs').insert({
        customer_id: event.customer_id,
        api_key_id: event.api_key_id,
        action: event.action,
        event_category: event.event_category,
        status: event.status,
        details: sanitizedDetails,
        ip_address: event.ip_address || 'unknown',
        user_agent: event.user_agent || 'unknown',
        correlation_id: event.correlation_id,
        session_id: event.session_id,
        processing_time_ms: event.processing_time_ms,
        created_at: new Date().toISOString(),
      });
      
      if (error) {
        console.error('Failed to insert audit log:', error);
        return;
      }
      
      // Log compliance events separately if needed
      if (event.compliance_tags && event.compliance_tags.length > 0) {
        await this.logComplianceEvent(event);
      }
      
      // Check for high-priority events that need immediate attention
      if (this.isHighPriorityEvent(event)) {
        await this.handleHighPriorityEvent(event);
      }
      
    } catch (error) {
      // Never let audit logging break the main flow
      console.error('Audit logging failed:', error);
    }
  }
  
  /**
   * Log API call with performance metrics
   */
  static async logApiCall(
    request: Request,
    response: Response,
    processingTime: number,
    customerId?: string,
    apiKeyId?: string,
    additionalDetails?: Record<string, any>
  ): Promise<void> {
    const url = new URL(request.url);
    const correlationId = request.headers.get('x-correlation-id') || crypto.randomUUID();
    
    await this.log({
      customer_id: customerId,
      api_key_id: apiKeyId,
      action: `${request.method} ${url.pathname}`,
      event_category: 'api_call',
      status: response.status < 400 ? 'success' : 'error',
      details: {
        method: request.method,
        path: url.pathname,
        query_params: Object.fromEntries(url.searchParams.entries()),
        status_code: response.status,
        content_length: response.headers.get('content-length'),
        ...additionalDetails,
      },
      ip_address: request.headers.get('x-forwarded-for') || 
                  request.headers.get('cf-connecting-ip') || 
                  'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      correlation_id: correlationId,
      processing_time_ms: processingTime,
    });
  }
  
  /**
   * Log authentication events
   */
  static async logAuthentication(
    action: 'login' | 'logout' | 'key_validation' | 'key_creation' | 'key_revocation',
    status: AuditEventStatus,
    details: Record<string, any>,
    request: Request,
    customerId?: string,
    apiKeyId?: string
  ): Promise<void> {
    await this.log({
      customer_id: customerId,
      api_key_id: apiKeyId,
      action: `auth_${action}`,
      event_category: 'authentication',
      status,
      details,
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      correlation_id: request.headers.get('x-correlation-id') || crypto.randomUUID(),
      compliance_tags: ['authentication', 'security'],
    });
  }
  
  /**
   * Log data access events for GDPR compliance
   */
  static async logDataAccess(
    action: string,
    resourceType: string,
    resourceId: string,
    customerId: string,
    details: Record<string, any>,
    request: Request
  ): Promise<void> {
    await this.log({
      customer_id: customerId,
      action,
      event_category: 'data_access',
      status: 'success',
      details,
      resource_type: resourceType,
      resource_id: resourceId,
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      correlation_id: request.headers.get('x-correlation-id') || crypto.randomUUID(),
      compliance_tags: ['gdpr', 'data_access'],
    });
  }
  
  /**
   * Log data modification events with before/after states
   */
  static async logDataModification(
    action: string,
    resourceType: string,
    resourceId: string,
    customerId: string,
    beforeState: Record<string, any>,
    afterState: Record<string, any>,
    request: Request
  ): Promise<void> {
    await this.log({
      customer_id: customerId,
      action,
      event_category: 'data_modification',
      status: 'success',
      details: {
        resource_type: resourceType,
        resource_id: resourceId,
        modification_type: action,
      },
      resource_type: resourceType,
      resource_id: resourceId,
      before_state: beforeState,
      after_state: afterState,
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      correlation_id: request.headers.get('x-correlation-id') || crypto.randomUUID(),
      compliance_tags: ['gdpr', 'data_modification'],
    });
  }
  
  /**
   * Log admin actions with enhanced tracking
   */
  static async logAdminAction(
    action: string,
    adminId: string,
    targetCustomerId: string | undefined,
    details: Record<string, any>,
    request: Request
  ): Promise<void> {
    await this.log({
      customer_id: targetCustomerId,
      action: `admin_${action}`,
      event_category: 'admin_action',
      status: 'success',
      details: {
        admin_id: adminId,
        target_customer_id: targetCustomerId,
        ...details,
      },
      ip_address: request.headers.get('x-forwarded-for') || 'unknown',
      user_agent: request.headers.get('user-agent') || 'unknown',
      correlation_id: request.headers.get('x-correlation-id') || crypto.randomUUID(),
      compliance_tags: ['admin', 'privileged_access'],
    });
  }
  
  /**
   * Sanitize sensitive data for audit logging
   */
  private static sanitizeAuditData(data: Record<string, any>): Record<string, any> {
    const sensitiveFields = [
      'password', 'token', 'key', 'secret', 'auth', 'credential',
      'api_key', 'access_token', 'refresh_token', 'jwt', 'bearer',
      'private_key', 'ssl_cert', 'ssl_key', 'encryption_key',
    ];
    
    const sanitized = { ...data };
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]';
      }
    }
    
    // Recursively sanitize nested objects
    for (const [key, value] of Object.entries(sanitized)) {
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        sanitized[key] = this.sanitizeAuditData(value);
      }
      // Sanitize arrays of objects
      else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'object' && item !== null ? this.sanitizeAuditData(item) : item
        );
      }
    }
    
    return sanitized;
  }
  
  /**
   * Log compliance events to separate compliance table
   */
  private static async logComplianceEvent(event: AuditEvent): Promise<void> {
    try {
      const complianceTypes = event.compliance_tags?.filter(tag => 
        ['gdpr', 'ccpa', 'hipaa', 'sox', 'pci'].includes(tag)
      ) || [];
      
      for (const complianceType of complianceTypes) {
        await supabase.from('compliance_events').insert({
          compliance_type: complianceType,
          event_type: event.action,
          customer_id: event.customer_id,
          data_subject_id: event.customer_id, // In most cases, customer is the data subject
          legal_basis: this.determineLegalBasis(event.action),
          data_categories: this.extractDataCategories(event.details),
          purpose: this.determinePurpose(event.action),
          automated_decision: this.isAutomatedDecision(event.action),
          audit_trail: {
            correlation_id: event.correlation_id,
            ip_address: event.ip_address,
            user_agent: event.user_agent,
            timestamp: new Date().toISOString(),
          },
          created_at: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Failed to log compliance event:', error);
    }
  }
  
  /**
   * Check if event requires immediate attention
   */
  private static isHighPriorityEvent(event: AuditEvent): boolean {
    const highPriorityActions = [
      'security_breach',
      'unauthorized_access',
      'data_export',
      'admin_privilege_escalation',
      'system_configuration_change',
      'bulk_data_deletion',
    ];
    
    return event.event_category === 'security' ||
           event.status === 'error' ||
           highPriorityActions.some(action => event.action.includes(action));
  }
  
  /**
   * Handle high-priority events with immediate alerts
   */
  private static async handleHighPriorityEvent(event: AuditEvent): Promise<void> {
    try {
      // Log to security events table
      if (event.event_category === 'security') {
        await supabase.from('security_events').insert({
          event_type: event.action.includes('injection') ? 'prompt_injection_attempt' : 'unauthorized_access',
          customer_id: event.customer_id,
          api_key_id: event.api_key_id,
          threat_details: event.details,
          ip_address: event.ip_address,
          user_agent: event.user_agent,
          severity: 'high',
          correlation_id: event.correlation_id,
          created_at: new Date().toISOString(),
        });
      }
      
      // Send alert webhook if configured
      const alertWebhookUrl = Deno.env.get('AUDIT_ALERT_WEBHOOK_URL');
      if (alertWebhookUrl) {
        await fetch(alertWebhookUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'high_priority_audit_event',
            event,
            timestamp: new Date().toISOString(),
          }),
        });
      }
    } catch (error) {
      console.error('Failed to handle high-priority event:', error);
    }
  }
  
  /**
   * Determine legal basis for GDPR compliance
   */
  private static determineLegalBasis(action: string): string {
    if (action.includes('consent')) return 'consent';
    if (action.includes('contract') || action.includes('billing')) return 'contract';
    if (action.includes('legal') || action.includes('compliance')) return 'legal_obligation';
    if (action.includes('security') || action.includes('fraud')) return 'vital_interests';
    return 'legitimate_interests';
  }
  
  /**
   * Extract data categories from event details
   */
  private static extractDataCategories(details: Record<string, any>): string[] {
    const categories: string[] = [];
    
    if (details.email || details.contact_email) categories.push('contact_data');
    if (details.payment || details.billing) categories.push('financial_data');
    if (details.document || details.file) categories.push('content_data');
    if (details.usage || details.metrics) categories.push('usage_data');
    if (details.ip_address || details.location) categories.push('technical_data');
    
    return categories.length > 0 ? categories : ['general_data'];
  }
  
  /**
   * Determine purpose for data processing
   */
  private static determinePurpose(action: string): string {
    if (action.includes('billing') || action.includes('payment')) return 'billing_and_payments';
    if (action.includes('security') || action.includes('auth')) return 'security_and_authentication';
    if (action.includes('analytics') || action.includes('metrics')) return 'analytics_and_improvement';
    if (action.includes('support') || action.includes('admin')) return 'customer_support';
    return 'service_provision';
  }
  
  /**
   * Check if action involves automated decision making
   */
  private static isAutomatedDecision(action: string): boolean {
    const automatedActions = [
      'ai_processing',
      'auto_classification',
      'fraud_detection',
      'risk_assessment',
      'auto_approval',
      'auto_rejection',
    ];
    
    return automatedActions.some(automated => action.includes(automated));
  }
}

/**
 * Compliance report generator
 */
export interface ComplianceReport {
  report_type: 'gdpr' | 'audit' | 'security' | 'data_retention';
  period: { start: string; end: string };
  total_events: number;
  events_by_category: Record<string, number>;
  events_by_status: Record<string, number>;
  security_incidents: any[];
  data_access_events: any[];
  compliance_violations: any[];
  recommendations: string[];
}

/**
 * Generate comprehensive compliance reports
 */
export async function generateComplianceReport(
  startDate: string,
  endDate: string,
  reportType: 'gdpr' | 'audit' | 'security' | 'data_retention',
  customerId?: string
): Promise<ComplianceReport> {
  try {
    let query = supabase
      .from('audit_logs')
      .select('*')
      .gte('created_at', startDate)
      .lte('created_at', endDate);
    
    if (customerId) {
      query = query.eq('customer_id', customerId);
    }
    
    if (reportType === 'security') {
      query = query.eq('event_category', 'security');
    }
    
    const { data: events, error } = await query;
    
    if (error) {
      throw new Error(`Failed to fetch audit data: ${error.message}`);
    }
    
    // Analyze events
    const eventsByCategory = groupEventsByCategory(events || []);
    const eventsByStatus = groupEventsByStatus(events || []);
    const securityIncidents = filterSecurityIncidents(events || []);
    const dataAccessEvents = filterDataAccessEvents(events || []);
    const complianceViolations = detectComplianceViolations(events || []);
    const recommendations = generateRecommendations(events || [], reportType);
    
    return {
      report_type: reportType,
      period: { start: startDate, end: endDate },
      total_events: events?.length || 0,
      events_by_category: eventsByCategory,
      events_by_status: eventsByStatus,
      security_incidents: securityIncidents,
      data_access_events: dataAccessEvents,
      compliance_violations: complianceViolations,
      recommendations,
    };
  } catch (error) {
    console.error('Failed to generate compliance report:', error);
    throw error;
  }
}

/**
 * Helper functions for compliance reporting
 */
function groupEventsByCategory(events: any[]): Record<string, number> {
  return events.reduce((acc, event) => {
    acc[event.event_category] = (acc[event.event_category] || 0) + 1;
    return acc;
  }, {});
}

function groupEventsByStatus(events: any[]): Record<string, number> {
  return events.reduce((acc, event) => {
    acc[event.status] = (acc[event.status] || 0) + 1;
    return acc;
  }, {});
}

function filterSecurityIncidents(events: any[]): any[] {
  return events.filter(event => 
    event.event_category === 'security' || 
    event.status === 'error' && event.details?.security_related
  );
}

function filterDataAccessEvents(events: any[]): any[] {
  return events.filter(event => 
    event.event_category === 'data_access' ||
    event.action.includes('export') ||
    event.action.includes('download')
  );
}

function detectComplianceViolations(events: any[]): any[] {
  // Detect potential compliance violations
  const violations: any[] = [];
  
  // Check for excessive data access
  const dataAccessCount = events.filter(e => e.event_category === 'data_access').length;
  if (dataAccessCount > 1000) {
    violations.push({
      type: 'excessive_data_access',
      severity: 'medium',
      description: `Unusually high number of data access events: ${dataAccessCount}`,
      recommendation: 'Review data access patterns and implement additional monitoring',
    });
  }
  
  // Check for failed security events
  const failedSecurityEvents = events.filter(e => 
    e.event_category === 'security' && e.status === 'error'
  ).length;
  
  if (failedSecurityEvents > 10) {
    violations.push({
      type: 'security_failures',
      severity: 'high',
      description: `High number of failed security events: ${failedSecurityEvents}`,
      recommendation: 'Investigate security failures and strengthen protection measures',
    });
  }
  
  return violations;
}

function generateRecommendations(events: any[], reportType: string): string[] {
  const recommendations: string[] = [];
  
  const errorRate = events.filter(e => e.status === 'error').length / Math.max(events.length, 1);
  
  if (errorRate > 0.1) {
    recommendations.push('High error rate detected. Review system stability and error handling.');
  }
  
  if (reportType === 'security') {
    const securityEvents = events.filter(e => e.event_category === 'security');
    if (securityEvents.length > 0) {
      recommendations.push('Security events detected. Review and enhance security measures.');
    }
  }
  
  if (reportType === 'gdpr') {
    const dataAccessEvents = events.filter(e => e.event_category === 'data_access');
    if (dataAccessEvents.length > 100) {
      recommendations.push('High volume of data access. Ensure proper consent and legal basis.');
    }
  }
  
  return recommendations;
}