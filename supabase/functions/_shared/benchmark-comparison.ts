/**
 * Benchmark Comparison Utility
 * 
 * Advanced benchmarking and comparison utilities for agent performance
 * Provides statistical comparisons, competitive analysis, and optimization insights
 */

import type { SupabaseClient } from 'jsr:@supabase/supabase-js@2.57.4';
import type { Database } from '../../../types/database.types.ts';
import type {
  AgentMetrics,
  BenchmarkReport
} from '../../../types/agent-performance.types.ts';

export interface ComprehensiveBenchmark {
  comparison_id: string;
  generated_at: Date;
  primary_agent: AgentBenchmarkData;
  comparison_agents: AgentBenchmarkData[];
  statistical_analysis: StatisticalComparison;
  performance_ranking: PerformanceRanking;
  recommendations: BenchmarkRecommendation[];
  confidence_score: number;
}

export interface AgentBenchmarkData {
  agent_id: string;
  agent_name: string;
  agent_type: 'default' | 'custom';
  category: string;
  metrics: AgentMetrics;
  sample_size: number;
  data_quality_score: number;
}

export interface StatisticalComparison {
  processing_time: StatisticalTest;
  accuracy: StatisticalTest;
  success_rate: StatisticalTest;
  cost_efficiency: StatisticalTest;
}

export interface StatisticalTest {
  metric_name: string;
  primary_value: number;
  comparison_values: number[];
  statistical_significance: boolean;
  p_value: number;
  effect_size: number;
  confidence_interval: { lower: number; upper: number };
  interpretation: string;
}

export interface PerformanceRanking {
  overall_rank: number;
  total_agents: number;
  category_rank: number;
  category_total: number;
  strengths: string[];
  weaknesses: string[];
  percentile: number;
}

export interface BenchmarkRecommendation {
  type: 'improvement' | 'optimization' | 'migration' | 'configuration';
  priority: 'critical' | 'high' | 'medium' | 'low';
  title: string;
  description: string;
  target_metric: string;
  expected_improvement: string;
  implementation_steps: string[];
  effort_estimate: string;
  risk_level: 'low' | 'medium' | 'high';
}

/**
 * Advanced benchmark comparison system
 */
export class BenchmarkComparison {
  constructor(private supabase: SupabaseClient<Database>) {}

  /**
   * Perform comprehensive benchmark analysis for an agent
   */
  async performComprehensiveBenchmark(
    agentId: string,
    timeframe: 'week' | 'month' | 'quarter' = 'month',
    includeAllCategories: boolean = false
  ): Promise<ComprehensiveBenchmark> {
    const primaryAgent = await this.getAgentBenchmarkData(agentId, timeframe);
    
    if (!primaryAgent) {
      throw new Error(`Agent ${agentId} not found or has insufficient data`);
    }

    // Get comparison agents
    const comparisonAgents = await this.getComparisonAgents(
      primaryAgent,
      timeframe,
      includeAllCategories
    );

    // Perform statistical analysis
    const statisticalAnalysis = this.performStatisticalAnalysis(
      primaryAgent,
      comparisonAgents
    );

    // Calculate performance ranking
    const performanceRanking = this.calculatePerformanceRanking(
      primaryAgent,
      comparisonAgents
    );

    // Generate recommendations
    const recommendations = this.generateBenchmarkRecommendations(
      primaryAgent,
      comparisonAgents,
      statisticalAnalysis,
      performanceRanking
    );

    // Calculate overall confidence score
    const confidenceScore = this.calculateConfidenceScore(
      primaryAgent,
      comparisonAgents,
      statisticalAnalysis
    );

    return {
      comparison_id: `benchmark-${agentId}-${Date.now()}`,
      generated_at: new Date(),
      primary_agent: primaryAgent,
      comparison_agents: comparisonAgents,
      statistical_analysis: statisticalAnalysis,
      performance_ranking: performanceRanking,
      recommendations: recommendations,
      confidence_score: confidenceScore
    };
  }

  /**
   * Get benchmark data for a specific agent
   */
  private async getAgentBenchmarkData(
    agentId: string,
    timeframe: string
  ): Promise<AgentBenchmarkData | null> {
    const timeframeDays = { week: 7, month: 30, quarter: 90 }[timeframe];
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    // Get agent info
    const { data: agent, error: agentError } = await this.supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) return null;

    // Get performance metrics
    const { data: metrics, error: metricsError } = await this.supabase
      .from('agent_performance_logs')
      .select('*')
      .eq('agent_id', agentId)
      .gte('timestamp', startDate.toISOString());

    if (metricsError || !metrics || metrics.length === 0) return null;

    // Calculate aggregated metrics
    const agentMetrics = this.calculateAgentMetrics(metrics);
    const dataQualityScore = this.calculateDataQualityScore(metrics);

    return {
      agent_id: agentId,
      agent_name: agent.name,
      agent_type: agent.is_default ? 'default' : 'custom',
      category: agent.category,
      metrics: agentMetrics,
      sample_size: metrics.length,
      data_quality_score: dataQualityScore
    };
  }

  /**
   * Get relevant comparison agents
   */
  private async getComparisonAgents(
    primaryAgent: AgentBenchmarkData,
    timeframe: string,
    includeAllCategories: boolean
  ): Promise<AgentBenchmarkData[]> {
    const timeframeDays = { week: 7, month: 30, quarter: 90 }[timeframe];
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    // Build query for comparison agents
    let query = this.supabase
      .from('agents')
      .select('*')
      .neq('id', primaryAgent.agent_id);

    if (!includeAllCategories) {
      query = query.eq('category', primaryAgent.category);
    }

    const { data: agents, error } = await query;

    if (error || !agents) return [];

    // Get performance data for each agent
    const comparisonAgents: AgentBenchmarkData[] = [];

    for (const agent of agents) {
      const { data: metrics } = await this.supabase
        .from('agent_performance_logs')
        .select('*')
        .eq('agent_id', agent.id)
        .gte('timestamp', startDate.toISOString());

      if (metrics && metrics.length >= 10) { // Minimum sample size
        const agentMetrics = this.calculateAgentMetrics(metrics);
        const dataQualityScore = this.calculateDataQualityScore(metrics);

        comparisonAgents.push({
          agent_id: agent.id,
          agent_name: agent.name,
          agent_type: agent.is_default ? 'default' : 'custom',
          category: agent.category,
          metrics: agentMetrics,
          sample_size: metrics.length,
          data_quality_score: dataQualityScore
        });
      }
    }

    return comparisonAgents;
  }

  /**
   * Calculate agent metrics from raw data
   */
  private calculateAgentMetrics(rawMetrics: any[]): AgentMetrics {
    const successful = rawMetrics.filter(m => m.success);
    const failed = rawMetrics.filter(m => !m.success);

    return {
      requestCount: rawMetrics.length,
      successRate: successful.length / rawMetrics.length,
      avgProcessingTime: rawMetrics.reduce((sum, m) => sum + m.processing_time_ms, 0) / rawMetrics.length,
      avgAccuracy: rawMetrics.reduce((sum, m) => sum + (m.accuracy_score || 0), 0) / rawMetrics.length,
      avgConfidence: rawMetrics.reduce((sum, m) => sum + (m.confidence_score || 0), 0) / rawMetrics.length,
      avgCost: rawMetrics.reduce((sum, m) => sum + m.cost_usd, 0) / rawMetrics.length,
      errorTypes: this.analyzeErrorTypes(failed)
    };
  }

  /**
   * Analyze error types from failed requests
   */
  private analyzeErrorTypes(failedMetrics: any[]): any[] {
    if (failedMetrics.length === 0) return [];

    const errorCounts: Record<string, number> = {};
    failedMetrics.forEach((_metric) => {
      const errorType = _metric.error_type || 'unknown';
      errorCounts[errorType] = (errorCounts[errorType] || 0) + 1;
    });

    return Object.entries(errorCounts).map(([error_type, count]) => ({
      error_type,
      count,
      percentage: parseFloat((count / failedMetrics.length * 100).toFixed(1)),
      recent_increase: false // Would need trend analysis
    }));
  }

  /**
   * Calculate data quality score
   */
  private calculateDataQualityScore(metrics: any[]): number {
    let score = 100;

    // Sample size factor
    if (metrics.length < 50) score -= 20;
    else if (metrics.length < 100) score -= 10;

    // Data completeness
    const nullAccuracy = metrics.filter(m => m.accuracy_score === null).length;
    const nullConfidence = metrics.filter(m => m.confidence_score === null).length;
    
    score -= (nullAccuracy / metrics.length) * 20;
    score -= (nullConfidence / metrics.length) * 10;

    // Data distribution (avoid outliers skewing results)
    const processingTimes = metrics.map(m => m.processing_time_ms);
    const median = this.calculateMedian(processingTimes);
    const outliers = processingTimes.filter(t => t > median * 5).length;
    
    score -= (outliers / metrics.length) * 15;

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Perform statistical analysis comparing agents
   */
  private performStatisticalAnalysis(
    primaryAgent: AgentBenchmarkData,
    comparisonAgents: AgentBenchmarkData[]
  ): StatisticalComparison {
    return {
      processing_time: this.performTTest(
        'processing_time',
        primaryAgent.metrics.avgProcessingTime,
        comparisonAgents.map(a => a.metrics.avgProcessingTime),
        'lower_better'
      ),
      accuracy: this.performTTest(
        'accuracy',
        primaryAgent.metrics.avgAccuracy,
        comparisonAgents.map(a => a.metrics.avgAccuracy),
        'higher_better'
      ),
      success_rate: this.performTTest(
        'success_rate',
        primaryAgent.metrics.successRate,
        comparisonAgents.map(a => a.metrics.successRate),
        'higher_better'
      ),
      cost_efficiency: this.performTTest(
        'cost_efficiency',
        primaryAgent.metrics.avgCost,
        comparisonAgents.map(a => a.metrics.avgCost),
        'lower_better'
      )
    };
  }

  /**
   * Perform simplified t-test analysis
   */
  private performTTest(
    metricName: string,
    primaryValue: number,
    comparisonValues: number[],
    direction: 'higher_better' | 'lower_better'
  ): StatisticalTest {
    if (comparisonValues.length === 0) {
      return {
        metric_name: metricName,
        primary_value: primaryValue,
        comparison_values: [],
        statistical_significance: false,
        p_value: 1.0,
        effect_size: 0,
        confidence_interval: { lower: primaryValue, upper: primaryValue },
        interpretation: 'No comparison data available'
      };
    }

    const mean = comparisonValues.reduce((a, b) => a + b, 0) / comparisonValues.length;
    const variance = comparisonValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / comparisonValues.length;
    const stdDev = Math.sqrt(variance);

    // Simplified effect size (Cohen's d approximation)
    const effectSize = stdDev === 0 ? 0 : Math.abs(primaryValue - mean) / stdDev;

    // Simplified p-value based on effect size
    const pValue = effectSize > 2 ? 0.01 
                 : effectSize > 1 ? 0.05 
                 : effectSize > 0.5 ? 0.1 
                 : 0.5;

    const isSignificant = pValue < 0.05;

    // Confidence interval (simplified)
    const margin = 1.96 * stdDev / Math.sqrt(comparisonValues.length);
    const confidenceInterval = {
      lower: primaryValue - margin,
      upper: primaryValue + margin
    };

    // Generate interpretation
    const betterWorse = direction === 'higher_better' 
      ? (primaryValue > mean ? 'better' : 'worse')
      : (primaryValue < mean ? 'better' : 'worse');

    const interpretation = isSignificant 
      ? `Statistically ${betterWorse} than comparison agents`
      : `No significant difference from comparison agents`;

    return {
      metric_name: metricName,
      primary_value: primaryValue,
      comparison_values: comparisonValues,
      statistical_significance: isSignificant,
      p_value: pValue,
      effect_size: effectSize,
      confidence_interval: confidenceInterval,
      interpretation
    };
  }

  /**
   * Calculate performance ranking
   */
  private calculatePerformanceRanking(
    primaryAgent: AgentBenchmarkData,
    comparisonAgents: AgentBenchmarkData[]
  ): PerformanceRanking {
    const allAgents = [primaryAgent, ...comparisonAgents];
    const categoryAgents = allAgents.filter(a => a.category === primaryAgent.category);

    // Calculate composite scores for ranking
    const scores = allAgents.map(agent => ({
      agent_id: agent.agent_id,
      score: this.calculateCompositeScore(agent.metrics)
    }));

    scores.sort((a, b) => b.score - a.score);

    const overallRank = scores.findIndex(s => s.agent_id === primaryAgent.agent_id) + 1;
    const percentile = Math.round((1 - (overallRank - 1) / scores.length) * 100);

    // Calculate category rank
    const categoryScores = categoryAgents.map(agent => ({
      agent_id: agent.agent_id,
      score: this.calculateCompositeScore(agent.metrics)
    }));
    categoryScores.sort((a, b) => b.score - a.score);
    const categoryRank = categoryScores.findIndex(s => s.agent_id === primaryAgent.agent_id) + 1;

    // Identify strengths and weaknesses
    const { strengths, weaknesses } = this.identifyStrengthsWeaknesses(
      primaryAgent,
      comparisonAgents
    );

    return {
      overall_rank: overallRank,
      total_agents: allAgents.length,
      category_rank: categoryRank,
      category_total: categoryAgents.length,
      strengths,
      weaknesses,
      percentile
    };
  }

  /**
   * Calculate composite performance score
   */
  private calculateCompositeScore(metrics: AgentMetrics): number {
    // Weighted composite score
    const weights = {
      accuracy: 0.4,
      successRate: 0.3,
      processingTime: 0.2, // Inverted (lower is better)
      cost: 0.1 // Inverted (lower is better)
    };

    const normalizedAccuracy = Math.min(metrics.avgAccuracy, 1) * 100;
    const normalizedSuccessRate = metrics.successRate * 100;
    const normalizedProcessingTime = Math.max(0, 100 - (metrics.avgProcessingTime / 100));
    const normalizedCost = Math.max(0, 100 - (metrics.avgCost * 1000));

    return (
      normalizedAccuracy * weights.accuracy +
      normalizedSuccessRate * weights.successRate +
      normalizedProcessingTime * weights.processingTime +
      normalizedCost * weights.cost
    );
  }

  /**
   * Identify agent strengths and weaknesses
   */
  private identifyStrengthsWeaknesses(
    primaryAgent: AgentBenchmarkData,
    comparisonAgents: AgentBenchmarkData[]
  ): { strengths: string[]; weaknesses: string[] } {
    const strengths: string[] = [];
    const weaknesses: string[] = [];

    if (comparisonAgents.length === 0) {
      return { strengths: ['No comparison data available'], weaknesses: [] };
    }

    const avgMetrics = this.calculateAverageMetrics(comparisonAgents);

    // Check each metric
    if (primaryAgent.metrics.avgAccuracy > avgMetrics.avgAccuracy * 1.1) {
      strengths.push('High extraction accuracy');
    } else if (primaryAgent.metrics.avgAccuracy < avgMetrics.avgAccuracy * 0.9) {
      weaknesses.push('Below-average accuracy');
    }

    if (primaryAgent.metrics.successRate > avgMetrics.successRate * 1.05) {
      strengths.push('Excellent reliability');
    } else if (primaryAgent.metrics.successRate < avgMetrics.successRate * 0.95) {
      weaknesses.push('Higher error rate');
    }

    if (primaryAgent.metrics.avgProcessingTime < avgMetrics.avgProcessingTime * 0.8) {
      strengths.push('Fast processing speed');
    } else if (primaryAgent.metrics.avgProcessingTime > avgMetrics.avgProcessingTime * 1.2) {
      weaknesses.push('Slower processing time');
    }

    if (primaryAgent.metrics.avgCost < avgMetrics.avgCost * 0.9) {
      strengths.push('Cost-efficient operation');
    } else if (primaryAgent.metrics.avgCost > avgMetrics.avgCost * 1.1) {
      weaknesses.push('Higher than average cost');
    }

    return { strengths, weaknesses };
  }

  /**
   * Calculate average metrics across comparison agents
   */
  private calculateAverageMetrics(agents: AgentBenchmarkData[]): AgentMetrics {
    if (agents.length === 0) {
      return {
        requestCount: 0,
        successRate: 0,
        avgProcessingTime: 0,
        avgAccuracy: 0,
        avgConfidence: 0,
        avgCost: 0,
        errorTypes: []
      };
    }

    return {
      requestCount: agents.reduce((sum, a) => sum + a.metrics.requestCount, 0) / agents.length,
      successRate: agents.reduce((sum, a) => sum + a.metrics.successRate, 0) / agents.length,
      avgProcessingTime: agents.reduce((sum, a) => sum + a.metrics.avgProcessingTime, 0) / agents.length,
      avgAccuracy: agents.reduce((sum, a) => sum + a.metrics.avgAccuracy, 0) / agents.length,
      avgConfidence: agents.reduce((sum, a) => sum + a.metrics.avgConfidence, 0) / agents.length,
      avgCost: agents.reduce((sum, a) => sum + a.metrics.avgCost, 0) / agents.length,
      errorTypes: [] // Would need more complex aggregation
    };
  }

  /**
   * Generate benchmark recommendations
   */
  private generateBenchmarkRecommendations(
    primaryAgent: AgentBenchmarkData,
    comparisonAgents: AgentBenchmarkData[],
    statisticalAnalysis: StatisticalComparison,
    performanceRanking: PerformanceRanking
  ): BenchmarkRecommendation[] {
    const recommendations: BenchmarkRecommendation[] = [];

    // Performance-based recommendations
    if (performanceRanking.percentile < 50) {
      recommendations.push({
        type: 'improvement',
        priority: 'high',
        title: 'Overall Performance Below Average',
        description: `Agent ranks in ${performanceRanking.percentile}th percentile`,
        target_metric: 'composite_score',
        expected_improvement: 'Move to top 50% of performers',
        implementation_steps: [
          'Analyze top-performing agents in same category',
          'Review and optimize prompt engineering',
          'Consider model upgrades or configuration changes'
        ],
        effort_estimate: '2-4 weeks',
        risk_level: 'medium'
      });
    }

    // Specific metric recommendations
    if (!statisticalAnalysis.accuracy.statistical_significance && 
        primaryAgent.metrics.avgAccuracy < 0.9) {
      recommendations.push({
        type: 'optimization',
        priority: 'high',
        title: 'Improve Extraction Accuracy',
        description: 'Accuracy below target and not statistically different from others',
        target_metric: 'accuracy',
        expected_improvement: '10-15% accuracy increase',
        implementation_steps: [
          'Add more specific examples to prompt',
          'Refine output schema validation',
          'Implement multi-pass validation'
        ],
        effort_estimate: '1-2 weeks',
        risk_level: 'low'
      });
    }

    return recommendations;
  }

  /**
   * Calculate confidence score for the benchmark
   */
  private calculateConfidenceScore(
    primaryAgent: AgentBenchmarkData,
    comparisonAgents: AgentBenchmarkData[],
    statisticalAnalysis: StatisticalComparison
  ): number {
    let confidence = 50; // Base confidence

    // Sample size factor
    if (primaryAgent.sample_size > 100) confidence += 20;
    else if (primaryAgent.sample_size > 50) confidence += 10;

    // Comparison agents factor
    if (comparisonAgents.length > 5) confidence += 15;
    else if (comparisonAgents.length > 2) confidence += 10;

    // Data quality factor
    confidence += (primaryAgent.data_quality_score / 100) * 15;

    // Statistical significance factor
    const significantTests = Object.values(statisticalAnalysis)
      .filter(test => test.statistical_significance).length;
    confidence += significantTests * 5;

    return Math.max(0, Math.min(100, Math.round(confidence)));
  }

  /**
   * Utility function to calculate median
   */
  private calculateMedian(values: number[]): number {
    const sorted = values.slice().sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
      return sorted[middle];
    }
  }
}

/**
 * Factory function for creating BenchmarkComparison
 */
export function createBenchmarkComparison(supabase: SupabaseClient<Database>): BenchmarkComparison {
  return new BenchmarkComparison(supabase);
}