/**
 * Job Status API Endpoint
 * 
 * GET /api/v1/jobs/{job_id} - Get job status and results
 * GET /api/v1/jobs - List customer's jobs with pagination
 * 
 * Features:
 * - Secure job status retrieval with customer isolation
 * - Real-time job progress tracking
 * - Comprehensive job details including results and error information
 * - Pagination and filtering for job lists
 * - Estimated completion time calculation
 */

import "jsr:@supabase/functions-js@2.5.0/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';
import {
  validateApiKey,
  corsHeaders as _corsHeaders,
  createApiResponse,
  handleCors,
  type ApiKeyValidationResult
} from '../_shared/api-key-utils.ts';
import { QueueManager, type QueueJob } from '../_shared/queue-manager.ts';

// =============================================================================
// TYPE DEFINITIONS
// =============================================================================

interface JobStatusResponse {
  success: boolean;
  data?: {
    jobId: string;
    status: string;
    progress?: {
      stage: string;
      percentage?: number;
      estimatedCompletion?: string;
    };
    result?: Record<string, unknown>;
    error?: {
      message: string;
      code?: string;
      retryCount: number;
      maxRetries: number;
      willRetry: boolean;
    };
    timestamps: {
      created: string;
      scheduled?: string;
      started?: string;
      completed?: string;
    };
    metadata: {
      correlationId: string;
      documentId: string;
      agentId?: string;
      jobType: string;
      priority: string;
    };
  };
  error?: string;
  timestamp: string;
}

interface JobListResponse {
  success: boolean;
  data?: {
    jobs: JobSummary[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
    summary: {
      totalJobs: number;
      queued: number;
      processing: number;
      completed: number;
      failed: number;
    };
  };
  error?: string;
  timestamp: string;
}

interface JobSummary {
  jobId: string;
  status: string;
  documentId: string;
  documentName?: string;
  agentName?: string;
  jobType: string;
  priority: string;
  created: string;
  completed?: string;
  error?: string;
  processingTime?: number;
}

interface JobListParams {
  page?: number;
  limit?: number;
  status?: string;
  jobType?: string;
  sortBy?: 'created' | 'completed' | 'priority';
  sortOrder?: 'asc' | 'desc';
  since?: string; // ISO date string
}

// =============================================================================
// ENVIRONMENT CONFIGURATION
// =============================================================================

const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('Missing required environment variables');
}

// Initialize clients
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const queueManager = new QueueManager(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

class JobStatusService {
  /**
   * Get detailed job status with enhanced information
   */
  static async getJobStatus(jobId: string, customerId: string): Promise<{
    success: boolean;
    job?: QueueJob;
    estimatedCompletion?: Date;
    error?: string;
  }> {
    try {
      // Get job details
      const jobResult = await queueManager.getJobStatus(jobId);
      if (!jobResult.success || !jobResult.job) {
        return { success: false, error: 'Job not found' };
      }

      const job = jobResult.job;

      // Verify job belongs to customer
      if (job.customer_id !== customerId) {
        return { success: false, error: 'Job not found' }; // Don't reveal existence
      }

      // Calculate estimated completion for queued jobs
      let estimatedCompletion: Date | undefined;
      if (job.status === 'queued') {
        estimatedCompletion = await this.calculateEstimatedCompletion(job);
      }

      return { success: true, job, estimatedCompletion };

    } catch {
      console.error('Get job status error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Calculate estimated completion time for queued jobs
   */
  private static async calculateEstimatedCompletion(job: QueueJob): Promise<Date> {
    try {
      // Get queue position by counting higher priority jobs scheduled before this one
      const { count } = await supabase
        .from('job_queue')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'queued')
        .or(`priority.lt.${job.priority},and(priority.eq.${job.priority},created_at.lt.${job.created_at})`);

      const queuePosition = count || 0;

      // Get average processing time from recent completed jobs
      const { data: avgData } = await supabase
        .from('job_queue')
        .select('actual_duration')
        .eq('status', 'completed')
        .not('actual_duration', 'is', null)
        .gte('completed_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours
        .limit(100);

      const avgProcessingTime = avgData && avgData.length > 0
        ? avgData.reduce((sum, job) => sum + (job.actual_duration || 0), 0) / avgData.length
        : 60; // Default 60 seconds

      return QueueManager.calculateEstimatedCompletion(queuePosition, avgProcessingTime, job.priority);

    } catch {
      console.error('Error calculating estimated completion:', error);
      // Return a reasonable default estimate
      return new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now
    }
  }

  /**
   * Get customer job list with pagination and filtering
   */
  static async getCustomerJobs(
    customerId: string,
    params: JobListParams
  ): Promise<{
    success: boolean;
    jobs?: JobSummary[];
    total?: number;
    summary?: Record<string, number>;
    error?: string;
  }> {
    try {
      const page = Math.max(1, params.page || 1);
      const limit = Math.min(100, Math.max(1, params.limit || 50));
      const offset = (page - 1) * limit;

      // Build query
      let query = supabase
        .from('job_queue')
        .select(`
          id,
          status,
          document_id,
          job_type,
          priority,
          created_at,
          completed_at,
          error_message,
          actual_duration,
          correlation_id,
          documents!inner(filename, original_filename),
          agents(name)
        `)
        .eq('customer_id', customerId);

      // Apply filters
      if (params.status) {
        query = query.eq('status', params.status);
      }

      if (params.jobType) {
        query = query.eq('job_type', params.jobType);
      }

      if (params.since) {
        query = query.gte('created_at', params.since);
      }

      // Apply sorting
      const sortBy = params.sortBy || 'created';
      const sortOrder = params.sortOrder || 'desc';
      
      const sortColumn = sortBy === 'created' ? 'created_at' 
                       : sortBy === 'completed' ? 'completed_at'
                       : 'priority';

      query = query.order(sortColumn, { ascending: sortOrder === 'asc' });

      // Get total count
      const { count, error: countError } = await supabase
        .from('job_queue')
        .select('*', { count: 'exact', head: true })
        .eq('customer_id', customerId);

      if (countError) {
        return { success: false, error: countError.message };
      }

      // Get paginated results
      const { data, error } = await query.range(offset, offset + limit - 1);

      if (error) {
        return { success: false, error: error.message };
      }

      // Transform to JobSummary format
      const jobs: JobSummary[] = (data || []).map(job => ({
        jobId: job.id,
        status: job.status,
        documentId: job.document_id,
        documentName: job.documents?.original_filename || job.documents?.filename,
        agentName: job.agents?.name,
        jobType: job.job_type,
        priority: job.priority,
        created: job.created_at,
        completed: job.completed_at,
        error: job.error_message,
        processingTime: job.actual_duration
      }));

      // Get summary statistics
      const { data: summaryData } = await supabase
        .from('job_queue')
        .select('status')
        .eq('customer_id', customerId);

      const summary = (summaryData || []).reduce((acc, job) => {
        acc[job.status] = (acc[job.status] || 0) + 1;
        acc.totalJobs = (acc.totalJobs || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return { 
        success: true, 
        jobs, 
        total: count || 0,
        summary: {
          totalJobs: summary.totalJobs || 0,
          queued: summary.queued || 0,
          processing: summary.processing || 0,
          completed: summary.completed || 0,
          failed: summary.failed || 0
        }
      };

    } catch {
      console.error('Get customer jobs error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Format job status response
   */
  static formatJobStatusResponse(
    job: QueueJob, 
    estimatedCompletion?: Date
  ): JobStatusResponse['data'] {
    const isCompleted = job.status === 'completed';
    const isFailed = job.status === 'failed' || job.status === 'dead_letter';
    const isProcessing = job.status === 'processing';
    const isQueued = job.status === 'queued';

    // Calculate progress
    let progress: JobStatusResponse['data']['progress'];
    if (isCompleted) {
      progress = {
        stage: 'completed',
        percentage: 100
      };
    } else if (isFailed) {
      progress = {
        stage: 'failed',
        percentage: 0
      };
    } else if (isProcessing) {
      progress = {
        stage: 'processing',
        percentage: 50 // Approximate
      };
    } else if (isQueued) {
      progress = {
        stage: 'queued',
        percentage: 0,
        estimatedCompletion: estimatedCompletion?.toISOString()
      };
    }

    return {
      jobId: job.id,
      status: job.status,
      progress,
      result: isCompleted ? job.result : undefined,
      error: isFailed ? {
        message: job.error_message || 'Unknown error',
        code: job.error_code,
        retryCount: job.retry_count,
        maxRetries: job.max_retries,
        willRetry: job.status === 'failed' && job.retry_count < job.max_retries
      } : undefined,
      timestamps: {
        created: job.created_at,
        scheduled: job.scheduled_for,
        started: job.started_at,
        completed: job.completed_at
      },
      metadata: {
        correlationId: job.correlation_id,
        documentId: job.document_id,
        agentId: job.agent_id,
        jobType: job.job_type,
        priority: job.priority
      }
    };
  }
}

// =============================================================================
// REQUEST HANDLERS
// =============================================================================

/**
 * Handle GET /jobs/{jobId} - Get specific job status
 */
async function handleJobStatus(
  jobId: string,
  apiKey: ApiKeyValidationResult
): Promise<Response> {
  try {
    const statusResult = await JobStatusService.getJobStatus(jobId, apiKey.customerId!);
    
    if (!statusResult.success) {
      return createApiResponse(statusResult.error || 'Job not found', 404, false);
    }

    const job = statusResult.job!;
    const responseData = JobStatusService.formatJobStatusResponse(job, statusResult.estimatedCompletion);

    const response: JobStatusResponse = {
      success: true,
      data: responseData,
      timestamp: new Date().toISOString()
    };

    return createApiResponse(response.data, 200, true);

  } catch {
    console.error('Handle job status error:', error);
    return createApiResponse('Internal server error', 500, false);
  }
}

/**
 * Handle GET /jobs - List customer jobs
 */
async function handleJobList(
  params: JobListParams,
  apiKey: ApiKeyValidationResult
): Promise<Response> {
  try {
    const jobsResult = await JobStatusService.getCustomerJobs(apiKey.customerId!, params);
    
    if (!jobsResult.success) {
      return createApiResponse(jobsResult.error || 'Failed to get jobs', 500, false);
    }

    const totalPages = Math.ceil((jobsResult.total || 0) / (params.limit || 50));

    const response: JobListResponse = {
      success: true,
      data: {
        jobs: jobsResult.jobs || [],
        pagination: {
          total: jobsResult.total || 0,
          page: params.page || 1,
          limit: params.limit || 50,
          totalPages
        },
        summary: jobsResult.summary || {
          totalJobs: 0,
          queued: 0,
          processing: 0,
          completed: 0,
          failed: 0
        }
      },
      timestamp: new Date().toISOString()
    };

    return createApiResponse(response.data, 200, true);

  } catch {
    console.error('Handle job list error:', error);
    return createApiResponse('Internal server error', 500, false);
  }
}

// =============================================================================
// MAIN HANDLER
// =============================================================================

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  // Only accept GET requests
  if (req.method !== 'GET') {
    return createApiResponse('Method not allowed', 405, false);
  }

  try {
    // Extract and validate API key
    const authHeader = req.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return createApiResponse('Missing or invalid Authorization header', 401, false);
    }

    const rawApiKey = authHeader.replace('Bearer ', '');
    const apiKeyValidation = await validateApiKey(supabase, rawApiKey);
    
    if (!apiKeyValidation.isValid) {
      return createApiResponse(apiKeyValidation.error || 'Invalid API key', 401, false);
    }

    // Parse URL and extract path parameters
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(part => part.length > 0);
    
    // Remove 'functions/v1/job-status' prefix if present
    const relevantParts = pathParts.slice(-2); // Get last 2 parts
    
    if (relevantParts.length === 1 && relevantParts[0] !== 'jobs') {
      // Pattern: /job-status/{jobId}
      const jobId = relevantParts[0];
      return await handleJobStatus(jobId, apiKeyValidation);
    } 
    else if (relevantParts.length === 1 && relevantParts[0] === 'jobs') {
      // Pattern: /job-status/jobs (list jobs)
      const searchParams = url.searchParams;
      const params: JobListParams = {
        page: parseInt(searchParams.get('page') || '1'),
        limit: parseInt(searchParams.get('limit') || '50'),
        status: searchParams.get('status') || undefined,
        jobType: searchParams.get('jobType') || undefined,
        sortBy: (searchParams.get('sortBy') as 'created' | 'completed' | 'priority') || 'created',
        sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
        since: searchParams.get('since') || undefined
      };

      return await handleJobList(params, apiKeyValidation);
    }
    else if (relevantParts.length === 0) {
      // Pattern: /job-status (list jobs - backwards compatibility)
      const searchParams = url.searchParams;
      const params: JobListParams = {
        page: parseInt(searchParams.get('page') || '1'),
        limit: parseInt(searchParams.get('limit') || '50'),
        status: searchParams.get('status') || undefined,
        jobType: searchParams.get('jobType') || undefined,
        sortBy: (searchParams.get('sortBy') as 'created' | 'completed' | 'priority') || 'created',
        sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
        since: searchParams.get('since') || undefined
      };

      return await handleJobList(params, apiKeyValidation);
    }
    else {
      return createApiResponse('Invalid endpoint path', 404, false);
    }

  } catch {
    console.error('Job status endpoint error:', error);
    return createApiResponse('Internal server error', 500, false);
  }
});