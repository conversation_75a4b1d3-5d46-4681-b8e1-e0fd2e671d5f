/**
 * Admin Credits Management Edge Function
 * Epic 4 Story 4.3: Credit transaction engine and management system
 * 
 * Endpoints:
 * - POST /{customer_id}/purchase - Purchase credits
 * - POST /{customer_id}/refund - Process refund
 * - POST /{customer_id}/adjust - Manual adjustment
 * - POST /{customer_id}/deduct - Deduct credits
 * - GET /{customer_id}/history - Credit transaction history
 * - POST /alerts - Configure credit alerts
 * - PUT /alerts/{alert_id} - Update alert configuration
 * - GET /{customer_id}/alerts - List customer alerts
 * - POST /alerts/check - Check and trigger alerts
 * - POST /bulk - Bulk credit operations
 * - POST /{customer_id}/pools - Create credit pool (enterprise)
 * - GET /{customer_id}/pools - List credit pools
 * - POST /pools/{pool_id}/allocate - Allocate credits from pool
 */

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3';
import type { Database } from '../../../types/database.types.ts';

// Specific types for Supabase client
type SupabaseClient = ReturnType<typeof createClient<Database>>;

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-correlation-id, x-admin-user-id, x-admin-role',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

// Request interfaces
interface CreditTransactionRequest {
  customer_id?: string;
  api_key_id?: string;
  amount: number;
  payment_reference?: string;
  admin_notes?: string;
  metadata?: Record<string, unknown>;
}

interface CreditHistoryParams {
  limit?: number;
  offset?: number;
  start_date?: string;
  end_date?: string;
  transaction_type?: string;
}

interface CreditAlertRequest {
  customer_id?: string;
  api_key_id?: string;
  alert_type: 'low_balance' | 'zero_balance' | 'usage_spike' | 'expiration_warning';
  threshold_value?: number;
  threshold_percentage?: number;
  notification_channels?: {
    email?: boolean;
    webhook?: boolean;
  };
}

interface BulkCreditOperation {
  operation: 'purchase' | 'refund' | 'adjustment';
  customer_ids: string[];
  amount: number;
  admin_notes?: string;
}

interface CreditPoolRequest {
  pool_name: string;
  total_credits: number;
  expires_at?: string;
}

interface PoolAllocationRequest {
  api_key_id: string;
  amount: number;
}

interface AdminContext {
  adminUserId?: string;
  adminRole?: string;
  correlationId: string;
}

/**
 * Validate admin authentication and permissions
 */
async function validateAdminAuth(request: Request): Promise<AdminContext> {
  const authHeader = request.headers.get('Authorization');
  const correlationId = request.headers.get('x-correlation-id') || crypto.randomUUID();

  if (!authHeader?.startsWith('Bearer ')) {
    throw new Error('Missing or invalid authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  
  // For now, validate against service role key (in production, implement proper admin auth)
  const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
  if (token !== serviceRoleKey) {
    throw new Error('Invalid admin credentials');
  }

  return {
    adminUserId: 'system-admin', // In production, extract from JWT
    adminRole: 'admin',
    correlationId
  };
}

/**
 * Create standardized API response
 */
function createResponse(
  data: SupabaseClient = null,
  error: SupabaseClient = null,
  status: number = 200,
  correlationId?: string
): Response {
  const response = {
    success: !error,
    timestamp: new Date().toISOString(),
    correlation_id: correlationId,
    ...(data && { data }),
    ...(error && { error: typeof error === 'string' ? { message: error } : error })
  };

  return new Response(JSON.stringify(response), {
    status,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  });
}

/**
 * Log audit event for compliance
 */
async function logAuditEvent(
  supabase: SupabaseClient,
  context: AdminContext,
  action: string,
  resourceType: string,
  resourceId: string,
  changes: Record<string, unknown> = {},
  metadata: Record<string, unknown> = {}
): Promise<void> {
  try {
    await supabase.from('audit_logs').insert([{
      id: crypto.randomUUID(),
      customer_id: changes.customer_id || null,
      admin_user_id: context.adminUserId,
      event_type: 'credit_transaction',
      action,
      resource_type: resourceType,
      resource_id: resourceId,
      changes,
      metadata: {
        ...metadata,
        correlation_id: context.correlationId,
        admin_role: context.adminRole
      },
      created_at: new Date().toISOString()
    }]);
  } catch (auditError) {
    console.error('Failed to log audit event:', auditError);
    // Don't fail the main operation due to audit logging issues
  }
}

/**
 * Validate customer ID format
 */
function validateCustomerId(customerId: string): void {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(customerId)) {
    throw new Error('Invalid customer ID format');
  }
}

/**
 * Validate credit transaction data
 */
function validateCreditTransaction(data: CreditTransactionRequest): void {
  if (!data.amount || typeof data.amount !== 'number') {
    throw new Error('Missing required fields: amount must be a number');
  }

  if (data.amount === 0) {
    throw new Error('Amount cannot be zero');
  }

  if (data.amount < 0) {
    throw new Error('Amount must be positive');
  }
}

/**
 * Process credit transaction using database function
 */
async function processCreditTransaction(
  supabase: SupabaseClient,
  context: AdminContext,
  customerId: string,
  transactionType: string,
  data: CreditTransactionRequest
): Promise<Record<string, unknown>> {
  validateCustomerId(customerId);
  validateCreditTransaction(data);

  // Use database function for atomic transaction processing
  const { data: result, error } = await supabase.rpc('process_credit_transaction', {
    p_customer_id: customerId,
    p_api_key_id: data.api_key_id || null,
    p_transaction_type: transactionType,
    p_amount: Math.abs(data.amount),
    p_payment_reference: data.payment_reference || null,
    p_admin_user_id: context.adminUserId,
    p_admin_notes: data.admin_notes || null,
    p_metadata: data.metadata || {}
  });

  if (error) {
    throw new Error(`Credit transaction failed: ${error.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    transactionType,
    'credit_transaction',
    result.transaction_id,
    { customer_id: customerId, amount: data.amount },
    { payment_reference: data.payment_reference }
  );

  return result;
}

/**
 * Get credit transaction history
 */
async function getCreditHistory(
  supabase: SupabaseClient,
  context: AdminContext,
  customerId: string,
  params: CreditHistoryParams
): Promise<Record<string, unknown>> {
  validateCustomerId(customerId);

  // Verify customer exists
  const { data: customer, error: customerError } = await supabase
    .from('customers')
    .select('id, credits_available, credits_purchased, credits_used_lifetime')
    .eq('id', customerId)
    .single();

  if (customerError || !customer) {
    throw new Error('Customer not found');
  }

  // Build query for transaction history
  let query = supabase
    .from('credit_transactions')
    .select(`
      id,
      transaction_type,
      amount,
      balance_before,
      balance_after,
      payment_reference,
      admin_notes,
      status,
      created_at
    `)
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false });

  // Apply filters
  if (params.transaction_type) {
    query = query.eq('transaction_type', params.transaction_type);
  }

  if (params.start_date) {
    query = query.gte('created_at', params.start_date);
  }

  if (params.end_date) {
    query = query.lte('created_at', params.end_date);
  }

  // Apply pagination
  const limit = Math.min(params.limit || 50, 100);
  const offset = params.offset || 0;
  query = query.range(offset, offset + limit - 1);

  const { data: transactions, error } = await query;

  if (error) {
    throw new Error(`Failed to fetch credit history: ${error.message}`);
  }

  // Calculate summary
  const summary = {
    current_balance: customer.credits_available,
    total_purchased: customer.credits_purchased || 0,
    total_used: customer.credits_used_lifetime || 0,
    total_refunded: transactions
      ?.filter(t => t.transaction_type === 'refund')
      .reduce((sum, t) => sum + t.amount, 0) || 0
  };

  return {
    transactions,
    summary,
    pagination: {
      limit,
      offset,
      has_more: transactions?.length === limit
    },
    date_range: {
      start: params.start_date,
      end: params.end_date
    }
  };
}

/**
 * Configure credit alert
 */
async function configureCreditAlert(
  supabase: SupabaseClient,
  context: AdminContext,
  data: CreditAlertRequest
): Promise<Record<string, unknown>> {
  if (!data.customer_id) {
    throw new Error('customer_id is required');
  }

  validateCustomerId(data.customer_id);

  if (!data.alert_type) {
    throw new Error('alert_type is required');
  }

  if (!data.threshold_value && !data.threshold_percentage) {
    throw new Error('Either threshold_value or threshold_percentage is required');
  }

  // Verify customer exists
  const { data: customer, error: customerError } = await supabase
    .from('customers')
    .select('id')
    .eq('id', data.customer_id)
    .single();

  if (customerError || !customer) {
    throw new Error('Customer not found');
  }

  // Create alert configuration
  const alertRecord = {
    id: crypto.randomUUID(),
    customer_id: data.customer_id,
    api_key_id: data.api_key_id || null,
    alert_type: data.alert_type,
    threshold_value: data.threshold_value || null,
    threshold_percentage: data.threshold_percentage || null,
    notification_channels: data.notification_channels || { email: true },
    is_enabled: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data: createdAlert, error } = await supabase
    .from('credit_alerts')
    .insert([alertRecord])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create credit alert: ${error.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'create',
    'credit_alert',
    createdAlert.id,
    { customer_id: data.customer_id, alert_type: data.alert_type }
  );

  return createdAlert;
}

/**
 * Update credit alert configuration
 */
async function updateCreditAlert(
  supabase: SupabaseClient,
  context: AdminContext,
  alertId: string,
  updateData: Partial<CreditAlertRequest>
): Promise<Record<string, unknown>> {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(alertId)) {
    throw new Error('Invalid alert ID format');
  }

  // Get current alert
  const { data: currentAlert, error: fetchError } = await supabase
    .from('credit_alerts')
    .select('*')
    .eq('id', alertId)
    .single();

  if (fetchError || !currentAlert) {
    throw new Error('Credit alert not found');
  }

  // Prepare update
  const updates: SupabaseClient = {
    updated_at: new Date().toISOString()
  };

  if (updateData.threshold_value !== undefined) {
    updates.threshold_value = updateData.threshold_value;
  }

  if (updateData.threshold_percentage !== undefined) {
    updates.threshold_percentage = updateData.threshold_percentage;
  }

  if (updateData.notification_channels !== undefined) {
    updates.notification_channels = updateData.notification_channels;
  }

  if ('is_enabled' in updateData) {
    updates.is_enabled = updateData.is_enabled;
  }

  // Update alert
  const { data: updatedAlert, error: updateError } = await supabase
    .from('credit_alerts')
    .update(updates)
    .eq('id', alertId)
    .select()
    .single();

  if (updateError) {
    throw new Error(`Failed to update credit alert: ${updateError.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'update',
    'credit_alert',
    alertId,
    { customer_id: currentAlert.customer_id, old_values: currentAlert, new_values: updates }
  );

  return updatedAlert;
}

/**
 * Check and trigger credit alerts
 */
async function checkCreditAlerts(
  supabase: SupabaseClient,
  context: AdminContext,
  customerId?: string
): Promise<Record<string, unknown>> {
  if (customerId) {
    validateCustomerId(customerId);
  }

  // Use database function to check alerts
  const { data: result, error } = await supabase.rpc('check_credit_alerts', {
    p_customer_id: customerId || null
  });

  if (error) {
    throw new Error(`Failed to check credit alerts: ${error.message}`);
  }

  return result;
}

/**
 * List customer credit alerts
 */
async function listCustomerAlerts(
  supabase: SupabaseClient,
  context: AdminContext,
  customerId: string
): Promise<Record<string, unknown>> {
  validateCustomerId(customerId);

  const { data: alerts, error } = await supabase
    .from('credit_alerts')
    .select('*')
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to list credit alerts: ${error.message}`);
  }

  return { alerts };
}

/**
 * Perform bulk credit operation
 */
async function performBulkCreditOperation(
  supabase: SupabaseClient,
  context: AdminContext,
  data: BulkCreditOperation
): Promise<Record<string, unknown>> {
  if (!data.customer_ids || !Array.isArray(data.customer_ids) || data.customer_ids.length === 0) {
    throw new Error('customer_ids must be a non-empty array');
  }

  if (data.customer_ids.length > 100) {
    throw new Error('Maximum 100 customers allowed per bulk operation');
  }

  if (!['purchase', 'refund', 'adjustment'].includes(data.operation)) {
    throw new Error('Invalid operation type. Must be purchase, refund, or adjustment');
  }

  if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
    throw new Error('Amount must be a positive number');
  }

  // Validate all customer IDs
  data.customer_ids.forEach(validateCustomerId);

  // Use database function for bulk operation
  const { data: result, error } = await supabase.rpc('bulk_credit_operation', {
    p_admin_user_id: context.adminUserId,
    p_operation: data.operation,
    p_customer_ids: data.customer_ids,
    p_amount: data.amount,
    p_admin_notes: data.admin_notes || null
  });

  if (error) {
    throw new Error(`Bulk operation failed: ${error.message}`);
  }

  // Log audit event for bulk operation
  await logAuditEvent(
    supabase,
    context,
    'bulk_operation',
    'credit_transaction',
    crypto.randomUUID(),
    { operation: data.operation, customer_count: data.customer_ids.length, amount: data.amount }
  );

  return result;
}

/**
 * Create credit pool (enterprise feature)
 */
async function createCreditPool(
  supabase: SupabaseClient,
  context: AdminContext,
  customerId: string,
  data: CreditPoolRequest
): Promise<Record<string, unknown>> {
  validateCustomerId(customerId);

  if (!data.pool_name?.trim()) {
    throw new Error('pool_name is required');
  }

  if (!data.total_credits || typeof data.total_credits !== 'number' || data.total_credits <= 0) {
    throw new Error('total_credits must be a positive number');
  }

  // Verify customer exists and is enterprise tier
  const { data: customer, error: customerError } = await supabase
    .from('customers')
    .select('tier')
    .eq('id', customerId)
    .single();

  if (customerError || !customer) {
    throw new Error('Customer not found');
  }

  if (customer.tier !== 'enterprise') {
    throw new Error('Credit pools are only available for enterprise customers');
  }

  // Create pool
  const poolRecord = {
    id: crypto.randomUUID(),
    customer_id: customerId,
    pool_name: data.pool_name.trim(),
    total_credits: data.total_credits,
    allocated_credits: 0,
    expires_at: data.expires_at || null,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  const { data: createdPool, error } = await supabase
    .from('credit_pools')
    .insert([poolRecord])
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to create credit pool: ${error.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'create',
    'credit_pool',
    createdPool.id,
    { customer_id: customerId, pool_name: data.pool_name, total_credits: data.total_credits }
  );

  return createdPool;
}

/**
 * List customer credit pools
 */
async function listCreditPools(
  supabase: SupabaseClient,
  context: AdminContext,
  customerId: string
): Promise<Record<string, unknown>> {
  validateCustomerId(customerId);

  const { data: pools, error } = await supabase
    .from('credit_pools')
    .select('*')
    .eq('customer_id', customerId)
    .eq('is_active', true)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to list credit pools: ${error.message}`);
  }

  // Calculate totals
  const totals = pools?.reduce((acc, pool) => ({
    total_credits: acc.total_credits + pool.total_credits,
    total_allocated: acc.total_allocated + pool.allocated_credits,
    total_available: acc.total_available + (pool.total_credits - pool.allocated_credits)
  }), { total_credits: 0, total_allocated: 0, total_available: 0 }) || 
  { total_credits: 0, total_allocated: 0, total_available: 0 };

  return {
    pools: pools || [],
    ...totals
  };
}

/**
 * Allocate credits from pool to API key
 */
async function allocateFromPool(
  supabase: SupabaseClient,
  context: AdminContext,
  poolId: string,
  data: PoolAllocationRequest
): Promise<Record<string, unknown>> {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  if (!uuidRegex.test(poolId)) {
    throw new Error('Invalid pool ID format');
  }

  if (!data.api_key_id) {
    throw new Error('api_key_id is required');
  }

  if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
    throw new Error('amount must be a positive number');
  }

  // Get pool details
  const { data: pool, error: poolError } = await supabase
    .from('credit_pools')
    .select('*')
    .eq('id', poolId)
    .eq('is_active', true)
    .single();

  if (poolError || !pool) {
    throw new Error('Credit pool not found');
  }

  // Check available credits
  const availableCredits = pool.total_credits - pool.allocated_credits;
  if (data.amount > availableCredits) {
    throw new Error(`Insufficient credits in pool. Available: ${availableCredits}, Requested: ${data.amount}`);
  }

  // Verify API key exists and belongs to same customer
  const { data: apiKey, error: keyError } = await supabase
    .from('api_keys')
    .select('customer_id')
    .eq('id', data.api_key_id)
    .single();

  if (keyError || !apiKey) {
    throw new Error('API key not found');
  }

  if (apiKey.customer_id !== pool.customer_id) {
    throw new Error('API key does not belong to the same customer as the pool');
  }

  // Perform allocation in transaction
  // Update pool allocated credits
  const { error: poolUpdateError } = await supabase
    .from('credit_pools')
    .update({
      allocated_credits: pool.allocated_credits + data.amount,
      updated_at: new Date().toISOString()
    })
    .eq('id', poolId);

  if (poolUpdateError) {
    throw new Error(`Failed to update pool allocation: ${poolUpdateError.message}`);
  }

  // Update API key
  const { error: keyUpdateError } = await supabase
    .from('api_keys')
    .update({
      credit_pool_id: poolId,
      credits_allocated: data.amount,
      updated_at: new Date().toISOString()
    })
    .eq('id', data.api_key_id);

  if (keyUpdateError) {
    // Rollback pool update
    await supabase
      .from('credit_pools')
      .update({
        allocated_credits: pool.allocated_credits,
        updated_at: new Date().toISOString()
      })
      .eq('id', poolId);
    
    throw new Error(`Failed to update API key allocation: ${keyUpdateError.message}`);
  }

  // Log audit event
  await logAuditEvent(
    supabase,
    context,
    'allocate',
    'credit_pool',
    poolId,
    { 
      customer_id: pool.customer_id,
      api_key_id: data.api_key_id,
      amount: data.amount,
      pool_remaining: availableCredits - data.amount
    }
  );

  return {
    pool_id: poolId,
    api_key_id: data.api_key_id,
    allocated_amount: data.amount,
    pool_remaining: availableCredits - data.amount
  };
}

/**
 * Main request handler
 */
serve(async (req: Request) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  let context: AdminContext;

  try {
    // Validate admin authentication
    context = await validateAdminAuth(req);
  } catch (authError) {
    return createResponse(null, authError.message, 401);
  }

  // Initialize Supabase client
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  const supabase = createClient<Database>(supabaseUrl, supabaseKey);

  try {
    const url = new URL(req.url);
    const pathSegments = url.pathname.split('/').filter(Boolean);
    const method = req.method;

    // Route handling
    if (method === 'POST' && pathSegments.length === 2 && pathSegments[1] === 'purchase') {
      // POST /{customer_id}/purchase
      const customerId = pathSegments[0];
      const data: CreditTransactionRequest = await req.json();
      const result = await processCreditTransaction(supabase, context, customerId, 'purchase', data);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 2 && pathSegments[1] === 'refund') {
      // POST /{customer_id}/refund
      const customerId = pathSegments[0];
      const data: CreditTransactionRequest = await req.json();
      const result = await processCreditTransaction(supabase, context, customerId, 'refund', data);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 2 && pathSegments[1] === 'adjust') {
      // POST /{customer_id}/adjust
      const customerId = pathSegments[0];
      const data: CreditTransactionRequest = await req.json();
      const result = await processCreditTransaction(supabase, context, customerId, 'adjustment', data);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 2 && pathSegments[1] === 'deduct') {
      // POST /{customer_id}/deduct
      const customerId = pathSegments[0];
      const data: CreditTransactionRequest = await req.json();
      const result = await processCreditTransaction(supabase, context, customerId, 'deduction', data);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'GET' && pathSegments.length === 2 && pathSegments[1] === 'history') {
      // GET /{customer_id}/history
      const customerId = pathSegments[0];
      const params: CreditHistoryParams = {
        limit: parseInt(url.searchParams.get('limit') || '50'),
        offset: parseInt(url.searchParams.get('offset') || '0'),
        start_date: url.searchParams.get('start_date') || undefined,
        end_date: url.searchParams.get('end_date') || undefined,
        transaction_type: url.searchParams.get('transaction_type') || undefined
      };
      const result = await getCreditHistory(supabase, context, customerId, params);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 1 && pathSegments[0] === 'alerts') {
      // POST /alerts
      const data: CreditAlertRequest = await req.json();
      const result = await configureCreditAlert(supabase, context, data);
      return createResponse(result, null, 201, context.correlationId);

    } else if (method === 'PUT' && pathSegments.length === 2 && pathSegments[0] === 'alerts') {
      // PUT /alerts/{alert_id}
      const alertId = pathSegments[1];
      const data: Partial<CreditAlertRequest> = await req.json();
      const result = await updateCreditAlert(supabase, context, alertId, data);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'GET' && pathSegments.length === 2 && pathSegments[1] === 'alerts') {
      // GET /{customer_id}/alerts
      const customerId = pathSegments[0];
      const result = await listCustomerAlerts(supabase, context, customerId);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 2 && pathSegments[0] === 'alerts' && pathSegments[1] === 'check') {
      // POST /alerts/check
      const data = await req.json();
      const result = await checkCreditAlerts(supabase, context, data.customer_id);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 1 && pathSegments[0] === 'bulk') {
      // POST /bulk
      const data: BulkCreditOperation = await req.json();
      const result = await performBulkCreditOperation(supabase, context, data);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 2 && pathSegments[1] === 'pools') {
      // POST /{customer_id}/pools
      const customerId = pathSegments[0];
      const data: CreditPoolRequest = await req.json();
      const result = await createCreditPool(supabase, context, customerId, data);
      return createResponse(result, null, 201, context.correlationId);

    } else if (method === 'GET' && pathSegments.length === 2 && pathSegments[1] === 'pools') {
      // GET /{customer_id}/pools
      const customerId = pathSegments[0];
      const result = await listCreditPools(supabase, context, customerId);
      return createResponse(result, null, 200, context.correlationId);

    } else if (method === 'POST' && pathSegments.length === 3 && pathSegments[0] === 'pools' && pathSegments[2] === 'allocate') {
      // POST /pools/{pool_id}/allocate
      const poolId = pathSegments[1];
      const data: PoolAllocationRequest = await req.json();
      const result = await allocateFromPool(supabase, context, poolId, data);
      return createResponse(result, null, 200, context.correlationId);

    } else {
      return createResponse(null, `Endpoint not found: ${method} ${url.pathname}`, 404, context.correlationId);
    }

  } catch (error) {
    console.error('Credit management error:', error);
    const statusCode = error.message.includes('not found') ? 404 :
                      error.message.includes('Invalid') ? 400 :
                      error.message.includes('Insufficient') ? 400 : 500;
    
    return createResponse(null, error.message, statusCode, context?.correlationId);
  }
});