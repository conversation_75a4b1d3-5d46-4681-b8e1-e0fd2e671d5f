/**
 * Usage Tracking Edge Function
 * 
 * Main API endpoint for usage tracking and credit management
 * Integrates CreditManager, CostCalculator, and UsageAggregator
 * 
 * Endpoints:
 * POST /usage-tracking/record - Record usage for document processing
 * GET /usage-tracking/analytics - Get usage analytics
 * GET /usage-tracking/billing - Generate billing export
 * GET /usage-tracking/metrics - Get real-time metrics
 * POST /usage-tracking/credits/deduct - Deduct credits
 * POST /usage-tracking/credits/add - Add credits
 * GET /usage-tracking/credits/balance - Get credit balance
 * 
 * GitHub Issue #11 - Epic 2, Story 5: Usage Tracking & Credit System
 */

import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';
import { CreditManager } from '../_shared/credit-manager.ts';
import { CostCalculator } from '../_shared/cost-calculator.ts';
import { UsageAggregator } from '../_shared/usage-aggregator.ts';
import { validate<PERSON><PERSON><PERSON><PERSON>, corsHead<PERSON>, createApiResponse } from '../_shared/api-key-utils.ts';
import type {
  ProcessingResult,
  CustomerContext,
  Database
} from '../../../types/usage-tracking.types.ts';

// Initialize Supabase client
const supabase = createClient<Database>(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Initialize usage tracking components
const creditManager = new CreditManager(supabase);
const costCalculator = new CostCalculator();
const usageAggregator = new UsageAggregator(supabase);

/**
 * Main request handler
 */
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    const pathname = url.pathname;
    const method = req.method;

    // Route requests to appropriate handlers
    if (method === 'POST' && pathname === '/usage-tracking/record') {
      return await handleRecordUsage(req);
    }
    
    if (method === 'GET' && pathname === '/usage-tracking/analytics') {
      return await handleGetAnalytics(req);
    }
    
    if (method === 'GET' && pathname === '/usage-tracking/billing') {
      return await handleGetBilling(req);
    }
    
    if (method === 'GET' && pathname === '/usage-tracking/metrics') {
      return await handleGetMetrics(req);
    }
    
    if (method === 'POST' && pathname === '/usage-tracking/credits/deduct') {
      return await handleDeductCredits(req);
    }
    
    if (method === 'POST' && pathname === '/usage-tracking/credits/add') {
      return await handleAddCredits(req);
    }
    
    if (method === 'GET' && pathname === '/usage-tracking/credits/balance') {
      return await handleGetBalance(req);
    }

    // Route not found
    return createApiResponse(
      { message: 'Endpoint not found', available_endpoints: [
        'POST /usage-tracking/record',
        'GET /usage-tracking/analytics',
        'GET /usage-tracking/billing',
        'GET /usage-tracking/metrics',
        'POST /usage-tracking/credits/deduct',
        'POST /usage-tracking/credits/add',
        'GET /usage-tracking/credits/balance'
      ]},
      404,
      false
    );

  } catch {
    console.error('Usage tracking function error:', error);
    return createApiResponse(
      { message: 'Internal server error', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
});

/**
 * Record usage for document processing
 */
async function handleRecordUsage(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse request body
    const body = await req.json();
    const processingResult: ProcessingResult = body.processingResult;
    const customerContext: CustomerContext = body.customerContext;

    if (!processingResult || !customerContext) {
      return createApiResponse(
        { message: 'Processing result and customer context are required' },
        400,
        false
      );
    }

    // Calculate costs
    const costCalculation = costCalculator.calculateCost(processingResult, customerContext);

    // Check and deduct credits
    const creditCheck = await creditManager.checkCreditLimit(
      keyValidation.keyId!,
      costCalculation.creditsToDeduct
    );

    if (creditCheck.hasInsufficientCredits) {
      return createApiResponse({
        message: 'Insufficient credits',
        required: costCalculation.creditsToDeduct,
        available: creditCheck.currentBalance,
        shortfall: creditCheck.shortfall
      }, 402, false);
    }

    // Deduct credits
    const creditTransaction = await creditManager.deductCredits(
      keyValidation.keyId!,
      costCalculation.creditsToDeduct,
      `Document processing: ${processingResult.model}`,
      {
        document_id: processingResult.documentId,
        agent_id: processingResult.agentId,
        model_used: processingResult.model,
        processing_time_ms: processingResult.processingTimeMs
      }
    );

    // Record detailed usage log
    await supabase.from('usage_logs').insert({
      customer_id: keyValidation.customerId!,
      api_key_id: keyValidation.keyId!,
      document_id: processingResult.documentId,
      operation_type: 'document_processing',
      model_used: processingResult.model,
      input_tokens: processingResult.inputTokens,
      output_tokens: processingResult.outputTokens,
      model_cost: costCalculation.modelCostUsd,
      customer_price: costCalculation.customerPriceUsd,
      profit_margin: costCalculation.profitMarginPercent,
      credits_used: costCalculation.creditsToDeduct,
      processing_time_ms: processingResult.processingTimeMs,
      success: processingResult.success,
      error_message: processingResult.errorMessage,
      metadata: {
        agent_id: processingResult.agentId,
        confidence: processingResult.confidence,
        cost_breakdown: costCalculation.breakdown
      }
    });

    // Check for low profit margin alerts
    const marginAlert = costCalculator.checkProfitMargin(
      costCalculation,
      keyValidation.customerId!,
      processingResult.model
    );

    const response = {
      success: true,
      usage_recorded: true,
      cost_calculation: costCalculation,
      credit_transaction: creditTransaction,
      profit_margin_alert: marginAlert
    };

    return createApiResponse(response);

  } catch {
    console.error('Error recording usage:', error);
    return createApiResponse(
      { message: 'Failed to record usage', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Get usage analytics
 */
async function handleGetAnalytics(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get('start_date');
    const endDate = url.searchParams.get('end_date');
    const period = url.searchParams.get('period') as 'day' | 'week' | 'month' | 'year' || 'day';
    const customerId = url.searchParams.get('customer_id') || keyValidation.customerId;

    if (!startDate || !endDate) {
      return createApiResponse(
        { message: 'start_date and end_date query parameters are required' },
        400,
        false
      );
    }

    const analytics = await usageAggregator.generateUsageAnalytics(
      new Date(startDate),
      new Date(endDate),
      period,
      customerId!
    );

    return createApiResponse(analytics);

  } catch {
    console.error('Error getting analytics:', error);
    return createApiResponse(
      { message: 'Failed to get analytics', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Generate billing export
 */
async function handleGetBilling(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse query parameters
    const url = new URL(req.url);
    const startDate = url.searchParams.get('start_date');
    const endDate = url.searchParams.get('end_date');
    const customerId = url.searchParams.get('customer_id') || keyValidation.customerId;

    if (!startDate || !endDate) {
      return createApiResponse(
        { message: 'start_date and end_date query parameters are required' },
        400,
        false
      );
    }

    const billingExport = await usageAggregator.generateBillingExport(
      customerId!,
      new Date(startDate),
      new Date(endDate)
    );

    return createApiResponse(billingExport);

  } catch {
    console.error('Error generating billing export:', error);
    return createApiResponse(
      { message: 'Failed to generate billing export', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Get real-time metrics
 */
async function handleGetMetrics(req: Request): Promise<Response> {
  try {
    // Validate API key (admin access only)
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Get real-time metrics
    const metrics = await usageAggregator.getRealtimeMetrics();

    return createApiResponse(metrics);

  } catch {
    console.error('Error getting metrics:', error);
    return createApiResponse(
      { message: 'Failed to get metrics', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Deduct credits from API key
 */
async function handleDeductCredits(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse request body
    const body = await req.json();
    const { credits, description = 'Manual credit deduction' } = body;

    if (!credits || credits <= 0) {
      return createApiResponse(
        { message: 'Credits must be a positive number' },
        400,
        false
      );
    }

    const transaction = await creditManager.deductCredits(
      keyValidation.keyId!,
      credits,
      description
    );

    return createApiResponse(transaction);

  } catch {
    console.error('Error deducting credits:', error);
    
    if (error.name === 'InsufficientCreditsError') {
      return createApiResponse(
        {
          message: error.message,
          required: error.required,
          available: error.available,
          shortfall: error.shortfall
        },
        402,
        false
      );
    }

    return createApiResponse(
      { message: 'Failed to deduct credits', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Add credits to API key
 */
async function handleAddCredits(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse request body
    const body = await req.json();
    const { credits, reason = 'Manual credit addition' } = body;

    if (!credits || credits <= 0) {
      return createApiResponse(
        { message: 'Credits must be a positive number' },
        400,
        false
      );
    }

    const transaction = await creditManager.addCredits(
      keyValidation.keyId!,
      credits,
      reason
    );

    return createApiResponse(transaction);

  } catch {
    console.error('Error adding credits:', error);
    return createApiResponse(
      { message: 'Failed to add credits', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Get credit balance for API key
 */
async function handleGetBalance(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Get current balance
    const balance = await creditManager.getCreditBalance(keyValidation.keyId!);

    // Get usage statistics
    const url = new URL(req.url);
    const periodDays = parseInt(url.searchParams.get('period_days') || '30');
    
    const usageStats = await creditManager.getUsageStatistics(
      keyValidation.keyId!,
      periodDays
    );

    const response = {
      current_balance: balance,
      usage_statistics: usageStats,
      api_key_id: keyValidation.keyId,
      customer_id: keyValidation.customerId
    };

    return createApiResponse(response);

  } catch {
    console.error('Error getting balance:', error);
    return createApiResponse(
      { message: 'Failed to get balance', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}