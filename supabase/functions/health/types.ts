/**
 * TypeScript interfaces for health check system
 * Epic 1 Story 5: Health Check & Basic Monitoring
 */

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency_ms: number;
  error?: string;
}

export interface PerformanceMetrics {
  response_time_ms: number;
  active_connections: number;
  edge_function_cold_start: boolean;
}

export interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  services: {
    database: ServiceHealth;
    openai: ServiceHealth;
    claude: ServiceHealth;
    llamaparse: ServiceHealth;
  };
  performance: PerformanceMetrics;
  correlation_id: string;
}

export interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  correlation_id: string;
  timestamp: string;
  metadata?: Record<string, any>;
}