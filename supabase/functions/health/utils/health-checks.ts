/**
 * Health check utilities for monitoring system status
 * Epic 1 Story 5: Health Check & Basic Monitoring
 */

import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';

export interface ServiceHealth {
  status: 'healthy' | 'degraded' | 'unhealthy';
  latency_ms: number;
  error?: string;
}

export interface PerformanceMetrics {
  response_time_ms: number;
  active_connections: number;
  edge_function_cold_start: boolean;
}

/**
 * Checks database connectivity and latency
 */
export async function checkDatabaseHealth(): Promise<ServiceHealth> {
  const start = performance.now();

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || 'http://127.0.0.1:14321';
    const supabaseKey = Deno.env.get('SUPABASE_ANON_KEY') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

    const supabase = createClient(supabaseUrl, supabaseKey);

    // Simple connectivity test - just check if we can connect to our schema
    const { error } = await supabase
      .from('customers')
      .select('id')
      .limit(1);

    const latency = Math.round(performance.now() - start);

    if (error) {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: error.message,
      };
    }

    // Consider healthy if latency < 150ms for local dev, degraded if < 300ms, unhealthy otherwise
    const status = latency < 150 ? 'healthy' : latency < 300 ? 'degraded' : 'unhealthy';

    return {
      status,
      latency_ms: latency,
    };
  } catch {
    return {
      status: 'unhealthy',
      latency_ms: Math.round(performance.now() - start),
      error: error instanceof Error ? error.message : 'Unknown database error',
    };
  }
}

/**
 * Checks OpenAI service connectivity
 */
export async function checkOpenAIHealth(): Promise<ServiceHealth> {
  const start = performance.now();

  try {
    const apiKey = Deno.env.get('OPENAI_API_KEY');

    if (!apiKey) {
      return {
        status: 'degraded',
        latency_ms: Math.round(performance.now() - start),
        error: 'API key not configured',
      };
    }

    // Lightweight health check - just check if we can reach the API
    const response = await fetch('https://api.openai.com/v1/models', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'User-Agent': 'IDP-Platform-Health-Check/1.0',
      },
      signal: AbortSignal.timeout(2000), // 2 second timeout
    });

    const latency = Math.round(performance.now() - start);

    if (!response.ok) {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: `HTTP ${response.status}: ${response.statusText}`,
      };
    }

    return {
      status: 'healthy',
      latency_ms: latency,
    };
  } catch {
    const latency = Math.round(performance.now() - start);

    if (error instanceof DOMException && error.name === 'TimeoutError') {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: 'Request timeout (>2s)',
      };
    }

    return {
      status: 'unhealthy',
      latency_ms: latency,
      error: error instanceof Error ? error.message : 'Unknown OpenAI error',
    };
  }
}

/**
 * Checks Claude (Anthropic) service connectivity
 */
export async function checkClaudeHealth(): Promise<ServiceHealth> {
  const start = performance.now();

  try {
    const apiKey = Deno.env.get('ANTHROPIC_API_KEY');

    if (!apiKey) {
      return {
        status: 'degraded',
        latency_ms: Math.round(performance.now() - start),
        error: 'API key not configured',
      };
    }

    // Lightweight health check for Anthropic Claude API
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
        'User-Agent': 'IDP-Platform-Health-Check/1.0',
      },
      body: JSON.stringify({
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'ping' }],
      }),
      signal: AbortSignal.timeout(2000), // 2 second timeout
    });

    const latency = Math.round(performance.now() - start);

    // Claude returns 400 for invalid requests, but that means the service is reachable
    if (response.status === 401) {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: 'Authentication failed - invalid API key',
      };
    }

    // Any response (including 400) indicates the service is reachable
    return {
      status: 'healthy',
      latency_ms: latency,
    };
  } catch {
    const latency = Math.round(performance.now() - start);

    if (error instanceof DOMException && error.name === 'TimeoutError') {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: 'Request timeout (>2s)',
      };
    }

    return {
      status: 'unhealthy',
      latency_ms: latency,
      error: error instanceof Error ? error.message : 'Unknown Claude error',
    };
  }
}

/**
 * Checks LlamaParse service connectivity
 */
export async function checkLlamaParseHealth(): Promise<ServiceHealth> {
  const start = performance.now();

  try {
    const apiKey = Deno.env.get('LLAMAPARSE_API_KEY');

    if (!apiKey) {
      return {
        status: 'degraded',
        latency_ms: Math.round(performance.now() - start),
        error: 'API key not configured',
      };
    }

    // Lightweight health check for LlamaParse
    const response = await fetch('https://api.llamaindex.ai/api/parsing/upload', {
      method: 'GET', // Just check if endpoint is reachable
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'User-Agent': 'IDP-Platform-Health-Check/1.0',
      },
      signal: AbortSignal.timeout(2000), // 2 second timeout
    });

    const latency = Math.round(performance.now() - start);

    if (response.status === 401) {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: 'Authentication failed - invalid API key',
      };
    }

    // Method not allowed (405) or other responses indicate service is reachable
    return {
      status: 'healthy',
      latency_ms: latency,
    };
  } catch {
    const latency = Math.round(performance.now() - start);

    if (error instanceof DOMException && error.name === 'TimeoutError') {
      return {
        status: 'unhealthy',
        latency_ms: latency,
        error: 'Request timeout (>2s)',
      };
    }

    return {
      status: 'unhealthy',
      latency_ms: latency,
      error: error instanceof Error ? error.message : 'Unknown LlamaParse error',
    };
  }
}

/**
 * Determines overall system status based on individual service health
 */
export function determineOverallStatus(services: Record<string, ServiceHealth>): 'healthy' | 'degraded' | 'unhealthy' {
  // Database is critical - if it's down, system is unhealthy
  if (services.database?.status === 'unhealthy') {
    return 'unhealthy';
  }

  // Check if all services are healthy
  const allServicesHealthy = Object.values(services).every(service => service.status === 'healthy');

  if (allServicesHealthy) {
    return 'healthy';
  }

  // If database is healthy but some AI services are down, system is degraded
  return 'degraded';
}

/**
 * Collects performance metrics
 */
export function collectPerformanceMetrics(startTime: number, coldStart = false): PerformanceMetrics {
  return {
    response_time_ms: Math.round(performance.now() - startTime),
    active_connections: 1, // Edge Functions are stateless, so always 1 for current request
    edge_function_cold_start: coldStart,
  };
}