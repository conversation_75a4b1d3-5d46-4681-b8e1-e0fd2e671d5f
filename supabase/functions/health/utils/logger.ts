/**
 * Structured logging framework for health monitoring
 * Epic 1 Story 5: Health Check & Basic Monitoring
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
  level: LogLevel;
  message: string;
  correlation_id: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Creates a structured log entry
 */
function createLogEntry(
  level: LogLevel,
  message: string,
  correlationId: string,
  metadata: Record<string, any> = {}
): LogEntry {
  return {
    level,
    message,
    correlation_id: correlationId,
    timestamp: new Date().toISOString(),
    metadata,
  };
}

/**
 * Outputs structured log to console
 */
function outputLog(entry: LogEntry): void {
  const logString = JSON.stringify(entry);

  // Use appropriate console method based on level
  switch (entry.level) {
    case 'error': {
      }
      console.error(logString);
      break;
    }
    case 'warn': {
      }
      console.warn(logString);
      break;
    }
    case 'debug': {
      }
      console.debug(logString);
      break;
    }
    default:
      console.log(logString);
  }
}

/**
 * Structured logger with correlation ID support
 */
export class Logger {
  constructor(private defaultCorrelationId: string) {}

  debug(message: string, metadata: Record<string, any> = {}, correlationId?: string): void {
    const entry = createLogEntry('debug', message, correlationId || this.defaultCorrelationId, metadata);
    outputLog(entry);
  }

  info(message: string, metadata: Record<string, any> = {}, correlationId?: string): void {
    const entry = createLogEntry('info', message, correlationId || this.defaultCorrelationId, metadata);
    outputLog(entry);
  }

  warn(message: string, metadata: Record<string, any> = {}, correlationId?: string): void {
    const entry = createLogEntry('warn', message, correlationId || this.defaultCorrelationId, metadata);
    outputLog(entry);
  }

  error(message: string, metadata: Record<string, any> = {}, correlationId?: string): void {
    const entry = createLogEntry('error', message, correlationId || this.defaultCorrelationId, metadata);
    outputLog(entry);
  }
}

/**
 * Creates a logger instance with correlation ID
 */
export function createLogger(correlationId: string): Logger {
  return new Logger(correlationId);
}