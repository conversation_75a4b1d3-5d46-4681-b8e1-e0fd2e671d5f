/**
 * Correlation ID utilities for request tracing
 * Epic 1 Story 5: Health Check & Basic Monitoring
 */

/**
 * Generates a unique correlation ID for request tracing
 * Format: req_${timestamp}_${random}
 * Example: req_1726958400123_h3x9k2m5p
 */
export function generateCorrelationId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `req_${timestamp}_${random}`;
}

/**
 * Validates correlation ID format
 */
export function isValidCorrelationId(id: string): boolean {
  return /^req_\d+_[a-z0-9]+$/.test(id);
}

/**
 * Extracts timestamp from correlation ID
 */
export function getTimestampFromCorrelationId(id: string): number | null {
  const match = id.match(/^req_(\d+)_[a-z0-9]+$/);
  return match ? parseInt(match[1], 10) : null;
}