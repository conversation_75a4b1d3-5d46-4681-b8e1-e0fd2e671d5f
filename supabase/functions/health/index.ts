/**
 * Comprehensive Health Check & Basic Monitoring
 * Epic 1 Story 5: Health Check & Basic Monitoring
 *
 * Provides comprehensive health checks with:
 * - Database connectivity monitoring
 * - AI service connectivity (OpenAI, Claude, LlamaParse)
 * - Performance metrics collection
 * - Correlation ID system for request tracing
 * - Structured logging framework
 * - <500ms response time requirement
 *
 * Authentication: PUBLIC (no API key required)
 * Rationale: Health checks must be accessible to load balancers, monitoring systems,
 * and operational tools without authentication barriers. Follows industry standards.
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Import utility functions
import { generateCorrelationId } from './utils/correlation-id.ts';
import { createLogger } from './utils/logger.ts';
import {
  checkDatabaseHealth,
  checkOpenAIHealth,
  checkClaudeHealth,
  checkLlamaParseHealth,
  determineOverallStatus,
  collectPerformanceMetrics,
} from './utils/health-checks.ts';
import type { HealthResponse } from './types.ts';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  // Support both apikey (our IDP Platform standard) and authorization (for compatibility)
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type, x-correlation-id',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
};

// Track cold starts
let isFirstRequest = true;

Deno.serve(async (req: Request): Promise<Response> => {
  const requestStartTime = performance.now();
  const correlationId = generateCorrelationId();
  const logger = createLogger(correlationId);
  const isColdStart = isFirstRequest;
  isFirstRequest = false;

  logger.info('Health check request received', {
    method: req.method,
    url: req.url,
    cold_start: isColdStart,
  });

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    logger.debug('CORS preflight request');
    return new Response(null, {
      status: 200,
      headers: {
        ...corsHeaders,
        'X-Correlation-ID': correlationId,
      },
    });
  }

  if (req.method !== 'GET') {
    logger.warn('Method not allowed', { method: req.method });
    return new Response(
      JSON.stringify({
        error: 'Method not allowed',
        correlation_id: correlationId,
      }),
      {
        status: 405,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
          'X-Correlation-ID': correlationId,
        },
      }
    );
  }

  try {
    // Run all health checks in parallel for faster response
    logger.info('Starting health checks');

    const [databaseHealth, openaiHealth, claudeHealth, llamaparseHealth] = await Promise.all([
      checkDatabaseHealth(),
      checkOpenAIHealth(),
      checkClaudeHealth(),
      checkLlamaParseHealth(),
    ]);

    const services = {
      database: databaseHealth,
      openai: openaiHealth,
      claude: claudeHealth,
      llamaparse: llamaparseHealth,
    };

    const overallStatus = determineOverallStatus(services);
    const performance = collectPerformanceMetrics(requestStartTime, isColdStart);

    const healthResponse: HealthResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      services,
      performance,
      correlation_id: correlationId,
    };

    logger.info('Health check completed', {
      status: overallStatus,
      response_time_ms: performance.response_time_ms,
      services_status: Object.fromEntries(
        Object.entries(services).map(([name, health]) => [name, health.status])
      ),
    });

    // Return appropriate HTTP status based on health
    const httpStatus = overallStatus === 'healthy' ? 200 :
                      overallStatus === 'degraded' ? 200 : 503;

    return new Response(
      JSON.stringify(healthResponse),
      {
        status: httpStatus,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
          'X-Correlation-ID': correlationId,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      }
    );
  } catch {
    const performance = collectPerformanceMetrics(requestStartTime, isColdStart);

    logger.error('Health check failed with unhandled error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      response_time_ms: performance.response_time_ms,
    });

    return new Response(
      JSON.stringify({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        error: error instanceof Error ? error.message : 'Unknown error',
        correlation_id: correlationId,
        performance,
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json',
          'X-Correlation-ID': correlationId,
        },
      }
    );
  }
});