// ================================================================================
// TYPES AND INTERFACES
// ================================================================================

export interface CustomizeAgentRequest {
  name?: string;
  description?: string;
  system_prompt?: string;
  output_schema?: JSONSchema;
  processing_config?: ProcessingConfig;
  preview_mode?: boolean;
  save_as_version?: string;
}

export interface ProcessingConfig {
  confidence_threshold?: number;
  retry_attempts?: number;
  model_preference?: string[];
  timeout_seconds?: number;
}

export interface JSONSchema {
  type: string;
  properties?: Record<string, JSONSchemaProperty>;
  required?: string[];
  additionalProperties?: boolean;
}

export interface JSONSchemaProperty {
  type: string;
  description?: string;
  enum?: string[];
  items?: JSONSchemaProperty;
  properties?: Record<string, JSONSchemaProperty>;
  required?: string[];
}

export interface ValidationResult {
  level: 'error' | 'warning' | 'info';
  field: string;
  message: string;
  suggestion: string;
}

export interface PreviewResult {
  document_id: string;
  document_type: string;
  success: boolean;
  processing_time_ms?: number;
  schema_valid?: boolean;
  extracted_data?: Record<string, unknown>;
  confidence_score?: number;
  error?: string;
  comparison_with_original?: Record<string, unknown>;
}

export interface CustomizeAgentResponse {
  success: boolean;
  agent?: Record<string, unknown>;
  validation_results: ValidationResult[];
  preview_results?: PreviewResult[];
  version_created?: string;
  error?: string;
}

export interface ApiKeyValidation {
  customerId: string;
  keyType: string;
  credits: number;
}

export interface BatchValidationResult {
  customerInfo: ApiKeyValidation;
  agentInfo?: Record<string, unknown>;
}

export interface PreviewCacheEntry {
  key: string;
  results: PreviewResult[];
  timestamp: number;
  expiry: number;
}

export interface PerformanceMetric {
  operation: string;
  duration_ms: number;
  timestamp: string;
  metadata?: Record<string, unknown>;
  customer_id?: string;
}