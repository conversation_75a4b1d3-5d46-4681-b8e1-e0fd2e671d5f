// ================================================================================
// HANDLER FUNCTIONS
// ================================================================================

import { supabase } from '../_shared/supabase.ts';
import { corsHeaders } from '../_shared/cors.ts';
import {
  CustomizeAgentRequest,
  CustomizeAgentResponse,
  ValidationResult,
} from './types.ts';
import {
  validateApiKey,
  validateApiKeyAndAgent,
  validateAgentOwnership,
  validateCustomPrompt,
  validateSchemaCustomization,
  validateProcessingConfig,
} from './validation.ts';
import {
  previewCustomization,
  applyCustomization,
  createAgentVersion,
  logPerformanceMetric,
  generateUseCases,
  calculateAccuracyRating,
  calculateAvgProcessingTime,
} from './utils.ts';

/**
 * Handle GET /agents - List available agents with pagination and filtering
 */
export async function handleListAgents(req: Request): Promise<Response> {
  const authHeader = req.headers.get('Authorization') || req.headers.get('apikey') || req.headers.get('x-api-key');
  const { customerId } = await validateApiKey(authHeader);

  // Parse query parameters
  const url = new URL(req.url);
  const page = Math.max(1, parseInt(url.searchParams.get('page') || '1') || 1);
  const limit = Math.min(Math.max(1, parseInt(url.searchParams.get('limit') || '10') || 10), 100); // Cap at 100
  const category = url.searchParams.get('category');
  const search = url.searchParams.get('search');
  const isDefault = url.searchParams.get('is_default');
  const status = url.searchParams.get('status') || 'active';

  // Build database query with access control - customer can see default + their own agents
  let query = supabase
    .from('agents')
    .select(`
      id,
      agent_id,
      name,
      category,
      description,
      is_default,
      customer_id,
      parent_agent_id:cloned_from,
      status,
      processing_config,
      performance_stats,
      usage_count,
      last_used_at,
      created_at,
      updated_at
    `, { count: 'exact' })
    .or(`is_default.eq.true,customer_id.eq.${customerId}`)
    .eq('status', status)
    .order('created_at', { ascending: false });

  // Apply filters
  if (category) {
    query = query.eq('category', category);
  }
  
  if (isDefault !== null) {
    query = query.eq('is_default', isDefault === 'true');
  }

  // Apply pagination
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  // Execute query
  const { data: agents, count, error } = await query;

  if (error) {
    console.error('Database error fetching agents:', error);
    throw new Error(`Failed to fetch agents: ${error.message}`);
  }

  // Apply search filter if provided (post-query for relevance scoring)
  let filteredAgents = agents || [];
  if (search) {
    filteredAgents = applySearchFilter(filteredAgents, search);
  }

  // Transform agents to match required response format
  const responseAgents = filteredAgents.map(agent => ({
    id: agent.agent_id,
    name: agent.name,
    category: agent.category,
    version: agent.processing_config?.version || "1.0.0",
    description: agent.description || `Extracts structured data from ${agent.category} documents`,
    is_default: agent.is_default,
    customer_id: agent.customer_id,
    parent_agent_id: agent.parent_agent_id,
    use_cases: generateUseCases(agent.category),
    supported_formats: ["pdf", "image", "docx"],
    accuracy_rating: calculateAccuracyRating(agent.performance_stats),
    avg_processing_time_ms: calculateAvgProcessingTime(agent.performance_stats),
    created_at: agent.created_at,
    updated_at: agent.updated_at
  }));

  // Calculate pagination metadata
  const totalPages = Math.ceil((count || 0) / limit);

  // Build response
  return new Response(JSON.stringify({
    agents: responseAgents,
    pagination: {
      current_page: page,
      total_pages: totalPages,
      total_count: count || 0,
      page_size: limit
    },
    filters_applied: {
      category: category || null,
      is_default: isDefault !== null ? (isDefault === 'true') : null,
      search: search || null,
      status
    }
  }), {
    status: 200,
    headers: { 
      'Content-Type': 'application/json', 
      'Cache-Control': 'max-age=300', // 5 minute cache as required
      ...corsHeaders 
    }
  });
}

/**
 * Apply search filter with relevance scoring
 */
function applySearchFilter(agents: Record<string, unknown>[], searchTerm: string): Record<string, unknown>[] {
  const lowercaseSearch = searchTerm.toLowerCase();
  
  return agents
    .map(agent => ({
      ...agent,
      relevanceScore: calculateRelevanceScore(agent, lowercaseSearch)
    }))
    .filter(agent => agent.relevanceScore > 0)
    .sort((a, b) => (b.relevanceScore as number) - (a.relevanceScore as number))
    .map(({ relevanceScore: _relevanceScore, ...agent }) => agent);
}

/**
 * Calculate relevance score for search results
 */
function calculateRelevanceScore(agent: Record<string, unknown>, searchTerm: string): number {
  let score = 0;
  
  // Exact matches get highest score
  if ((agent.name as string)?.toLowerCase().includes(searchTerm)) {
    score += 10;
  }
  
  if ((agent.description as string)?.toLowerCase().includes(searchTerm)) {
    score += 8;
  }
  
  if ((agent.category as string)?.toLowerCase().includes(searchTerm)) {
    score += 6;
  }
  
  // Use cases matching
  const useCases = generateUseCases(agent.category as string);
  if (useCases.some((useCase: string) => useCase.toLowerCase().includes(searchTerm))) {
    score += 4;
  }
  
  return score;
}

/**
 * Handle GET /agents/{id} - Get agent details
 */
export async function handleGetAgentDetails(req: Request, agentId: string): Promise<Response> {
  const authHeader = req.headers.get('Authorization') || req.headers.get('apikey') || req.headers.get('x-api-key');
  const { customerId } = await validateApiKey(authHeader);

  // Get agent with access control
  const { data: agent, error } = await supabase
    .from('agents')
    .select(`
      id,
      agent_id,
      name,
      category,
      description,
      system_prompt,
      output_schema,
      processing_config,
      is_default,
      customer_id,
      parent_agent_id:cloned_from,
      status,
      performance_stats,
      usage_count,
      last_used_at,
      created_at,
      updated_at,
      last_customized_at,
      last_customized_by
    `)
    .eq('agent_id', agentId)
    .or(`is_default.eq.true,customer_id.eq.${customerId}`)
    .single();

  if (error || !agent) {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Agent not found or access denied'
      }),
      { 
        status: 404, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }

  // Transform to match required response format
  const responseAgent = {
    id: agent.agent_id,
    name: agent.name,
    category: agent.category,
    version: agent.processing_config?.version || "1.0.0",
    description: agent.description || `Extracts structured data from ${agent.category} documents`,
    system_prompt: agent.system_prompt,
    output_schema: agent.output_schema,
    processing_config: agent.processing_config,
    is_default: agent.is_default,
    customer_id: agent.customer_id,
    parent_agent_id: agent.parent_agent_id,
    use_cases: generateUseCases(agent.category),
    supported_formats: ["pdf", "image", "docx"],
    accuracy_rating: calculateAccuracyRating(agent.performance_stats),
    avg_processing_time_ms: calculateAvgProcessingTime(agent.performance_stats),
    usage_count: agent.usage_count || 0,
    last_used_at: agent.last_used_at,
    created_at: agent.created_at,
    updated_at: agent.updated_at,
    last_customized_at: agent.last_customized_at,
    last_customized_by: agent.last_customized_by
  };

  return new Response(JSON.stringify({
    agent: responseAgent
  }), {
    status: 200,
    headers: { 
      'Content-Type': 'application/json',
      'Cache-Control': 'max-age=300', // 5 minute cache
      ...corsHeaders 
    }
  });
}

/**
 * Handle PUT /agents/{id} - Agent customization
 */
export async function handleAgentCustomization(req: Request, agentId: string): Promise<Response> {
  const operationStartTime = performance.now();
  
  try {
    // OPTIMIZATION 1: Batched validation - single DB query for API key + agent
    const authHeader = req.headers.get('Authorization') || req.headers.get('apikey') || req.headers.get('x-api-key');
    const batchResult = await validateApiKeyAndAgent(authHeader, agentId);
    const { customerId } = batchResult.customerInfo;
    
    // Parse request body
    const customization: CustomizeAgentRequest = await req.json();
    
    // Use batched agent info or fall back to separate query if needed
    let agent = batchResult.agentInfo;
    if (!agent) {
      // Fallback if batched query didn't include agent info
      agent = await validateAgentOwnership(agentId, customerId);
    }

  // Validate customizations
  const validationResults: ValidationResult[] = [];

  if (customization.system_prompt) {
    const promptValidation = await validateCustomPrompt(customization.system_prompt, agent.category);
    validationResults.push(...promptValidation);
  }

  if (customization.output_schema) {
    const schemaValidation = await validateSchemaCustomization(
      agent.output_schema,
      customization.output_schema,
      agent.category
    );
    validationResults.push(...schemaValidation);
  }

  if (customization.processing_config) {
    const configValidation = await validateProcessingConfig(customization.processing_config);
    validationResults.push(...configValidation);
  }

  // Check for blocking errors
  const hasErrors = validationResults.some(r => r.level === 'error');

  if (hasErrors && !customization.preview_mode) {
    return new Response(
      JSON.stringify({
        success: false,
        validation_results: validationResults,
        error: 'Validation errors must be resolved before saving'
      } as CustomizeAgentResponse),
      { 
        status: 400, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }

  // Handle preview mode with OPTIMIZATION 3: Caching
  if (customization.preview_mode) {
    const previewResults = await previewCustomization(agent, customization, customerId);
    
    return new Response(
      JSON.stringify({
        success: true,
        agent: agent,
        validation_results: validationResults,
        preview_results: previewResults
      } as CustomizeAgentResponse),
      { 
        status: 200, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }

  // Apply customizations
  const customizedAgent = await applyCustomization(agent, customization, customerId);

  // Create version if requested
  let versionCreated: string | undefined;
  if (customization.save_as_version) {
    versionCreated = await createAgentVersion(
      agentId,
      customization.save_as_version,
      customerId,
      customization
    );
  }

  // Log overall performance metrics
    const totalOperationTime = performance.now() - operationStartTime;
    await logPerformanceMetric('agent_customization_complete', totalOperationTime, {
      agent_id: agentId,
      customer_id: customerId,
      preview_mode: customization.preview_mode || false,
      has_version: !!customization.save_as_version,
      validation_errors: validationResults.filter(r => r.level === 'error').length
    });

    return new Response(
      JSON.stringify({
        success: true,
        agent: customizedAgent,
        validation_results: validationResults,
        version_created: versionCreated
      } as CustomizeAgentResponse),
      { 
        status: 200, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  } catch {
    // Log performance metrics for failed operations too
    const totalOperationTime = performance.now() - operationStartTime;
    await logPerformanceMetric('agent_customization_error', totalOperationTime, {
      agent_id: agentId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Handle GET /agents/{id}/versions - Get agent versions
 */
export async function handleGetAgentVersions(req: Request, agentId: string): Promise<Response> {
  const authHeader = req.headers.get('Authorization') || req.headers.get('apikey') || req.headers.get('x-api-key');
  const { customerId } = await validateApiKey(authHeader);

  // Validate agent access
  await validateAgentOwnership(agentId, customerId);

  // Get versions
  const { data: versions, error } = await supabase
    .from('agent_versions')
    .select('*')
    .eq('agent_id', agentId)
    .order('created_at', { ascending: false });

  if (error) {
    throw new Error(`Failed to fetch versions: ${error.message}`);
  }

  return new Response(
    JSON.stringify({
      success: true,
      versions: versions || []
    }),
    { 
      status: 200, 
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    }
  );
}

/**
 * Handle POST /agents/{id}/rollback - Agent rollback
 */
export async function handleAgentRollback(req: Request, agentId: string): Promise<Response> {
  const authHeader = req.headers.get('Authorization') || req.headers.get('apikey') || req.headers.get('x-api-key');
  const { customerId } = await validateApiKey(authHeader);

  // Validate agent access
  await validateAgentOwnership(agentId, customerId);

  const { version_id } = await req.json();
  
  if (!version_id) {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'version_id is required'
      }),
      { 
        status: 400, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }

  // Perform rollback
  const { data: _success, error } = await supabase.rpc(
    'rollback_agent_to_version',
    {
      p_agent_id: agentId,
      p_version_id: version_id,
      p_customer_id: customerId
    }
  );

  if (error) {
    throw new Error(`Rollback failed: ${error.message}`);
  }

  // Get updated agent
  const { data: agent } = await supabase
    .from('agents')
    .select('*')
    .eq('id', agentId)
    .single();

  return new Response(
    JSON.stringify({
      success: true,
      agent: agent,
      message: `Successfully rolled back to version ${version_id}`
    }),
    { 
      status: 200, 
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    }
  );
}