// ================================================================================
// AGENTS API - MAIN ENTRY POINT
// ================================================================================
// Epic 3, Story 4: Agent Customization System
// 
// This is the main entry point for the agents API, providing:
// - Agent listing and details
// - Agent customization with validation
// - Version control and rollback
// - Preview mode for testing changes
//
// Architecture: Clean, modular design with separated concerns:
// - types.ts: TypeScript interfaces and types
// - validation.ts: API key and input validation
// - handlers.ts: HTTP request handlers  
// - utils.ts: Business logic and utilities
// ================================================================================

import { corsHeaders } from '../_shared/cors.ts';
import {
  handleListAgents,
  handleGetAgentDetails,
  handleAgentCustomization,
  handleGetAgentVersions,
  handleAgentRollback,
} from './handlers.ts';

// ================================================================================
// MAIN REQUEST HANDLER
// ================================================================================

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  try {
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);

    // Route: GET /agents - List available agents
    if (req.method === 'GET' && pathParts.length === 1 && pathParts[0] === 'agents') {
      return await handleListAgents(req);
    }

    // Route: GET /agents/{id} - Get agent details
    if (req.method === 'GET' && pathParts.length === 2 && pathParts[0] === 'agents') {
      const agentId = pathParts[1];
      return await handleGetAgentDetails(req, agentId);
    }

    // Route: PUT /agents/{id} - Customize agent
    if (req.method === 'PUT' && pathParts.length >= 2 && pathParts[pathParts.length - 2] === 'agents') {
      const agentId = pathParts[pathParts.length - 1];
      return await handleAgentCustomization(req, agentId);
    }

    // Route: GET /agents/{id}/versions - List agent versions
    if (req.method === 'GET' && pathParts.includes('versions')) {
      const agentId = pathParts[pathParts.indexOf('agents') + 1];
      return await handleGetAgentVersions(req, agentId);
    }

    // Route: POST /agents/{id}/rollback - Rollback to version
    if (req.method === 'POST' && pathParts.includes('rollback')) {
      const agentId = pathParts[pathParts.indexOf('agents') + 1];
      return await handleAgentRollback(req, agentId);
    }

    // 404 - Route not found
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Endpoint not found',
        available_endpoints: [
          'GET /agents - List available agents',
          'GET /agents/{id} - Get agent details',
          'PUT /agents/{id} - Customize agent',
          'GET /agents/{id}/versions - List agent versions',
          'POST /agents/{id}/rollback - Rollback to version'
        ]
      }),
      { 
        status: 404, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );

  } catch (error) {
    console.error('Handler error:', error);
    const status = (error as { status?: number })?.status || 500;
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      }),
      { 
        status: status, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      }
    );
  }
});