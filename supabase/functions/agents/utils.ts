// ================================================================================
// UTILITY FUNCTIONS
// ================================================================================

import { supabase } from '../_shared/supabase.ts';
import {
  PerformanceMetric,
  PreviewResult,
  PreviewCacheEntry,
  CustomizeAgentRequest,
  JSONSchema,
} from './types.ts';

// ================================================================================
// PREVIEW CACHE CLASS
// ================================================================================

class PreviewCache {
  private cache = new Map<string, PreviewCacheEntry>();
  private readonly TTL_MS = 10 * 60 * 1000; // 10 minutes
  private readonly MAX_ENTRIES = 100;

  /**
   * Generate cache key from customization parameters
   */
  private generateCacheKey(
    agentId: string, 
    customization: CustomizeAgentRequest, 
    customerId: string
  ): string {
    const keyData = {
      agentId,
      customerId,
      prompt: customization.system_prompt?.substring(0, 100), // First 100 chars
      schema: JSON.stringify(customization.output_schema),
      config: JSON.stringify(customization.processing_config)
    };
    return btoa(JSON.stringify(keyData)).substring(0, 32);
  }

  /**
   * Get cached preview results if available and not expired
   */
  get(agentId: string, customization: CustomizeAgentRequest, customerId: string): PreviewResult[] | null {
    const key = this.generateCacheKey(agentId, customization, customerId);
    const entry = this.cache.get(key);

    if (!entry) {
      return null;
    }

    // Check if expired
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    console.log(`[CACHE-HIT] Preview cache hit for agent ${agentId}`);
    return entry.results;
  }

  /**
   * Store preview results in cache
   */
  set(
    agentId: string, 
    customization: CustomizeAgentRequest, 
    customerId: string, 
    results: PreviewResult[]
  ): void {
    const key = this.generateCacheKey(agentId, customization, customerId);
    const now = Date.now();

    // Evict expired entries and maintain size limit
    this.cleanup();

    const entry: PreviewCacheEntry = {
      key,
      results,
      timestamp: now,
      expiry: now + this.TTL_MS
    };

    this.cache.set(key, entry);
    console.log(`[CACHE-SET] Cached preview results for agent ${agentId}, cache size: ${this.cache.size}`);
  }

  /**
   * Clean up expired entries and enforce size limits
   */
  private cleanup(): void {
    const now = Date.now();
    
    // Remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
      }
    }

    // If still over limit, remove oldest entries
    if (this.cache.size >= this.MAX_ENTRIES) {
      const entries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, entries.length - this.MAX_ENTRIES + 1);
      for (const [key] of toRemove) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Clear all cache entries (useful for testing)
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; hitRate: number } {
    return {
      size: this.cache.size,
      hitRate: 0 // TODO: Track hit rate over time
    };
  }
}

// Global cache instance
const previewCache = new PreviewCache();

// ================================================================================
// PERFORMANCE UTILITIES
// ================================================================================

/**
 * Log performance metrics for monitoring and optimization
 */
export async function logPerformanceMetric(
  operation: string, 
  duration_ms: number, 
  metadata: Record<string, unknown> = {}
): Promise<void> {
  try {
    const metric: PerformanceMetric = {
      operation,
      duration_ms: Math.round(duration_ms * 100) / 100, // Round to 2 decimals
      timestamp: new Date().toISOString(),
      metadata,
      customer_id: metadata.customer_id as string
    };

    // Log to console for immediate debugging
    console.log(`[PERF] ${operation}: ${duration_ms.toFixed(2)}ms`, metadata);

    // Store in database for historical analysis (if table exists)
    try {
      await supabase
        .from('performance_metrics')
        .insert([{
          operation: metric.operation,
          duration_ms: metric.duration_ms,
          timestamp: metric.timestamp,
          metadata: metric.metadata,
          customer_id: metric.customer_id
        }]);
    } catch (dbError) {
      // Table may not exist yet - that's OK, we still have console logs
      console.debug('Performance metrics table not available:', dbError);
    }

    // Real-time alerting for slow operations
    if (duration_ms > 2000) { // Alert on operations > 2 seconds
      console.warn(`[PERF-ALERT] Slow operation detected: ${operation} took ${duration_ms.toFixed(2)}ms`);
    }
  } catch {
    // Don't let metrics logging break the main flow
    console.error('Failed to log performance metric:', error);
  }
}

// ================================================================================
// AGENT UTILITIES
// ================================================================================

/**
 * Generate use cases based on category
 */
export function generateUseCases(category: string): string[] {
  const useCaseMap: Record<string, string[]> = {
    invoice: ["accounts payable", "expense tracking", "vendor management", "tax preparation"],
    receipt: ["expense reports", "reimbursements", "personal finance", "business accounting"],
    contract: ["legal review", "compliance tracking", "term extraction", "renewal management"],
    insurance: ["claims processing", "policy management", "risk assessment", "compliance"],
    general: ["document processing", "data extraction", "content analysis"]
  };
  
  return useCaseMap[category] || useCaseMap.general;
}

/**
 * Calculate accuracy rating from performance stats
 */
export function calculateAccuracyRating(performanceStats: Record<string, unknown>): number {
  if (!performanceStats || typeof performanceStats !== 'object') {
    return 0.85; // Default rating
  }
  
  const accuracy = (performanceStats.accuracy_rate as number) || (performanceStats.avg_confidence as number) || 0.85;
  return Math.max(0, Math.min(1, accuracy));
}

/**
 * Calculate average processing time from performance stats
 */
export function calculateAvgProcessingTime(performanceStats: Record<string, unknown>): number {
  if (!performanceStats || typeof performanceStats !== 'object') {
    return 2400; // Default 2.4 seconds
  }
  
  return (performanceStats.avg_processing_time_ms as number) || (performanceStats.processing_time as number) || 2400;
}

// ================================================================================
// PREVIEW UTILITIES
// ================================================================================

/**
 * Preview agent customization with test documents
 */
export async function previewCustomization(
  agent: Record<string, unknown>,
  customization: CustomizeAgentRequest,
  customerId?: string
): Promise<PreviewResult[]> {
  const startTime = performance.now();

  // Check cache first if customer ID is available
  if (customerId) {
    const cachedResults = previewCache.get(agent.id as string, customization, customerId);
    if (cachedResults) {
      await logPerformanceMetric('preview_cache_hit', performance.now() - startTime, {
        agent_id: agent.id,
        customer_id: customerId
      });
      return cachedResults;
    }
  }

  // Create temporary agent configuration for preview
  const previewAgent = {
    ...agent,
    system_prompt: customization.system_prompt || agent.system_prompt,
    output_schema: customization.output_schema || agent.output_schema,
    processing_config: { ...agent.processing_config, ...customization.processing_config }
  };

  // Get test documents for agent category
  const testDocuments = await getTestDocuments(agent.category as string, 3);
  const results: PreviewResult[] = [];

  for (const testDoc of testDocuments) {
    try {
      const docStartTime = performance.now();
      
      // Simulate document processing (in real implementation, would call AI model)
      const mockResult = await simulateDocumentProcessing(testDoc.content, previewAgent);
      
      const processingTime = performance.now() - docStartTime;

      // Validate result against schema
      const schemaValid = await validateAgainstSchema(mockResult, previewAgent.output_schema as JSONSchema);

      results.push({
        document_id: testDoc.id,
        document_type: testDoc.type,
        success: true,
        processing_time_ms: Math.round(processingTime),
        schema_valid: schemaValid,
        extracted_data: mockResult,
        confidence_score: calculateConfidenceScore(mockResult),
        comparison_with_original: testDoc.expected_output 
          ? compareResults(mockResult, testDoc.expected_output)
          : undefined
      });

    } catch {
      results.push({
        document_id: testDoc.id,
        document_type: testDoc.type,
        success: false,
        error: (error as Error).message,
        processing_time_ms: 0,
        schema_valid: false
      });
    }
  }

  // Cache results if customer ID is available
  if (customerId) {
    previewCache.set(agent.id as string, customization, customerId, results);
  }

  await logPerformanceMetric('preview_customization_complete', performance.now() - startTime, {
    agent_id: agent.id,
    customer_id: customerId,
    document_count: results.length
  });

  return results;
}

/**
 * Get test documents for preview functionality
 */
async function getTestDocuments(category: string, count: number = 3): Promise<Record<string, unknown>[]> {
  // In real implementation, would fetch from database or generate test data
  const mockDocuments: Record<string, Record<string, unknown>[]> = {
    invoice: [
      {
        id: 'test-invoice-1',
        type: 'invoice',
        content: 'INVOICE #INV-2025-001\nFrom: ACME Corp\n123 Business St\nTotal: $1,234.56\nDate: 2025-01-15',
        expected_output: {
          vendor: { name: 'ACME Corp', address: '123 Business St' },
          invoice: { number: 'INV-2025-001', date: '2025-01-15' },
          financial: { total_amount: 1234.56 }
        }
      }
    ],
    receipt: [
      {
        id: 'test-receipt-1',
        type: 'receipt',
        content: 'Store Receipt\nWalmart #1234\nTotal: $45.67\nDate: 2025-01-15 14:30',
        expected_output: {
          merchant: { name: 'Walmart #1234' },
          transaction: { date: '2025-01-15' },
          payment: { total: 45.67 }
        }
      }
    ]
  };

  return (mockDocuments[category] || mockDocuments.invoice).slice(0, count);
}

/**
 * Simulate document processing for preview
 */
async function simulateDocumentProcessing(content: string, agent: Record<string, unknown>): Promise<Record<string, unknown>> {
  // This would be replaced with actual AI model calls in production
  const mockResults: Record<string, Record<string, unknown>> = {
    invoice: {
      vendor: { name: 'Mock Vendor Inc', address: '123 Test St' },
      invoice: { number: 'TEST-001', date: '2025-01-15' },
      financial: { total_amount: 999.99, currency: 'USD' }
    },
    receipt: {
      merchant: { name: 'Mock Store' },
      transaction: { date: '2025-01-15' },
      payment: { total: 99.99, method: 'cash' }
    }
  };

  // Return mock result based on agent category
  return mockResults[agent.category as string] || mockResults.invoice;
}

/**
 * Validate extracted data against JSON schema
 */
async function validateAgainstSchema(data: Record<string, unknown>, schema: JSONSchema): Promise<boolean> {
  try {
    if (schema.type === 'object' && schema.properties) {
      // Check required fields
      if (schema.required) {
        for (const requiredField of schema.required) {
          if (!(requiredField in data)) {
            return false;
          }
        }
      }

      // Check field types
      for (const [fieldName, fieldSchema] of Object.entries(schema.properties)) {
        if (fieldName in data) {
          const value = data[fieldName];
          if (!validateFieldType(value, fieldSchema)) {
            return false;
          }
        }
      }
    }
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate field type against schema
 */
function validateFieldType(value: unknown, fieldSchema: Record<string, unknown>): boolean {
  if (!fieldSchema.type) return true;

  switch (fieldSchema.type) {
    case 'string': {
      }
      return typeof value === 'string';
    }
    case 'number': {
      }
      return typeof value === 'number';
    }
    case 'boolean': {
      }
      return typeof value === 'boolean';
    }
    case 'object': {
      }
      return typeof value === 'object' && value !== null && !Array.isArray(value);
    }
    case 'array': {
      }
      return Array.isArray(value);
    }
    default:
      return true;
  }
}

/**
 * Calculate confidence score for extraction results
 */
function calculateConfidenceScore(extraction: Record<string, unknown>): number {
  // Mock confidence calculation - in production would use model confidence
  const hasRequiredFields = Object.keys(extraction).length > 0;
  const baseConfidence = hasRequiredFields ? 0.85 : 0.3;
  
  // Add small random variation
  const variation = (Math.random() - 0.5) * 0.2;
  return Math.max(0, Math.min(1, baseConfidence + variation));
}

/**
 * Compare results for accuracy analysis
 */
function compareResults(actual: Record<string, unknown>, expected: Record<string, unknown>): Record<string, unknown> {
  const comparison = {
    accuracy: 0,
    differences: [] as string[],
    matching_fields: [] as string[],
    missing_fields: [] as string[]
  };

  const expectedKeys = Object.keys(expected);
  const _actualKeys = Object.keys(actual);

  let matchingCount = 0;

  for (const key of expectedKeys) {
    if (key in actual) {
      if (JSON.stringify(actual[key]) === JSON.stringify(expected[key])) {
        comparison.matching_fields.push(key);
        matchingCount++;
      } else {
        comparison.differences.push(`Field "${key}" differs`);
      }
    } else {
      comparison.missing_fields.push(key);
    }
  }

  comparison.accuracy = expectedKeys.length > 0 ? matchingCount / expectedKeys.length : 0;

  return comparison;
}

// ================================================================================
// CUSTOMIZATION UTILITIES
// ================================================================================

/**
 * Apply customization to agent
 */
export async function applyCustomization(
  agent: Record<string, unknown>,
  customization: CustomizeAgentRequest,
  customerId: string
): Promise<Record<string, unknown>> {
  const updateData: Record<string, unknown> = {
    updated_at: new Date().toISOString(),
    last_customized_at: new Date().toISOString(),
    last_customized_by: customerId
  };

  // Apply customizations
  if (customization.name !== undefined) {
    updateData.name = customization.name;
  }
  if (customization.description !== undefined) {
    updateData.description = customization.description;
  }
  if (customization.system_prompt !== undefined) {
    updateData.system_prompt = customization.system_prompt;
  }
  if (customization.output_schema !== undefined) {
    updateData.output_schema = customization.output_schema;
  }
  if (customization.processing_config !== undefined) {
    updateData.processing_config = {
      ...agent.processing_config,
      ...customization.processing_config
    };
  }

  // Update agent in database
  const { data: updatedAgent, error } = await supabase
    .from('agents')
    .update(updateData)
    .eq('id', agent.id)
    .select()
    .single();

  if (error) {
    throw new Error(`Failed to update agent: ${error.message}`);
  }

  // Log the change
  await logAgentChange(agent.id as string, customerId, 'customization', customization);

  return updatedAgent;
}

/**
 * Create agent version
 */
export async function createAgentVersion(
  agentId: string,
  versionName: string,
  customerId: string,
  changes: CustomizeAgentRequest
): Promise<string> {
  const { data: versionId, error } = await supabase.rpc(
    'create_agent_version',
    {
      p_agent_id: agentId,
      p_version_name: versionName,
      p_customer_id: customerId,
      p_changes_summary: changes
    }
  );

  if (error) {
    throw new Error(`Failed to create version: ${error.message}`);
  }

  return versionId;
}

/**
 * Log agent changes
 */
async function logAgentChange(
  agentId: string,
  customerId: string,
  changeType: string,
  changes: Record<string, unknown>
): Promise<void> {
  await supabase.from('agent_change_log').insert({
    agent_id: agentId,
    customer_id: customerId,
    change_type: changeType,
    changes: changes,
    timestamp: new Date().toISOString(),
    validation_passed: true
  });
}