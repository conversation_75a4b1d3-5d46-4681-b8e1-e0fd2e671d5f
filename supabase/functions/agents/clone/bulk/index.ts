// Bulk Agent Cloning System Edge Function
// GitHub Issue #15: Agent Cloning System - Bulk Operations
// Handles bulk agent cloning with comprehensive validation and audit logging

import "jsr:@supabase/functions-js@2.4.0/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey',
};

// Type definitions
interface Agent {
  id: string;
  agent_id: string;
  name: string;
  description: string | null;
  category: string;
  prompt: string;
  json_schema: Record<string, unknown>;
  version: number;
  is_default: boolean;
  customer_id: string | null;
  parent_agent_id: string | null;
  clone_source_id: string | null;
  clone_generation: number;
  is_customizable: boolean;
  customization_locked: boolean;
  cloning_allowed: boolean;
  status: string;
  settings: Record<string, unknown>;
  performance_metrics: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

interface BulkCloneRequest {
  source_agent_ids: string[];
  name_prefix?: string;
  category_override?: string;
  description_suffix?: string;
}

interface BulkCloneResponse {
  success: boolean;
  successful_clones: Array<{
    source_agent_id: string;
    cloned_agent_id: string;
    cloned_agent_name: string;
  }>;
  failed_clones: Array<{
    agent_id: string;
    error: string;
  }>;
  total_requested: number;
  successful_count: number;
  failed_count: number;
  processing_time_ms: number;
  operation_id?: string;
}

interface CustomerContext {
  customerId: string;
  apiKeyId: string;
  keyType: 'test' | 'production';
  credits: number;
  tier: string;
  rateLimit: Record<string, unknown>;
}

interface CloneLimits {
  max_clones: number;
  max_clone_depth: number;
  can_clone_custom_agents: boolean;
  can_bulk_clone: boolean;
}

// API Key validation function (reused from single clone)
async function validateApiKey(authHeader: string | null): Promise<CustomerContext> {
  if (!authHeader) {
    throw new Error('Missing Authorization header');
  }

  // Extract API key from header
  let apiKey = authHeader;
  if (authHeader.startsWith('Bearer ')) {
    apiKey = authHeader.replace('Bearer ', '');
  }

  if (!apiKey) {
    throw new Error('Invalid API key format');
  }

  // Hash the API key for database lookup
  const encoder = new TextEncoder();
  const _data = encoder.encode(apiKey);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const keyHash = Array.from(new Uint8Array(hashBuffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  // Look up the API key in the database
  const { data: keyData, error: keyError } = await supabase
    .from('api_keys')
    .select(`
      id,
      customer_id,
      key_type,
      credits,
      rate_limits,
      revoked,
      expires_at,
      customers (
        id,
        company_name,
        tier,
        status
      )
    `)
    .eq('key_hash', keyHash)
    .eq('revoked', false)
    .single();

  if (keyError || !keyData) {
    throw new Error('Invalid API key');
  }

  // Check if key is expired
  if (keyData.expires_at && new Date(keyData.expires_at) < new Date()) {
    throw new Error('API key expired');
  }

  // Check if customer account is active
  if (keyData.customers.status !== 'active') {
    throw new Error('Customer account inactive');
  }

  return {
    customerId: keyData.customer_id,
    apiKeyId: keyData.id,
    keyType: keyData.key_type,
    credits: keyData.credits,
    tier: keyData.customers.tier,
    rateLimit: keyData.rate_limits
  };
}

// Generate correlation ID for request tracking
function generateCorrelationId(): string {
  return `bulk_clone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get customer clone limits
async function getCustomerCloneLimits(tier: string): Promise<CloneLimits> {
  const { data: limits, error } = await supabase
    .from('customer_clone_limits')
    .select('*')
    .eq('tier', tier)
    .single();

  if (error || !limits) {
    throw new Error('Invalid customer tier or limits not found');
  }

  return {
    max_clones: limits.max_clones,
    max_clone_depth: limits.max_clone_depth,
    can_clone_custom_agents: limits.can_clone_custom_agents,
    can_bulk_clone: limits.can_bulk_clone
  };
}

// Get current clone count for customer
async function getCurrentCloneCount(customerId: string): Promise<number> {
  const { data: clones, error } = await supabase
    .from('agents')
    .select('id', { count: 'exact' })
    .eq('customer_id', customerId)
    .not('parent_agent_id', 'is', null); // Only count cloned agents

  if (error) {
    console.error('Error getting clone count:', error);
    return 0;
  }

  return clones?.length || 0;
}

// Validate bulk clone permissions
async function validateBulkClonePermissions(
  sourceAgentIds: string[],
  customerContext: CustomerContext
): Promise<void> {
  // Get customer clone limits
  const cloneLimits = await getCustomerCloneLimits(customerContext.tier);

  // Check if customer can bulk clone
  if (!cloneLimits.can_bulk_clone) {
    throw new Error(`${customerContext.tier} tier customers cannot use bulk cloning`);
  }

  // Check total clone limits
  const currentCloneCount = await getCurrentCloneCount(customerContext.customerId);
  const requestedCloneCount = sourceAgentIds.length;

  if (cloneLimits.max_clones !== -1) {
    const totalAfterCloning = currentCloneCount + requestedCloneCount;
    if (totalAfterCloning > cloneLimits.max_clones) {
      throw new Error(
        `Bulk clone would exceed limit. Current: ${currentCloneCount}, ` +
        `Requested: ${requestedCloneCount}, Max: ${cloneLimits.max_clones}`
      );
    }
  }

  // Validate maximum bulk size (prevent abuse)
  const MAX_BULK_SIZE = 25;
  if (requestedCloneCount > MAX_BULK_SIZE) {
    throw new Error(`Bulk clone size too large. Maximum ${MAX_BULK_SIZE} agents per request`);
  }
}

// Get multiple source agents with access validation
async function getSourceAgents(agentIds: string[], customerId: string): Promise<Map<string, Agent>> {
  const { data: agents, error } = await supabase
    .from('agents')
    .select('*')
    .in('id', agentIds)
    .or(`is_default.eq.true,customer_id.eq.${customerId}`) // Can clone defaults or own agents
    .eq('status', 'active');

  if (error) {
    console.error('Error fetching source agents:', error);
    throw new Error('Failed to fetch source agents');
  }

  const agentMap = new Map<string, Agent>();
  agents?.forEach(agent => {
    agentMap.set(agent.id, agent);
  });

  return agentMap;
}

// Generate unique clone name for bulk operation
async function generateBulkCloneName(
  baseName: string,
  customerId: string,
  namePrefix?: string
): Promise<string> {
  const prefix = namePrefix ? `${namePrefix} ` : '';
  let attempt = 1;
  let candidateName = `${prefix}${baseName} (Custom)`;

  while (true) {
    const { data: conflict } = await supabase
      .from('agents')
      .select('id')
      .eq('name', candidateName)
      .eq('customer_id', customerId)
      .eq('status', 'active')
      .single();

    if (!conflict) {
      return candidateName;
    }

    attempt++;
    candidateName = `${prefix}${baseName} (Custom ${attempt})`;
  }
}

// Generate agent ID
function generateAgentId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `bulk-${timestamp}-${random}`;
}

// Validate individual agent for cloning
function validateAgentForCloning(agent: Agent, cloneLimits: CloneLimits): string | null {
  // Check if agent allows cloning
  if (!agent.cloning_allowed) {
    return 'Agent cloning is disabled';
  }

  // Check if deprecated
  if (agent.agent_id.includes('deprecated')) {
    return 'Cannot clone deprecated agents';
  }

  // Check if customer can clone non-default agents
  if (!agent.is_default && !cloneLimits.can_clone_custom_agents) {
    return 'Customer tier cannot clone custom agents';
  }

  // Check clone depth limits
  if (agent.clone_generation >= cloneLimits.max_clone_depth) {
    return `Clone depth limit reached (max: ${cloneLimits.max_clone_depth})`;
  }

  return null; // No errors
}

// Create single cloned agent (optimized for bulk operations)
async function createBulkClonedAgent(
  sourceAgent: Agent,
  customerContext: CustomerContext,
  cloneName: string,
  categoryOverride?: string,
  descriptionSuffix?: string
): Promise<Agent> {
  const cloneId = crypto.randomUUID();
  const agentId = generateAgentId();

  const description = sourceAgent.description 
    ? `${sourceAgent.description}${descriptionSuffix ? ` ${descriptionSuffix}` : ''}`
    : `Bulk cloned from ${sourceAgent.name}${descriptionSuffix ? ` ${descriptionSuffix}` : ''}`;

  // Prepare clone data
  const cloneData = {
    id: cloneId,
    agent_id: agentId,
    name: cloneName,
    description,
    category: categoryOverride || sourceAgent.category,
    prompt: sourceAgent.prompt,
    json_schema: sourceAgent.json_schema,
    version: 1,
    is_default: false,
    customer_id: customerContext.customerId,
    parent_agent_id: sourceAgent.id,
    clone_source_id: sourceAgent.id,
    clone_generation: sourceAgent.clone_generation + 1,
    is_customizable: true,
    customization_locked: false,
    cloning_allowed: true,
    status: 'active',
    settings: sourceAgent.settings || {},
    performance_metrics: {
      inherited_from: sourceAgent.id,
      bulk_clone_created_at: new Date().toISOString()
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  // Create the cloned agent
  const { data: clonedAgent, error: createError } = await supabase
    .from('agents')
    .insert(cloneData)
    .select()
    .single();

  if (createError || !clonedAgent) {
    throw new Error(`Failed to create clone: ${createError?.message || 'Unknown error'}`);
  }

  return clonedAgent;
}

// Process bulk clone operation
async function processBulkClone(
  bulkRequest: BulkCloneRequest,
  customerContext: CustomerContext,
  _correlationId: string
): Promise<{
  successful: Array<{ sourceAgentId: string; clonedAgent: Agent }>;
  failed: Array<{ sourceAgentId: string; error: string }>;
}> {
  const _result = {
    successful: [] as Array<{ sourceAgentId: string; clonedAgent: Agent }>,
    failed: [] as Array<{ sourceAgentId: string; error: string }>
  };

  // Get clone limits
  const cloneLimits = await getCustomerCloneLimits(customerContext.tier);

  // Get source agents
  const sourceAgents = await getSourceAgents(bulkRequest.source_agent_ids, customerContext.customerId);

  // Process each agent
  for (const sourceAgentId of bulkRequest.source_agent_ids) {
    try {
      const sourceAgent = sourceAgents.get(sourceAgentId);
      
      if (!sourceAgent) {
        result.failed.push({
          sourceAgentId,
          error: 'Agent not found or access denied'
        });
        continue;
      }

      // Validate individual agent
      const validationError = validateAgentForCloning(sourceAgent, cloneLimits);
      if (validationError) {
        result.failed.push({
          sourceAgentId,
          error: validationError
        });
        continue;
      }

      // Generate unique clone name
      const cloneName = await generateBulkCloneName(
        sourceAgent.name,
        customerContext.customerId,
        bulkRequest.name_prefix
      );

      // Create the clone
      const clonedAgent = await createBulkClonedAgent(
        sourceAgent,
        customerContext,
        cloneName,
        bulkRequest.category_override,
        bulkRequest.description_suffix
      );

      // Create inheritance tracking (in parallel for performance)
      supabase.rpc('create_agent_inheritance', {
        child_agent_uuid: clonedAgent.id,
        parent_agent_uuid: sourceAgent.id,
        inheritance_type_param: 'clone'
      }).then(() => {
        console.log(`Inheritance created for ${clonedAgent.id}`);
      }).catch(error => {
        console.error(`Failed to create inheritance for ${clonedAgent.id}:`, error);
      });

      // Create clone configuration (in parallel for performance)
      supabase.from('agent_clone_configs').insert({
        agent_id: clonedAgent.id,
        customer_id: customerContext.customerId,
        clone_name: cloneName,
        clone_description: clonedAgent.description,
        customizations: {},
        is_customizable: true,
        clone_generation: sourceAgent.clone_generation + 1,
        cloned_at: new Date().toISOString(),
        customization_count: 0
      }).then(() => {
        console.log(`Clone config created for ${clonedAgent.id}`);
      }).catch(error => {
        console.error(`Failed to create clone config for ${clonedAgent.id}:`, error);
      });

      result.successful.push({
        sourceAgentId,
        clonedAgent
      });

    } catch {
      result.failed.push({
        sourceAgentId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  return result;
}

// Log bulk clone operation
async function logBulkCloneOperation(
  customerContext: CustomerContext,
  sourceAgentIds: string[],
  successfulClones: string[],
  failedClones: Array<{ agent_id: string; error: string }>,
  processingTimeMs: number,
  correlationId: string,
  request: Request
): Promise<void> {
  // Log to audit_logs table
  await supabase.from('audit_logs').insert({
    customer_id: customerContext.customerId,
    api_key_id: customerContext.apiKeyId,
    action: 'agent_bulk_clone',
    resource_type: 'agent',
    resource_id: correlationId,
    success: successfulClones.length > 0,
    metadata: {
      source_agent_ids: sourceAgentIds,
      successful_clone_ids: successfulClones,
      failed_clones: failedClones,
      total_requested: sourceAgentIds.length,
      successful_count: successfulClones.length,
      failed_count: failedClones.length,
      processing_time_ms: processingTimeMs,
      correlation_id: correlationId
    },
    ip_address: request.headers.get('x-forwarded-for'),
    user_agent: request.headers.get('user-agent')
  });

  // Log to clone operations table
  await supabase.rpc('log_clone_operation', {
    customer_uuid: customerContext.customerId,
    api_key_uuid: customerContext.apiKeyId,
    operation_type_param: 'bulk_clone',
    source_agent_ids_param: sourceAgentIds,
    target_agent_ids_param: successfulClones,
    operation_status_param: failedClones.length === 0 ? 'completed' : 
                           successfulClones.length === 0 ? 'failed' : 'partial',
    success_count_param: successfulClones.length,
    failure_count_param: failedClones.length,
    error_details_param: failedClones.length > 0 ? { failed_clones: failedClones } : {},
    processing_time_ms_param: processingTimeMs,
    ip_address_param: request.headers.get('x-forwarded-for'),
    user_agent_param: request.headers.get('user-agent'),
    correlation_id_param: correlationId
  });
}

// Main bulk clone handler
async function handleBulkClone(req: Request, customerContext: CustomerContext): Promise<BulkCloneResponse> {
  const startTime = performance.now();
  const correlationId = generateCorrelationId();

  try {
    // Parse request body
    const bulkRequest: BulkCloneRequest = await req.json();

    // Validate request
    if (!bulkRequest.source_agent_ids || !Array.isArray(bulkRequest.source_agent_ids)) {
      throw new Error('source_agent_ids is required and must be an array');
    }

    if (bulkRequest.source_agent_ids.length === 0) {
      throw new Error('At least one source agent ID is required');
    }

    // Remove duplicates
    const uniqueSourceIds = [...new Set(bulkRequest.source_agent_ids)];
    if (uniqueSourceIds.length !== bulkRequest.source_agent_ids.length) {
      console.log(`Removed ${bulkRequest.source_agent_ids.length - uniqueSourceIds.length} duplicate agent IDs`);
    }

    // Validate bulk clone permissions
    await validateBulkClonePermissions(uniqueSourceIds, customerContext);

    // Process bulk clone
    const { successful, failed } = await processBulkClone(
      { ...bulkRequest, source_agent_ids: uniqueSourceIds },
      customerContext,
      correlationId
    );

    const processingTime = Math.round(performance.now() - startTime);

    // Log operation
    await logBulkCloneOperation(
      customerContext,
      uniqueSourceIds,
      successful.map(s => s.clonedAgent.id),
      failed,
      processingTime,
      correlationId,
      req
    );

    // Prepare response
    const response: BulkCloneResponse = {
      success: true,
      successful_clones: successful.map(s => ({
        source_agent_id: s.sourceAgentId,
        cloned_agent_id: s.clonedAgent.id,
        cloned_agent_name: s.clonedAgent.name
      })),
      failed_clones: failed,
      total_requested: uniqueSourceIds.length,
      successful_count: successful.length,
      failed_count: failed.length,
      processing_time_ms: processingTime,
      operation_id: correlationId
    };

    return response;

  } catch {
    const processingTime = Math.round(performance.now() - startTime);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Log failed operation
    await logBulkCloneOperation(
      customerContext,
      [],
      [],
      [{ agent_id: 'bulk_operation', error: errorMessage }],
      processingTime,
      correlationId,
      req
    );

    return {
      success: false,
      successful_clones: [],
      failed_clones: [{ agent_id: 'bulk_operation', error: errorMessage }],
      total_requested: 0,
      successful_count: 0,
      failed_count: 1,
      processing_time_ms: processingTime,
      operation_id: correlationId
    };
  }
}

// Main handler
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Method not allowed. Use POST for bulk cloning.',
        allowed_methods: ['POST']
      }),
      {
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }

  try {
    // Extract API key from headers
    const authHeader = req.headers.get('Authorization') || req.headers.get('apikey');
    const customerContext = await validateApiKey(authHeader);

    // Handle bulk clone request
    const _result = await handleBulkClone(req, customerContext);

    const statusCode = result.success ? 200 : 400;

    return new Response(JSON.stringify(result), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });

  } catch {
    console.error('Error processing bulk clone request:', error);

    const isAuthError = error instanceof Error && (
      error.message.includes('API key') ||
      error.message.includes('Authorization') ||
      error.message.includes('access denied') ||
      error.message.includes('inactive')
    );

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      }),
      {
        status: isAuthError ? 401 : 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});