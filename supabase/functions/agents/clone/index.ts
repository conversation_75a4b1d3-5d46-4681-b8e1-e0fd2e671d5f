// Agent Cloning System Edge Function
// GitHub Issue #15: Agent Cloning System
// Handles single agent cloning with comprehensive validation and audit logging

import "jsr:@supabase/functions-js@2.4.0/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";

// Environment variables
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

// Create Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, apikey',
};

// Type definitions
interface Agent {
  id: string;
  agent_id: string;
  name: string;
  description: string | null;
  category: string;
  prompt: string;
  json_schema: Record<string, unknown>;
  version: number;
  is_default: boolean;
  customer_id: string | null;
  parent_agent_id: string | null;
  clone_source_id: string | null;
  clone_generation: number;
  is_customizable: boolean;
  customization_locked: boolean;
  cloning_allowed: boolean;
  status: string;
  settings: Record<string, unknown>;
  performance_metrics: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

interface _Customer {
  id: string;
  company_name: string;
  tier: 'free' | 'starter' | 'professional' | 'enterprise';
  status: string;
}

interface _ApiKey {
  id: string;
  customer_id: string;
  key_type: 'test' | 'production';
  credits: number;
  rate_limits: Record<string, unknown>;
}

interface CloneAgentRequest {
  source_agent_id: string;
  name?: string;
  description?: string;
  category?: string;
  customize_immediately?: boolean;
}

interface CloneAgentResponse {
  success: boolean;
  cloned_agent?: {
    id: string;
    name: string;
    parent_agent_id: string;
    customer_id: string;
    is_customizable: boolean;
    cloned_at: string;
  };
  customization_url?: string;
  error?: string;
}

interface CustomerContext {
  customerId: string;
  apiKeyId: string;
  keyType: 'test' | 'production';
  credits: number;
  tier: string;
  rateLimit: Record<string, unknown>;
}

// API Key validation function
async function validateApiKey(authHeader: string | null): Promise<CustomerContext> {
  if (!authHeader) {
    throw new Error('Missing Authorization header');
  }

  // Extract API key from header
  let apiKey = authHeader;
  if (authHeader.startsWith('Bearer ')) {
    apiKey = authHeader.replace('Bearer ', '');
  }

  if (!apiKey) {
    throw new Error('Invalid API key format');
  }

  // Hash the API key for database lookup
  const encoder = new TextEncoder();
  const _data = encoder.encode(apiKey);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const keyHash = Array.from(new Uint8Array(hashBuffer))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  // Look up the API key in the database
  const { data: keyData, error: keyError } = await supabase
    .from('api_keys')
    .select(`
      id,
      customer_id,
      key_type,
      credits,
      rate_limits,
      revoked,
      expires_at,
      customers (
        id,
        company_name,
        tier,
        status
      )
    `)
    .eq('key_hash', keyHash)
    .eq('revoked', false)
    .single();

  if (keyError || !keyData) {
    throw new Error('Invalid API key');
  }

  // Check if key is expired
  if (keyData.expires_at && new Date(keyData.expires_at) < new Date()) {
    throw new Error('API key expired');
  }

  // Check if customer account is active
  if (keyData.customers.status !== 'active') {
    throw new Error('Customer account inactive');
  }

  return {
    customerId: keyData.customer_id,
    apiKeyId: keyData.id,
    keyType: keyData.key_type,
    credits: keyData.credits,
    tier: keyData.customers.tier,
    rateLimit: keyData.rate_limits
  };
}

// Generate correlation ID for request tracking
function generateCorrelationId(): string {
  return `clone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get source agent with access validation
async function getSourceAgent(agentId: string, customerId: string): Promise<Agent | null> {
  const { data: agent, error } = await supabase
    .from('agents')
    .select('*')
    .eq('id', agentId)
    .or(`is_default.eq.true,customer_id.eq.${customerId}`) // Can clone defaults or own agents
    .eq('status', 'active')
    .single();

  if (error || !agent) {
    return null;
  }

  return agent;
}

// Validate clone permissions
async function validateClonePermissions(sourceAgent: Agent, customerContext: CustomerContext): Promise<void> {
  // Check if agent allows cloning
  if (!sourceAgent.cloning_allowed) {
    throw new Error('Agent cloning is disabled for this agent');
  }

  // Get customer clone limits
  const { data: cloneLimits } = await supabase
    .from('customer_clone_limits')
    .select('*')
    .eq('tier', customerContext.tier)
    .single();

  if (!cloneLimits) {
    throw new Error('Invalid customer tier');
  }

  // Check if customer can clone non-default agents
  if (!sourceAgent.is_default && !cloneLimits.can_clone_custom_agents) {
    throw new Error(`${customerContext.tier} tier customers can only clone default agents`);
  }

  // Check clone limits
  const { data: currentClones } = await supabase
    .from('agents')
    .select('id', { count: 'exact' })
    .eq('customer_id', customerContext.customerId)
    .not('parent_agent_id', 'is', null); // Only count cloned agents

  const currentCloneCount = currentClones?.length || 0;

  if (cloneLimits.max_clones !== -1 && currentCloneCount >= cloneLimits.max_clones) {
    throw new Error(`Clone limit reached. Maximum ${cloneLimits.max_clones} clones allowed for ${customerContext.tier} tier`);
  }

  // Check clone depth limits
  if (sourceAgent.clone_generation >= cloneLimits.max_clone_depth) {
    throw new Error(`Clone depth limit reached. Maximum depth ${cloneLimits.max_clone_depth} for ${customerContext.tier} tier`);
  }

  // Check if agent is deprecated or has issues
  if (sourceAgent.agent_id.includes('deprecated')) {
    throw new Error('Cannot clone deprecated agents');
  }
}

// Check naming conflicts
async function checkNamingConflict(name: string, customerId: string): Promise<void> {
  const { data: existingAgent } = await supabase
    .from('agents')
    .select('id')
    .eq('name', name)
    .eq('customer_id', customerId)
    .eq('status', 'active')
    .single();

  if (existingAgent) {
    throw new Error(`Agent with name "${name}" already exists. Please choose a different name.`);
  }
}

// Generate unique clone name
async function generateUniqueCloneName(baseName: string, customerId: string): Promise<string> {
  let attempt = 1;
  let candidateName = `${baseName} (Custom)`;

  while (true) {
    const { data: conflict } = await supabase
      .from('agents')
      .select('id')
      .eq('name', candidateName)
      .eq('customer_id', customerId)
      .eq('status', 'active')
      .single();

    if (!conflict) {
      return candidateName;
    }

    attempt++;
    candidateName = `${baseName} (Custom ${attempt})`;
  }
}

// Generate agent ID
function generateAgentId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return `custom-${timestamp}-${random}`;
}

// Create cloned agent
async function createClonedAgent(
  sourceAgent: Agent,
  customerContext: CustomerContext,
  cloneName: string,
  cloneDescription?: string,
  categoryOverride?: string
): Promise<Agent> {
  const cloneId = crypto.randomUUID();
  const agentId = generateAgentId();

  // Prepare clone data
  const cloneData = {
    id: cloneId,
    agent_id: agentId,
    name: cloneName,
    description: cloneDescription || `Customized version of ${sourceAgent.name}`,
    category: categoryOverride || sourceAgent.category,
    prompt: sourceAgent.prompt, // Inherited, but customizable
    json_schema: sourceAgent.json_schema, // Inherited, but customizable
    version: 1, // Clones start at v1.0
    is_default: false,
    customer_id: customerContext.customerId,
    parent_agent_id: sourceAgent.id,
    clone_source_id: sourceAgent.id, // For clarity
    clone_generation: sourceAgent.clone_generation + 1,
    is_customizable: true,
    customization_locked: false, // Allow customization
    cloning_allowed: true, // Clones can be cloned unless specifically disabled
    status: 'active',
    settings: sourceAgent.settings || {},
    performance_metrics: {
      inherited_from: sourceAgent.id,
      clone_created_at: new Date().toISOString()
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  // Create the cloned agent
  const { data: clonedAgent, error: createError } = await supabase
    .from('agents')
    .insert(cloneData)
    .select()
    .single();

  if (createError || !clonedAgent) {
    console.error('Error creating cloned agent:', createError);
    throw new Error('Failed to create cloned agent');
  }

  // Create inheritance tracking record
  await supabase.rpc('create_agent_inheritance', {
    child_agent_uuid: cloneId,
    parent_agent_uuid: sourceAgent.id,
    inheritance_type_param: 'clone'
  });

  // Create clone configuration record
  await supabase
    .from('agent_clone_configs')
    .insert({
      agent_id: cloneId,
      customer_id: customerContext.customerId,
      clone_name: cloneName,
      clone_description: cloneDescription,
      customizations: {},
      is_customizable: true,
      clone_generation: sourceAgent.clone_generation + 1,
      cloned_at: new Date().toISOString(),
      customization_count: 0
    });

  return clonedAgent;
}

// Log clone operation for audit trail
async function logCloneOperation(
  customerContext: CustomerContext,
  sourceAgentId: string,
  clonedAgentId: string,
  cloneName: string,
  success: boolean,
  errorMessage?: string,
  request?: Request,
  processingTimeMs?: number
): Promise<void> {
  const correlationId = generateCorrelationId();

  // Log to audit_logs table
  await supabase.from('audit_logs').insert({
    customer_id: customerContext.customerId,
    api_key_id: customerContext.apiKeyId,
    action: 'agent_clone',
    resource_type: 'agent',
    resource_id: clonedAgentId,
    success,
    error_message: errorMessage,
    metadata: {
      source_agent_id: sourceAgentId,
      cloned_agent_id: clonedAgentId,
      clone_name: cloneName,
      clone_timestamp: new Date().toISOString(),
      correlation_id: correlationId
    },
    ip_address: request?.headers.get('x-forwarded-for'),
    user_agent: request?.headers.get('user-agent')
  });

  // Log to clone operations table
  await supabase.rpc('log_clone_operation', {
    customer_uuid: customerContext.customerId,
    api_key_uuid: customerContext.apiKeyId,
    operation_type_param: 'single_clone',
    source_agent_ids_param: [sourceAgentId],
    target_agent_ids_param: success ? [clonedAgentId] : [],
    operation_status_param: success ? 'completed' : 'failed',
    success_count_param: success ? 1 : 0,
    failure_count_param: success ? 0 : 1,
    error_details_param: errorMessage ? { error: errorMessage } : {},
    request_payload_param: { clone_name: cloneName },
    processing_time_ms_param: processingTimeMs,
    ip_address_param: request?.headers.get('x-forwarded-for'),
    user_agent_param: request?.headers.get('user-agent'),
    correlation_id_param: correlationId
  });
}

// Main clone handler
async function handleCloneAgent(req: Request, customerContext: CustomerContext): Promise<CloneAgentResponse> {
  const startTime = performance.now();

  try {
    // Parse request body
    const cloneRequest: CloneAgentRequest = await req.json();

    // Validate request
    if (!cloneRequest.source_agent_id) {
      throw new Error('Source agent ID is required');
    }

    if (cloneRequest.name && cloneRequest.name.length > 100) {
      throw new Error('Agent name too long (max 100 characters)');
    }

    if (cloneRequest.name && cloneRequest.name.trim().length === 0) {
      throw new Error('Agent name cannot be empty');
    }

    // Get source agent
    const sourceAgent = await getSourceAgent(cloneRequest.source_agent_id, customerContext.customerId);
    if (!sourceAgent) {
      throw new Error('Source agent not found or access denied');
    }

    // Validate clone permissions
    await validateClonePermissions(sourceAgent, customerContext);

    // Determine clone name
    let cloneName: string;
    if (cloneRequest.name) {
      await checkNamingConflict(cloneRequest.name, customerContext.customerId);
      cloneName = cloneRequest.name;
    } else {
      cloneName = await generateUniqueCloneName(sourceAgent.name, customerContext.customerId);
    }

    // Create the cloned agent
    const clonedAgent = await createClonedAgent(
      sourceAgent,
      customerContext,
      cloneName,
      cloneRequest.description,
      cloneRequest.category
    );

    const processingTime = Math.round(performance.now() - startTime);

    // Log successful operation
    await logCloneOperation(
      customerContext,
      sourceAgent.id,
      clonedAgent.id,
      cloneName,
      true,
      undefined,
      req,
      processingTime
    );

    // Prepare response
    const response: CloneAgentResponse = {
      success: true,
      cloned_agent: {
        id: clonedAgent.id,
        name: clonedAgent.name,
        parent_agent_id: clonedAgent.parent_agent_id!,
        customer_id: clonedAgent.customer_id!,
        is_customizable: clonedAgent.is_customizable,
        cloned_at: clonedAgent.created_at
      }
    };

    // Add customization URL if requested
    if (cloneRequest.customize_immediately) {
      response.customization_url = `/api/v1/agents/${clonedAgent.id}/customize`;
    }

    return response;

  } catch {
    const processingTime = Math.round(performance.now() - startTime);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Log failed operation
    await logCloneOperation(
      customerContext,
      '',
      '',
      '',
      false,
      errorMessage,
      req,
      processingTime
    );

    return {
      success: false,
      error: errorMessage
    };
  }
}

// Main handler
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: corsHeaders,
    });
  }

  // Only allow POST method
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Method not allowed. Use POST to clone agents.',
        allowed_methods: ['POST']
      }),
      {
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }

  try {
    // Extract API key from headers
    const authHeader = req.headers.get('Authorization') || req.headers.get('apikey');
    const customerContext = await validateApiKey(authHeader);

    // Handle clone request
    const _result = await handleCloneAgent(req, customerContext);

    const statusCode = result.success ? 200 : 400;

    return new Response(JSON.stringify(result), {
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });

  } catch {
    console.error('Error processing clone request:', error);

    const isAuthError = error instanceof Error && (
      error.message.includes('API key') ||
      error.message.includes('Authorization') ||
      error.message.includes('access denied') ||
      error.message.includes('inactive')
    );

    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error',
      }),
      {
        status: isAuthError ? 401 : 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
        },
      }
    );
  }
});