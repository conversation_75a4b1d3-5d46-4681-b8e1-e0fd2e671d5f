{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "resolveJsonModule": true, "allowImportingTsExtensions": true, "noEmit": true, "types": ["node"]}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"]}