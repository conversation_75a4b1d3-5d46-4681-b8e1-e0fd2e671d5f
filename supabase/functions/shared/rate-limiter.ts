/**
 * Rate Limiter for Agent Customization Endpoints
 * Prevents abuse and ensures fair resource usage
 */

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: Request) => string; // Custom key generation
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

// In-memory store for rate limiting (in production, use Redis)
const rateLimitStore = new Map<string, RateLimitEntry>();

// Cleanup old entries periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}, 60000); // Cleanup every minute

export class RateLimiter {
  private config: Required<RateLimitConfig>;

  constructor(config: RateLimitConfig) {
    this.config = {
      keyGenerator: (req) => this.getClientKey(req),
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      ...config
    };
  }

  private getClientKey(req: Request): string {
    // Extract API key from Authorization header
    const authHeader = req.headers.get('authorization');
    if (authHeader?.startsWith('Bearer ')) {
      const apiKey = authHeader.substring(7);
      return `api_key:${apiKey}`;
    }

    // Fallback to IP address (not ideal for production)
    const xff = req.headers.get('x-forwarded-for');
    const ip = xff ? xff.split(',')[0].trim() : 'unknown';
    return `ip:${ip}`;
  }

  /**
   * Check if request should be rate limited
   * Returns null if allowed, or rate limit info if blocked
   */
  checkLimit(req: Request): RateLimitResult {
    const key = this.config.keyGenerator(req);
    const now = Date.now();
    
    let entry = rateLimitStore.get(key);
    
    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired one
      entry = {
        count: 1,
        resetTime: now + this.config.windowMs,
        firstRequest: now
      };
      rateLimitStore.set(key, entry);
      
      return {
        allowed: true,
        count: 1,
        remaining: this.config.maxRequests - 1,
        resetTime: entry.resetTime,
        retryAfter: null
      };
    }
    
    // Check if limit exceeded
    if (entry.count >= this.config.maxRequests) {
      return {
        allowed: false,
        count: entry.count,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter: Math.ceil((entry.resetTime - now) / 1000)
      };
    }
    
    // Increment counter
    entry.count++;
    rateLimitStore.set(key, entry);
    
    return {
      allowed: true,
      count: entry.count,
      remaining: this.config.maxRequests - entry.count,
      resetTime: entry.resetTime,
      retryAfter: null
    };
  }

  /**
   * Record the result of a request (for conditional counting)
   */
  recordResult(req: Request, success: boolean): void {
    if (this.config.skipSuccessfulRequests && success) {
      this.decrementCount(req);
    } else if (this.config.skipFailedRequests && !success) {
      this.decrementCount(req);
    }
  }

  private decrementCount(req: Request): void {
    const key = this.config.keyGenerator(req);
    const entry = rateLimitStore.get(key);
    
    if (entry && entry.count > 0) {
      entry.count--;
      rateLimitStore.set(key, entry);
    }
  }
}

export interface RateLimitResult {
  allowed: boolean;
  count: number;
  remaining: number;
  resetTime: number;
  retryAfter: number | null;
}

// Predefined rate limiters for different operations
export const rateLimiters = {
  // Standard agent customization: 50 requests per 15 minutes per API key
  agentCustomization: new RateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50
  }),

  // Preview operations: 20 requests per 5 minutes per API key
  previewOperations: new RateLimiter({
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 20
  }),

  // Version operations: 100 requests per hour per API key
  versionOperations: new RateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 100
  }),

  // Schema validation: 200 requests per 10 minutes per API key
  schemaValidation: new RateLimiter({
    windowMs: 10 * 60 * 1000, // 10 minutes
    maxRequests: 200,
    skipFailedRequests: true // Don't count validation failures
  }),

  // Heavy operations (bulk customization): 10 requests per 30 minutes
  bulkOperations: new RateLimiter({
    windowMs: 30 * 60 * 1000, // 30 minutes
    maxRequests: 10
  })
};

/**
 * Middleware function to apply rate limiting
 */
export function withRateLimit(
  limiter: RateLimiter,
  handler: (req: Request) => Promise<Response>
) {
  return async (req: Request): Promise<Response> => {
    const limitResult = limiter.checkLimit(req);
    
    if (!limitResult.allowed) {
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          message: `Too many requests. Try again in ${limitResult.retryAfter} seconds.`,
          limit: limiter['config'].maxRequests,
          remaining: limitResult.remaining,
          resetTime: limitResult.resetTime
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'X-RateLimit-Limit': limiter['config'].maxRequests.toString(),
            'X-RateLimit-Remaining': limitResult.remaining.toString(),
            'X-RateLimit-Reset': limitResult.resetTime.toString(),
            'Retry-After': limitResult.retryAfter?.toString() || '0'
          }
        }
      );
    }
    
    try {
      const response = await handler(req);
      
      // Add rate limit headers to successful responses
      const headers = new Headers(response.headers);
      headers.set('X-RateLimit-Limit', limiter['config'].maxRequests.toString());
      headers.set('X-RateLimit-Remaining', limitResult.remaining.toString());
      headers.set('X-RateLimit-Reset', limitResult.resetTime.toString());
      
      // Record result for conditional counting
      limiter.recordResult(req, response.ok);
      
      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers
      });
      
    } catch {
      // Record failed request
      limiter.recordResult(req, false);
      throw error;
    }
  };
}

/**
 * Get rate limit status for monitoring/debugging
 */
export function getRateLimitStatus(key?: string): any {
  if (key) {
    return rateLimitStore.get(key) || null;
  }
  
  // Return summary statistics
  const now = Date.now();
  const activeEntries = Array.from(rateLimitStore.values()).filter(entry => now <= entry.resetTime);
  
  return {
    totalKeys: rateLimitStore.size,
    activeKeys: activeEntries.length,
    totalRequests: activeEntries.reduce((sum, entry) => sum + entry.count, 0),
    oldestEntry: Math.min(...activeEntries.map(entry => entry.firstRequest)),
    nextReset: Math.min(...activeEntries.map(entry => entry.resetTime))
  };
}