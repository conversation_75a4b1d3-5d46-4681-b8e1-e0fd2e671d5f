/**
 * Observability and Monitoring for Agent Customization
 * Comprehensive logging, metrics, and alerting system
 */

export interface MetricEvent {
  name: string;
  value: number;
  timestamp: number;
  tags: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface LogEvent {
  level: 'debug' | 'info' | 'warn' | 'error' | 'critical';
  message: string;
  timestamp: number;
  context: {
    operation: string;
    agentId?: string;
    customerId?: string;
    apiKey?: string;
    requestId?: string;
  };
  metadata?: Record<string, any>;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

export interface PerformanceEvent {
  operation: string;
  duration: number;
  success: boolean;
  timestamp: number;
  context: {
    agentId?: string;
    customerId?: string;
    requestSize?: number;
    responseSize?: number;
  };
  metadata?: Record<string, any>;
}

class ObservabilityService {
  private metrics: MetricEvent[] = [];
  private logs: LogEvent[] = [];
  private performanceEvents: PerformanceEvent[] = [];
  private readonly maxRetention = 10000; // Keep last 10k events
  private alertThresholds: Record<string, number> = {
    errorRate: 0.05, // 5% error rate threshold
    avgResponseTime: 2000, // 2 second average response time
    failedValidations: 10 // 10 failed validations per minute
  };

  /**
   * Record a metric event
   */
  recordMetric(name: string, value: number, tags: Record<string, string> = {}, metadata?: Record<string, any>) {
    const event: MetricEvent = {
      name,
      value,
      timestamp: Date.now(),
      tags: {
        service: 'agent-customization',
        environment: Deno.env.get('ENVIRONMENT') || 'development',
        ...tags
      },
      metadata
    };

    this.metrics.push(event);
    this.trimArray(this.metrics);

    // Console output for development
    if (Deno.env.get('ENVIRONMENT') !== 'production') {
      console.log(`METRIC: ${name} = ${value}`, tags);
    }

    // Check for alert conditions
    this.checkAlerts(name, value, tags);
  }

  /**
   * Log an event with structured data
   */
  log(
    level: LogEvent['level'],
    message: string,
    context: LogEvent['context'],
    metadata?: Record<string, any>,
    error?: Error
  ) {
    const event: LogEvent = {
      level,
      message,
      timestamp: Date.now(),
      context: {
        ...context,
        requestId: context.requestId || crypto.randomUUID()
      },
      metadata
    };

    if (error) {
      event.error = {
        name: error.name,
        message: error.message,
        stack: error.stack
      };
    }

    this.logs.push(event);
    this.trimArray(this.logs);

    // Console output with structured formatting
    const logLine = {
      timestamp: new Date(event.timestamp).toISOString(),
      level: event.level.toUpperCase(),
      message: event.message,
      ...event.context,
      ...(event.metadata && { metadata: event.metadata }),
      ...(event.error && { error: event.error })
    };

    if (level === 'error' || level === 'critical') {
      console.error(JSON.stringify(logLine, null, 2));
    } else if (level === 'warn') {
      console.warn(JSON.stringify(logLine, null, 2));
    } else {
      console.log(JSON.stringify(logLine, null, 2));
    }
  }

  /**
   * Record performance metrics for operations
   */
  recordPerformance(
    operation: string,
    startTime: number,
    success: boolean,
    context: PerformanceEvent['context'] = {},
    metadata?: Record<string, any>
  ) {
    const duration = Date.now() - startTime;
    
    const event: PerformanceEvent = {
      operation,
      duration,
      success,
      timestamp: Date.now(),
      context,
      metadata
    };

    this.performanceEvents.push(event);
    this.trimArray(this.performanceEvents);

    // Record related metrics
    this.recordMetric(`${operation}.duration`, duration, {
      operation,
      success: success.toString(),
      customerId: context.customerId || 'unknown'
    });

    this.recordMetric(`${operation}.count`, 1, {
      operation,
      result: success ? 'success' : 'failure',
      customerId: context.customerId || 'unknown'
    });

    // Log slow operations
    if (duration > 5000) { // 5 seconds
      this.log('warn', `Slow operation detected: ${operation}`, {
        operation,
        agentId: context.agentId,
        customerId: context.customerId
      }, { duration, ...metadata });
    }
  }

  /**
   * Get aggregated metrics for monitoring dashboard
   */
  getMetricsSummary(timeWindow: number = 300000): any { // Default 5 minutes
    const cutoff = Date.now() - timeWindow;
    const _recentMetrics = this.metrics.filter(m => m.timestamp > cutoff);
    const recentPerformance = this.performanceEvents.filter(p => p.timestamp > cutoff);
    const recentLogs = this.logs.filter(l => l.timestamp > cutoff);

    // Calculate success rates
    const totalOps = recentPerformance.length;
    const successfulOps = recentPerformance.filter(p => p.success).length;
    const errorRate = totalOps > 0 ? (totalOps - successfulOps) / totalOps : 0;

    // Calculate average response times
    const responseTimes = recentPerformance.map(p => p.duration);
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    // Group operations
    const operationStats = recentPerformance.reduce((acc, event) => {
      if (!acc[event.operation]) {
        acc[event.operation] = { count: 0, successCount: 0, totalDuration: 0 };
      }
      acc[event.operation].count++;
      if (event.success) acc[event.operation].successCount++;
      acc[event.operation].totalDuration += event.duration;
      return acc;
    }, {} as Record<string, any>);

    // Log level distribution
    const logLevels = recentLogs.reduce((acc, log) => {
      acc[log.level] = (acc[log.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      timeWindow: timeWindow / 1000, // Convert to seconds
      overview: {
        totalOperations: totalOps,
        successfulOperations: successfulOps,
        errorRate: Math.round(errorRate * 10000) / 100, // Percentage with 2 decimals
        averageResponseTime: Math.round(avgResponseTime),
        totalLogs: recentLogs.length
      },
      operationBreakdown: Object.entries(operationStats).map(([op, stats]: [string, any]) => ({
        operation: op,
        count: stats.count,
        successRate: Math.round((stats.successCount / stats.count) * 10000) / 100,
        avgDuration: Math.round(stats.totalDuration / stats.count)
      })),
      logLevels,
      alerts: this.getActiveAlerts()
    };
  }

  /**
   * Check for alert conditions and log warnings
   */
  private checkAlerts(metricName: string, value: number, tags: Record<string, string>) {
    // Error rate alerts
    if (metricName.includes('.count') && tags.result === 'failure') {
      const recentFailures = this.metrics.filter(m => 
        m.name === metricName &&
        m.tags.result === 'failure' &&
        m.timestamp > Date.now() - 60000 // Last minute
      ).length;

      if (recentFailures > this.alertThresholds.failedValidations) {
        this.log('critical', `High failure rate detected: ${recentFailures} failures in last minute`, {
          operation: 'alert-system',
          customerId: tags.customerId
        }, { metricName, recentFailures, threshold: this.alertThresholds.failedValidations });
      }
    }

    // Response time alerts
    if (metricName.includes('.duration') && value > this.alertThresholds.avgResponseTime) {
      this.log('warn', `Slow response detected: ${value}ms`, {
        operation: tags.operation || 'unknown',
        customerId: tags.customerId
      }, { duration: value, threshold: this.alertThresholds.avgResponseTime });
    }
  }

  /**
   * Get currently active alerts
   */
  private getActiveAlerts(): any[] {
    const alerts = [];
    const recentLogs = this.logs.filter(l => l.timestamp > Date.now() - 300000); // Last 5 minutes

    // Check for critical logs
    const criticalLogs = recentLogs.filter(l => l.level === 'critical');
    if (criticalLogs.length > 0) {
      alerts.push({
        type: 'critical_errors',
        count: criticalLogs.length,
        lastOccurrence: criticalLogs[criticalLogs.length - 1].timestamp,
        message: `${criticalLogs.length} critical errors in the last 5 minutes`
      });
    }

    // Check for high error rates
    const recentPerformance = this.performanceEvents.filter(p => p.timestamp > Date.now() - 300000);
    if (recentPerformance.length > 20) { // Only check if we have sufficient data
      const errorRate = recentPerformance.filter(p => !p.success).length / recentPerformance.length;
      if (errorRate > this.alertThresholds.errorRate) {
        alerts.push({
          type: 'high_error_rate',
          errorRate: Math.round(errorRate * 10000) / 100,
          threshold: this.alertThresholds.errorRate * 100,
          message: `Error rate of ${Math.round(errorRate * 100)}% exceeds threshold`
        });
      }
    }

    return alerts;
  }

  /**
   * Trim arrays to prevent memory issues
   */
  private trimArray(array: any[]) {
    if (array.length > this.maxRetention) {
      array.splice(0, array.length - this.maxRetention);
    }
  }

  /**
   * Export data for external monitoring systems
   */
  exportMetrics(): {
    metrics: MetricEvent[];
    logs: LogEvent[];
    performance: PerformanceEvent[];
    summary: any;
  } {
    return {
      metrics: [...this.metrics],
      logs: [...this.logs],
      performance: [...this.performanceEvents],
      summary: this.getMetricsSummary()
    };
  }

  /**
   * Clear old data (for cleanup)
   */
  cleanup(olderThan: number = 3600000) { // Default 1 hour
    const cutoff = Date.now() - olderThan;
    
    this.metrics = this.metrics.filter(m => m.timestamp > cutoff);
    this.logs = this.logs.filter(l => l.timestamp > cutoff);
    this.performanceEvents = this.performanceEvents.filter(p => p.timestamp > cutoff);
  }
}

// Global observability instance
export const observability = new ObservabilityService();

/**
 * Decorator for automatic performance monitoring
 */
export function withObservability<T extends (...args: any[]) => Promise<any>>(
  operation: string,
  fn: T,
  contextExtractor?: (...args: Parameters<T>) => { agentId?: string; customerId?: string; }
): T {
  return (async (...args: Parameters<T>) => {
    const startTime = Date.now();
    const context = contextExtractor ? contextExtractor(...args) : {};
    
    observability.log('info', `Starting ${operation}`, {
      operation,
      ...context
    });

    try {
      const _result = await fn(...args);
      
      observability.recordPerformance(operation, startTime, true, context);
      observability.log('info', `Completed ${operation}`, {
        operation,
        ...context
      });

      return result;
    } catch {
      observability.recordPerformance(operation, startTime, false, context);
      observability.log('error', `Failed ${operation}`, {
        operation,
        ...context
      }, undefined, error as Error);

      throw error;
    }
  }) as T;
}

/**
 * Validation-specific observability helpers
 */
export const ValidationObservability = {
  recordPromptValidation: (success: boolean, issueCount: number, customerId: string) => {
    observability.recordMetric('prompt_validation.success', success ? 1 : 0, { customerId });
    observability.recordMetric('prompt_validation.issues', issueCount, { customerId });
  },

  recordSchemaValidation: (success: boolean, fieldCount: number, customerId: string) => {
    observability.recordMetric('schema_validation.success', success ? 1 : 0, { customerId });
    observability.recordMetric('schema_validation.field_count', fieldCount, { customerId });
  },

  recordPreviewOperation: (documentCount: number, successCount: number, avgConfidence: number, customerId: string) => {
    observability.recordMetric('preview.document_count', documentCount, { customerId });
    observability.recordMetric('preview.success_rate', successCount / documentCount, { customerId });
    observability.recordMetric('preview.avg_confidence', avgConfidence, { customerId });
  }
};

/**
 * Health check endpoint data
 */
export function getHealthStatus(): any {
  const summary = observability.getMetricsSummary(300000); // 5 minute window
  const alerts = summary.alerts;
  
  return {
    status: alerts.length === 0 ? 'healthy' : alerts.some(a => a.type === 'critical_errors') ? 'critical' : 'warning',
    timestamp: Date.now(),
    uptime: Date.now() - (observability as any).startTime || 0,
    metrics: summary.overview,
    alerts: alerts,
    services: {
      database: 'healthy', // This would be checked against actual DB
      rateLimit: 'healthy',
      validation: summary.overview.errorRate < 10 ? 'healthy' : 'degraded'
    }
  };
}