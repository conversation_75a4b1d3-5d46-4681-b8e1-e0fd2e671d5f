/**
 * Queue Processor Edge Function
 * 
 * Processes jobs from the queue system asynchronously
 * Called by pg_cron every 10 seconds to process queued jobs
 * 
 * Features:
 * - Batch processing of up to 3 jobs per invocation
 * - Multi-model AI processing with fallback logic
 * - Comprehensive error handling and retry logic
 * - Performance monitoring and metrics
 * - Webhook notifications for job completion
 */

import "jsr:@supabase/functions-js@2.5.0/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';
import { QueueManager, type NextJobResult, type ProcessingResult } from '../_shared/queue-manager.ts';
import { corsHeaders as _corsHeaders, createApiResponse, handleCors } from '../_shared/api-key-utils.ts';

// =============================================================================
// TYPE DEFINITIONS
// =============================================================================

interface ProcessorRequest {
  action?: 'process_batch' | 'process_single' | 'health_check' | 'metrics';
  jobId?: string;
  batchSize?: number;
  timeout?: number;
}

interface ProcessorResponse {
  success: boolean;
  processed: number;
  results: ProcessingResult[];
  metrics?: {
    totalProcessingTime: number;
    avgProcessingTime: number;
    successRate: number;
  };
  error?: string;
  timestamp: string;
}

interface DocumentData {
  id: string;
  filename: string;
  fileType: string;
  fileSize: number;
  storagePath: string;
  metadata: Record<string, unknown>;
}

interface AgentData {
  id: string;
  name: string;
  prompt: string;
  jsonSchema: Record<string, unknown>;
  category: string;
}

// =============================================================================
// ENVIRONMENT CONFIGURATION
// =============================================================================

const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
const CLAUDE_API_KEY = Deno.env.get('CLAUDE_API_KEY');
const LLAMAPARSE_API_KEY = Deno.env.get('LLAMAPARSE_API_KEY');

if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('Missing required environment variables: SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
}

// Initialize clients
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
const queueManager = new QueueManager(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, `queue-processor-${Date.now()}`);

// =============================================================================
// AI PROCESSING SERVICES
// =============================================================================

class AIProcessingService {
  /**
   * Process document using multi-model AI with fallback logic
   */
  static async processDocument(
    documentContent: string,
    agent: AgentData,
    correlationId: string
  ): Promise<{
    success: boolean;
    extractedData?: Record<string, unknown>;
    confidence?: number;
    model?: string;
    error?: string;
    errorCode?: string;
  }> {
    const models = [
      { name: 'openai', processor: this.processWithOpenAI },
      { name: 'claude', processor: this.processWithClaude },
      { name: 'llamaparse', processor: this.processWithLlamaParse }
    ];

    for (const model of models) {
      try {
        console.log(`[${correlationId}] Trying ${model.name} for document processing`);
        
        const _result = await model.processor(documentContent, agent, correlationId);
        
        if (result.success) {
          console.log(`[${correlationId}] Successfully processed with ${model.name}`);
          return {
            ...result,
            model: model.name
          };
        }
        
        console.log(`[${correlationId}] ${model.name} failed: ${result.error}`);
        
      } catch {
        console.error(`[${correlationId}] ${model.name} exception:`, error);
        // Continue to next model
      }
    }

    return {
      success: false,
      error: 'All AI models failed to process document',
      errorCode: 'ALL_MODELS_FAILED'
    };
  }

  /**
   * Process with OpenAI (primary)
   */
  private static async processWithOpenAI(
    documentContent: string,
    agent: AgentData,
    _correlationId: string
  ): Promise<{
    success: boolean;
    extractedData?: Record<string, unknown>;
    confidence?: number;
    error?: string;
  }> {
    if (!OPENAI_API_KEY) {
      return { success: false, error: 'OpenAI API key not configured' };
    }

    try {
      const messages = [
        {
          role: 'system',
          content: `${agent.prompt}

Extract data according to this JSON schema: ${JSON.stringify(agent.jsonSchema)}

Return only valid JSON matching the schema.`
        },
        {
          role: 'user',
          content: `Extract data from this document:

${documentContent}`
        }
      ];

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini',
          messages,
          temperature: 0.1,
          response_format: { type: 'json_object' }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { success: false, error: `OpenAI API error: ${response.status} - ${errorText}` };
      }

      const _data = await response.json();
      const extractedContent = data.choices?.[0]?.message?.content;

      if (!extractedContent) {
        return { success: false, error: 'No content returned from OpenAI' };
      }

      try {
        const extractedData = JSON.parse(extractedContent);
        return {
          success: true,
          extractedData,
          confidence: 0.95 // High confidence for structured response
        };
      } catch (_parseError) {
        return { success: false, error: 'Failed to parse JSON response from OpenAI' };
      }

    } catch {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'OpenAI processing failed'
      };
    }
  }

  /**
   * Process with Claude (fallback)
   */
  private static async processWithClaude(
    documentContent: string,
    agent: AgentData,
    _correlationId: string
  ): Promise<{
    success: boolean;
    extractedData?: Record<string, unknown>;
    confidence?: number;
    error?: string;
  }> {
    if (!CLAUDE_API_KEY) {
      return { success: false, error: 'Claude API key not configured' };
    }

    try {
      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${CLAUDE_API_KEY}`,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: 'claude-3-haiku-20240307',
          max_tokens: 4000,
          temperature: 0.1,
          messages: [
            {
              role: 'user',
              content: `${agent.prompt}\n\nExtract data according to this JSON schema: ${JSON.stringify(agent.jsonSchema)}\n\nDocument content:\n${documentContent}\n\nReturn only valid JSON matching the schema.`
            }
          ]
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        return { success: false, error: `Claude API error: ${response.status} - ${errorText}` };
      }

      const _data = await response.json();
      const extractedContent = data.content?.[0]?.text;

      if (!extractedContent) {
        return { success: false, error: 'No content returned from Claude' };
      }

      try {
        const extractedData = JSON.parse(extractedContent);
        return {
          success: true,
          extractedData,
          confidence: 0.90 // High confidence for Claude
        };
      } catch (_parseError) {
        return { success: false, error: 'Failed to parse JSON response from Claude' };
      }

    } catch {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Claude processing failed'
      };
    }
  }

  /**
   * Process with LlamaParse (final fallback for PDFs)
   */
  private static async processWithLlamaParse(
    _documentContent: string,
    _agent: AgentData,
    _correlationId: string
  ): Promise<{
    success: boolean;
    extractedData?: Record<string, unknown>;
    confidence?: number;
    error?: string;
  }> {
    if (!LLAMAPARSE_API_KEY) {
      return { success: false, error: 'LlamaParse API key not configured' };
    }

    // For now, return a basic extraction result
    // In production, this would integrate with actual LlamaParse API
    return {
      success: true,
      extractedData: {
        source: 'llamaparse',
        text: documentContent.slice(0, 1000), // First 1000 chars
        extraction_method: 'fallback'
      },
      confidence: 0.75 // Lower confidence for fallback
    };
  }
}

// =============================================================================
// DOCUMENT PROCESSOR
// =============================================================================

class DocumentProcessor {
  /**
   * Process a single job
   */
  static async processJob(job: NextJobResult): Promise<ProcessingResult> {
    const startTime = performance.now();
    const correlationId = job.correlationId;

    try {
      console.log(`[${correlationId}] Processing job ${job.jobId} for document ${job.documentId}`);

      // Get document data
      const documentResult = await this.getDocumentData(job.documentId);
      if (!documentResult.success) {
        return {
          jobId: job.jobId,
          success: false,
          error: documentResult.error || 'Failed to get document data',
          errorCode: 'DOCUMENT_NOT_FOUND',
          processingTimeMs: performance.now() - startTime
        };
      }

      const document = documentResult.document!;

      // Get agent data (use default if not specified)
      const agentResult = await this.getAgentData(job.agentId, job.customerId);
      if (!agentResult.success) {
        return {
          jobId: job.jobId,
          success: false,
          error: agentResult.error || 'Failed to get agent data',
          errorCode: 'AGENT_NOT_FOUND',
          processingTimeMs: performance.now() - startTime
        };
      }

      const agent = agentResult.agent!;

      // Extract document content
      const contentResult = await this.extractDocumentContent(document);
      if (!contentResult.success) {
        return {
          jobId: job.jobId,
          success: false,
          error: contentResult.error || 'Failed to extract document content',
          errorCode: 'CONTENT_EXTRACTION_FAILED',
          processingTimeMs: performance.now() - startTime
        };
      }

      const documentContent = contentResult.content!;

      // Process with AI
      const aiResult = await AIProcessingService.processDocument(
        documentContent,
        agent,
        correlationId
      );

      if (!aiResult.success) {
        return {
          jobId: job.jobId,
          success: false,
          error: aiResult.error || 'AI processing failed',
          errorCode: aiResult.errorCode || 'AI_PROCESSING_FAILED',
          processingTimeMs: performance.now() - startTime
        };
      }

      // Send webhook notification if configured
      if (job.jobData.webhookUrl) {
        await this.sendWebhookNotification(
          job.jobData.webhookUrl as string,
          job.jobId,
          'completed',
          aiResult.extractedData
        ).catch(error => {
          console.warn(`[${correlationId}] Webhook notification failed:`, error);
        });
      }

      return {
        jobId: job.jobId,
        success: true,
        extractedData: aiResult.extractedData,
        confidence: aiResult.confidence,
        model: aiResult.model,
        processingTimeMs: performance.now() - startTime,
        creditsUsed: 1 // Base credit cost
      };

    } catch {
      console.error(`[${correlationId}] Job processing exception:`, error);
      
      return {
        jobId: job.jobId,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown processing error',
        errorCode: 'PROCESSING_EXCEPTION',
        processingTimeMs: performance.now() - startTime
      };
    }
  }

  /**
   * Get document data from database
   */
  private static async getDocumentData(documentId: string): Promise<{
    success: boolean;
    document?: DocumentData;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('documents')
        .select('id, filename, file_type, file_size, storage_path, metadata')
        .eq('id', documentId)
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      if (!data) {
        return { success: false, error: 'Document not found' };
      }

      return {
        success: true,
        document: {
          id: data.id,
          filename: data.filename,
          fileType: data.file_type,
          fileSize: data.file_size,
          storagePath: data.storage_path,
          metadata: data.metadata || {}
        }
      };

    } catch {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database error'
      };
    }
  }

  /**
   * Get agent data from database
   */
  private static async getAgentData(agentId?: string, _customerId?: string): Promise<{
    success: boolean;
    agent?: AgentData;
    error?: string;
  }> {
    try {
      let query = supabase
        .from('agents')
        .select('id, name, prompt, json_schema, category');

      if (agentId) {
        // Get specific agent (customer's or default)
        query = query.eq('id', agentId);
      } else {
        // Get default invoice agent as fallback
        query = query.eq('is_default', true).eq('category', 'invoice').limit(1);
      }

      const { data, error } = await query.single();

      if (error) {
        return { success: false, error: error.message };
      }

      if (!data) {
        return { success: false, error: 'Agent not found' };
      }

      return {
        success: true,
        agent: {
          id: data.id,
          name: data.name,
          prompt: data.prompt,
          jsonSchema: data.json_schema || {},
          category: data.category
        }
      };

    } catch {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database error'
      };
    }
  }

  /**
   * Extract content from document
   * For now, returns placeholder text extraction
   */
  private static async extractDocumentContent(document: DocumentData): Promise<{
    success: boolean;
    content?: string;
    error?: string;
  }> {
    try {
      // In production, this would:
      // 1. Download file from storage
      // 2. Extract text based on file type (PDF, images, etc.)
      // 3. Preprocess and clean the text
      
      // For now, return placeholder content
      const placeholderContent = `Document: ${document.filename}
Type: ${document.fileType}
Size: ${document.fileSize} bytes

[This is placeholder content. In production, this would contain the actual extracted text from the document based on its type (PDF text extraction, OCR for images, etc.)]

Sample extracted text would appear here...`;

      return {
        success: true,
        content: placeholderContent
      };

    } catch {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Content extraction failed'
      };
    }
  }

  /**
   * Send webhook notification
   */
  private static async sendWebhookNotification(
    webhookUrl: string,
    jobId: string,
    status: string,
    data?: Record<string, unknown>
  ): Promise<void> {
    try {
      const payload = {
        jobId,
        status,
        timestamp: new Date().toISOString(),
        data
      };

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'IDP-Platform-Queue-Processor/1.0'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`Webhook returned ${response.status}: ${response.statusText}`);
      }

    } catch {
      console.error('Webhook notification failed:', error);
      throw error;
    }
  }
}

// =============================================================================
// MAIN HANDLER
// =============================================================================

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  // Only accept POST requests (called by pg_cron and manual triggers)
  if (req.method !== 'POST') {
    return createApiResponse('Method not allowed', 405, false);
  }

  const startTime = performance.now();

  try {
    const requestBody: ProcessorRequest = await req.json().catch(() => ({}));
    const action = requestBody.action || 'process_batch';
    const batchSize = Math.min(requestBody.batchSize || 3, 10); // Max 10 jobs per batch
    const timeout = Math.min(requestBody.timeout || 50000, 55000); // Max 55 seconds (Edge Function limit)

    console.log(`Queue processor starting: action=${action}, batchSize=${batchSize}`);

    // Handle different actions
    switch (action) {
      case 'health_check': {
      }
        return createApiResponse({
          status: 'healthy',
          processingNode: queueManager.getProcessingNodeId(),
          timestamp: new Date().toISOString()
        }, 200, true);

      case 'metrics': {
      }
        const metricsResult = await queueManager.getQueueMetrics();
        if (!metricsResult.success) {
          return createApiResponse(`Failed to get metrics: ${metricsResult.error}`, 500, false);
        }
        return createApiResponse(metricsResult.metrics, 200, true);

      case 'process_single': { {
        if (!requestBody.jobId) {
          return createApiResponse('Missing jobId for process_single action', 400, false);
        }
        
        // Get specific job and process it
        const jobResult = await queueManager.getJobStatus(requestBody.jobId);
        if (!jobResult.success || !jobResult.job) {
          return createApiResponse(`Job not found: ${requestBody.jobId}`, 404, false);
        }

        // Convert to NextJobResult format and process
        const singleJob: NextJobResult = {
          jobId: jobResult.job.id,
          customerId: jobResult.job.customer_id,
          documentId: jobResult.job.document_id,
          agentId: jobResult.job.agent_id,
          jobType: jobResult.job.job_type,
          jobData: jobResult.job.job_data,
          priority: jobResult.job.priority,
          retryCount: jobResult.job.retry_count,
          correlationId: jobResult.job.correlation_id
        };

        const singleResult = await DocumentProcessor.processJob(singleJob);
        
        return createApiResponse({
          success: true,
          processed: 1,
          results: [singleResult],
          timestamp: new Date().toISOString()
        }, 200, true);
      }

      case 'process_batch': {
      }
      default: {
        // Process batch of jobs
        const results: ProcessingResult[] = [];
        const batchStartTime = performance.now();

        for (let i = 0; i < batchSize; i++) {
          // Check timeout
          if (performance.now() - batchStartTime > timeout - 5000) { // 5s buffer
            console.log(`Batch processing timeout reached after ${i} jobs`);
            break;
    }
          }

          const processResult = await queueManager.processNextJob(async (job) => {
            return await DocumentProcessor.processJob(job);
          });

          if (!processResult.success) {
            console.error('Failed to process job:', processResult.error);
            break;
    }
          }

          if (!processResult.processed) {
            console.log('No more jobs to process');
            break;
    } // No more jobs in queue
          }

          if (processResult.result) {
            results.push(processResult.result);
          }
        }

        // Calculate metrics
        const totalProcessingTime = performance.now() - startTime;
        const avgProcessingTime = results.length > 0 ? totalProcessingTime / results.length : 0;
        const successfulJobs = results.filter(r => r.success).length;
        const successRate = results.length > 0 ? (successfulJobs / results.length) * 100 : 0;

        const response: ProcessorResponse = {
          success: true,
          processed: results.length,
          results,
          metrics: {
            totalProcessingTime,
            avgProcessingTime,
            successRate
          },
          timestamp: new Date().toISOString()
        };

        console.log(`Batch completed: processed=${results.length}, success_rate=${successRate.toFixed(1)}%, time=${totalProcessingTime.toFixed(0)}ms`);

        return createApiResponse(response, 200, true);
      }
    }
  } catch (error) {
    console.error('Queue processor error:', error);
    return createApiResponse(
      `Queue processor error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      500,
      false
    );
  }
});