/**
 * Admin API Key Generation Endpoint
 * 
 * Generates test and production API keys for customers
 * Supports dual key architecture (skt_/skp_) with different credit allocations
 */

import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
import {
  ApiKeyGenerationRequest,
  ApiKeyGenerationResult,
  _generateSecureApiKey,
  handleCors,
  createApiResponse,
  logApiUsage
} from "../../_shared/api-key-utils.ts";

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleCors();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return createApiResponse('Method not allowed', 405, false);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey);

  try {
    // Parse and validate request
    const body = await req.json() as ApiKeyGenerationRequest;
    
    // Validate required fields
    if (!body.customerId || !body.keyType) {
      return createApiResponse('Missing required fields: customerId, keyType', 400, false);
    }

    if (!['test', 'production'].includes(body.keyType)) {
      return createApiResponse('Invalid keyType. Must be "test" or "production"', 400, false);
    }

    // Verify customer exists
    const { data: customer, error: customerError } = await supabase
      .from('customers')
      .select('id, status')
      .eq('id', body.customerId)
      .single();

    if (customerError || !customer) {
      return createApiResponse('Customer not found', 404, false);
    }

    if (customer.status !== 'active') {
      return createApiResponse('Customer account is not active', 403, false);
    }

    // Generate API key using database function
    const { data: keyData, error: keyError } = await supabase.rpc('generate_api_key', {
      p_customer_id: body.customerId,
      p_key_type: body.keyType,
      p_credits: body.credits,
      p_name: body.name || `${body.keyType} Key`
    });

    if (keyError) {
      console.error('Key generation error:', keyError);
      return createApiResponse('Failed to generate API key', 500, false);
    }

    const _result = keyData[0];

    if (!result.success) {
      return createApiResponse(result.message, 400, false);
    }

    // Log the key generation
    await logApiUsage(
      supabase,
      body.customerId,
      result.key_id,
      'api_key_generation',
      true,
      0, // No credits consumed for key generation
      {
        key_type: body.keyType,
        initial_credits: result.credits,
        expires_at: result.expires_at
      }
    );

    // Prepare response
    const response: ApiKeyGenerationResult = {
      success: true,
      data: {
        rawKey: result.raw_key,
        keyId: result.key_id,
        customerId: body.customerId,
        keyType: body.keyType,
        credits: result.credits,
        expiresAt: result.expires_at
      }
    };

    // Log successful generation (without raw key)
    console.log('API key generated successfully:', {
      keyId: result.key_id,
      customerId: body.customerId,
      keyType: body.keyType,
      credits: result.credits,
      hasExpiry: !!result.expires_at
    });

    return createApiResponse(response.data, 200, true);

  } catch {
    console.error('Generate key error:', error);
    
    return createApiResponse(
      error instanceof Error ? error.message : 'Internal server error',
      500,
      false
    );
  }
});

// Add error handling for unhandled promise rejections
globalThis.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});