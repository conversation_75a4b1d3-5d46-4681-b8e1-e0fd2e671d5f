/**
 * Agent Metrics Edge Function
 * 
 * GitHub Issue #18: Agent Performance Tracking
 * Main API endpoint for agent performance tracking, benchmarking,
 * pattern analysis, and alerting systems.
 * 
 * Endpoints:
 * POST /agent-metrics/record - Record agent performance metrics
 * GET /agent-metrics/summary - Get agent performance summary
 * GET /agent-metrics/benchmark - Get agent benchmark report
 * GET /agent-metrics/insights - Get customization insights
 * GET /agent-metrics/alerts - Get performance alerts
 * POST /agent-metrics/alerts/acknowledge - Acknowledge alerts
 */

import 'jsr:@supabase/functions-js/edge-runtime.d.ts';
import { createClient } from 'jsr:@supabase/supabase-js@2.57.4';
import type { Database } from '../../../types/database.types.ts';
import {
  AgentPerformanceTracker,
  AgentBenchmarker,
  CustomizationAnalyzer,
  _PerformanceAlerter
} from '../_shared/agent-performance.ts';
import { validateApiKey, corsHeaders, createApiResponse } from '../_shared/api-key-utils.ts';
import type {
  _AgentPerformanceMetrics,
  RecordPerformanceRequest,
  RecordPerformanceResponse,
  _GetBenchmarkRequest,
  GetBenchmarkResponse,
  _GetCustomizationInsightsRequest,
  GetCustomizationInsightsResponse,
  _Get_PerformanceAlertsRequest,
  Get_PerformanceAlertsResponse,
  _GetAgentSummaryRequest,
  GetAgentSummaryResponse
} from '../../../types/agent-performance.types.ts';

// Initialize Supabase client
const supabase = createClient<Database>(
  Deno.env.get('SUPABASE_URL') ?? '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
);

// Initialize performance tracking components
const performanceTracker = new AgentPerformanceTracker(supabase);
const benchmarker = new AgentBenchmarker(supabase);
const _analyzer = new CustomizationAnalyzer(supabase);
const alerter = new _PerformanceAlerter(supabase);

/**
 * Main request handler
 */
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: corsHeaders });
  }

  try {
    const url = new URL(req.url);
    const pathname = url.pathname;
    const method = req.method;

    // Route requests to appropriate handlers
    if (method === 'POST' && pathname === '/agent-metrics/record') {
      return await handleRecordMetrics(req);
    }
    
    if (method === 'GET' && pathname === '/agent-metrics/summary') {
      return await handleGetSummary(req);
    }
    
    if (method === 'GET' && pathname === '/agent-metrics/benchmark') {
      return await handleGetBenchmark(req);
    }
    
    if (method === 'GET' && pathname === '/agent-metrics/insights') {
      return await handleGetInsights(req);
    }
    
    if (method === 'GET' && pathname === '/agent-metrics/alerts') {
      return await handleGetAlerts(req);
    }
    
    if (method === 'POST' && pathname === '/agent-metrics/alerts/acknowledge') {
      return await handleAcknowledgeAlerts(req);
    }

    // Route not found
    return createApiResponse(
      { 
        message: 'Endpoint not found', 
        available_endpoints: [
          'POST /agent-metrics/record',
          'GET /agent-metrics/summary',
          'GET /agent-metrics/benchmark',
          'GET /agent-metrics/insights',
          'GET /agent-metrics/alerts',
          'POST /agent-metrics/alerts/acknowledge'
        ]
      },
      404,
      false
    );

  } catch {
    console.error('Agent metrics function error:', error);
    return createApiResponse(
      { message: 'Internal server error', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
});

/**
 * Record agent performance metrics
 */
async function handleRecordMetrics(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse request body
    const body: RecordPerformanceRequest = await req.json();
    const metrics = body.metrics;

    if (!metrics) {
      return createApiResponse(
        { message: 'Performance metrics are required' },
        400,
        false
      );
    }

    // Validate that customer owns the agent
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('customer_id')
      .eq('id', metrics.agent_id)
      .single();

    if (agentError || !agent) {
      return createApiResponse(
        { message: 'Agent not found' },
        404,
        false
      );
    }

    if (agent.customer_id && agent.customer_id !== keyValidation.customerId) {
      return createApiResponse(
        { message: 'Unauthorized access to agent' },
        403,
        false
      );
    }

    // Record performance metrics
    const result = await performanceTracker.recordPerformance(metrics);

    // Check for alerts
    const alerts = await alerter.check_PerformanceAlerts(metrics);

    const response: RecordPerformanceResponse = {
      ...result,
      alertsTriggered: alerts
    };

    return createApiResponse(response);

  } catch {
    console.error('Error recording agent metrics:', error);
    return createApiResponse(
      { message: 'Failed to record metrics', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}

/**
 * Get agent performance summary
 */
async function handleGetSummary(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse query parameters
    const url = new URL(req.url);
    const agentId = url.searchParams.get('agentId');
    const _timeframeDays = parseInt(url.searchParams.get('timeframeDays') || '30');

    if (!agentId) {
      return createApiResponse(
        { message: 'agentId query parameter is required' },
        400,
        false
      );
    }

    // Validate agent access
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('customer_id, name')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      return createApiResponse(
        { message: 'Agent not found' },
        404,
        false
      );
    }

    if (agent.customer_id && agent.customer_id !== keyValidation.customerId) {
      return createApiResponse(
        { message: 'Unauthorized access to agent' },
        403,
        false
      );
    }

    // Get agent summary
    const summary = await performanceTracker.getAgentSummary(agentId);

    // Calculate trends (simplified)
    const trends = {
      processing_time: 'stable' as const,
      accuracy: 'stable' as const,
      usage: 'stable' as const
    };

    const response: GetAgentSummaryResponse = {
      success: true,
      summary: summary,
      trends: trends
    };

    return createApiResponse(response);

  } catch {
    console.error('Error getting agent summary:', error);
    return createApiResponse(
      { 
        success: false,
        summary: null as any,
        trends: null as any,
        error: error instanceof Error ? error.message : String(error) 
      },
      500,
      false
    );
  }
}

/**
 * Get agent benchmark report
 */
async function handleGetBenchmark(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse query parameters
    const url = new URL(req.url);
    const agentId = url.searchParams.get('agentId');
    const timeframe = url.searchParams.get('timeframe') as 'day' | 'week' | 'month' || 'week';

    if (!agentId) {
      return createApiResponse(
        { message: 'agentId query parameter is required' },
        400,
        false
      );
    }

    // Validate agent access
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('customer_id, cloned_from')
      .eq('id', agentId)
      .single();

    if (agentError || !agent) {
      return createApiResponse(
        { message: 'Agent not found' },
        404,
        false
      );
    }

    if (agent.customer_id && agent.customer_id !== keyValidation.customerId) {
      return createApiResponse(
        { message: 'Unauthorized access to agent' },
        403,
        false
      );
    }

    if (!agent.cloned_from) {
      return createApiResponse(
        { message: 'Cannot benchmark default agents' },
        400,
        false
      );
    }

    // Generate benchmark report
    const benchmark = await benchmarker.benchmarkAgentPerformance(agentId, timeframe);

    const response: GetBenchmarkResponse = {
      success: true,
      benchmark: benchmark
    };

    return createApiResponse(response);

  } catch {
    console.error('Error getting benchmark report:', error);
    return createApiResponse(
      { 
        success: false,
        benchmark: null as any,
        error: error instanceof Error ? error.message : String(error) 
      },
      500,
      false
    );
  }
}

/**
 * Get customization insights (admin only)
 */
async function handleGetInsights(req: Request): Promise<Response> {
  try {
    // Validate API key (would need admin check in real implementation)
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse query parameters
    const url = new URL(req.url);
    const _timeframe = url.searchParams.get('timeframe') as 'week' | 'month' | 'quarter' || 'month';
    const _category = url.searchParams.get('category');
    const _minUsageCount = parseInt(url.searchParams.get('minUsageCount') || '5');

    // Generate customization insights
    const insights = await analyzer.analyzeCustomizationPatterns();

    const response: GetCustomizationInsightsResponse = {
      success: true,
      insights: insights
    };

    return createApiResponse(response);

  } catch {
    console.error('Error getting customization insights:', error);
    return createApiResponse(
      { 
        success: false,
        insights: null as any,
        error: error instanceof Error ? error.message : String(error) 
      },
      500,
      false
    );
  }
}

/**
 * Get performance alerts
 */
async function handleGetAlerts(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse query parameters
    const url = new URL(req.url);
    const agentId = url.searchParams.get('agentId');
    const severity = url.searchParams.getAll('severity');
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const since = url.searchParams.get('since');

    // Build query
    let query = supabase
      .from('performance_alerts')
      .select('*')
      .eq('customer_id', keyValidation.customerId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (agentId) {
      query = query.eq('agent_id', agentId);
    }

    if (severity.length > 0) {
      query = query.in('severity', severity);
    }

    if (since) {
      query = query.gte('created_at', since);
    }

    const { data: alerts, error } = await query;

    if (error) {
      throw new Error(`Failed to get alerts: ${error.message}`);
    }

    // Convert database format to API format
    const formattedAlerts = (alerts || []).map(alert => ({
      type: alert.alert_type,
      severity: alert.severity,
      agent_id: alert.agent_id,
      customer_id: alert.customer_id,
      message: alert.message,
      timestamp: new Date(alert.created_at),
      threshold_value: alert.threshold_value,
      actual_value: alert.actual_value,
      metadata: alert.metadata
    }));

    const response: Get_PerformanceAlertsResponse = {
      success: true,
      alerts: formattedAlerts,
      total_count: formattedAlerts.length
    };

    return createApiResponse(response);

  } catch {
    console.error('Error getting performance alerts:', error);
    return createApiResponse(
      { 
        success: false,
        alerts: [],
        total_count: 0,
        error: error instanceof Error ? error.message : String(error) 
      },
      500,
      false
    );
  }
}

/**
 * Acknowledge performance alerts
 */
async function handleAcknowledgeAlerts(req: Request): Promise<Response> {
  try {
    // Validate API key
    const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
    if (!apiKey) {
      return createApiResponse({ message: 'API key required' }, 401, false);
    }

    const keyValidation = await validateApiKey(supabase, apiKey);
    if (!keyValidation.isValid) {
      return createApiResponse({ message: keyValidation.error }, 401, false);
    }

    // Parse request body
    const body = await req.json();
    const { alertIds } = body;

    if (!alertIds || !Array.isArray(alertIds)) {
      return createApiResponse(
        { message: 'alertIds array is required' },
        400,
        false
      );
    }

    // Update alerts
    const { error } = await supabase
      .from('performance_alerts')
      .update({
        acknowledged: true,
        acknowledged_by: keyValidation.customerId,
        acknowledged_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .in('id', alertIds)
      .eq('customer_id', keyValidation.customerId);

    if (error) {
      throw new Error(`Failed to acknowledge alerts: ${error.message}`);
    }

    return createApiResponse({
      success: true,
      acknowledged_count: alertIds.length
    });

  } catch {
    console.error('Error acknowledging alerts:', error);
    return createApiResponse(
      { message: 'Failed to acknowledge alerts', error: error instanceof Error ? error.message : String(error) },
      500,
      false
    );
  }
}