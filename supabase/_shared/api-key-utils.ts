/**
 * API Key Management Utilities
 * 
 * Shared utilities for secure API key generation, validation, and hashing
 * Used across multiple Edge Functions for consistency
 */

// Type definitions for API key operations
export interface ApiKeyValidationResult {
  isValid: boolean;
  customerId?: string;
  keyId?: string;
  keyType?: 'test' | 'production';
  credits?: number;
  status?: 'active' | 'suspended' | 'revoked';
  isExpired?: boolean;
  error?: string;
}

export interface ApiKeyGenerationRequest {
  customerId: string;
  keyType: 'test' | 'production';
  credits?: number;
  name?: string;
}

export interface ApiKeyGenerationResult {
  success: boolean;
  data?: {
    rawKey: string;
    keyId: string;
    customerId: string;
    keyType: 'test' | 'production';
    credits: number;
    expiresAt?: string;
  };
  error?: string;
}

/**
 * Generate a cryptographically secure API key with proper prefix
 */
export function generateSecureApiKey(keyType: 'test' | 'production'): string {
  const prefix = keyType === 'test' ? 'skt_' : 'skp_';

  // Generate 32 hex characters (16 bytes = 32 hex chars)
  const randomBytes = crypto.getRandomValues(new Uint8Array(16));
  const hexString = Array.from(randomBytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');

  return `${prefix}${hexString}`;
}

/**
 * Hash API key using SHA-256 for secure storage
 */
export async function hashApiKey(rawKey: string): Promise<string> {
  const encoder = new TextEncoder();
  const _data = encoder.encode(rawKey);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

/**
 * Validate API key format
 */
export function validateKeyFormat(key: string): boolean {
  // Test keys: skt_[32 hex chars]
  // Production keys: skp_[32 hex chars]
  return /^sk[tp]_[a-f0-9]{32}$/.test(key);
}

/**
 * Extract key type from API key
 */
export function extractKeyType(key: string): 'test' | 'production' | null {
  if (key.startsWith('skt_')) return 'test';
  if (key.startsWith('skp_')) return 'production';
  return null;
}

/**
 * Validate API key against database and return customer context
 */
export async function validateApiKey(
  supabase: any,
  rawKey: string
): Promise<ApiKeyValidationResult> {
  try {
    // Validate format first
    if (!validateKeyFormat(rawKey)) {
      return {
        isValid: false,
        error: 'Invalid API key format'
      };
    }

    // Use database function to validate key
    const { data, error } = await supabase.rpc('validate_api_key_auth', {
      raw_key: rawKey
    });

    if (error) {
      console.error('Database validation error:', error);
      return {
        isValid: false,
        error: 'Failed to validate API key'
      };
    }

    if (!data || data.length === 0) {
      return {
        isValid: false,
        error: 'Invalid API key'
      };
    }

    const keyData = data[0];

    // Check if key is expired
    if (keyData.is_expired) {
      return {
        isValid: false,
        error: 'API key has expired'
      };
    }

    // Check if key is active
    if (keyData.status !== 'active') {
      return {
        isValid: false,
        error: `API key is ${keyData.status}`
      };
    }

    return {
      isValid: true,
      customerId: keyData.customer_id,
      keyId: keyData.key_id,
      keyType: keyData.key_type,
      credits: keyData.credits,
      status: keyData.status,
      isExpired: keyData.is_expired
    };

  } catch {
    console.error('API key validation error:', error);
    return {
      isValid: false,
      error: 'Internal validation error'
    };
  }
}

/**
 * Check rate limits for an API key
 */
export async function checkRateLimit(
  supabase: any,
  keyId: string,
  windowType: 'minute' | 'hour' | 'day' = 'minute'
): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
  try {
    const { data, error } = await supabase.rpc('increment_rate_limit', {
      p_api_key_id: keyId,
      p_window_type: windowType
    });

    if (error) {
      console.error('Rate limit check error:', error);
      // Fail open for rate limiting errors
      return { allowed: true, remaining: 0, resetTime: new Date() };
    }

    const _result = data[0];
    const resetTime = new Date();
    
    // Calculate reset time based on window type
    switch (windowType) {
      case 'minute': {
        resetTime.setSeconds(0, 0);
        resetTime.setMinutes(resetTime.getMinutes() + 1);
        break;
    }
      case 'hour': {
        resetTime.setMinutes(0, 0, 0);
        resetTime.setHours(resetTime.getHours() + 1);
        break;
    }
      case 'day': {
        resetTime.setHours(0, 0, 0, 0);
        resetTime.setDate(resetTime.getDate() + 1);
        break;
    }
    }

    return {
      allowed: !result.limit_exceeded,
      remaining: Math.max(0, 100 - result.new_count), // Assuming 100 as default limit
      resetTime
    };

  } catch {
    console.error('Rate limit error:', error);
    // Fail open for rate limiting errors
    return { allowed: true, remaining: 0, resetTime: new Date() };
  }
}

/**
 * Deduct credits from an API key
 */
export async function deductCredits(
  supabase: any,
  keyId: string,
  creditsToDeduct: number
): Promise<{ success: boolean; remainingCredits?: number; error?: string }> {
  try {
    const { data, error } = await supabase.rpc('deduct_api_key_credits', {
      p_key_id: keyId,
      p_credits_to_deduct: creditsToDeduct
    });

    if (error) {
      console.error('Credit deduction error:', error);
      return { success: false, error: 'Failed to deduct credits' };
    }

    const _result = data[0];
    
    if (!result.success) {
      return { 
        success: false, 
        error: result.message,
        remainingCredits: result.remaining_credits 
      };
    }

    return {
      success: true,
      remainingCredits: result.remaining_credits
    };

  } catch {
    console.error('Credit deduction error:', error);
    return { success: false, error: 'Internal error during credit deduction' };
  }
}

/**
 * Log API usage for audit and billing
 */
export async function logApiUsage(
  supabase: any,
  customerId: string,
  keyId: string,
  operationType: string,
  success: boolean,
  creditsUsed: number = 1,
  metadata: Record<string, any> = {}
): Promise<void> {
  try {
    await supabase.from('usage_logs').insert({
      customer_id: customerId,
      api_key_id: keyId,
      operation_type: operationType,
      success,
      credits_used: creditsUsed,
      model_cost: 0, // Will be updated when we know actual costs
      customer_price: creditsUsed * 0.01, // Assuming $0.01 per credit
      metadata,
      created_at: new Date().toISOString()
    });
  } catch {
    // Log usage errors but don't fail the main operation
    console.error('Failed to log API usage:', error);
  }
}

/**
 * CORS headers for API responses
 */
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
};

/**
 * Create standardized API response
 */
export function createApiResponse(
  data: any,
  status: number = 200,
  success: boolean = true
): Response {
  return new Response(
    JSON.stringify({
      success,
      data: success ? data : undefined,
      error: success ? undefined : data,
      timestamp: new Date().toISOString()
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders
      }
    }
  );
}

/**
 * Handle CORS preflight requests
 */
export function handleCors(): Response {
  return new Response(null, {
    status: 200,
    headers: corsHeaders
  });
}