-- Fix contract agent prompt to include "legal" text as expected by test
-- Test expects to find "legal" but prompt only has "governing law"

UPDATE public.agents 
SET prompt = REPLACE(prompt, '2. Contract details (type, effective date, expiration date, governing law)', '2. Contract details (type, effective date, expiration date, legal governing law)'),
    system_prompt = REPLACE(system_prompt, '2. Contract details (type, effective date, expiration date, governing law)', '2. Contract details (type, effective date, expiration date, legal governing law)')
WHERE agent_id = 'default-contract-v1' AND is_default = true;

-- Verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-contract-v1' AND is_default = true;
    
    IF prompt_text LIKE '%legal%' THEN
        RAISE NOTICE 'Contract agent prompt fix successful - contains "legal"';
    ELSE
        RAISE EXCEPTION 'Contract agent prompt fix failed - does not contain "legal"';
    END IF;
END;
$$;