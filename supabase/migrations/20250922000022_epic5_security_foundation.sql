-- ================================================================================
-- EPIC 5: SECURITY FOUNDATION - Security Events & Monitoring Tables
-- ================================================================================

-- Security events table for threat monitoring
CREATE TABLE IF NOT EXISTS security_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type text NOT NULL CHECK (event_type IN (
    'prompt_injection_attempt',
    'suspicious_input', 
    'rate_limit_violation',
    'malicious_file_upload',
    'unauthorized_access',
    'brute_force_attempt'
  )),
  customer_id uuid REFERENCES customers(id) ON DELETE SET NULL,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE SET NULL,
  threat_details jsonb NOT NULL DEFAULT '{}',
  ip_address text NOT NULL,
  user_agent text NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  correlation_id text NOT NULL,
  created_at timestamptz DEFAULT now()
);

-- Indexes for security events
CREATE INDEX idx_security_events_type_severity ON security_events(event_type, severity);
CREATE INDEX idx_security_events_customer_date ON security_events(customer_id, created_at DESC);
CREATE INDEX idx_security_events_correlation ON security_events(correlation_id);
CREATE INDEX idx_security_events_ip_date ON security_events(ip_address, created_at DESC);
CREATE INDEX idx_security_events_created_at ON security_events(created_at DESC);

-- Security configuration table
CREATE TABLE IF NOT EXISTS security_config (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  config_key text UNIQUE NOT NULL,
  config_value jsonb NOT NULL,
  description text,
  updated_by text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Insert default security configuration
INSERT INTO security_config (config_key, config_value, description) VALUES
('input_sanitization', '{
  "max_input_length": 50000,
  "rate_limit_window_ms": 60000,
  "max_requests_per_window": 100,
  "blocked_patterns_enabled": true,
  "html_sanitization_enabled": true
}', 'Input sanitization and prompt injection protection settings'),
('threat_detection', '{
  "auto_block_enabled": true,
  "risk_score_threshold": 75,
  "alert_webhook_enabled": true,
  "log_all_attempts": true
}', 'Threat detection and response configuration'),
('rate_limiting', '{
  "global_rate_limit": 1000,
  "per_key_rate_limit": 100,
  "burst_detection_enabled": true,
  "progressive_delays": true
}', 'Rate limiting and DDoS protection settings');

-- Performance metrics table (enhanced for Epic 5)
CREATE TABLE IF NOT EXISTS performance_metrics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  endpoint text NOT NULL,
  method text NOT NULL,
  status_code integer NOT NULL,
  response_time_ms integer NOT NULL,
  db_query_time_ms integer,
  ai_service_time_ms integer,
  queue_wait_time_ms integer,
  customer_id uuid REFERENCES customers(id) ON DELETE SET NULL,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE SET NULL,
  correlation_id text,
  ip_address text,
  user_agent text,
  request_size_bytes integer,
  response_size_bytes integer,
  created_at timestamptz DEFAULT now()
);

-- Indexes for performance metrics (conditional creation based on existing schema)
DO $$
BEGIN
  -- Check if performance_metrics table exists and what columns it has
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'performance_metrics') THEN
    -- Create indexes only for columns that exist in both the table and our expected schema
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'endpoint')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'created_at') THEN
      CREATE INDEX IF NOT EXISTS idx_performance_metrics_endpoint_date ON performance_metrics(endpoint, created_at DESC);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'customer_id')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'created_at') THEN
      CREATE INDEX IF NOT EXISTS idx_performance_metrics_customer_date ON performance_metrics(customer_id, created_at DESC);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'status_code')
       AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'created_at') THEN
      CREATE INDEX IF NOT EXISTS idx_performance_metrics_status_date ON performance_metrics(status_code, created_at DESC);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'response_time_ms') THEN
      CREATE INDEX IF NOT EXISTS idx_performance_metrics_response_time ON performance_metrics(response_time_ms DESC);
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'performance_metrics' AND column_name = 'correlation_id') THEN
      CREATE INDEX IF NOT EXISTS idx_performance_metrics_correlation ON performance_metrics(correlation_id);
    END IF;
  END IF;
END $$;

-- Audit logs table enhancements (if not exists from previous epics)
DO $$ 
BEGIN
  -- Add new columns to audit_logs if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'event_category') THEN
    ALTER TABLE audit_logs ADD COLUMN event_category text;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'user_agent') THEN
    ALTER TABLE audit_logs ADD COLUMN user_agent text;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'request_id') THEN
    ALTER TABLE audit_logs ADD COLUMN request_id text;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'session_id') THEN
    ALTER TABLE audit_logs ADD COLUMN session_id text;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'audit_logs' AND column_name = 'processing_time_ms') THEN
    ALTER TABLE audit_logs ADD COLUMN processing_time_ms integer;
  END IF;
END $$;

-- Enhanced indexes for audit_logs
CREATE INDEX IF NOT EXISTS idx_audit_logs_category_date ON audit_logs(event_category, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_customer_action ON audit_logs(customer_id, action, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_request_id ON audit_logs(request_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);

-- Error logs table for standardized error tracking
CREATE TABLE IF NOT EXISTS error_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  error_code text NOT NULL,
  error_message text NOT NULL,
  stack_trace text,
  endpoint text,
  method text,
  customer_id uuid REFERENCES customers(id) ON DELETE SET NULL,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE SET NULL,
  correlation_id text NOT NULL,
  ip_address text,
  user_agent text,
  request_data jsonb,
  context_data jsonb DEFAULT '{}',
  severity text CHECK (severity IN ('low', 'medium', 'high', 'critical')) DEFAULT 'medium',
  resolved boolean DEFAULT false,
  resolution_notes text,
  created_at timestamptz DEFAULT now(),
  resolved_at timestamptz
);

-- Indexes for error logs
CREATE INDEX idx_error_logs_code_date ON error_logs(error_code, created_at DESC);
CREATE INDEX idx_error_logs_customer_date ON error_logs(customer_id, created_at DESC);
CREATE INDEX idx_error_logs_correlation ON error_logs(correlation_id);
CREATE INDEX idx_error_logs_severity_resolved ON error_logs(severity, resolved);
CREATE INDEX idx_error_logs_created_at ON error_logs(created_at DESC);

-- Alert thresholds table for monitoring
CREATE TABLE IF NOT EXISTS alert_thresholds (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  metric_name text NOT NULL,
  threshold_type text NOT NULL CHECK (threshold_type IN ('response_time', 'error_rate', 'availability', 'security_events')),
  threshold_value numeric NOT NULL,
  duration_minutes integer NOT NULL,
  severity text NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  enabled boolean DEFAULT true,
  webhook_url text,
  email_recipients text[],
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Insert default alert thresholds
INSERT INTO alert_thresholds (metric_name, threshold_type, threshold_value, duration_minutes, severity, webhook_url) VALUES
('API Response Time', 'response_time', 5000, 5, 'high', NULL),
('Error Rate', 'error_rate', 0.05, 10, 'medium', NULL),
('Security Events', 'security_events', 10, 5, 'critical', NULL),
('System Availability', 'availability', 0.99, 15, 'critical', NULL);

-- Compliance audit trail table
CREATE TABLE IF NOT EXISTS compliance_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  compliance_type text NOT NULL CHECK (compliance_type IN ('gdpr', 'ccpa', 'hipaa', 'sox', 'pci')),
  event_type text NOT NULL,
  customer_id uuid REFERENCES customers(id) ON DELETE SET NULL,
  data_subject_id text,
  legal_basis text,
  data_categories text[],
  purpose text,
  retention_period interval,
  automated_decision boolean DEFAULT false,
  third_party_sharing boolean DEFAULT false,
  cross_border_transfer boolean DEFAULT false,
  consent_given boolean,
  consent_withdrawn_at timestamptz,
  deletion_requested_at timestamptz,
  deletion_completed_at timestamptz,
  audit_trail jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
);

-- Indexes for compliance events
CREATE INDEX idx_compliance_events_type_customer ON compliance_events(compliance_type, customer_id);
CREATE INDEX idx_compliance_events_subject ON compliance_events(data_subject_id);
CREATE INDEX idx_compliance_events_deletion ON compliance_events(deletion_requested_at, deletion_completed_at);

-- Row Level Security for all new tables
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE error_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE alert_thresholds ENABLE ROW LEVEL SECURITY;
ALTER TABLE compliance_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for security events (admin access only)
CREATE POLICY "Admin access to security events" ON security_events
  FOR ALL USING (
    current_setting('app.user_role', true) = 'admin' OR
    current_setting('app.user_role', true) = 'service_role'
  );

-- RLS Policies for security config (admin access only)
CREATE POLICY "Admin access to security config" ON security_config
  FOR ALL USING (
    current_setting('app.user_role', true) = 'admin' OR
    current_setting('app.user_role', true) = 'service_role'
  );

-- RLS Policies for performance metrics
CREATE POLICY "Customer access to own performance metrics" ON performance_metrics
  FOR SELECT USING (
    customer_id = current_setting('app.customer_id', true)::uuid OR
    current_setting('app.user_role', true) = 'admin' OR
    current_setting('app.user_role', true) = 'service_role'
  );

-- RLS Policies for error logs
CREATE POLICY "Customer access to own error logs" ON error_logs
  FOR SELECT USING (
    customer_id = current_setting('app.customer_id', true)::uuid OR
    current_setting('app.user_role', true) = 'admin' OR
    current_setting('app.user_role', true) = 'service_role'
  );

-- RLS Policies for alert thresholds (admin access only)
CREATE POLICY "Admin access to alert thresholds" ON alert_thresholds
  FOR ALL USING (
    current_setting('app.user_role', true) = 'admin' OR
    current_setting('app.user_role', true) = 'service_role'
  );

-- RLS Policies for compliance events
CREATE POLICY "Customer access to own compliance events" ON compliance_events
  FOR SELECT USING (
    customer_id = current_setting('app.customer_id', true)::uuid OR
    current_setting('app.user_role', true) = 'admin' OR
    current_setting('app.user_role', true) = 'service_role'
  );

-- Functions for security monitoring
CREATE OR REPLACE FUNCTION get_security_metrics(
  p_start_date timestamptz DEFAULT now() - interval '24 hours',
  p_end_date timestamptz DEFAULT now()
) RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  SELECT jsonb_build_object(
    'total_events', COUNT(*),
    'events_by_type', jsonb_object_agg(event_type, type_count),
    'events_by_severity', jsonb_object_agg(severity, severity_count),
    'top_threat_ips', top_ips,
    'period', jsonb_build_object('start', p_start_date, 'end', p_end_date)
  ) INTO result
  FROM (
    SELECT 
      event_type,
      severity,
      COUNT(*) OVER (PARTITION BY event_type) as type_count,
      COUNT(*) OVER (PARTITION BY severity) as severity_count
    FROM security_events 
    WHERE created_at >= p_start_date AND created_at <= p_end_date
  ) events,
  LATERAL (
    SELECT jsonb_agg(
      jsonb_build_object('ip', ip_address, 'event_count', cnt)
      ORDER BY cnt DESC
    ) FILTER (WHERE rn <= 10)
    FROM (
      SELECT ip_address, COUNT(*) as cnt, ROW_NUMBER() OVER (ORDER BY COUNT(*) DESC) as rn
      FROM security_events 
      WHERE created_at >= p_start_date AND created_at <= p_end_date
      GROUP BY ip_address
    ) top_ips_ranked
  ) top_ips(top_ips);
  
  RETURN COALESCE(result, '{"total_events": 0}'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check alert thresholds
CREATE OR REPLACE FUNCTION check_alert_thresholds()
RETURNS void AS $$
DECLARE
  threshold_record alert_thresholds%ROWTYPE;
  metric_value numeric;
  should_alert boolean;
BEGIN
  -- Loop through all enabled thresholds
  FOR threshold_record IN 
    SELECT * FROM alert_thresholds WHERE enabled = true
  LOOP
    should_alert := false;
    
    CASE threshold_record.threshold_type
      WHEN 'response_time' THEN
        SELECT AVG(response_time_ms) INTO metric_value
        FROM performance_metrics 
        WHERE created_at >= now() - (threshold_record.duration_minutes || ' minutes')::interval
          AND response_time_ms > threshold_record.threshold_value;
        
        should_alert := metric_value IS NOT NULL AND metric_value > threshold_record.threshold_value;
        
      WHEN 'error_rate' THEN
        SELECT 
          CASE WHEN total_requests > 0 
               THEN error_requests::numeric / total_requests::numeric 
               ELSE 0 
          END INTO metric_value
        FROM (
          SELECT 
            COUNT(*) as total_requests,
            COUNT(*) FILTER (WHERE status_code >= 400) as error_requests
          FROM performance_metrics 
          WHERE created_at >= now() - (threshold_record.duration_minutes || ' minutes')::interval
        ) error_stats;
        
        should_alert := metric_value > threshold_record.threshold_value;
        
      WHEN 'security_events' THEN
        SELECT COUNT(*) INTO metric_value
        FROM security_events 
        WHERE created_at >= now() - (threshold_record.duration_minutes || ' minutes')::interval
          AND severity IN ('high', 'critical');
        
        should_alert := metric_value > threshold_record.threshold_value;
        
      ELSE
        CONTINUE;
    END CASE;
    
    -- Log the alert if threshold is breached
    IF should_alert THEN
      INSERT INTO audit_logs (
        action, 
        event_category, 
        status, 
        details, 
        correlation_id,
        created_at
      ) VALUES (
        'alert_threshold_breached',
        'monitoring',
        'warning',
        jsonb_build_object(
          'threshold_id', threshold_record.id,
          'metric_name', threshold_record.metric_name,
          'threshold_value', threshold_record.threshold_value,
          'actual_value', metric_value,
          'severity', threshold_record.severity
        ),
        gen_random_uuid()::text,
        now()
      );
    END IF;
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Comments for documentation
COMMENT ON TABLE security_events IS 'Security events and threat detection logs';
COMMENT ON TABLE security_config IS 'Security configuration settings';
COMMENT ON TABLE performance_metrics IS 'API performance and latency metrics';
COMMENT ON TABLE error_logs IS 'Standardized error tracking and debugging';
COMMENT ON TABLE alert_thresholds IS 'Monitoring alert configuration';
COMMENT ON TABLE compliance_events IS 'GDPR and compliance audit trail';

COMMENT ON FUNCTION get_security_metrics IS 'Generate security metrics summary for monitoring dashboard';
COMMENT ON FUNCTION check_alert_thresholds IS 'Check and trigger alerts based on configured thresholds';