-- Epic 4.6: Customer Support Tools
-- Migration for customer support and troubleshooting capabilities

-- Create customer error aggregation view as specified in the epic
CREATE VIEW customer_errors AS
SELECT 
  customer_id,
  DATE(created_at) as error_date,
  details->>'error_code' as error_code,
  COUNT(*) as error_count,
  MIN(created_at) as first_occurrence,
  MAX(created_at) as last_occurrence,
  array_agg(DISTINCT details->>'correlation_id') as correlation_ids,
  array_agg(DISTINCT resource_id) as affected_resources,
  AVG(CASE WHEN risk_level = 'low' THEN 1 
           WHEN risk_level = 'medium' THEN 2 
           WHEN risk_level = 'high' THEN 3 
           WHEN risk_level = 'critical' THEN 4 
           ELSE 1 END) as avg_risk_score
FROM audit_logs 
WHERE event_type = 'processing_error'
  AND details->>'error_code' IS NOT NULL
GROUP BY customer_id, DATE(created_at), details->>'error_code';

-- Create index for better performance on customer error queries
CREATE INDEX idx_audit_logs_customer_error_date 
ON audit_logs (customer_id, created_at) 
WHERE event_type = 'processing_error';

CREATE INDEX idx_audit_logs_error_code
ON audit_logs ((details->>'error_code'))
WHERE event_type = 'processing_error';

-- Create customer support sessions table for impersonation tracking
CREATE TABLE IF NOT EXISTS customer_support_sessions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  admin_user_id text NOT NULL,
  session_token text UNIQUE NOT NULL,
  reason text NOT NULL,
  duration_minutes integer NOT NULL DEFAULT 30,
  started_at timestamptz NOT NULL DEFAULT now(),
  expires_at timestamptz NOT NULL,
  ended_at timestamptz,
  is_active boolean NOT NULL DEFAULT true,
  admin_notes text,
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Create index for active sessions lookup
CREATE INDEX idx_support_sessions_active 
ON customer_support_sessions (customer_id, is_active, expires_at) 
WHERE is_active = true;

-- Create notification templates table
CREATE TABLE IF NOT EXISTS notification_templates (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  template_type text NOT NULL,
  subject_template text NOT NULL,
  message_template text NOT NULL,
  priority text NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'critical')),
  is_active boolean NOT NULL DEFAULT true,
  variables jsonb DEFAULT '{}', -- Available template variables
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now(),
  UNIQUE(template_type)
);

-- Create customer notifications log
CREATE TABLE IF NOT EXISTS customer_notifications (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  template_type text NOT NULL,
  subject text NOT NULL,
  message text NOT NULL,
  priority text NOT NULL DEFAULT 'medium',
  sent_at timestamptz NOT NULL DEFAULT now(),
  sent_by text NOT NULL, -- Admin user ID
  delivery_status text NOT NULL DEFAULT 'sent' CHECK (delivery_status IN ('sent', 'delivered', 'failed', 'bounced')),
  delivery_details jsonb DEFAULT '{}',
  correlation_id text,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Create index for notification history queries
CREATE INDEX idx_customer_notifications_customer_date 
ON customer_notifications (customer_id, sent_at DESC);

-- Insert default notification templates
INSERT INTO notification_templates (template_type, subject_template, message_template, priority, variables) VALUES
('credit_alert', 
 'Low Credit Balance Alert - {{customer_name}}', 
 'Hello {{customer_name}},

Your account currently has {{credits_remaining}} credits remaining. To ensure uninterrupted service, please consider adding more credits to your account.

Current tier: {{tier}}
Credits remaining: {{credits_remaining}}

You can add credits through your dashboard or contact our support team for assistance.

Best regards,
The IDP Platform Team', 
 'medium',
 '{"customer_name": "string", "credits_remaining": "number", "tier": "string"}'::jsonb),

('service_alert', 
 'Service Alert - {{alert_type}}', 
 'Hello {{customer_name}},

We wanted to inform you about an important service update:

{{message_body}}

Affected services: {{affected_services}}
Expected resolution: {{resolution_time}}

We apologize for any inconvenience and appreciate your patience.

Best regards,
The IDP Platform Team', 
 'high',
 '{"customer_name": "string", "alert_type": "string", "message_body": "string", "affected_services": "string", "resolution_time": "string"}'::jsonb),

('issue_resolution', 
 'Issue Resolved - {{issue_type}}', 
 'Hello {{customer_name}},

Good news! The issue you reported has been resolved.

Issue: {{issue_description}}
Resolution: {{resolution_description}}
Resolved at: {{resolved_at}}

If you continue to experience any problems, please don''t hesitate to contact our support team.

Best regards,
The IDP Platform Team', 
 'medium',
 '{"customer_name": "string", "issue_type": "string", "issue_description": "string", "resolution_description": "string", "resolved_at": "string"}'::jsonb),

('processing_failure', 
 'Processing Issue Detected - Action Required', 
 'Hello {{customer_name}},

We''ve detected recurring processing issues with your account that may require attention:

Issue: {{error_type}}
Frequency: {{error_frequency}}
Last occurrence: {{last_error_time}}

Suggested actions:
{{suggested_actions}}

Our support team is available to help resolve this issue. Please contact us if you need assistance.

Best regards,
The IDP Platform Team', 
 'high',
 '{"customer_name": "string", "error_type": "string", "error_frequency": "string", "last_error_time": "string", "suggested_actions": "string"}'::jsonb);

-- Create materialized view for customer support metrics
CREATE MATERIALIZED VIEW customer_support_metrics AS
SELECT 
  c.id as customer_id,
  c.customer_id as customer_identifier,
  c.name as customer_name,
  c.tier,
  c.status,
  
  -- Error metrics (last 30 days)
  COALESCE(error_stats.total_errors, 0) as total_errors_30d,
  COALESCE(error_stats.unique_error_types, 0) as unique_error_types_30d,
  COALESCE(error_stats.avg_daily_errors, 0) as avg_daily_errors_30d,
  error_stats.most_common_error,
  
  -- Usage metrics (last 30 days)
  COALESCE(usage_stats.total_requests, 0) as total_requests_30d,
  COALESCE(usage_stats.successful_requests, 0) as successful_requests_30d,
  COALESCE(usage_stats.error_rate, 0) as error_rate_30d,
  COALESCE(usage_stats.avg_processing_time, 0) as avg_processing_time_30d,
  
  -- Support activity
  COALESCE(support_stats.total_notifications, 0) as total_notifications_sent,
  COALESCE(support_stats.last_notification_date, NULL) as last_notification_date,
  COALESCE(support_stats.impersonation_sessions, 0) as impersonation_sessions_count,
  
  -- Risk indicators
  CASE 
    WHEN COALESCE(usage_stats.error_rate, 0) > 0.1 THEN 'high'
    WHEN COALESCE(usage_stats.error_rate, 0) > 0.05 THEN 'medium'
    ELSE 'low'
  END as error_risk_level,
  
  CASE 
    WHEN c.credits_available < 50 THEN 'high'
    WHEN c.credits_available < 200 THEN 'medium'
    ELSE 'low'
  END as credit_risk_level,
  
  now() as last_updated

FROM customers c

-- Error statistics subquery
LEFT JOIN (
  SELECT 
    customer_id,
    COUNT(*) as total_errors,
    COUNT(DISTINCT details->>'error_code') as unique_error_types,
    COUNT(*) / 30.0 as avg_daily_errors,
    mode() WITHIN GROUP (ORDER BY details->>'error_code') as most_common_error
  FROM audit_logs 
  WHERE event_type = 'processing_error' 
    AND created_at >= now() - interval '30 days'
  GROUP BY customer_id
) error_stats ON c.id = error_stats.customer_id

-- Usage statistics subquery
LEFT JOIN (
  SELECT
    customer_id,
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE success = true) as successful_requests,
    1.0 - (COUNT(*) FILTER (WHERE success = true)::float / COUNT(*)) as error_rate,
    AVG(processing_time_ms) as avg_processing_time
  FROM usage_logs
  WHERE created_at >= now() - interval '30 days'
  GROUP BY customer_id
) usage_stats ON c.id = usage_stats.customer_id

-- Support activity subquery
LEFT JOIN (
  SELECT
    c2.id as customer_id,
    COUNT(DISTINCT cn.id) as total_notifications,
    MAX(cn.sent_at) as last_notification_date,
    COUNT(DISTINCT css.id) as impersonation_sessions
  FROM customers c2
  LEFT JOIN customer_notifications cn ON c2.id = cn.customer_id
  LEFT JOIN customer_support_sessions css ON c2.id = css.customer_id
  GROUP BY c2.id
) support_stats ON c.id = support_stats.customer_id

WHERE c.deleted_at IS NULL;

-- Create index for support metrics queries
CREATE INDEX idx_customer_support_metrics_risk 
ON customer_support_metrics (error_risk_level, credit_risk_level);

-- Create function to refresh support metrics
CREATE OR REPLACE FUNCTION refresh_customer_support_metrics()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  REFRESH MATERIALIZED VIEW customer_support_metrics;
END;
$$;

-- Schedule metrics refresh every hour
SELECT cron.schedule(
  'refresh_support_metrics',
  '0 * * * *', -- Every hour
  'SELECT refresh_customer_support_metrics();'
);

-- Add RLS policies for support tables
ALTER TABLE customer_support_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_notifications ENABLE ROW LEVEL SECURITY;

-- Admin-only access policies
CREATE POLICY "admin_support_sessions" ON customer_support_sessions
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "admin_notification_templates" ON notification_templates
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "admin_customer_notifications" ON customer_notifications
  FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Add comments for documentation
COMMENT ON VIEW customer_errors IS 'Aggregated view of customer errors by date and error code for support analysis';
COMMENT ON TABLE customer_support_sessions IS 'Tracks admin impersonation sessions for customer support';
COMMENT ON TABLE notification_templates IS 'Templates for automated customer notifications';
COMMENT ON TABLE customer_notifications IS 'Log of all notifications sent to customers';
COMMENT ON MATERIALIZED VIEW customer_support_metrics IS 'Comprehensive customer support metrics and risk indicators';
