-- Performance Metrics Table Migration
-- Performance Optimization: Comprehensive observability system
-- 
-- This migration adds performance monitoring capabilities for agent customization
-- operations to enable production monitoring and optimization.

-- ================================================================================
-- PERFORMANCE METRICS TABLE
-- ================================================================================

CREATE TABLE public.performance_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation TEXT NOT NULL,
    duration_ms NUMERIC(10,2) NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    metadata JSONB DEFAULT '{}',
    customer_id UUID REFERENCES public.customers(id),
    
    -- Performance analysis fields
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    
    -- Indexing for analytics
    created_date DATE
);

-- Trigger to automatically set created_date
CREATE OR REPLACE FUNCTION set_performance_metrics_date() RETURNS TRIGGER AS $$
BEGIN
    NEW.created_date = DATE(NEW.timestamp);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_performance_metrics_date
    BEFORE INSERT OR UPDATE ON public.performance_metrics
    FOR EACH ROW EXECUTE FUNCTION set_performance_metrics_date();

-- Performance indexes for analytics queries
CREATE INDEX idx_performance_metrics_operation ON public.performance_metrics(operation);
CREATE INDEX idx_performance_metrics_timestamp ON public.performance_metrics(timestamp DESC);
CREATE INDEX idx_performance_metrics_customer_id ON public.performance_metrics(customer_id);
CREATE INDEX idx_performance_metrics_date_operation ON public.performance_metrics(created_date, operation);
CREATE INDEX idx_performance_metrics_duration ON public.performance_metrics(duration_ms DESC);

-- Enable Row Level Security
ALTER TABLE public.performance_metrics ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Customers can only see their own metrics
CREATE POLICY "customers_own_performance_metrics" ON public.performance_metrics
    FOR ALL USING (
        customer_id = (auth.jwt() ->> 'customer_id')::UUID
        OR customer_id IS NULL  -- Allow null for system-wide metrics
    );

-- Comments for documentation
COMMENT ON TABLE public.performance_metrics IS 
'Performance monitoring data for agent customization operations and system optimization';

COMMENT ON COLUMN public.performance_metrics.operation IS 
'Type of operation measured (e.g., agent_customization_complete, preview_generation, validation_batch)';

COMMENT ON COLUMN public.performance_metrics.duration_ms IS 
'Operation duration in milliseconds with 2 decimal precision';

COMMENT ON COLUMN public.performance_metrics.metadata IS 
'Additional context data: agent_id, validation errors, cache hits, etc.';

-- ================================================================================
-- PERFORMANCE ANALYTICS VIEWS
-- ================================================================================

-- View for performance dashboard
CREATE VIEW public.performance_summary AS
SELECT 
    operation,
    COUNT(*) as total_operations,
    ROUND(AVG(duration_ms)::numeric, 2) as avg_duration_ms,
    ROUND(MIN(duration_ms)::numeric, 2) as min_duration_ms,
    ROUND(MAX(duration_ms)::numeric, 2) as max_duration_ms,
    ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY duration_ms)::numeric, 2) as median_duration_ms,
    ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY duration_ms)::numeric, 2) as p95_duration_ms,
    COUNT(*) FILTER (WHERE success = false) as error_count,
    ROUND((100.0 * COUNT(*) FILTER (WHERE success = false) / COUNT(*))::numeric, 2) as error_rate_percent
FROM public.performance_metrics
WHERE timestamp > NOW() - INTERVAL '24 hours'
GROUP BY operation
ORDER BY total_operations DESC;

COMMENT ON VIEW public.performance_summary IS 
'24-hour performance summary by operation type with key metrics and error rates';

-- View for recent slow operations
CREATE VIEW public.slow_operations AS
SELECT 
    operation,
    duration_ms,
    timestamp,
    customer_id,
    metadata,
    error_message
FROM public.performance_metrics
WHERE duration_ms > 2000  -- Operations slower than 2 seconds
    AND timestamp > NOW() - INTERVAL '7 days'
ORDER BY duration_ms DESC, timestamp DESC
LIMIT 100;

COMMENT ON VIEW public.slow_operations IS 
'Recent operations exceeding 2-second performance threshold for investigation';

-- ================================================================================
-- PERFORMANCE MONITORING FUNCTIONS
-- ================================================================================

-- Function to clean up old performance metrics (retention policy)
CREATE OR REPLACE FUNCTION public.cleanup_performance_metrics(retention_days INTEGER DEFAULT 30)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.performance_metrics
    WHERE timestamp < NOW() - (retention_days || ' days')::INTERVAL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

COMMENT ON FUNCTION public.cleanup_performance_metrics(INTEGER) IS 
'Cleanup function for performance metrics retention (default 30 days)';

-- Function to get performance percentiles for specific operation
CREATE OR REPLACE FUNCTION public.get_performance_percentiles(
    operation_name TEXT,
    time_window_hours INTEGER DEFAULT 24
)
RETURNS TABLE (
    p50 NUMERIC,
    p75 NUMERIC,
    p90 NUMERIC,
    p95 NUMERIC,
    p99 NUMERIC
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ROUND(PERCENTILE_CONT(0.50) WITHIN GROUP (ORDER BY duration_ms), 2),
        ROUND(PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY duration_ms), 2),
        ROUND(PERCENTILE_CONT(0.90) WITHIN GROUP (ORDER BY duration_ms), 2),
        ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY duration_ms), 2),
        ROUND(PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY duration_ms), 2)
    FROM public.performance_metrics
    WHERE operation = operation_name
        AND timestamp > NOW() - (time_window_hours || ' hours')::INTERVAL;
END;
$$;

COMMENT ON FUNCTION public.get_performance_percentiles(TEXT, INTEGER) IS 
'Calculate performance percentiles for specific operation type within time window';

-- ================================================================================
-- MIGRATION VALIDATION
-- ================================================================================

DO $$
DECLARE
    table_count INTEGER;
    view_count INTEGER;
    function_count INTEGER;
BEGIN
    -- Verify table creation
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = 'performance_metrics';
    
    -- Verify views creation
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views
    WHERE table_schema = 'public'
    AND table_name IN ('performance_summary', 'slow_operations');
    
    -- Verify functions creation
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN ('cleanup_performance_metrics', 'get_performance_percentiles');
    
    -- Validation checks
    IF table_count != 1 THEN
        RAISE EXCEPTION 'Migration failed: performance_metrics table not created';
    END IF;
    
    IF view_count != 2 THEN
        RAISE EXCEPTION 'Migration failed: Expected 2 views, found %', view_count;
    END IF;
    
    IF function_count != 2 THEN
        RAISE EXCEPTION 'Migration failed: Expected 2 functions, found %', function_count;
    END IF;
    
    RAISE NOTICE 'Performance metrics migration successful:';
    RAISE NOTICE '  - performance_metrics table created with indexes';
    RAISE NOTICE '  - % views created for analytics', view_count;
    RAISE NOTICE '  - % functions created for maintenance', function_count;
    RAISE NOTICE '  - RLS policies applied for data security';
END;
$$;