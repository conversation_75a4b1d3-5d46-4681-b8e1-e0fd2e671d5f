-- Async Aggregation Optimization Migration
-- 
-- Addresses performance concerns from QA review:
-- - Replaces synchronous triggers with async processing
-- - Implements job queue for aggregation processing
-- - Adds performance monitoring and alerts

-- ================================================================================
-- 1. AGGREGATION JOBS QUEUE TABLE
-- ================================================================================

CREATE TABLE public.aggregation_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Job identification
    job_type TEXT NOT NULL CHECK (job_type IN ('daily_aggregation', 'agent_summary', 'performance_alert')),
    agent_id UUID NOT NULL,
    
    -- Job parameters
    target_date DATE,
    priority INTEGER NOT NULL DEFAULT 5 CHECK (priority BETWEEN 1 AND 10),
    
    -- Job status
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'retrying')),
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    
    -- Error tracking
    error_message TEXT,
    last_error JSONB,
    
    -- Performance tracking
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    processing_time_ms INTEGER,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Indexes for job queue management
CREATE INDEX idx_aggregation_jobs_status ON public.aggregation_jobs(status);
CREATE INDEX idx_aggregation_jobs_priority ON public.aggregation_jobs(priority, created_at);
CREATE INDEX idx_aggregation_jobs_agent_id ON public.aggregation_jobs(agent_id);
CREATE INDEX idx_aggregation_jobs_job_type ON public.aggregation_jobs(job_type);
CREATE INDEX idx_aggregation_jobs_created_at ON public.aggregation_jobs(created_at);

COMMENT ON TABLE public.aggregation_jobs IS 
'Job queue for async processing of performance aggregations';

-- ================================================================================
-- 2. AGGREGATION PERFORMANCE MONITORING
-- ================================================================================

CREATE TABLE public.aggregation_performance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Job context
    job_type TEXT NOT NULL,
    agent_id UUID,
    
    -- Performance metrics
    execution_time_ms INTEGER NOT NULL,
    records_processed INTEGER NOT NULL DEFAULT 0,
    memory_usage_mb INTEGER,
    cpu_usage_percent NUMERIC(5,2),
    
    -- Status
    status TEXT NOT NULL CHECK (status IN ('success', 'failure', 'timeout')),
    error_details JSONB,
    
    -- Timestamp
    executed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Indexes for monitoring
CREATE INDEX idx_aggregation_performance_job_type ON public.aggregation_performance(job_type);
CREATE INDEX idx_aggregation_performance_executed_at ON public.aggregation_performance(executed_at);
CREATE INDEX idx_aggregation_performance_execution_time ON public.aggregation_performance(execution_time_ms);

COMMENT ON TABLE public.aggregation_performance IS 
'Performance monitoring for aggregation job execution';

-- ================================================================================
-- 3. ASYNC AGGREGATION FUNCTIONS
-- ================================================================================

-- Function to queue aggregation jobs
CREATE OR REPLACE FUNCTION queue_aggregation_job(
    p_job_type TEXT,
    p_agent_id UUID,
    p_target_date DATE DEFAULT NULL,
    p_priority INTEGER DEFAULT 5
) RETURNS UUID AS $$
DECLARE
    v_job_id UUID;
    v_existing_job_id UUID;
BEGIN
    -- Check for existing pending job to avoid duplicates
    SELECT id INTO v_existing_job_id
    FROM aggregation_jobs
    WHERE job_type = p_job_type
        AND agent_id = p_agent_id
        AND (p_target_date IS NULL OR target_date = p_target_date)
        AND status IN ('pending', 'processing');

    IF v_existing_job_id IS NOT NULL THEN
        -- Job already exists, return existing ID
        RETURN v_existing_job_id;
    END IF;

    -- Create new job
    INSERT INTO aggregation_jobs (
        job_type,
        agent_id,
        target_date,
        priority,
        metadata
    ) VALUES (
        p_job_type,
        p_agent_id,
        p_target_date,
        p_priority,
        jsonb_build_object(
            'queued_at', NOW(),
            'source', 'async_trigger'
        )
    ) RETURNING id INTO v_job_id;

    RETURN v_job_id;
END;
$$ LANGUAGE plpgsql;

-- Function to process daily aggregation job
CREATE OR REPLACE FUNCTION process_daily_aggregation_job(p_job_id UUID) RETURNS BOOLEAN AS $$
DECLARE
    v_job aggregation_jobs%ROWTYPE;
    v_start_time TIMESTAMPTZ;
    v_processing_time INTEGER;
    v_records_processed INTEGER := 0;
BEGIN
    v_start_time := NOW();
    
    -- Get job details
    SELECT * INTO v_job FROM aggregation_jobs WHERE id = p_job_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Job not found: %', p_job_id;
    END IF;

    -- Update job status to processing
    UPDATE aggregation_jobs 
    SET status = 'processing', started_at = v_start_time, attempts = attempts + 1
    WHERE id = p_job_id;

    BEGIN
        -- Call the existing aggregation function
        PERFORM update_agent_performance_daily(
            v_job.agent_id,
            'invoice', -- Will be enhanced to handle all document types
            COALESCE(v_job.target_date, CURRENT_DATE)
        );
        
        v_records_processed := 1;

        -- Update job status to completed
        v_processing_time := EXTRACT(EPOCH FROM (NOW() - v_start_time)) * 1000;
        
        UPDATE aggregation_jobs 
        SET 
            status = 'completed',
            completed_at = NOW(),
            processing_time_ms = v_processing_time
        WHERE id = p_job_id;

        -- Record performance metrics
        INSERT INTO aggregation_performance (
            job_type,
            agent_id,
            execution_time_ms,
            records_processed,
            status,
            metadata
        ) VALUES (
            v_job.job_type,
            v_job.agent_id,
            v_processing_time,
            v_records_processed,
            'success',
            jsonb_build_object(
                'job_id', p_job_id,
                'target_date', v_job.target_date
            )
        );

        RETURN TRUE;

    EXCEPTION WHEN OTHERS THEN
        -- Update job status to failed
        UPDATE aggregation_jobs 
        SET 
            status = CASE 
                WHEN attempts >= max_attempts THEN 'failed'
                ELSE 'retrying'
            END,
            error_message = SQLERRM,
            last_error = jsonb_build_object(
                'error', SQLERRM,
                'state', SQLSTATE,
                'occurred_at', NOW()
            )
        WHERE id = p_job_id;

        -- Record performance metrics
        INSERT INTO aggregation_performance (
            job_type,
            agent_id,
            execution_time_ms,
            records_processed,
            status,
            error_details
        ) VALUES (
            v_job.job_type,
            v_job.agent_id,
            EXTRACT(EPOCH FROM (NOW() - v_start_time)) * 1000,
            v_records_processed,
            'failure',
            jsonb_build_object(
                'error', SQLERRM,
                'state', SQLSTATE,
                'job_id', p_job_id
            )
        );

        RETURN FALSE;
    END;
END;
$$ LANGUAGE plpgsql;

-- Function to get next pending job
CREATE OR REPLACE FUNCTION get_next_aggregation_job() 
RETURNS TABLE(job_id UUID, job_type TEXT, agent_id UUID, target_date DATE) AS $$
DECLARE
    v_job_id UUID;
BEGIN
    -- Get highest priority pending job
    SELECT id INTO v_job_id
    FROM aggregation_jobs
    WHERE status = 'pending'
        OR (status = 'retrying' AND attempts < max_attempts)
    ORDER BY priority DESC, created_at ASC
    LIMIT 1
    FOR UPDATE SKIP LOCKED;

    IF v_job_id IS NOT NULL THEN
        -- Return job details
        RETURN QUERY
        SELECT aj.id, aj.job_type, aj.agent_id, aj.target_date
        FROM aggregation_jobs aj
        WHERE aj.id = v_job_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- ================================================================================
-- 4. REPLACE SYNCHRONOUS TRIGGERS WITH ASYNC QUEUING
-- ================================================================================

-- Drop the existing synchronous trigger
DROP TRIGGER IF EXISTS trigger_agent_performance_logs_aggregation ON public.agent_performance_logs;

-- Create new async trigger function
CREATE OR REPLACE FUNCTION trigger_queue_async_aggregation() RETURNS TRIGGER AS $$
DECLARE
    v_job_id UUID;
BEGIN
    -- Queue daily aggregation job (debounced - won't create duplicates)
    SELECT queue_aggregation_job(
        'daily_aggregation',
        NEW.agent_id,
        DATE(NEW.timestamp),
        7 -- High priority for recent data
    ) INTO v_job_id;
    
    -- Queue agent summary update (lower priority)
    SELECT queue_aggregation_job(
        'agent_summary',
        NEW.agent_id,
        NULL, -- No specific date for summary
        5 -- Medium priority
    ) INTO v_job_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create new async trigger
CREATE TRIGGER trigger_agent_performance_logs_async_aggregation
    AFTER INSERT ON public.agent_performance_logs
    FOR EACH ROW
    EXECUTE FUNCTION trigger_queue_async_aggregation();

-- ================================================================================
-- 5. JOB PROCESSING UTILITIES
-- ================================================================================

-- Function to clean up old completed jobs
CREATE OR REPLACE FUNCTION cleanup_old_aggregation_jobs(
    p_days_to_keep INTEGER DEFAULT 7
) RETURNS INTEGER AS $$
DECLARE
    v_deleted_count INTEGER;
BEGIN
    DELETE FROM aggregation_jobs
    WHERE status IN ('completed', 'failed')
        AND completed_at < NOW() - INTERVAL '1 day' * p_days_to_keep;
    
    GET DIAGNOSTICS v_deleted_count = ROW_COUNT;
    
    RETURN v_deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get job queue statistics
CREATE OR REPLACE FUNCTION get_aggregation_job_stats()
RETURNS TABLE(
    status TEXT,
    count BIGINT,
    avg_processing_time_ms NUMERIC,
    oldest_pending_age_minutes NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    WITH job_stats AS (
        SELECT 
            aj.status,
            COUNT(*) as count,
            AVG(aj.processing_time_ms) as avg_processing_time_ms,
            CASE 
                WHEN aj.status = 'pending' THEN 
                    EXTRACT(EPOCH FROM (NOW() - MIN(aj.created_at))) / 60
                ELSE NULL 
            END as oldest_pending_age_minutes
        FROM aggregation_jobs aj
        WHERE aj.created_at > NOW() - INTERVAL '24 hours'
        GROUP BY aj.status
    )
    SELECT 
        js.status,
        js.count,
        js.avg_processing_time_ms,
        js.oldest_pending_age_minutes
    FROM job_stats js;
END;
$$ LANGUAGE plpgsql;

-- ================================================================================
-- 6. PERFORMANCE MONITORING AND ALERTS
-- ================================================================================

-- Function to check aggregation system health
CREATE OR REPLACE FUNCTION check_aggregation_system_health()
RETURNS TABLE(
    check_name TEXT,
    status TEXT,
    message TEXT,
    details JSONB
) AS $$
DECLARE
    v_pending_jobs INTEGER;
    v_failed_jobs INTEGER;
    v_avg_processing_time NUMERIC;
    v_old_pending_jobs INTEGER;
BEGIN
    -- Check pending job queue depth
    SELECT COUNT(*) INTO v_pending_jobs
    FROM aggregation_jobs
    WHERE status = 'pending';

    -- Check recent failed jobs
    SELECT COUNT(*) INTO v_failed_jobs
    FROM aggregation_jobs
    WHERE status = 'failed'
        AND created_at > NOW() - INTERVAL '1 hour';

    -- Check average processing time
    SELECT AVG(execution_time_ms) INTO v_avg_processing_time
    FROM aggregation_performance
    WHERE executed_at > NOW() - INTERVAL '1 hour'
        AND status = 'success';

    -- Check for old pending jobs
    SELECT COUNT(*) INTO v_old_pending_jobs
    FROM aggregation_jobs
    WHERE status = 'pending'
        AND created_at < NOW() - INTERVAL '30 minutes';

    -- Return health check results
    RETURN QUERY VALUES
    (
        'queue_depth',
        CASE 
            WHEN v_pending_jobs > 100 THEN 'critical'
            WHEN v_pending_jobs > 50 THEN 'warning'
            ELSE 'healthy'
        END,
        format('%s pending jobs in queue', v_pending_jobs),
        jsonb_build_object('pending_count', v_pending_jobs)
    ),
    (
        'recent_failures',
        CASE 
            WHEN v_failed_jobs > 10 THEN 'critical'
            WHEN v_failed_jobs > 5 THEN 'warning'
            ELSE 'healthy'
        END,
        format('%s failed jobs in last hour', v_failed_jobs),
        jsonb_build_object('failed_count', v_failed_jobs)
    ),
    (
        'processing_performance',
        CASE 
            WHEN v_avg_processing_time > 5000 THEN 'warning'
            WHEN v_avg_processing_time > 10000 THEN 'critical'
            ELSE 'healthy'
        END,
        format('Average processing time: %sms', COALESCE(v_avg_processing_time::INTEGER, 0)),
        jsonb_build_object('avg_time_ms', v_avg_processing_time)
    ),
    (
        'job_staleness',
        CASE 
            WHEN v_old_pending_jobs > 10 THEN 'critical'
            WHEN v_old_pending_jobs > 0 THEN 'warning'
            ELSE 'healthy'
        END,
        format('%s jobs pending for >30 minutes', v_old_pending_jobs),
        jsonb_build_object('stale_count', v_old_pending_jobs)
    );
END;
$$ LANGUAGE plpgsql;

-- ================================================================================
-- 7. ROW LEVEL SECURITY
-- ================================================================================

-- Enable RLS on new tables
ALTER TABLE public.aggregation_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.aggregation_performance ENABLE ROW LEVEL SECURITY;

-- RLS policies for aggregation jobs (system-level access)
CREATE POLICY "System can manage aggregation jobs" ON public.aggregation_jobs
    FOR ALL USING (true); -- System-level operations

-- RLS policies for aggregation performance (read-only for monitoring)
CREATE POLICY "System can manage aggregation performance" ON public.aggregation_performance
    FOR ALL USING (true); -- System-level operations

-- ================================================================================
-- 8. GRANTS AND PERMISSIONS
-- ================================================================================

-- Grant permissions for Edge Functions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.aggregation_jobs TO authenticated;
GRANT SELECT, INSERT ON public.aggregation_performance TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION queue_aggregation_job(TEXT, UUID, DATE, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION process_daily_aggregation_job(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_next_aggregation_job() TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_aggregation_jobs(INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_aggregation_job_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION check_aggregation_system_health() TO authenticated;

-- ================================================================================
-- 9. INITIAL VALIDATION
-- ================================================================================

DO $$
DECLARE
    v_test_job_id UUID;
    v_queue_stats RECORD;
BEGIN
    -- Test job queuing
    SELECT queue_aggregation_job(
        'daily_aggregation',
        '550e8400-e29b-41d4-a716-************'::UUID,
        CURRENT_DATE,
        5
    ) INTO v_test_job_id;

    -- Verify job was created
    IF v_test_job_id IS NOT NULL THEN
        RAISE NOTICE 'Async aggregation system initialized successfully';
        RAISE NOTICE 'Test job created: %', v_test_job_id;
        
        -- Clean up test job
        DELETE FROM aggregation_jobs WHERE id = v_test_job_id;
    ELSE
        RAISE EXCEPTION 'Failed to create test aggregation job';
    END IF;

    RAISE NOTICE 'Async aggregation optimization migration completed successfully';
    RAISE NOTICE 'Benefits:';
    RAISE NOTICE '  - Eliminates synchronous trigger performance bottlenecks';
    RAISE NOTICE '  - Enables horizontal scaling of aggregation processing';
    RAISE NOTICE '  - Provides job queue monitoring and error handling';
    RAISE NOTICE '  - Supports configurable retry policies';
    RAISE NOTICE '  - Includes comprehensive performance monitoring';
END;
$$;