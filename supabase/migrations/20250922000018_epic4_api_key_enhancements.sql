-- Epic 4.2: Advanced API Key Operations
-- Migration: Enhanced API key management capabilities
-- Created: 2025-09-22

-- Add enhanced API key management columns
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS expires_at timestamp;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS scope_restrictions jsonb DEFAULT '{}';
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS suspended_at timestamp;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS suspension_reason text;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON api_keys(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_api_keys_suspended_at ON api_keys(suspended_at) WHERE suspended_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_api_keys_scope_restrictions ON api_keys USING gin(scope_restrictions);

-- Add constraint for suspension logic
ALTER TABLE api_keys ADD CONSTRAINT chk_suspension_reason 
  CHECK ((suspended_at IS NULL AND suspension_reason IS NULL) OR 
         (suspended_at IS NOT NULL AND suspension_reason IS NOT NULL));

-- Create function for API key scope validation
CREATE OR REPLACE FUNCTION validate_api_key_scope(
  p_key_hash text,
  p_endpoint text DEFAULT NULL,
  p_agent_id text DEFAULT NULL,
  p_file_size bigint DEFAULT NULL
) RETURNS boolean AS $$
DECLARE
  v_key_record record;
  v_scope_restrictions jsonb;
  v_allowed_endpoints text[];
  v_allowed_agents text[];
  v_max_file_size bigint;
BEGIN
  -- Get API key record with scope restrictions
  SELECT 
    ak.*,
    ak.scope_restrictions
  INTO v_key_record
  FROM api_keys ak
  WHERE ak.key_hash = p_key_hash
    AND ak.is_active = true
    AND ak.suspended_at IS NULL
    AND (ak.expires_at IS NULL OR ak.expires_at > NOW());
  
  -- Key not found or inactive
  IF NOT FOUND THEN
    RETURN false;
  END IF;
  
  v_scope_restrictions := v_key_record.scope_restrictions;
  
  -- No restrictions means full access
  IF v_scope_restrictions = '{}'::jsonb OR v_scope_restrictions IS NULL THEN
    RETURN true;
  END IF;
  
  -- Check endpoint restrictions
  IF p_endpoint IS NOT NULL AND v_scope_restrictions ? 'allowed_endpoints' THEN
    v_allowed_endpoints := ARRAY(SELECT jsonb_array_elements_text(v_scope_restrictions->'allowed_endpoints'));
    IF NOT (p_endpoint = ANY(v_allowed_endpoints)) THEN
      RETURN false;
    END IF;
  END IF;
  
  -- Check agent restrictions
  IF p_agent_id IS NOT NULL AND v_scope_restrictions ? 'allowed_agents' THEN
    v_allowed_agents := ARRAY(SELECT jsonb_array_elements_text(v_scope_restrictions->'allowed_agents'));
    IF NOT (p_agent_id = ANY(v_allowed_agents)) THEN
      RETURN false;
    END IF;
  END IF;
  
  -- Check file size restrictions
  IF p_file_size IS NOT NULL AND v_scope_restrictions ? 'max_file_size' THEN
    v_max_file_size := (v_scope_restrictions->>'max_file_size')::bigint;
    IF p_file_size > v_max_file_size THEN
      RETURN false;
    END IF;
  END IF;
  
  RETURN true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for bulk API key operations
CREATE OR REPLACE FUNCTION bulk_api_key_operation(
  p_admin_user_id text,
  p_operation text,
  p_key_ids text[],
  p_parameters jsonb DEFAULT '{}'::jsonb
) RETURNS jsonb AS $$
DECLARE
  v_result jsonb := '{"success": [], "failed": [], "total": 0}'::jsonb;
  v_key_id text;
  v_customer_id text;
  v_operation_success boolean;
  v_error_message text;
BEGIN
  -- Validate operation type
  IF p_operation NOT IN ('suspend', 'activate', 'update_scope', 'expire') THEN
    RAISE EXCEPTION 'Invalid operation: %', p_operation;
  END IF;
  
  v_result := jsonb_set(v_result, '{total}', to_jsonb(array_length(p_key_ids, 1)));
  
  -- Process each key ID
  FOREACH v_key_id IN ARRAY p_key_ids
  LOOP
    v_operation_success := false;
    v_error_message := NULL;
    
    BEGIN
      -- Get customer_id for audit logging
      SELECT customer_id INTO v_customer_id
      FROM api_keys
      WHERE id = v_key_id::uuid;
      
      IF NOT FOUND THEN
        v_error_message := 'API key not found';
      ELSE
        CASE p_operation
          WHEN 'suspend' THEN
            UPDATE api_keys 
            SET suspended_at = NOW(),
                suspension_reason = COALESCE(p_parameters->>'reason', 'Bulk suspension'),
                updated_at = NOW()
            WHERE id = v_key_id::uuid;
            v_operation_success := true;
            
          WHEN 'activate' THEN
            UPDATE api_keys 
            SET suspended_at = NULL,
                suspension_reason = NULL,
                updated_at = NOW()
            WHERE id = v_key_id::uuid;
            v_operation_success := true;
            
          WHEN 'update_scope' THEN
            UPDATE api_keys 
            SET scope_restrictions = COALESCE(p_parameters->'scope_restrictions', scope_restrictions),
                updated_at = NOW()
            WHERE id = v_key_id::uuid;
            v_operation_success := true;
            
          WHEN 'expire' THEN
            UPDATE api_keys 
            SET expires_at = COALESCE((p_parameters->>'expires_at')::timestamp, NOW()),
                updated_at = NOW()
            WHERE id = v_key_id::uuid;
            v_operation_success := true;
        END CASE;
        
        -- Log audit event
        INSERT INTO audit_logs (
          id, customer_id, admin_user_id, action, resource_type, resource_id,
          changes, metadata, created_at
        ) VALUES (
          gen_random_uuid(), v_customer_id, p_admin_user_id, 
          'bulk_' || p_operation, 'api_key', v_key_id,
          p_parameters, jsonb_build_object('bulk_operation', true),
          NOW()
        );
      END IF;
      
    EXCEPTION WHEN OTHERS THEN
      v_operation_success := false;
      v_error_message := SQLERRM;
    END;
    
    -- Record result
    IF v_operation_success THEN
      v_result := jsonb_set(
        v_result, 
        '{success}', 
        (v_result->'success') || to_jsonb(v_key_id)
      );
    ELSE
      v_result := jsonb_set(
        v_result, 
        '{failed}', 
        (v_result->'failed') || jsonb_build_object('key_id', v_key_id, 'error', v_error_message)
      );
    END IF;
  END LOOP;
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION validate_api_key_scope(text, text, text, bigint) TO authenticated;
GRANT EXECUTE ON FUNCTION bulk_api_key_operation(text, text, text[], jsonb) TO service_role;

-- Add comments for documentation
COMMENT ON COLUMN api_keys.expires_at IS 'Optional expiration timestamp for API key';
COMMENT ON COLUMN api_keys.scope_restrictions IS 'JSON object defining access limitations (endpoints, agents, file size)';
COMMENT ON COLUMN api_keys.suspended_at IS 'Timestamp when key was suspended (NULL if active)';
COMMENT ON COLUMN api_keys.suspension_reason IS 'Human-readable reason for suspension';

COMMENT ON FUNCTION validate_api_key_scope(text, text, text, bigint) IS 'Validates API key access against scope restrictions';
COMMENT ON FUNCTION bulk_api_key_operation(text, text, text[], jsonb) IS 'Performs bulk operations on multiple API keys with audit logging';