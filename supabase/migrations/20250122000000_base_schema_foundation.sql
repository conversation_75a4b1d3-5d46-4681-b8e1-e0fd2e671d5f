-- Base Schema Foundation for IDP Platform
-- Creates foundational tables required for agent customization system
-- 
-- This migration establishes the core data model for the API-first document processing platform

-- ================================================================================
-- 1. CUSTOMERS TABLE
-- ================================================================================
-- Core customer accounts and billing tiers

CREATE TABLE public.customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    tier TEXT NOT NULL DEFAULT 'starter' CHECK (tier IN ('starter', 'professional', 'enterprise')),
    
    -- Billing and credits
    credits_available INTEGER NOT NULL DEFAULT 100,
    credits_used INTEGER NOT NULL DEFAULT 0,
    billing_cycle TEXT NOT NULL DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'annual')),
    
    -- Account status
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'cancelled')),
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_active_at TIMESTAMPTZ
);

CREATE INDEX idx_customers_customer_id ON public.customers(customer_id);
CREATE INDEX idx_customers_email ON public.customers(email);
CREATE INDEX idx_customers_status ON public.customers(status);

COMMENT ON TABLE public.customers IS 
'Customer accounts with billing information and credit management';

-- ================================================================================
-- 2. API KEYS TABLE
-- ================================================================================
-- API authentication with dual key system (test vs production)

CREATE TABLE public.api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    
    -- Key identification
    key_hash TEXT UNIQUE NOT NULL,
    key_prefix TEXT NOT NULL, -- 'skt_' or 'skp_'
    key_type TEXT NOT NULL CHECK (key_type IN ('test', 'production')),
    
    -- Key properties
    name TEXT NOT NULL,
    description TEXT,
    
    -- Usage limits
    credits_allocated INTEGER NOT NULL DEFAULT 100,
    credits_used INTEGER NOT NULL DEFAULT 0,
    rate_limit_per_minute INTEGER NOT NULL DEFAULT 60,
    rate_limit_per_hour INTEGER NOT NULL DEFAULT 1000,
    
    -- Key status
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_revoked BOOLEAN NOT NULL DEFAULT false,
    
    -- Expiration
    expires_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_used_at TIMESTAMPTZ,
    
    -- Metadata
    created_by TEXT,
    tags JSONB DEFAULT '[]'
);

CREATE INDEX idx_api_keys_customer_id ON public.api_keys(customer_id);
CREATE INDEX idx_api_keys_key_hash ON public.api_keys(key_hash);
CREATE INDEX idx_api_keys_key_type ON public.api_keys(key_type);
CREATE INDEX idx_api_keys_active ON public.api_keys(is_active) WHERE is_active = true;

COMMENT ON TABLE public.api_keys IS 
'API keys for customer authentication with dual test/production system';

-- ================================================================================
-- 3. AGENTS TABLE
-- ================================================================================
-- Document processing agents with customization support

CREATE TABLE public.agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id TEXT UNIQUE NOT NULL,
    
    -- Ownership
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    is_default BOOLEAN NOT NULL DEFAULT false,
    
    -- Agent definition
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL CHECK (category IN ('invoice', 'contract', 'receipt', 'insurance', 'general')),
    
    -- Processing configuration
    system_prompt TEXT NOT NULL,
    output_schema JSONB NOT NULL,
    processing_config JSONB NOT NULL DEFAULT '{}',
    
    -- Customization metadata
    is_customizable BOOLEAN NOT NULL DEFAULT true,
    current_version TEXT,
    last_customized_at TIMESTAMPTZ,
    last_customized_by UUID REFERENCES public.customers(id),
    
    -- Performance tracking
    performance_stats JSONB DEFAULT '{}',
    usage_count INTEGER NOT NULL DEFAULT 0,
    last_used_at TIMESTAMPTZ,
    
    -- Clone relationship
    cloned_from UUID REFERENCES public.agents(id),
    
    -- Status
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deprecated')),
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_agents_agent_id ON public.agents(agent_id);
CREATE INDEX idx_agents_customer_id ON public.agents(customer_id);
CREATE INDEX idx_agents_category ON public.agents(category);
CREATE INDEX idx_agents_is_default ON public.agents(is_default) WHERE is_default = true;
CREATE INDEX idx_agents_customer_customizable ON public.agents(customer_id, is_customizable) 
WHERE is_customizable = true;

COMMENT ON TABLE public.agents IS 
'Document processing agents with customization and cloning capabilities';

COMMENT ON COLUMN public.agents.processing_config IS 
'JSON configuration: {"confidence_threshold": 0.8, "retry_attempts": 3, "model_preference": ["openai/gpt-4o"], "timeout_seconds": 30}';

-- ================================================================================
-- 4. DOCUMENTS TABLE
-- ================================================================================
-- Processed documents with retention policies

CREATE TABLE public.documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    
    -- Document identification
    document_hash TEXT NOT NULL,
    original_filename TEXT,
    file_size INTEGER,
    mime_type TEXT,
    
    -- Processing info
    agent_id UUID REFERENCES public.agents(id),
    processed_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processing_time_ms INTEGER,
    
    -- Storage
    storage_path TEXT,
    content_preview TEXT,
    
    -- Retention (based on API key type)
    retention_expires_at TIMESTAMPTZ,
    
    -- Status
    status TEXT NOT NULL DEFAULT 'processed' CHECK (status IN ('uploaded', 'processing', 'processed', 'failed', 'expired')),
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_documents_customer_id ON public.documents(customer_id);
CREATE INDEX idx_documents_document_hash ON public.documents(document_hash);
CREATE INDEX idx_documents_agent_id ON public.documents(agent_id);
CREATE INDEX idx_documents_retention_expires ON public.documents(retention_expires_at);

COMMENT ON TABLE public.documents IS 
'Processed documents with retention policies based on API key type';

-- ================================================================================
-- 5. EXTRACTION RESULTS TABLE
-- ================================================================================
-- AI extraction results with embeddings for similarity search

CREATE TABLE public.extraction_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES public.documents(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES public.agents(id),
    customer_id UUID NOT NULL REFERENCES public.customers(id),
    
    -- Extraction data
    extracted_data JSONB NOT NULL,
    confidence_score NUMERIC(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    
    -- AI model info
    model_used TEXT NOT NULL,
    model_version TEXT,
    processing_cost_usd NUMERIC(10,6),
    
    -- Embeddings for similarity search (commented until pgvector enabled)
    -- content_embedding vector(1536), -- OpenAI embedding dimension
    
    -- Performance metrics
    input_tokens INTEGER,
    output_tokens INTEGER,
    processing_time_ms INTEGER,
    
    -- Validation
    schema_valid BOOLEAN NOT NULL DEFAULT true,
    validation_errors JSONB,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_extraction_results_document_id ON public.extraction_results(document_id);
CREATE INDEX idx_extraction_results_agent_id ON public.extraction_results(agent_id);
CREATE INDEX idx_extraction_results_customer_id ON public.extraction_results(customer_id);

-- Vector similarity index (requires pgvector extension)
-- CREATE INDEX idx_extraction_results_embedding ON public.extraction_results 
-- USING hnsw (content_embedding vector_cosine_ops);

COMMENT ON TABLE public.extraction_results IS 
'AI extraction results with embeddings for document similarity and deduplication';

-- ================================================================================
-- 6. USAGE LOGS TABLE
-- ================================================================================
-- Comprehensive usage tracking for billing and analytics

CREATE TABLE public.usage_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES public.customers(id),
    api_key_id UUID NOT NULL REFERENCES public.api_keys(id),
    
    -- Request details
    endpoint TEXT NOT NULL,
    method TEXT NOT NULL,
    
    -- Document processing
    document_id UUID REFERENCES public.documents(id),
    agent_id UUID REFERENCES public.agents(id),
    
    -- Cost tracking
    credits_used INTEGER NOT NULL DEFAULT 0,
    model_cost_usd NUMERIC(10,6), -- What we pay AI provider
    customer_price_usd NUMERIC(10,6), -- What customer pays us
    
    -- Performance
    processing_time_ms INTEGER,
    response_size_bytes INTEGER,
    
    -- AI model details
    model_used TEXT,
    input_tokens INTEGER,
    output_tokens INTEGER,
    
    -- Request metadata
    ip_address INET,
    user_agent TEXT,
    request_id TEXT,
    
    -- Status
    status_code INTEGER NOT NULL,
    success BOOLEAN NOT NULL DEFAULT true,
    error_message TEXT,
    
    -- Timestamp
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_usage_logs_customer_id ON public.usage_logs(customer_id);
CREATE INDEX idx_usage_logs_api_key_id ON public.usage_logs(api_key_id);
CREATE INDEX idx_usage_logs_created_at ON public.usage_logs(created_at);
CREATE INDEX idx_usage_logs_endpoint ON public.usage_logs(endpoint);

COMMENT ON TABLE public.usage_logs IS 
'Comprehensive usage tracking for billing, analytics, and audit compliance';

-- ================================================================================
-- 7. AUDIT LOGS TABLE
-- ================================================================================
-- Security and compliance audit logging

CREATE TABLE public.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES public.customers(id),
    
    -- Event details
    event_type TEXT NOT NULL,
    resource_type TEXT NOT NULL,
    resource_id TEXT,
    
    -- Action details
    action TEXT NOT NULL,
    actor_id TEXT,
    actor_type TEXT NOT NULL DEFAULT 'api_key',
    
    -- Context
    details JSONB NOT NULL DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    
    -- Security
    risk_level TEXT NOT NULL DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    
    -- Timestamp
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_audit_logs_customer_id ON public.audit_logs(customer_id);
CREATE INDEX idx_audit_logs_event_type ON public.audit_logs(event_type);
CREATE INDEX idx_audit_logs_resource_type ON public.audit_logs(resource_type);
CREATE INDEX idx_audit_logs_created_at ON public.audit_logs(created_at);
CREATE INDEX idx_audit_logs_risk_level ON public.audit_logs(risk_level);

COMMENT ON TABLE public.audit_logs IS 
'Security and compliance audit trail for all platform activities';

-- ================================================================================
-- 8. ROW LEVEL SECURITY
-- ================================================================================
-- Enable RLS on all customer-facing tables

ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.extraction_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (will be enhanced in subsequent migrations)
CREATE POLICY "customers_own_data" ON public.customers
    FOR ALL USING (id = (auth.jwt() ->> 'customer_id')::UUID);

CREATE POLICY "customers_own_api_keys" ON public.api_keys
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

CREATE POLICY "customers_own_agents" ON public.agents
    FOR ALL USING (
        customer_id = (auth.jwt() ->> 'customer_id')::UUID
        OR is_default = true
    );

CREATE POLICY "customers_own_documents" ON public.documents
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

CREATE POLICY "customers_own_extractions" ON public.extraction_results
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

CREATE POLICY "customers_own_usage" ON public.usage_logs
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

CREATE POLICY "customers_own_audit" ON public.audit_logs
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

-- ================================================================================
-- 9. SEED DEFAULT AGENTS
-- ================================================================================
-- Create default agents for immediate platform functionality

INSERT INTO public.agents (
    agent_id,
    name,
    description,
    category,
    system_prompt,
    output_schema,
    is_default,
    is_customizable,
    status
) VALUES 
(
    'default-invoice-extractor',
    'Invoice Data Extractor',
    'Extracts structured data from invoices including vendor, amounts, dates, and line items',
    'invoice',
    'You are an expert invoice data extraction system. Extract the following information from the provided invoice document:

1. Vendor information (name, address, contact details)
2. Invoice details (number, date, due date)
3. Financial information (subtotal, tax, total amount, currency)
4. Line items (description, quantity, unit price, total)
5. Payment terms and methods

Return the extracted data as valid JSON matching the provided schema. Be precise with numbers and dates. If information is unclear or missing, use null values.',
    '{
        "type": "object",
        "properties": {
            "vendor": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "address": {"type": "string"},
                    "email": {"type": "string"},
                    "phone": {"type": "string"}
                },
                "required": ["name"]
            },
            "invoice": {
                "type": "object",
                "properties": {
                    "number": {"type": "string"},
                    "date": {"type": "string", "format": "date"},
                    "due_date": {"type": "string", "format": "date"}
                },
                "required": ["number", "date"]
            },
            "financial": {
                "type": "object",
                "properties": {
                    "subtotal": {"type": "number"},
                    "tax_amount": {"type": "number"},
                    "total_amount": {"type": "number"},
                    "currency": {"type": "string"}
                },
                "required": ["total_amount"]
            },
            "line_items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "description": {"type": "string"},
                        "quantity": {"type": "number"},
                        "unit_price": {"type": "number"},
                        "total": {"type": "number"}
                    },
                    "required": ["description", "total"]
                }
            }
        },
        "required": ["vendor", "invoice", "financial"],
        "additionalProperties": false
    }',
    true,
    true,
    'active'
),
(
    'default-receipt-extractor',
    'Receipt Data Extractor',
    'Extracts structured data from receipts including merchant, items, and payment information',
    'receipt',
    'You are an expert receipt data extraction system. Extract the following information from the provided receipt:

1. Merchant information (name, location, contact)
2. Transaction details (date, time, receipt number)
3. Items purchased (description, price, quantity)
4. Payment information (subtotal, tax, total, payment method)

Return the extracted data as valid JSON matching the provided schema. Be accurate with prices and quantities.',
    '{
        "type": "object",
        "properties": {
            "merchant": {
                "type": "object",
                "properties": {
                    "name": {"type": "string"},
                    "location": {"type": "string"},
                    "phone": {"type": "string"}
                },
                "required": ["name"]
            },
            "transaction": {
                "type": "object",
                "properties": {
                    "date": {"type": "string", "format": "date"},
                    "time": {"type": "string"},
                    "receipt_number": {"type": "string"}
                },
                "required": ["date"]
            },
            "items": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "description": {"type": "string"},
                        "price": {"type": "number"},
                        "quantity": {"type": "number"}
                    },
                    "required": ["description", "price"]
                }
            },
            "payment": {
                "type": "object",
                "properties": {
                    "subtotal": {"type": "number"},
                    "tax": {"type": "number"},
                    "total": {"type": "number"},
                    "method": {"type": "string"}
                },
                "required": ["total"]
            }
        },
        "required": ["merchant", "transaction", "payment"],
        "additionalProperties": false
    }',
    true,
    true,
    'active'
);

-- ================================================================================
-- 10. HELPER FUNCTIONS
-- ================================================================================
-- Utility functions for platform operations

CREATE OR REPLACE FUNCTION public.generate_api_key_hash(raw_key TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN encode(digest(raw_key || 'idp_platform_salt', 'sha256'), 'hex');
END;
$$;

COMMENT ON FUNCTION public.generate_api_key_hash(TEXT) IS 
'Generates secure hash for API key storage (never store raw keys)';

-- Trigger to update updated_at timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Apply update triggers
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON public.customers
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_agents_updated_at
    BEFORE UPDATE ON public.agents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_documents_updated_at
    BEFORE UPDATE ON public.documents
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- ================================================================================
-- 11. MIGRATION VALIDATION
-- ================================================================================

DO $$
DECLARE
    table_count INTEGER;
    agent_count INTEGER;
BEGIN
    -- Count core tables
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN (
        'customers', 'api_keys', 'agents', 'documents', 
        'extraction_results', 'usage_logs', 'audit_logs'
    );
    
    -- Count default agents
    SELECT COUNT(*) INTO agent_count
    FROM public.agents
    WHERE is_default = true;
    
    -- Validation
    IF table_count != 7 THEN
        RAISE EXCEPTION 'Base schema migration failed: Expected 7 tables, found %', table_count;
    END IF;
    
    IF agent_count != 2 THEN
        RAISE EXCEPTION 'Default agents not created: Expected 2, found %', agent_count;
    END IF;
    
    RAISE NOTICE 'Base schema migration successful:';
    RAISE NOTICE '  - % core tables created', table_count;
    RAISE NOTICE '  - % default agents seeded', agent_count;
    RAISE NOTICE '  - RLS policies applied';
    RAISE NOTICE '  - Helper functions created';
END;
$$;