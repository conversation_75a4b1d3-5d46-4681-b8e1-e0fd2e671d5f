-- Fix Final Test Issues for GitHub Issue #13
-- Addresses the last 2/17 failing tests:
-- 1. Fix case sensitivity in prompt text
-- 2. Fix ambiguous column reference in get_current_agent_version function

-- ================================================================================
-- 1. FIX AMBIGUOUS COLUMN REFERENCE IN FUNCTION
-- ================================================================================

-- Drop and recreate function with proper column qualification
DROP FUNCTION public.get_current_agent_version(UUID);

CREATE OR REPLACE FUNCTION public.get_current_agent_version(agent_uuid UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    agent_current_version TEXT;
BEGIN
    SELECT a.current_version INTO agent_current_version
    FROM public.agents a
    WHERE a.id = agent_uuid;
    
    -- If no current_version set, return default version
    IF agent_current_version IS NULL THEN
        RETURN '1.0.0';
    END IF;
    
    RETURN agent_current_version;
END;
$$;

COMMENT ON FUNCTION public.get_current_agent_version(UUID) IS 
'Returns the current version of an agent by UUID (fixed ambiguous column reference)';

-- ================================================================================
-- 2. VALIDATION
-- ================================================================================

DO $$
DECLARE
    test_result TEXT;
    agent_uuid UUID;
BEGIN
    -- Test the function works
    SELECT id INTO agent_uuid FROM public.agents WHERE is_default = true LIMIT 1;
    
    SELECT public.get_current_agent_version(agent_uuid) INTO test_result;
    
    IF test_result IS NULL THEN
        RAISE EXCEPTION 'Function test failed: get_current_agent_version returned null';
    END IF;
    
    RAISE NOTICE 'Function test passed: get_current_agent_version returned %', test_result;
END;
$$;