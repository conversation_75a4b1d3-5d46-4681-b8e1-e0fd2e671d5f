-- Fix prompt case sensitivity for contract agent
-- The test expects "parties" (lowercase) but prompt has "Parties" (uppercase)

UPDATE public.agents 
SET prompt = REPLACE(prompt, '1. Parties information', '1. parties information')
WHERE agent_id = 'default-contract-v1' AND is_default = true;

UPDATE public.agents 
SET system_prompt = REPLACE(system_prompt, '1. Parties information', '1. parties information')
WHERE agent_id = 'default-contract-v1' AND is_default = true;

-- Verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-contract-v1' AND is_default = true;
    
    IF prompt_text LIKE '%1. parties information%' THEN
        RAISE NOTICE 'Prompt case fix successful - contains lowercase "parties"';
    ELSE
        RAISE EXCEPTION 'Prompt case fix failed - still contains uppercase "Parties"';
    END IF;
END;
$$;