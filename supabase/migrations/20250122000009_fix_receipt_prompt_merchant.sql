-- Fix receipt agent prompt to include "merchant" text as expected by test
-- Test expects to find "merchant" but prompt has "Merchant information"

UPDATE public.agents 
SET prompt = REPLACE(prompt, '1. Merchant information', '1. merchant information'),
    system_prompt = REPLACE(system_prompt, '1. Merchant information', '1. merchant information')
WHERE agent_id = 'default-receipt-v1' AND is_default = true;

-- Verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-receipt-v1' AND is_default = true;
    
    IF prompt_text LIKE '%merchant%' THEN
        RAISE NOTICE 'Receipt agent prompt fix successful - contains "merchant"';
    ELSE
        RAISE EXCEPTION 'Receipt agent prompt fix failed - does not contain "merchant"';
    END IF;
END;
$$;