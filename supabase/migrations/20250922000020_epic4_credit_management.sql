-- Epic 4 Story 4.3: Credit Management System
-- Migration: 20250922000018_epic4_credit_management.sql

-- Credit transactions table for tracking all credit operations
CREATE TABLE IF NOT EXISTS credit_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE SET NULL,
  transaction_type text NOT NULL CHECK (transaction_type IN ('purchase', 'deduction', 'refund', 'adjustment')),
  amount integer NOT NULL,
  balance_before integer NOT NULL,
  balance_after integer NOT NULL,
  payment_reference text,
  stripe_payment_intent_id text,
  admin_user_id text,
  admin_notes text,
  status text NOT NULL DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
  metadata jsonb DEFAULT '{}',
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  
  -- Indexes for performance
  CONSTRAINT credit_transactions_amount_check CHECK (amount != 0)
);

-- Credit pools for enterprise customers
CREATE TABLE IF NOT EXISTS credit_pools (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  pool_name text NOT NULL,
  total_credits integer NOT NULL DEFAULT 0,
  allocated_credits integer NOT NULL DEFAULT 0,
  available_credits integer GENERATED ALWAYS AS (total_credits - allocated_credits) STORED,
  expires_at timestamp,
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  
  -- Constraints
  CONSTRAINT credit_pools_positive_credits CHECK (total_credits >= 0),
  CONSTRAINT credit_pools_allocated_not_exceed_total CHECK (allocated_credits <= total_credits),
  CONSTRAINT credit_pools_unique_name_per_customer UNIQUE (customer_id, pool_name)
);

-- Credit alerts configuration
CREATE TABLE IF NOT EXISTS credit_alerts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE CASCADE,
  alert_type text NOT NULL CHECK (alert_type IN ('low_balance', 'zero_balance', 'usage_spike', 'expiration_warning')),
  threshold_value integer,
  threshold_percentage integer,
  is_enabled boolean NOT NULL DEFAULT true,
  last_triggered_at timestamp,
  notification_channels jsonb DEFAULT '{"email": true, "webhook": false}',
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  
  -- Constraints
  CONSTRAINT credit_alerts_threshold_check CHECK (
    (threshold_value IS NOT NULL AND threshold_value >= 0) OR 
    (threshold_percentage IS NOT NULL AND threshold_percentage BETWEEN 0 AND 100)
  )
);

-- Billing integration tracking
CREATE TABLE IF NOT EXISTS billing_events (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  event_type text NOT NULL CHECK (event_type IN ('payment_success', 'payment_failed', 'invoice_created', 'subscription_updated', 'refund_processed')),
  stripe_event_id text UNIQUE,
  amount_cents integer,
  currency text DEFAULT 'usd',
  credits_affected integer,
  invoice_id text,
  subscription_id text,
  metadata jsonb DEFAULT '{}',
  processed_at timestamp DEFAULT now(),
  created_at timestamp DEFAULT now()
);

-- Indexes for performance optimization
CREATE INDEX IF NOT EXISTS idx_credit_transactions_customer_id ON credit_transactions(customer_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_api_key_id ON credit_transactions(api_key_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON credit_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_created_at ON credit_transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_payment_ref ON credit_transactions(payment_reference);

CREATE INDEX IF NOT EXISTS idx_credit_pools_customer_id ON credit_pools(customer_id);
CREATE INDEX IF NOT EXISTS idx_credit_pools_active ON credit_pools(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_credit_pools_expires_at ON credit_pools(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_credit_alerts_customer_id ON credit_alerts(customer_id);
CREATE INDEX IF NOT EXISTS idx_credit_alerts_api_key_id ON credit_alerts(api_key_id);
CREATE INDEX IF NOT EXISTS idx_credit_alerts_enabled ON credit_alerts(is_enabled) WHERE is_enabled = true;

CREATE INDEX IF NOT EXISTS idx_billing_events_customer_id ON billing_events(customer_id);
CREATE INDEX IF NOT EXISTS idx_billing_events_stripe_id ON billing_events(stripe_event_id);
CREATE INDEX IF NOT EXISTS idx_billing_events_type ON billing_events(event_type);

-- Add credit management columns to existing tables if not present
DO $$ 
BEGIN
  -- Add credit tracking columns to customers table
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'credits_purchased') THEN
    ALTER TABLE customers ADD COLUMN credits_purchased integer NOT NULL DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'credits_used_lifetime') THEN
    ALTER TABLE customers ADD COLUMN credits_used_lifetime integer NOT NULL DEFAULT 0;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'last_billing_date') THEN
    ALTER TABLE customers ADD COLUMN last_billing_date timestamp;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'customers' AND column_name = 'billing_cycle') THEN
    ALTER TABLE customers ADD COLUMN billing_cycle text DEFAULT 'monthly' CHECK (billing_cycle IN ('monthly', 'quarterly', 'annually', 'custom'));
  END IF;
  
  -- Add credit pool reference to api_keys
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'credit_pool_id') THEN
    ALTER TABLE api_keys ADD COLUMN credit_pool_id uuid REFERENCES credit_pools(id) ON DELETE SET NULL;
  END IF;
  
  -- Add low balance alert thresholds to api_keys
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'api_keys' AND column_name = 'low_balance_threshold') THEN
    ALTER TABLE api_keys ADD COLUMN low_balance_threshold integer DEFAULT 100;
  END IF;
END $$;

-- Database functions for credit operations

-- Function to process credit transaction atomically
CREATE OR REPLACE FUNCTION process_credit_transaction(
  p_customer_id uuid,
  p_transaction_type text,
  p_amount integer,
  p_api_key_id uuid DEFAULT NULL,
  p_payment_reference text DEFAULT NULL,
  p_admin_user_id text DEFAULT NULL,
  p_admin_notes text DEFAULT NULL,
  p_metadata jsonb DEFAULT '{}'
) RETURNS jsonb AS $$
DECLARE
  v_customer_record customers%ROWTYPE;
  v_api_key_record api_keys%ROWTYPE;
  v_balance_before integer;
  v_balance_after integer;
  v_transaction_id uuid;
  v_result jsonb;
BEGIN
  -- Lock customer record for update
  SELECT * INTO v_customer_record 
  FROM customers 
  WHERE id = p_customer_id 
  FOR UPDATE;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Customer not found: %', p_customer_id;
  END IF;
  
  v_balance_before := v_customer_record.credits_available;
  
  -- Calculate new balance based on transaction type
  CASE p_transaction_type
    WHEN 'purchase', 'adjustment' THEN
      v_balance_after := v_balance_before + p_amount;
    WHEN 'deduction', 'refund' THEN
      v_balance_after := v_balance_before - ABS(p_amount);
    ELSE
      RAISE EXCEPTION 'Invalid transaction type: %', p_transaction_type;
  END CASE;
  
  -- Validate sufficient balance for deductions
  IF p_transaction_type IN ('deduction') AND v_balance_after < 0 THEN
    RAISE EXCEPTION 'Insufficient credits. Required: %, Available: %', ABS(p_amount), v_balance_before;
  END IF;
  
  -- Create transaction record
  INSERT INTO credit_transactions (
    customer_id,
    api_key_id,
    transaction_type,
    amount,
    balance_before,
    balance_after,
    payment_reference,
    admin_user_id,
    admin_notes,
    metadata
  ) VALUES (
    p_customer_id,
    p_api_key_id,
    p_transaction_type,
    p_amount,
    v_balance_before,
    v_balance_after,
    p_payment_reference,
    p_admin_user_id,
    p_admin_notes,
    p_metadata
  ) RETURNING id INTO v_transaction_id;
  
  -- Update customer balance
  UPDATE customers 
  SET 
    credits_available = v_balance_after,
    credits_purchased = CASE 
      WHEN p_transaction_type = 'purchase' THEN credits_purchased + p_amount
      ELSE credits_purchased
    END,
    updated_at = now()
  WHERE id = p_customer_id;
  
  -- Update API key balance if specified
  IF p_api_key_id IS NOT NULL THEN
    SELECT * INTO v_api_key_record
    FROM api_keys
    WHERE id = p_api_key_id;
    
    IF FOUND THEN
      UPDATE api_keys
      SET 
        credits_allocated = CASE
          WHEN p_transaction_type IN ('purchase', 'adjustment') THEN credits_allocated + p_amount
          WHEN p_transaction_type IN ('deduction') THEN credits_allocated - ABS(p_amount)
          ELSE credits_allocated
        END,
        updated_at = now()
      WHERE id = p_api_key_id;
    END IF;
  END IF;
  
  -- Return transaction details
  v_result := jsonb_build_object(
    'transaction_id', v_transaction_id,
    'customer_id', p_customer_id,
    'transaction_type', p_transaction_type,
    'amount', p_amount,
    'balance_before', v_balance_before,
    'balance_after', v_balance_after,
    'timestamp', now()
  );
  
  RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function to check and trigger credit alerts
CREATE OR REPLACE FUNCTION check_credit_alerts(p_customer_id uuid DEFAULT NULL)
RETURNS jsonb AS $$
DECLARE
  v_alert_record credit_alerts%ROWTYPE;
  v_customer_record customers%ROWTYPE;
  v_api_key_record api_keys%ROWTYPE;
  v_alerts_triggered jsonb[] := '{}';
  v_current_balance integer;
  v_should_trigger boolean := false;
BEGIN
  -- If customer_id provided, check only that customer's alerts
  FOR v_alert_record IN 
    SELECT * FROM credit_alerts 
    WHERE is_enabled = true 
    AND (p_customer_id IS NULL OR customer_id = p_customer_id)
  LOOP
    -- Get current customer balance
    SELECT * INTO v_customer_record 
    FROM customers 
    WHERE id = v_alert_record.customer_id;
    
    CONTINUE WHEN NOT FOUND;
    
    v_current_balance := v_customer_record.credits_available;
    v_should_trigger := false;
    
    -- Check alert conditions
    CASE v_alert_record.alert_type
      WHEN 'low_balance' THEN
        IF v_alert_record.threshold_value IS NOT NULL THEN
          v_should_trigger := v_current_balance <= v_alert_record.threshold_value;
        ELSIF v_alert_record.threshold_percentage IS NOT NULL THEN
          v_should_trigger := v_current_balance <= (v_customer_record.credits_purchased * v_alert_record.threshold_percentage / 100);
        END IF;
      WHEN 'zero_balance' THEN
        v_should_trigger := v_current_balance <= 0;
      -- Add more alert types as needed
    END CASE;
    
    -- Trigger alert if conditions met and not recently triggered
    IF v_should_trigger AND (
      v_alert_record.last_triggered_at IS NULL OR 
      v_alert_record.last_triggered_at < now() - interval '1 hour'
    ) THEN
      -- Update last triggered timestamp
      UPDATE credit_alerts 
      SET last_triggered_at = now() 
      WHERE id = v_alert_record.id;
      
      -- Add to triggered alerts array
      v_alerts_triggered := v_alerts_triggered || jsonb_build_object(
        'alert_id', v_alert_record.id,
        'customer_id', v_alert_record.customer_id,
        'alert_type', v_alert_record.alert_type,
        'current_balance', v_current_balance,
        'threshold', COALESCE(v_alert_record.threshold_value, v_alert_record.threshold_percentage),
        'triggered_at', now()
      );
    END IF;
  END LOOP;
  
  RETURN jsonb_build_object(
    'alerts_triggered', v_alerts_triggered,
    'check_timestamp', now()
  );
END;
$$ LANGUAGE plpgsql;

-- Function for bulk credit operations
CREATE OR REPLACE FUNCTION bulk_credit_operation(
  p_admin_user_id text,
  p_operation text,
  p_customer_ids uuid[],
  p_amount integer,
  p_admin_notes text DEFAULT NULL
) RETURNS jsonb AS $$
DECLARE
  v_customer_id uuid;
  v_results jsonb[] := '{}';
  v_transaction_result jsonb;
  v_success_count integer := 0;
  v_error_count integer := 0;
BEGIN
  -- Validate operation type
  IF p_operation NOT IN ('purchase', 'refund', 'adjustment') THEN
    RAISE EXCEPTION 'Invalid bulk operation type: %', p_operation;
  END IF;
  
  -- Process each customer
  FOREACH v_customer_id IN ARRAY p_customer_ids
  LOOP
    BEGIN
      -- Process individual transaction
      SELECT process_credit_transaction(
        v_customer_id,
        NULL, -- no specific API key
        p_operation,
        p_amount,
        NULL, -- no payment reference for bulk ops
        p_admin_user_id,
        p_admin_notes || ' (Bulk Operation)',
        jsonb_build_object('bulk_operation', true)
      ) INTO v_transaction_result;
      
      v_results := v_results || jsonb_build_object(
        'customer_id', v_customer_id,
        'success', true,
        'transaction', v_transaction_result
      );
      
      v_success_count := v_success_count + 1;
      
    EXCEPTION WHEN OTHERS THEN
      v_results := v_results || jsonb_build_object(
        'customer_id', v_customer_id,
        'success', false,
        'error', SQLERRM
      );
      
      v_error_count := v_error_count + 1;
    END;
  END LOOP;
  
  RETURN jsonb_build_object(
    'operation', p_operation,
    'total_customers', array_length(p_customer_ids, 1),
    'success_count', v_success_count,
    'error_count', v_error_count,
    'results', v_results,
    'processed_at', now()
  );
END;
$$ LANGUAGE plpgsql;

-- Create audit trigger for credit transactions
CREATE OR REPLACE FUNCTION audit_credit_transaction() RETURNS trigger AS $$
BEGIN
  INSERT INTO audit_logs (
    customer_id,
    event_type,
    resource_type,
    resource_id,
    action,
    actor_id,
    actor_type,
    details,
    risk_level
  ) VALUES (
    NEW.customer_id,
    'credit_transaction',
    'credit_transaction',
    NEW.id::text,
    NEW.transaction_type,
    NEW.admin_user_id,
    'admin',
    jsonb_build_object(
      'amount', NEW.amount,
      'balance_before', NEW.balance_before,
      'balance_after', NEW.balance_after,
      'payment_reference', NEW.payment_reference
    ),
    CASE 
      WHEN NEW.transaction_type = 'refund' AND NEW.amount > 1000 THEN 'high'
      WHEN NEW.transaction_type = 'adjustment' THEN 'medium'
      ELSE 'low'
    END
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS audit_credit_transaction_trigger ON credit_transactions;
CREATE TRIGGER audit_credit_transaction_trigger
  AFTER INSERT ON credit_transactions
  FOR EACH ROW EXECUTE FUNCTION audit_credit_transaction();

-- Grant permissions to service role
GRANT ALL ON credit_transactions TO service_role;
GRANT ALL ON credit_pools TO service_role;
GRANT ALL ON credit_alerts TO service_role;
GRANT ALL ON billing_events TO service_role;

GRANT EXECUTE ON FUNCTION process_credit_transaction(uuid, text, integer, uuid, text, text, text, jsonb) TO service_role;
GRANT EXECUTE ON FUNCTION check_credit_alerts(uuid) TO service_role;
GRANT EXECUTE ON FUNCTION bulk_credit_operation(text, text, uuid[], integer, text) TO service_role;

-- Insert default credit alert configurations for existing customers
INSERT INTO credit_alerts (customer_id, alert_type, threshold_value, is_enabled)
SELECT 
  id, 
  'low_balance', 
  100, 
  true
FROM customers
WHERE NOT EXISTS (
  SELECT 1 FROM credit_alerts 
  WHERE credit_alerts.customer_id = customers.id 
  AND alert_type = 'low_balance'
);

COMMENT ON TABLE credit_transactions IS 'Tracks all credit-related financial transactions';
COMMENT ON TABLE credit_pools IS 'Enterprise credit pooling for shared credit allocation';
COMMENT ON TABLE credit_alerts IS 'Configurable alerts for credit balance monitoring';
COMMENT ON TABLE billing_events IS 'Integration tracking for Stripe billing events';
COMMENT ON FUNCTION process_credit_transaction(uuid, text, integer, uuid, text, text, text, jsonb) IS 'Atomic credit transaction processing with balance validation';
COMMENT ON FUNCTION check_credit_alerts(uuid) IS 'Automated credit alert checking and triggering';
COMMENT ON FUNCTION bulk_credit_operation(text, text, uuid[], integer, text) IS 'Bulk credit operations for administrative efficiency';