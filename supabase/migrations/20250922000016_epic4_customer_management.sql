-- Epic 4: Customer Management Extensions
-- Extends customer table for advanced admin capabilities
-- Migration: 20250922000016_epic4_customer_management.sql

-- ================================================================================
-- 1. EXTEND CUSTOMERS TABLE
-- ================================================================================

-- Add new columns for Epic 4 customer management
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS company_name TEXT,
ADD COLUMN IF NOT EXISTS contact_email TEXT,
ADD COLUMN IF NOT EXISTS tier_settings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS suspension_reason TEXT,
ADD COLUMN IF NOT EXISTS suspended_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Update status enum to include Epic 4 statuses
ALTER TABLE public.customers 
DROP CONSTRAINT IF EXISTS customers_status_check;

ALTER TABLE public.customers 
ADD CONSTRAINT customers_status_check 
CHECK (status IN ('trial', 'active', 'suspended', 'enterprise', 'cancelled'));

-- Add tier settings validation
ALTER TABLE public.customers 
ADD CONSTRAINT customers_tier_settings_valid 
CHECK (
  tier_settings IS NULL OR (
    jsonb_typeof(tier_settings) = 'object' AND
    (tier_settings->>'max_api_keys')::integer >= 0 AND
    (tier_settings->>'default_credit_limit')::integer >= 0 AND
    (tier_settings->>'rate_limit_multiplier')::numeric >= 1
  )
);

-- Add indexes for new columns
CREATE INDEX IF NOT EXISTS idx_customers_company_name ON public.customers(company_name);
CREATE INDEX IF NOT EXISTS idx_customers_contact_email ON public.customers(contact_email);
CREATE INDEX IF NOT EXISTS idx_customers_suspended_at ON public.customers(suspended_at);
CREATE INDEX IF NOT EXISTS idx_customers_deleted_at ON public.customers(deleted_at);

-- Full text search index for customer search
CREATE INDEX IF NOT EXISTS idx_customers_search ON public.customers 
USING GIN (to_tsvector('english', name || ' ' || COALESCE(company_name, '') || ' ' || email));

COMMENT ON COLUMN public.customers.company_name IS 'Official company name for business customers';
COMMENT ON COLUMN public.customers.contact_email IS 'Primary contact email (may differ from login email)';
COMMENT ON COLUMN public.customers.tier_settings IS 'Tier-specific configuration: {max_api_keys, default_credit_limit, rate_limit_multiplier}';
COMMENT ON COLUMN public.customers.suspension_reason IS 'Reason for account suspension';
COMMENT ON COLUMN public.customers.suspended_at IS 'Timestamp when account was suspended';
COMMENT ON COLUMN public.customers.deleted_at IS 'Soft delete timestamp for data retention compliance';

-- ================================================================================
-- 2. EXTEND API KEYS TABLE
-- ================================================================================

-- Add Epic 4 API key management columns
ALTER TABLE public.api_keys 
ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS scope_restrictions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS suspended_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS suspension_reason TEXT,
ADD COLUMN IF NOT EXISTS last_used_ip INET,
ADD COLUMN IF NOT EXISTS usage_notes TEXT;

-- Add indexes for new API key columns
CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at ON public.api_keys(expires_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_suspended_at ON public.api_keys(suspended_at);
CREATE INDEX IF NOT EXISTS idx_api_keys_last_used_ip ON public.api_keys(last_used_ip);

-- Scope restrictions validation
ALTER TABLE public.api_keys 
ADD CONSTRAINT api_keys_scope_restrictions_valid 
CHECK (
  scope_restrictions IS NULL OR jsonb_typeof(scope_restrictions) = 'object'
);

COMMENT ON COLUMN public.api_keys.expires_at IS 'API key expiration timestamp';
COMMENT ON COLUMN public.api_keys.scope_restrictions IS 'JSON object defining allowed endpoints, agents, file sizes, etc.';
COMMENT ON COLUMN public.api_keys.suspended_at IS 'Timestamp when key was suspended';
COMMENT ON COLUMN public.api_keys.suspension_reason IS 'Reason for key suspension';
COMMENT ON COLUMN public.api_keys.last_used_ip IS 'Last IP address that used this key';

-- ================================================================================
-- 3. CREDIT TRANSACTIONS TABLE
-- ================================================================================

-- Create table for detailed credit transaction tracking
CREATE TABLE IF NOT EXISTS public.credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    api_key_id UUID REFERENCES public.api_keys(id) ON DELETE SET NULL,
    
    -- Transaction details
    amount INTEGER NOT NULL,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('purchase', 'deduction', 'refund', 'adjustment', 'bonus')),
    
    -- Payment and billing
    payment_reference TEXT,
    invoice_id TEXT,
    stripe_payment_intent_id TEXT,
    
    -- Transaction context
    reason TEXT,
    admin_notes TEXT,
    admin_user_id TEXT,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    correlation_id TEXT,
    
    -- Balance tracking
    balance_before INTEGER NOT NULL DEFAULT 0,
    balance_after INTEGER NOT NULL DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    processed_at TIMESTAMPTZ
);

-- Indexes for credit transactions
CREATE INDEX idx_credit_transactions_customer_id ON public.credit_transactions(customer_id);
CREATE INDEX idx_credit_transactions_api_key_id ON public.credit_transactions(api_key_id);
CREATE INDEX idx_credit_transactions_type ON public.credit_transactions(transaction_type);
CREATE INDEX idx_credit_transactions_created_at ON public.credit_transactions(created_at);
CREATE INDEX idx_credit_transactions_payment_ref ON public.credit_transactions(payment_reference);

-- RLS for credit transactions
ALTER TABLE public.credit_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "customers_own_credit_transactions" ON public.credit_transactions
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

COMMENT ON TABLE public.credit_transactions IS 'Detailed credit transaction history for billing and audit';

-- ================================================================================
-- 4. RATE LIMITS TABLE
-- ================================================================================

-- Create table for configurable rate limiting
CREATE TABLE IF NOT EXISTS public.rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key_id UUID REFERENCES public.api_keys(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    
    -- Rate limit configuration
    limit_type TEXT NOT NULL CHECK (limit_type IN ('per_minute', 'per_hour', 'per_day', 'per_month')),
    limit_value INTEGER NOT NULL CHECK (limit_value > 0),
    burst_allowance INTEGER DEFAULT 0 CHECK (burst_allowance >= 0),
    
    -- Current usage tracking
    current_usage INTEGER DEFAULT 0,
    burst_used INTEGER DEFAULT 0,
    reset_at TIMESTAMPTZ NOT NULL,
    
    -- Configuration metadata
    endpoint_pattern TEXT,
    priority INTEGER DEFAULT 100,
    
    -- Status
    is_active BOOLEAN NOT NULL DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for rate limits
CREATE INDEX idx_rate_limits_api_key_id ON public.rate_limits(api_key_id);
CREATE INDEX idx_rate_limits_customer_id ON public.rate_limits(customer_id);
CREATE INDEX idx_rate_limits_reset_at ON public.rate_limits(reset_at);
CREATE INDEX idx_rate_limits_active ON public.rate_limits(is_active) WHERE is_active = true;

-- Unique constraint to prevent duplicate rate limit configurations
CREATE UNIQUE INDEX idx_rate_limits_unique ON public.rate_limits(
    COALESCE(api_key_id, '00000000-0000-0000-0000-000000000000'),
    COALESCE(customer_id, '00000000-0000-0000-0000-000000000000'),
    limit_type,
    COALESCE(endpoint_pattern, '')
) WHERE is_active = true;

-- RLS for rate limits
ALTER TABLE public.rate_limits ENABLE ROW LEVEL SECURITY;

CREATE POLICY "customers_own_rate_limits" ON public.rate_limits
    FOR ALL USING (customer_id = (auth.jwt() ->> 'customer_id')::UUID);

COMMENT ON TABLE public.rate_limits IS 'Configurable rate limiting with burst capabilities';

-- ================================================================================
-- 5. ANALYTICS VIEWS
-- ================================================================================

-- Create materialized view for daily usage analytics
CREATE MATERIALIZED VIEW IF NOT EXISTS public.daily_usage_summary AS
SELECT 
    customer_id,
    api_key_id,
    DATE(created_at) as usage_date,
    COUNT(*) as total_requests,
    SUM(credits_used) as total_credits,
    SUM(COALESCE(model_cost_usd, 0)) as total_model_cost,
    SUM(COALESCE(customer_price_usd, 0)) as total_revenue,
    AVG(processing_time_ms) as avg_processing_time,
    COUNT(DISTINCT agent_id) as unique_agents_used,
    COUNT(CASE WHEN success = true THEN 1 END) as successful_requests,
    COUNT(CASE WHEN success = false THEN 1 END) as failed_requests
FROM public.usage_logs
GROUP BY customer_id, api_key_id, DATE(created_at);

-- Index for the materialized view
CREATE UNIQUE INDEX idx_daily_usage_summary_unique ON public.daily_usage_summary(customer_id, api_key_id, usage_date);
CREATE INDEX idx_daily_usage_summary_date ON public.daily_usage_summary(usage_date);

COMMENT ON MATERIALIZED VIEW public.daily_usage_summary IS 'Daily aggregated usage statistics for analytics dashboard';

-- Create monthly usage summary view
CREATE MATERIALIZED VIEW IF NOT EXISTS public.monthly_usage_summary AS
SELECT 
    customer_id,
    DATE_TRUNC('month', usage_date) as usage_month,
    SUM(total_requests) as total_requests,
    SUM(total_credits) as total_credits,
    SUM(total_model_cost) as total_model_cost,
    SUM(total_revenue) as total_revenue,
    AVG(avg_processing_time) as avg_processing_time,
    SUM(successful_requests) as successful_requests,
    SUM(failed_requests) as failed_requests,
    ROUND((SUM(total_revenue) - SUM(total_model_cost)) / NULLIF(SUM(total_revenue), 0) * 100, 2) as profit_margin_percent
FROM public.daily_usage_summary
GROUP BY customer_id, DATE_TRUNC('month', usage_date);

-- Index for monthly summary
CREATE UNIQUE INDEX idx_monthly_usage_summary_unique ON public.monthly_usage_summary(customer_id, usage_month);

-- ================================================================================
-- 6. ENHANCED FUNCTIONS
-- ================================================================================

-- Function to get customer tier settings with defaults
CREATE OR REPLACE FUNCTION public.get_customer_tier_settings(customer_tier TEXT)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN CASE customer_tier
        WHEN 'starter' THEN '{
            "max_api_keys": 3,
            "default_credit_limit": 100,
            "rate_limit_multiplier": 1,
            "max_file_size": 10485760,
            "allowed_endpoints": ["extract", "agents"]
        }'::jsonb
        WHEN 'professional' THEN '{
            "max_api_keys": 10,
            "default_credit_limit": 1000,
            "rate_limit_multiplier": 2,
            "max_file_size": 52428800,
            "allowed_endpoints": ["extract", "agents", "analytics"]
        }'::jsonb
        WHEN 'enterprise' THEN '{
            "max_api_keys": 100,
            "default_credit_limit": 10000,
            "rate_limit_multiplier": 5,
            "max_file_size": 104857600,
            "allowed_endpoints": ["*"]
        }'::jsonb
        ELSE '{
            "max_api_keys": 1,
            "default_credit_limit": 50,
            "rate_limit_multiplier": 1
        }'::jsonb
    END;
END;
$$;

-- Function to process credit transaction
CREATE OR REPLACE FUNCTION public.process_credit_transaction(
    p_customer_id UUID,
    p_amount INTEGER,
    p_transaction_type TEXT,
    p_payment_reference TEXT DEFAULT NULL,
    p_admin_notes TEXT DEFAULT NULL,
    p_correlation_id TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    current_credits INTEGER;
    new_credits INTEGER;
    transaction_id UUID;
BEGIN
    -- Get current credits
    SELECT credits_available INTO current_credits
    FROM public.customers
    WHERE id = p_customer_id
    FOR UPDATE;
    
    IF current_credits IS NULL THEN
        RAISE EXCEPTION 'Customer not found: %', p_customer_id;
    END IF;
    
    -- Calculate new balance
    CASE p_transaction_type
        WHEN 'purchase', 'adjustment', 'bonus' THEN
            new_credits := current_credits + p_amount;
        WHEN 'deduction', 'refund' THEN
            new_credits := current_credits - p_amount;
        ELSE
            RAISE EXCEPTION 'Invalid transaction type: %', p_transaction_type;
    END CASE;
    
    -- Validate sufficient credits for deductions
    IF p_transaction_type IN ('deduction', 'refund') AND new_credits < 0 THEN
        RAISE EXCEPTION 'Insufficient credits. Current: %, Requested: %', current_credits, p_amount;
    END IF;
    
    -- Insert transaction record
    INSERT INTO public.credit_transactions (
        customer_id,
        amount,
        transaction_type,
        payment_reference,
        admin_notes,
        correlation_id,
        balance_before,
        balance_after,
        processed_at
    ) VALUES (
        p_customer_id,
        p_amount,
        p_transaction_type,
        p_payment_reference,
        p_admin_notes,
        p_correlation_id,
        current_credits,
        new_credits,
        NOW()
    ) RETURNING id INTO transaction_id;
    
    -- Update customer credits
    UPDATE public.customers
    SET credits_available = new_credits,
        updated_at = NOW()
    WHERE id = p_customer_id;
    
    -- Return transaction details
    RETURN jsonb_build_object(
        'transaction_id', transaction_id,
        'balance_before', current_credits,
        'balance_after', new_credits,
        'amount', p_amount,
        'transaction_type', p_transaction_type
    );
END;
$$;

-- ================================================================================
-- 7. TRIGGERS AND AUTOMATION
-- ================================================================================

-- Trigger to automatically populate tier_settings on customer creation
CREATE OR REPLACE FUNCTION public.populate_tier_settings()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Set tier_settings if not provided
    IF NEW.tier_settings IS NULL OR NEW.tier_settings = '{}'::jsonb THEN
        NEW.tier_settings := public.get_customer_tier_settings(NEW.tier);
    END IF;
    
    -- Set company_name to name if not provided
    IF NEW.company_name IS NULL THEN
        NEW.company_name := NEW.name;
    END IF;
    
    -- Set contact_email to email if not provided
    IF NEW.contact_email IS NULL THEN
        NEW.contact_email := NEW.email;
    END IF;
    
    RETURN NEW;
END;
$$;

CREATE TRIGGER trigger_populate_tier_settings
    BEFORE INSERT OR UPDATE ON public.customers
    FOR EACH ROW
    EXECUTE FUNCTION public.populate_tier_settings();

-- Trigger to refresh materialized views
CREATE OR REPLACE FUNCTION public.refresh_usage_views()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Refresh daily summary (only if the date has changed)
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.daily_usage_summary;
    
    -- Refresh monthly summary less frequently
    IF DATE_TRUNC('hour', NEW.created_at) != DATE_TRUNC('hour', OLD.created_at) OR OLD IS NULL THEN
        REFRESH MATERIALIZED VIEW CONCURRENTLY public.monthly_usage_summary;
    END IF;
    
    RETURN NEW;
END;
$$;

-- ================================================================================
-- 8. VALIDATION AND TESTING
-- ================================================================================

DO $$
BEGIN
    RAISE NOTICE 'Epic 4 Customer Management migration completed:';
    RAISE NOTICE '  - Extended customers and api_keys tables with Epic 4 columns';
    RAISE NOTICE '  - Created credit_transactions and rate_limits tables';
    RAISE NOTICE '  - Created materialized views for analytics';
    RAISE NOTICE '  - Created helper functions for customer management';
    RAISE NOTICE '  - Applied RLS policies and indexes';
    RAISE NOTICE '  - Ready for Epic 4 admin functionality';
END;
$$;