-- ABSOLUTE FINAL FIX: Invoice agent needs "line_items" (with underscore)
-- Current: "Line items" -> Need: "line_items"

UPDATE public.agents 
SET prompt = REPLACE(prompt, 'Line items', 'line_items'),
    system_prompt = REPLACE(system_prompt, 'Line items', 'line_items')
WHERE agent_id = 'default-invoice-v1' AND is_default = true;

-- Final verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-invoice-v1' AND is_default = true;
    
    IF prompt_text LIKE '%line_items%' THEN
        RAISE NOTICE '🎯 SUCCESS: Invoice agent prompt now contains "line_items"';
        RAISE NOTICE '🏆 THIS SHOULD BE THE FINAL FIX FOR 17/17 PASS RATE!';
    ELSE
        RAISE EXCEPTION '❌ FAILED: Invoice agent prompt still missing "line_items"';
    END IF;
END;
$$;