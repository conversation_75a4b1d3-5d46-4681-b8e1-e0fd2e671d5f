-- Epic 4 Story 4.5: Usage Analytics Dashboard
-- Comprehensive analytics tables and views for usage tracking and reporting

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "pg_cron";
CREATE EXTENSION IF NOT EXISTS "tablefunc";

-- 1. Usage Analytics Aggregation Table
CREATE TABLE IF NOT EXISTS public.usage_analytics_hourly (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
  api_key_id UUID REFERENCES public.api_keys(id) ON DELETE SET NULL,
  hour_bucket TIMESTAMP WITH TIME ZONE NOT NULL,
  
  -- Request Metrics
  total_requests INTEGER NOT NULL DEFAULT 0,
  successful_requests INTEGER NOT NULL DEFAULT 0,
  failed_requests INTEGER NOT NULL DEFAULT 0,
  
  -- Credit Metrics
  credits_consumed DECIMAL(15, 2) NOT NULL DEFAULT 0,
  credits_refunded DECIMAL(15, 2) NOT NULL DEFAULT 0,
  
  -- Cost & Revenue Metrics
  total_cost DECIMAL(15, 4) NOT NULL DEFAULT 0,
  total_revenue DECIMAL(15, 4) NOT NULL DEFAULT 0,
  profit_margin DECIMAL(5, 2) GENERATED ALWAYS AS (
    CASE 
      WHEN total_revenue > 0 THEN ((total_revenue - total_cost) / total_revenue * 100)
      ELSE 0 
    END
  ) STORED,
  
  -- Performance Metrics
  avg_processing_time_ms INTEGER,
  min_processing_time_ms INTEGER,
  max_processing_time_ms INTEGER,
  p95_processing_time_ms INTEGER,
  p99_processing_time_ms INTEGER,
  
  -- Document Processing Metrics
  documents_processed INTEGER NOT NULL DEFAULT 0,
  total_pages INTEGER NOT NULL DEFAULT 0,
  total_tokens_input INTEGER NOT NULL DEFAULT 0,
  total_tokens_output INTEGER NOT NULL DEFAULT 0,
  
  -- AI Model Usage
  openai_calls INTEGER NOT NULL DEFAULT 0,
  claude_calls INTEGER NOT NULL DEFAULT 0,
  llamaparse_calls INTEGER NOT NULL DEFAULT 0,
  
  -- Error Tracking
  validation_errors INTEGER NOT NULL DEFAULT 0,
  processing_errors INTEGER NOT NULL DEFAULT 0,
  rate_limit_errors INTEGER NOT NULL DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  CONSTRAINT unique_customer_hour UNIQUE(customer_id, hour_bucket)
);

-- 2. Daily Rollup Table
CREATE TABLE IF NOT EXISTS public.usage_analytics_daily (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
  date_bucket DATE NOT NULL,
  
  -- Aggregated Request Metrics
  total_requests INTEGER NOT NULL DEFAULT 0,
  successful_requests INTEGER NOT NULL DEFAULT 0,
  failed_requests INTEGER NOT NULL DEFAULT 0,
  success_rate DECIMAL(5, 2) GENERATED ALWAYS AS (
    CASE 
      WHEN total_requests > 0 THEN (successful_requests::DECIMAL / total_requests * 100)
      ELSE 0 
    END
  ) STORED,
  
  -- Aggregated Credit Metrics
  credits_consumed DECIMAL(15, 2) NOT NULL DEFAULT 0,
  credits_refunded DECIMAL(15, 2) NOT NULL DEFAULT 0,
  net_credits DECIMAL(15, 2) GENERATED ALWAYS AS (credits_consumed - credits_refunded) STORED,
  
  -- Aggregated Cost & Revenue
  total_cost DECIMAL(15, 4) NOT NULL DEFAULT 0,
  total_revenue DECIMAL(15, 4) NOT NULL DEFAULT 0,
  profit DECIMAL(15, 4) GENERATED ALWAYS AS (total_revenue - total_cost) STORED,
  profit_margin DECIMAL(5, 2) GENERATED ALWAYS AS (
    CASE 
      WHEN total_revenue > 0 THEN ((total_revenue - total_cost) / total_revenue * 100)
      ELSE 0 
    END
  ) STORED,
  
  -- Performance Statistics
  avg_processing_time_ms INTEGER,
  p50_processing_time_ms INTEGER,
  p95_processing_time_ms INTEGER,
  p99_processing_time_ms INTEGER,
  
  -- Document Metrics
  documents_processed INTEGER NOT NULL DEFAULT 0,
  total_pages INTEGER NOT NULL DEFAULT 0,
  avg_pages_per_doc DECIMAL(8, 2) GENERATED ALWAYS AS (
    CASE 
      WHEN documents_processed > 0 THEN (total_pages::DECIMAL / documents_processed)
      ELSE 0 
    END
  ) STORED,
  
  -- Token Usage
  total_tokens_input INTEGER NOT NULL DEFAULT 0,
  total_tokens_output INTEGER NOT NULL DEFAULT 0,
  total_tokens INTEGER GENERATED ALWAYS AS (total_tokens_input + total_tokens_output) STORED,
  
  -- Model Distribution
  openai_percentage DECIMAL(5, 2),
  claude_percentage DECIMAL(5, 2),
  llamaparse_percentage DECIMAL(5, 2),
  
  -- Active API Keys
  active_api_keys INTEGER NOT NULL DEFAULT 0,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  CONSTRAINT unique_customer_day UNIQUE(customer_id, date_bucket)
);

-- 3. Customer Behavior Analytics Table
CREATE TABLE IF NOT EXISTS public.customer_behavior_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
  analysis_date DATE NOT NULL DEFAULT CURRENT_DATE,
  
  -- Engagement Metrics
  days_active_last_30 INTEGER NOT NULL DEFAULT 0,
  days_active_last_7 INTEGER NOT NULL DEFAULT 0,
  last_active_date DATE,
  account_age_days INTEGER,
  
  -- Usage Patterns
  peak_usage_hour INTEGER, -- 0-23
  peak_usage_day INTEGER, -- 1-7 (Mon-Sun)
  usage_pattern VARCHAR(50), -- 'steady', 'growing', 'declining', 'sporadic'
  
  -- Growth Metrics
  requests_growth_rate DECIMAL(8, 2), -- Month-over-month %
  revenue_growth_rate DECIMAL(8, 2), -- Month-over-month %
  
  -- Risk Indicators
  churn_risk_score DECIMAL(5, 2), -- 0-100
  credit_exhaustion_days INTEGER, -- Predicted days until credits run out
  
  -- Value Metrics
  lifetime_value DECIMAL(15, 2),
  monthly_recurring_revenue DECIMAL(15, 2),
  average_transaction_value DECIMAL(15, 2),
  
  -- API Usage Distribution
  most_used_endpoint VARCHAR(255),
  endpoint_diversity_score DECIMAL(5, 2), -- 0-100
  
  -- Document Processing Patterns
  avg_document_size_kb DECIMAL(10, 2),
  preferred_file_types JSONB, -- Array of file types with percentages
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  CONSTRAINT unique_customer_analysis UNIQUE(customer_id, analysis_date)
);

-- 4. System Performance Metrics Table
CREATE TABLE IF NOT EXISTS public.system_performance_metrics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  metric_timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  metric_type VARCHAR(50) NOT NULL, -- 'api', 'database', 'ai_service'
  
  -- API Metrics
  api_response_time_ms INTEGER,
  api_requests_per_second DECIMAL(10, 2),
  api_error_rate DECIMAL(5, 2),
  api_uptime_percentage DECIMAL(5, 2),
  
  -- Database Metrics
  db_connection_pool_size INTEGER,
  db_active_connections INTEGER,
  db_query_time_avg_ms INTEGER,
  db_slow_queries INTEGER,
  
  -- AI Service Metrics
  ai_service_name VARCHAR(50),
  ai_availability BOOLEAN,
  ai_response_time_ms INTEGER,
  ai_error_count INTEGER,
  ai_fallback_triggered BOOLEAN,
  
  -- Resource Usage
  cpu_usage_percentage DECIMAL(5, 2),
  memory_usage_mb INTEGER,
  disk_usage_percentage DECIMAL(5, 2),
  
  -- Queue Metrics
  queue_depth INTEGER,
  queue_processing_rate DECIMAL(10, 2),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 5. Revenue Analytics Table
CREATE TABLE IF NOT EXISTS public.revenue_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
  month_year DATE NOT NULL, -- First day of month
  
  -- Revenue Breakdown
  base_revenue DECIMAL(15, 2) NOT NULL DEFAULT 0,
  overage_revenue DECIMAL(15, 2) NOT NULL DEFAULT 0,
  total_revenue DECIMAL(15, 2) GENERATED ALWAYS AS (base_revenue + overage_revenue) STORED,
  
  -- Cost Breakdown
  openai_costs DECIMAL(15, 4) NOT NULL DEFAULT 0,
  claude_costs DECIMAL(15, 4) NOT NULL DEFAULT 0,
  llamaparse_costs DECIMAL(15, 4) NOT NULL DEFAULT 0,
  infrastructure_costs DECIMAL(15, 4) NOT NULL DEFAULT 0,
  total_costs DECIMAL(15, 4) GENERATED ALWAYS AS (
    openai_costs + claude_costs + llamaparse_costs + infrastructure_costs
  ) STORED,
  
  -- Profitability
  gross_profit DECIMAL(15, 2) GENERATED ALWAYS AS (
    base_revenue + overage_revenue - openai_costs - claude_costs - llamaparse_costs - infrastructure_costs
  ) STORED,
  gross_margin DECIMAL(5, 2) GENERATED ALWAYS AS (
    CASE 
      WHEN (base_revenue + overage_revenue) > 0 
      THEN ((base_revenue + overage_revenue - openai_costs - claude_costs - llamaparse_costs - infrastructure_costs) / 
            (base_revenue + overage_revenue) * 100)
      ELSE 0 
    END
  ) STORED,
  
  -- Usage Metrics
  api_calls INTEGER NOT NULL DEFAULT 0,
  credits_consumed DECIMAL(15, 2) NOT NULL DEFAULT 0,
  documents_processed INTEGER NOT NULL DEFAULT 0,
  
  -- Billing Status
  invoice_generated BOOLEAN DEFAULT FALSE,
  payment_received BOOLEAN DEFAULT FALSE,
  payment_date DATE,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  CONSTRAINT unique_customer_month UNIQUE(customer_id, month_year)
);

-- Create indexes for query performance
CREATE INDEX idx_usage_hourly_customer_hour ON public.usage_analytics_hourly(customer_id, hour_bucket DESC);
CREATE INDEX idx_usage_hourly_hour ON public.usage_analytics_hourly(hour_bucket DESC);
CREATE INDEX idx_usage_daily_customer_date ON public.usage_analytics_daily(customer_id, date_bucket DESC);
CREATE INDEX idx_usage_daily_date ON public.usage_analytics_daily(date_bucket DESC);
CREATE INDEX idx_behavior_customer ON public.customer_behavior_analytics(customer_id, analysis_date DESC);
CREATE INDEX idx_behavior_churn_risk ON public.customer_behavior_analytics(churn_risk_score DESC) 
  WHERE churn_risk_score > 70;
CREATE INDEX idx_performance_timestamp ON public.system_performance_metrics(metric_timestamp DESC);
CREATE INDEX idx_performance_type ON public.system_performance_metrics(metric_type, metric_timestamp DESC);
CREATE INDEX idx_revenue_customer_month ON public.revenue_analytics(customer_id, month_year DESC);

-- Create materialized view for real-time dashboard
CREATE MATERIALIZED VIEW IF NOT EXISTS public.dashboard_metrics AS
WITH today_stats AS (
  SELECT 
    COUNT(DISTINCT customer_id) as active_customers,
    SUM(total_requests) as total_requests,
    SUM(successful_requests) as successful_requests,
    SUM(credits_consumed) as credits_consumed,
    SUM(total_revenue) as revenue_today,
    SUM(total_cost) as costs_today,
    AVG(avg_processing_time_ms) as avg_response_time
  FROM usage_analytics_hourly
  WHERE hour_bucket >= date_trunc('day', now())
),
ai_health AS (
  SELECT 
    ai_service_name,
    ai_availability,
    ai_response_time_ms,
    ai_error_count
  FROM system_performance_metrics
  WHERE metric_timestamp >= now() - interval '5 minutes'
    AND ai_service_name IS NOT NULL
  ORDER BY metric_timestamp DESC
),
top_customers AS (
  SELECT 
    c.id,
    c.name as company_name,
    SUM(u.total_revenue) as revenue_30d,
    SUM(u.total_requests) as requests_30d
  FROM customers c
  JOIN usage_analytics_daily u ON c.id = u.customer_id
  WHERE u.date_bucket >= CURRENT_DATE - interval '30 days'
  GROUP BY c.id, c.name
  ORDER BY revenue_30d DESC
  LIMIT 10
)
SELECT 
  ts.active_customers,
  ts.total_requests,
  ts.successful_requests,
  ts.credits_consumed,
  ts.revenue_today,
  ts.costs_today,
  ts.avg_response_time,
  (SELECT json_agg(ai_health.*) FROM ai_health) as ai_services,
  (SELECT json_agg(top_customers.*) FROM top_customers) as top_customers_list,
  now() as last_updated
FROM today_stats ts;

-- Create refresh functions for materialized view
CREATE OR REPLACE FUNCTION refresh_dashboard_metrics()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY public.dashboard_metrics;
END;
$$ LANGUAGE plpgsql;

-- Function to aggregate hourly data from usage_logs
CREATE OR REPLACE FUNCTION aggregate_usage_hourly()
RETURNS void AS $$
BEGIN
  INSERT INTO usage_analytics_hourly (
    customer_id, api_key_id, hour_bucket,
    total_requests, successful_requests, failed_requests,
    credits_consumed, total_cost, total_revenue,
    avg_processing_time_ms, min_processing_time_ms, max_processing_time_ms,
    documents_processed, total_pages, total_tokens_input, total_tokens_output,
    openai_calls, claude_calls, llamaparse_calls
  )
  SELECT 
    customer_id,
    api_key_id,
    date_trunc('hour', created_at) as hour_bucket,
    COUNT(*) as total_requests,
    COUNT(*) FILTER (WHERE status = 'success') as successful_requests,
    COUNT(*) FILTER (WHERE status = 'error') as failed_requests,
    SUM(credits_used) as credits_consumed,
    SUM(model_cost) as total_cost,
    SUM(customer_price) as total_revenue,
    AVG(processing_time_ms)::INTEGER as avg_processing_time_ms,
    MIN(processing_time_ms) as min_processing_time_ms,
    MAX(processing_time_ms) as max_processing_time_ms,
    COUNT(DISTINCT document_id) as documents_processed,
    SUM((metadata->>'pages')::INTEGER) as total_pages,
    SUM(input_tokens) as total_tokens_input,
    SUM(output_tokens) as total_tokens_output,
    COUNT(*) FILTER (WHERE model_used LIKE 'gpt%') as openai_calls,
    COUNT(*) FILTER (WHERE model_used LIKE 'claude%') as claude_calls,
    COUNT(*) FILTER (WHERE model_used = 'llamaparse') as llamaparse_calls
  FROM usage_logs
  WHERE created_at >= date_trunc('hour', now() - interval '1 hour')
    AND created_at < date_trunc('hour', now())
  GROUP BY customer_id, api_key_id, date_trunc('hour', created_at)
  ON CONFLICT (customer_id, hour_bucket) 
  DO UPDATE SET
    total_requests = EXCLUDED.total_requests,
    successful_requests = EXCLUDED.successful_requests,
    failed_requests = EXCLUDED.failed_requests,
    credits_consumed = EXCLUDED.credits_consumed,
    total_cost = EXCLUDED.total_cost,
    total_revenue = EXCLUDED.total_revenue,
    avg_processing_time_ms = EXCLUDED.avg_processing_time_ms,
    min_processing_time_ms = EXCLUDED.min_processing_time_ms,
    max_processing_time_ms = EXCLUDED.max_processing_time_ms,
    documents_processed = EXCLUDED.documents_processed,
    total_pages = EXCLUDED.total_pages,
    total_tokens_input = EXCLUDED.total_tokens_input,
    total_tokens_output = EXCLUDED.total_tokens_output,
    openai_calls = EXCLUDED.openai_calls,
    claude_calls = EXCLUDED.claude_calls,
    llamaparse_calls = EXCLUDED.llamaparse_calls,
    updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Function to rollup hourly data to daily
CREATE OR REPLACE FUNCTION rollup_daily_analytics()
RETURNS void AS $$
BEGIN
  INSERT INTO usage_analytics_daily (
    customer_id, date_bucket,
    total_requests, successful_requests, failed_requests,
    credits_consumed, total_cost, total_revenue,
    avg_processing_time_ms, p50_processing_time_ms, p95_processing_time_ms, p99_processing_time_ms,
    documents_processed, total_pages,
    total_tokens_input, total_tokens_output,
    openai_percentage, claude_percentage, llamaparse_percentage,
    active_api_keys
  )
  SELECT 
    customer_id,
    date_bucket,
    SUM(total_requests) as total_requests,
    SUM(successful_requests) as successful_requests,
    SUM(failed_requests) as failed_requests,
    SUM(credits_consumed) as credits_consumed,
    SUM(total_cost) as total_cost,
    SUM(total_revenue) as total_revenue,
    AVG(avg_processing_time_ms)::INTEGER as avg_processing_time_ms,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY avg_processing_time_ms)::INTEGER as p50,
    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY avg_processing_time_ms)::INTEGER as p95,
    PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY avg_processing_time_ms)::INTEGER as p99,
    SUM(documents_processed) as documents_processed,
    SUM(total_pages) as total_pages,
    SUM(total_tokens_input) as total_tokens_input,
    SUM(total_tokens_output) as total_tokens_output,
    CASE 
      WHEN SUM(openai_calls + claude_calls + llamaparse_calls) > 0
      THEN (SUM(openai_calls)::DECIMAL / SUM(openai_calls + claude_calls + llamaparse_calls) * 100)
      ELSE 0
    END as openai_percentage,
    CASE 
      WHEN SUM(openai_calls + claude_calls + llamaparse_calls) > 0
      THEN (SUM(claude_calls)::DECIMAL / SUM(openai_calls + claude_calls + llamaparse_calls) * 100)
      ELSE 0
    END as claude_percentage,
    CASE 
      WHEN SUM(openai_calls + claude_calls + llamaparse_calls) > 0
      THEN (SUM(llamaparse_calls)::DECIMAL / SUM(openai_calls + claude_calls + llamaparse_calls) * 100)
      ELSE 0
    END as llamaparse_percentage,
    COUNT(DISTINCT api_key_id) as active_api_keys
  FROM (
    SELECT 
      customer_id,
      api_key_id,
      DATE(hour_bucket) as date_bucket,
      total_requests,
      successful_requests,
      failed_requests,
      credits_consumed,
      total_cost,
      total_revenue,
      avg_processing_time_ms,
      documents_processed,
      total_pages,
      total_tokens_input,
      total_tokens_output,
      openai_calls,
      claude_calls,
      llamaparse_calls
    FROM usage_analytics_hourly
    WHERE hour_bucket >= CURRENT_DATE - interval '1 day'
      AND hour_bucket < CURRENT_DATE
  ) hourly_data
  GROUP BY customer_id, date_bucket
  ON CONFLICT (customer_id, date_bucket)
  DO UPDATE SET
    total_requests = EXCLUDED.total_requests,
    successful_requests = EXCLUDED.successful_requests,
    failed_requests = EXCLUDED.failed_requests,
    credits_consumed = EXCLUDED.credits_consumed,
    total_cost = EXCLUDED.total_cost,
    total_revenue = EXCLUDED.total_revenue,
    avg_processing_time_ms = EXCLUDED.avg_processing_time_ms,
    p50_processing_time_ms = EXCLUDED.p50_processing_time_ms,
    p95_processing_time_ms = EXCLUDED.p95_processing_time_ms,
    p99_processing_time_ms = EXCLUDED.p99_processing_time_ms,
    documents_processed = EXCLUDED.documents_processed,
    total_pages = EXCLUDED.total_pages,
    total_tokens_input = EXCLUDED.total_tokens_input,
    total_tokens_output = EXCLUDED.total_tokens_output,
    openai_percentage = EXCLUDED.openai_percentage,
    claude_percentage = EXCLUDED.claude_percentage,
    llamaparse_percentage = EXCLUDED.llamaparse_percentage,
    active_api_keys = EXCLUDED.active_api_keys,
    updated_at = now();
END;
$$ LANGUAGE plpgsql;

-- Schedule cron jobs for aggregation
SELECT cron.schedule('aggregate_hourly_usage', '5 * * * *', 'SELECT aggregate_usage_hourly();');
SELECT cron.schedule('rollup_daily_analytics', '0 1 * * *', 'SELECT rollup_daily_analytics();');
SELECT cron.schedule('refresh_dashboard', '*/5 * * * *', 'SELECT refresh_dashboard_metrics();');

-- Grant permissions
GRANT ALL ON public.usage_analytics_hourly TO service_role;
GRANT ALL ON public.usage_analytics_daily TO service_role;
GRANT ALL ON public.customer_behavior_analytics TO service_role;
GRANT ALL ON public.system_performance_metrics TO service_role;
GRANT ALL ON public.revenue_analytics TO service_role;
GRANT SELECT ON public.dashboard_metrics TO service_role;
GRANT ALL ON public.dashboard_metrics TO postgres;

-- Add RLS policies for service role
ALTER TABLE public.usage_analytics_hourly ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_analytics_daily ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_behavior_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.revenue_analytics ENABLE ROW LEVEL SECURITY;

-- Service role has full access
CREATE POLICY "Service role has full access to hourly analytics" ON public.usage_analytics_hourly
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role has full access to daily analytics" ON public.usage_analytics_daily
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role has full access to behavior analytics" ON public.customer_behavior_analytics
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role has full access to performance metrics" ON public.system_performance_metrics
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role has full access to revenue analytics" ON public.revenue_analytics
  FOR ALL USING (auth.role() = 'service_role');