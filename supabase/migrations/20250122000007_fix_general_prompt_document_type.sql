-- Fix general agent prompt to include "document_type" text as expected by test
-- Test expects to find "document_type" but prompt has "Document type classification"

UPDATE public.agents 
SET prompt = REPLACE(prompt, '1. Document type classification', '1. document_type classification'),
    system_prompt = REPLACE(system_prompt, '1. Document type classification', '1. document_type classification')
WHERE agent_id = 'default-general-v1' AND is_default = true;

-- Verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-general-v1' AND is_default = true;
    
    IF prompt_text LIKE '%document_type%' THEN
        RAISE NOTICE 'General agent prompt fix successful - contains "document_type"';
    ELSE
        RAISE EXCEPTION 'General agent prompt fix failed - does not contain "document_type"';
    END IF;
END;
$$;