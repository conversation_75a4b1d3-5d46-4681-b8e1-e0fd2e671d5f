-- Fix processing_status table column schema to match code expectations
-- Issue: Code expects current_stage and stages columns but they don't exist

-- Add the missing columns that the status-tracker expects
ALTER TABLE public.processing_status 
ADD COLUMN IF NOT EXISTS current_stage VARCHAR(50);

ALTER TABLE public.processing_status 
ADD COLUMN IF NOT EXISTS stages JSONB DEFAULT '[]'::jsonb;

-- Update the existing stage column to be current_stage for compatibility
UPDATE public.processing_status 
SET current_stage = stage 
WHERE current_stage IS NULL;

-- Add index for the new current_stage column
CREATE INDEX IF NOT EXISTS idx_processing_status_current_stage 
ON public.processing_status(current_stage);

-- Add helpful comment
COMMENT ON COLUMN public.processing_status.current_stage IS 'Current processing stage name';
COMMENT ON COLUMN public.processing_status.stages IS 'Array of stage information with status and timing';