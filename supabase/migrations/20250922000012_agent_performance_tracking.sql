-- Agent Performance Tracking Migration
-- 
-- GitHub Issue #18: Agent Performance Tracking
-- Creates tables and functions for comprehensive agent performance monitoring,
-- benchmarking, pattern analysis, and alerting systems.

-- ================================================================================
-- 1. AGENT PERFORMANCE LOGS TABLE
-- ================================================================================
-- Raw performance metrics for each document processing request

CREATE TABLE public.agent_performance_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core identifiers
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    document_id UUID REFERENCES public.documents(id) ON DELETE CASCADE,
    
    -- Document and processing context
    document_type TEXT NOT NULL CHECK (document_type IN ('invoice', 'contract', 'receipt', 'insurance', 'general')),
    correlation_id TEXT NOT NULL,
    
    -- Performance metrics
    processing_time_ms INTEGER NOT NULL CHECK (processing_time_ms > 0),
    accuracy_score NUMERIC(5,4) CHECK (accuracy_score >= 0 AND accuracy_score <= 1),
    confidence_score NUMERIC(5,4) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    
    -- Success/failure tracking
    success BOOLEAN NOT NULL,
    error_type TEXT,
    error_message TEXT,
    
    -- AI model information
    model_used TEXT NOT NULL,
    input_tokens INTEGER NOT NULL DEFAULT 0,
    output_tokens INTEGER NOT NULL DEFAULT 0,
    
    -- Cost tracking
    cost_usd NUMERIC(10,6) NOT NULL DEFAULT 0,
    
    -- Timestamps
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'
);

-- Indexes for performance logs
CREATE INDEX idx_agent_performance_logs_agent_id ON public.agent_performance_logs(agent_id);
CREATE INDEX idx_agent_performance_logs_customer_id ON public.agent_performance_logs(customer_id);
CREATE INDEX idx_agent_performance_logs_document_type ON public.agent_performance_logs(document_type);
CREATE INDEX idx_agent_performance_logs_timestamp ON public.agent_performance_logs(timestamp);
CREATE INDEX idx_agent_performance_logs_success ON public.agent_performance_logs(success);
CREATE INDEX idx_agent_performance_logs_correlation_id ON public.agent_performance_logs(correlation_id);

-- Composite indexes for common queries
CREATE INDEX idx_agent_performance_logs_agent_timestamp ON public.agent_performance_logs(agent_id, timestamp);
CREATE INDEX idx_agent_performance_logs_customer_timestamp ON public.agent_performance_logs(customer_id, timestamp);
CREATE INDEX idx_agent_performance_logs_agent_doc_type ON public.agent_performance_logs(agent_id, document_type);

COMMENT ON TABLE public.agent_performance_logs IS 
'Raw performance metrics for each document processing request';

-- ================================================================================
-- 2. AGENT PERFORMANCE DAILY AGGREGATIONS TABLE
-- ================================================================================
-- Pre-computed daily aggregations for faster analytics

CREATE TABLE public.agent_performance_daily (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Aggregation key
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    document_type TEXT NOT NULL CHECK (document_type IN ('invoice', 'contract', 'receipt', 'insurance', 'general')),
    date DATE NOT NULL,
    
    -- Request counts
    total_requests INTEGER NOT NULL DEFAULT 0,
    successful_requests INTEGER NOT NULL DEFAULT 0,
    failed_requests INTEGER NOT NULL DEFAULT 0,
    
    -- Processing time metrics
    avg_processing_time_ms INTEGER NOT NULL DEFAULT 0,
    median_processing_time_ms INTEGER NOT NULL DEFAULT 0,
    p95_processing_time_ms INTEGER NOT NULL DEFAULT 0,
    min_processing_time_ms INTEGER NOT NULL DEFAULT 0,
    max_processing_time_ms INTEGER NOT NULL DEFAULT 0,
    
    -- Accuracy metrics
    avg_accuracy_score NUMERIC(5,4) DEFAULT 0,
    avg_confidence_score NUMERIC(5,4) DEFAULT 0,
    min_accuracy_score NUMERIC(5,4) DEFAULT 0,
    max_accuracy_score NUMERIC(5,4) DEFAULT 0,
    
    -- Cost metrics
    total_cost_usd NUMERIC(10,6) NOT NULL DEFAULT 0,
    avg_cost_per_request NUMERIC(10,6) NOT NULL DEFAULT 0,
    
    -- Token usage
    total_input_tokens INTEGER NOT NULL DEFAULT 0,
    total_output_tokens INTEGER NOT NULL DEFAULT 0,
    avg_input_tokens INTEGER NOT NULL DEFAULT 0,
    avg_output_tokens INTEGER NOT NULL DEFAULT 0,
    
    -- Error analysis
    error_breakdown JSONB DEFAULT '{}', -- {"timeout": 5, "validation": 2}
    model_distribution JSONB DEFAULT '{}', -- {"openai/gpt-4o": 50, "claude-3": 30}
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    UNIQUE(agent_id, document_type, date)
);

-- Indexes for daily aggregations
CREATE INDEX idx_agent_performance_daily_agent_date ON public.agent_performance_daily(agent_id, date);
CREATE INDEX idx_agent_performance_daily_date ON public.agent_performance_daily(date);
CREATE INDEX idx_agent_performance_daily_doc_type ON public.agent_performance_daily(document_type);

COMMENT ON TABLE public.agent_performance_daily IS 
'Pre-computed daily aggregations for agent performance analytics';

-- ================================================================================
-- 3. PERFORMANCE ALERTS TABLE
-- ================================================================================
-- Automated alerts for performance degradation

CREATE TABLE public.performance_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Alert context
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
    
    -- Alert details
    alert_type TEXT NOT NULL CHECK (alert_type IN ('slow_processing', 'low_accuracy', 'high_error_rate', 'cost_spike', 'degraded_performance')),
    severity TEXT NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    message TEXT NOT NULL,
    
    -- Threshold information
    threshold_value NUMERIC(10,4),
    actual_value NUMERIC(10,4),
    
    -- Alert lifecycle
    acknowledged BOOLEAN NOT NULL DEFAULT false,
    acknowledged_by UUID REFERENCES public.customers(id),
    acknowledged_at TIMESTAMPTZ,
    resolved BOOLEAN NOT NULL DEFAULT false,
    resolved_by UUID REFERENCES public.customers(id),
    resolved_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'
);

-- Indexes for alerts
CREATE INDEX idx_performance_alerts_agent_id ON public.performance_alerts(agent_id);
CREATE INDEX idx_performance_alerts_customer_id ON public.performance_alerts(customer_id);
CREATE INDEX idx_performance_alerts_severity ON public.performance_alerts(severity);
CREATE INDEX idx_performance_alerts_alert_type ON public.performance_alerts(alert_type);
CREATE INDEX idx_performance_alerts_unresolved ON public.performance_alerts(resolved) WHERE resolved = false;
CREATE INDEX idx_performance_alerts_created_at ON public.performance_alerts(created_at);

COMMENT ON TABLE public.performance_alerts IS 
'Automated alerts for agent performance degradation and issues';

-- ================================================================================
-- 4. AGENT CUSTOMIZATION TRACKING TABLE
-- ================================================================================
-- Track customization patterns for analysis

CREATE TABLE public.agent_customization_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Agent relationship
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    parent_agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES public.customers(id) ON DELETE CASCADE,
    
    -- Customization details
    customization_type TEXT NOT NULL CHECK (customization_type IN ('prompt', 'schema', 'config')),
    changes_made JSONB NOT NULL,
    
    -- Performance comparison
    performance_before JSONB,
    performance_after JSONB,
    performance_evaluation_period_days INTEGER DEFAULT 7,
    
    -- Timestamps
    customized_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    performance_evaluated_at TIMESTAMPTZ,
    
    -- Additional metadata
    metadata JSONB DEFAULT '{}'
);

-- Indexes for customization tracking
CREATE INDEX idx_agent_customization_tracking_agent_id ON public.agent_customization_tracking(agent_id);
CREATE INDEX idx_agent_customization_tracking_parent_id ON public.agent_customization_tracking(parent_agent_id);
CREATE INDEX idx_agent_customization_tracking_customer_id ON public.agent_customization_tracking(customer_id);
CREATE INDEX idx_agent_customization_tracking_type ON public.agent_customization_tracking(customization_type);
CREATE INDEX idx_agent_customization_tracking_customized_at ON public.agent_customization_tracking(customized_at);

COMMENT ON TABLE public.agent_customization_tracking IS 
'Track agent customization patterns for analysis and insights';

-- ================================================================================
-- 5. UPDATE EXISTING AGENTS TABLE
-- ================================================================================
-- Add performance tracking fields to existing agents table

ALTER TABLE public.agents ADD COLUMN IF NOT EXISTS avg_processing_time_ms INTEGER DEFAULT 0;
ALTER TABLE public.agents ADD COLUMN IF NOT EXISTS accuracy_rating NUMERIC(5,4) DEFAULT 0;
ALTER TABLE public.agents ADD COLUMN IF NOT EXISTS success_rate NUMERIC(5,4) DEFAULT 1.0;
ALTER TABLE public.agents ADD COLUMN IF NOT EXISTS last_performance_update TIMESTAMPTZ;
ALTER TABLE public.agents ADD COLUMN IF NOT EXISTS performance_trend TEXT CHECK (performance_trend IN ('improving', 'declining', 'stable'));

-- Update existing performance_stats column comment
COMMENT ON COLUMN public.agents.performance_stats IS 
'Cached performance summary: {"avg_processing_time": 2500, "accuracy": 0.95, "success_rate": 0.98, "total_requests": 1000}';

-- ================================================================================
-- 6. FUNCTIONS FOR PERFORMANCE AGGREGATION
-- ================================================================================

-- Function to update daily aggregations
CREATE OR REPLACE FUNCTION update_agent_performance_daily(
    p_agent_id UUID,
    p_document_type TEXT,
    p_date DATE
) RETURNS VOID AS $$
DECLARE
    v_stats RECORD;
    v_error_breakdown JSONB;
    v_model_distribution JSONB;
BEGIN
    -- Calculate aggregated statistics for the day
    SELECT 
        COUNT(*) as total_requests,
        COUNT(*) FILTER (WHERE success = true) as successful_requests,
        COUNT(*) FILTER (WHERE success = false) as failed_requests,
        ROUND(AVG(processing_time_ms)) as avg_processing_time_ms,
        ROUND(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY processing_time_ms)) as median_processing_time_ms,
        ROUND(PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY processing_time_ms)) as p95_processing_time_ms,
        MIN(processing_time_ms) as min_processing_time_ms,
        MAX(processing_time_ms) as max_processing_time_ms,
        ROUND(AVG(accuracy_score), 4) as avg_accuracy_score,
        ROUND(AVG(confidence_score), 4) as avg_confidence_score,
        ROUND(MIN(accuracy_score), 4) as min_accuracy_score,
        ROUND(MAX(accuracy_score), 4) as max_accuracy_score,
        ROUND(SUM(cost_usd), 6) as total_cost_usd,
        ROUND(AVG(cost_usd), 6) as avg_cost_per_request,
        SUM(input_tokens) as total_input_tokens,
        SUM(output_tokens) as total_output_tokens,
        ROUND(AVG(input_tokens)) as avg_input_tokens,
        ROUND(AVG(output_tokens)) as avg_output_tokens
    INTO v_stats
    FROM agent_performance_logs
    WHERE agent_id = p_agent_id 
        AND document_type = p_document_type
        AND DATE(timestamp) = p_date;

    -- Build error breakdown JSON
    SELECT jsonb_object_agg(error_type, error_count)
    INTO v_error_breakdown
    FROM (
        SELECT 
            COALESCE(error_type, 'unknown') as error_type,
            COUNT(*) as error_count
        FROM agent_performance_logs
        WHERE agent_id = p_agent_id 
            AND document_type = p_document_type
            AND DATE(timestamp) = p_date
            AND success = false
        GROUP BY error_type
    ) error_stats;

    -- Build model distribution JSON
    SELECT jsonb_object_agg(model_used, model_count)
    INTO v_model_distribution
    FROM (
        SELECT 
            model_used,
            COUNT(*) as model_count
        FROM agent_performance_logs
        WHERE agent_id = p_agent_id 
            AND document_type = p_document_type
            AND DATE(timestamp) = p_date
        GROUP BY model_used
    ) model_stats;

    -- Upsert the daily aggregation
    INSERT INTO agent_performance_daily (
        agent_id,
        document_type,
        date,
        total_requests,
        successful_requests,
        failed_requests,
        avg_processing_time_ms,
        median_processing_time_ms,
        p95_processing_time_ms,
        min_processing_time_ms,
        max_processing_time_ms,
        avg_accuracy_score,
        avg_confidence_score,
        min_accuracy_score,
        max_accuracy_score,
        total_cost_usd,
        avg_cost_per_request,
        total_input_tokens,
        total_output_tokens,
        avg_input_tokens,
        avg_output_tokens,
        error_breakdown,
        model_distribution,
        updated_at
    ) VALUES (
        p_agent_id,
        p_document_type,
        p_date,
        COALESCE(v_stats.total_requests, 0),
        COALESCE(v_stats.successful_requests, 0),
        COALESCE(v_stats.failed_requests, 0),
        COALESCE(v_stats.avg_processing_time_ms, 0),
        COALESCE(v_stats.median_processing_time_ms, 0),
        COALESCE(v_stats.p95_processing_time_ms, 0),
        COALESCE(v_stats.min_processing_time_ms, 0),
        COALESCE(v_stats.max_processing_time_ms, 0),
        COALESCE(v_stats.avg_accuracy_score, 0),
        COALESCE(v_stats.avg_confidence_score, 0),
        COALESCE(v_stats.min_accuracy_score, 0),
        COALESCE(v_stats.max_accuracy_score, 0),
        COALESCE(v_stats.total_cost_usd, 0),
        COALESCE(v_stats.avg_cost_per_request, 0),
        COALESCE(v_stats.total_input_tokens, 0),
        COALESCE(v_stats.total_output_tokens, 0),
        COALESCE(v_stats.avg_input_tokens, 0),
        COALESCE(v_stats.avg_output_tokens, 0),
        COALESCE(v_error_breakdown, '{}'),
        COALESCE(v_model_distribution, '{}'),
        NOW()
    )
    ON CONFLICT (agent_id, document_type, date)
    DO UPDATE SET
        total_requests = EXCLUDED.total_requests,
        successful_requests = EXCLUDED.successful_requests,
        failed_requests = EXCLUDED.failed_requests,
        avg_processing_time_ms = EXCLUDED.avg_processing_time_ms,
        median_processing_time_ms = EXCLUDED.median_processing_time_ms,
        p95_processing_time_ms = EXCLUDED.p95_processing_time_ms,
        min_processing_time_ms = EXCLUDED.min_processing_time_ms,
        max_processing_time_ms = EXCLUDED.max_processing_time_ms,
        avg_accuracy_score = EXCLUDED.avg_accuracy_score,
        avg_confidence_score = EXCLUDED.avg_confidence_score,
        min_accuracy_score = EXCLUDED.min_accuracy_score,
        max_accuracy_score = EXCLUDED.max_accuracy_score,
        total_cost_usd = EXCLUDED.total_cost_usd,
        avg_cost_per_request = EXCLUDED.avg_cost_per_request,
        total_input_tokens = EXCLUDED.total_input_tokens,
        total_output_tokens = EXCLUDED.total_output_tokens,
        avg_input_tokens = EXCLUDED.avg_input_tokens,
        avg_output_tokens = EXCLUDED.avg_output_tokens,
        error_breakdown = EXCLUDED.error_breakdown,
        model_distribution = EXCLUDED.model_distribution,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Function to update agent summary performance
CREATE OR REPLACE FUNCTION update_agent_summary_performance(p_agent_id UUID) RETURNS VOID AS $$
DECLARE
    v_summary RECORD;
    v_trend TEXT;
BEGIN
    -- Calculate 30-day performance summary
    SELECT 
        ROUND(AVG(processing_time_ms)) as avg_processing_time,
        ROUND(AVG(accuracy_score), 4) as avg_accuracy,
        ROUND(CAST(COUNT(*) FILTER (WHERE success = true) AS NUMERIC) / COUNT(*), 4) as success_rate
    INTO v_summary
    FROM agent_performance_logs
    WHERE agent_id = p_agent_id 
        AND timestamp >= NOW() - INTERVAL '30 days';

    -- Determine performance trend (simplified - compare last 7 days vs previous 7 days)
    WITH recent_perf AS (
        SELECT AVG(accuracy_score) as recent_accuracy
        FROM agent_performance_logs
        WHERE agent_id = p_agent_id 
            AND timestamp >= NOW() - INTERVAL '7 days'
    ),
    previous_perf AS (
        SELECT AVG(accuracy_score) as previous_accuracy
        FROM agent_performance_logs
        WHERE agent_id = p_agent_id 
            AND timestamp >= NOW() - INTERVAL '14 days'
            AND timestamp < NOW() - INTERVAL '7 days'
    )
    SELECT 
        CASE 
            WHEN r.recent_accuracy > p.previous_accuracy * 1.05 THEN 'improving'
            WHEN r.recent_accuracy < p.previous_accuracy * 0.95 THEN 'declining'
            ELSE 'stable'
        END
    INTO v_trend
    FROM recent_perf r, previous_perf p;

    -- Update agents table with summary performance
    UPDATE agents 
    SET 
        avg_processing_time_ms = COALESCE(v_summary.avg_processing_time, avg_processing_time_ms),
        accuracy_rating = COALESCE(v_summary.avg_accuracy, accuracy_rating),
        success_rate = COALESCE(v_summary.success_rate, success_rate),
        performance_trend = COALESCE(v_trend, performance_trend),
        last_performance_update = NOW()
    WHERE id = p_agent_id;
END;
$$ LANGUAGE plpgsql;

-- ================================================================================
-- 7. TRIGGERS FOR AUTOMATIC AGGREGATION
-- ================================================================================

-- Trigger function to update aggregations when new performance data is inserted
CREATE OR REPLACE FUNCTION trigger_update_performance_aggregations() RETURNS TRIGGER AS $$
BEGIN
    -- Update daily aggregations
    PERFORM update_agent_performance_daily(
        NEW.agent_id,
        NEW.document_type,
        DATE(NEW.timestamp)
    );
    
    -- Update agent summary (async - could be moved to a background job)
    PERFORM update_agent_summary_performance(NEW.agent_id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on performance logs
CREATE TRIGGER trigger_agent_performance_logs_aggregation
    AFTER INSERT ON agent_performance_logs
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_performance_aggregations();

-- ================================================================================
-- 8. ROW LEVEL SECURITY (RLS)
-- ================================================================================

-- Enable RLS on all performance tracking tables
ALTER TABLE public.agent_performance_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_performance_daily ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.performance_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.agent_customization_tracking ENABLE ROW LEVEL SECURITY;

-- RLS policies for agent_performance_logs
CREATE POLICY "Users can view their own performance logs" ON public.agent_performance_logs
    FOR SELECT USING (customer_id = auth.uid()::uuid);

CREATE POLICY "Users can insert their own performance logs" ON public.agent_performance_logs
    FOR INSERT WITH CHECK (customer_id = auth.uid()::uuid);

-- RLS policies for agent_performance_daily  
CREATE POLICY "Users can view their own daily aggregations" ON public.agent_performance_daily
    FOR SELECT USING (
        agent_id IN (
            SELECT id FROM agents WHERE customer_id = auth.uid()::uuid OR is_default = true
        )
    );

-- RLS policies for performance_alerts
CREATE POLICY "Users can view their own alerts" ON public.performance_alerts
    FOR SELECT USING (customer_id = auth.uid()::uuid OR customer_id IS NULL);

CREATE POLICY "Users can update their own alerts" ON public.performance_alerts
    FOR UPDATE USING (customer_id = auth.uid()::uuid);

-- RLS policies for agent_customization_tracking
CREATE POLICY "Users can view their own customization tracking" ON public.agent_customization_tracking
    FOR SELECT USING (customer_id = auth.uid()::uuid);

CREATE POLICY "Users can insert their own customization tracking" ON public.agent_customization_tracking
    FOR INSERT WITH CHECK (customer_id = auth.uid()::uuid);

-- ================================================================================
-- 9. INITIAL DATA AND COMMENTS
-- ================================================================================

-- Add helpful comments
COMMENT ON FUNCTION update_agent_performance_daily(UUID, TEXT, DATE) IS 
'Updates daily performance aggregations for a specific agent and document type';

COMMENT ON FUNCTION update_agent_summary_performance(UUID) IS 
'Updates the 30-day performance summary for an agent in the agents table';

COMMENT ON TRIGGER trigger_agent_performance_logs_aggregation ON public.agent_performance_logs IS 
'Automatically updates performance aggregations when new metrics are recorded';

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON public.agent_performance_logs TO authenticated;
GRANT SELECT ON public.agent_performance_daily TO authenticated;
GRANT SELECT, UPDATE ON public.performance_alerts TO authenticated;
GRANT SELECT, INSERT ON public.agent_customization_tracking TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION update_agent_performance_daily(UUID, TEXT, DATE) TO authenticated;
GRANT EXECUTE ON FUNCTION update_agent_summary_performance(UUID) TO authenticated;