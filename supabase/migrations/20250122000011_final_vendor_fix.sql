-- FINAL FIX: Invoice agent needs "vendor" (lowercase) for test to pass
-- Current: "Vendor information" -> Need: "vendor information"

UPDATE public.agents 
SET prompt = REPLACE(prompt, 'Vendor information', 'vendor information'),
    system_prompt = REPLACE(system_prompt, 'Vendor information', 'vendor information')
WHERE agent_id = 'default-invoice-v1' AND is_default = true;

-- Simple verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-invoice-v1' AND is_default = true;
    
    IF prompt_text LIKE '%vendor information%' THEN
        RAISE NOTICE 'SUCCESS: Invoice agent prompt now contains "vendor information"';
    ELSE
        RAISE EXCEPTION 'FAILED: Invoice agent prompt still does not contain "vendor information"';
    END IF;
END;
$$;