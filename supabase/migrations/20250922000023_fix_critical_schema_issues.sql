-- Fix critical schema issues blocking tests
-- Issue 1: processing_status table missing 'progress' and 'stage_index' columns
-- Issue 2: document_cache table completely missing

-- Fix processing_status table
ALTER TABLE public.processing_status 
ADD COLUMN IF NOT EXISTS progress INTEGER DEFAULT 0;

ALTER TABLE public.processing_status 
ADD COLUMN IF NOT EXISTS stage_index INTEGER DEFAULT 0;

-- <PERSON><PERSON> missing document_cache table
CREATE TABLE IF NOT EXISTS public.document_cache (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_hash VARCHAR(64) NOT NULL UNIQUE,
    content_hash VARCHAR(64) NOT NULL,
    extracted_data JSONB NOT NULL,
    agent_id UUID NOT NULL REFERENCES public.agents(id),
    customer_id UUID NOT NULL REFERENCES public.customers(id),
    confidence_score DECIMAL(3,2),
    processing_time_ms INTEGER,
    model_used VARCHAR(50),
    cache_hit_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days')
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_document_cache_hash ON public.document_cache(document_hash);
CREATE INDEX IF NOT EXISTS idx_document_cache_content_hash ON public.document_cache(content_hash);
CREATE INDEX IF NOT EXISTS idx_document_cache_customer ON public.document_cache(customer_id);
CREATE INDEX IF NOT EXISTS idx_document_cache_agent ON public.document_cache(agent_id);
CREATE INDEX IF NOT EXISTS idx_document_cache_expires ON public.document_cache(expires_at);

-- Add comments
COMMENT ON TABLE public.document_cache IS 'Cache for processed document extraction results';
COMMENT ON COLUMN public.processing_status.progress IS 'Processing progress percentage (0-100)';
COMMENT ON COLUMN public.processing_status.stage_index IS 'Current stage index in processing pipeline';

-- Enable RLS on document_cache
ALTER TABLE public.document_cache ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for document_cache
CREATE POLICY "document_cache_customer_isolation" ON public.document_cache
    FOR ALL USING (customer_id = current_setting('app.customer_id', true)::uuid);