-- Epic 4 Story 4.4: Advanced Rate Limiting System
-- Migration: 20250922000019_epic4_rate_limiting.sql
-- TDD Implementation: Database schema for multi-tier rate limiting with burst capabilities

-- Rate limit configurations table extensions for Story 4.4
-- Add new columns to existing rate_limits table for advanced functionality

-- Add geographic and endpoint restrictions
ALTER TABLE rate_limits ADD COLUMN IF NOT EXISTS geographic_regions text[] DEFAULT NULL;
ALTER TABLE rate_limits ADD COLUMN IF NOT EXISTS endpoint_patterns text[] DEFAULT NULL;

-- Add advanced algorithm support
ALTER TABLE rate_limits ADD COLUMN IF NOT EXISTS algorithm_type text DEFAULT 'sliding_window';
ALTER TABLE rate_limits ADD COLUMN IF NOT EXISTS priority_level integer DEFAULT 1;

-- Add metadata columns
ALTER TABLE rate_limits ADD COLUMN IF NOT EXISTS created_by uuid DEFAULT NULL;

-- Add constraints for new columns
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'rate_limits_algorithm_check') THEN
    ALTER TABLE rate_limits ADD CONSTRAINT rate_limits_algorithm_check 
    CHECK (algorithm_type IN ('sliding_window', 'token_bucket', 'leaky_bucket', 'fixed_window'));
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM pg_constraint WHERE conname = 'rate_limits_priority_check') THEN
    ALTER TABLE rate_limits ADD CONSTRAINT rate_limits_priority_check 
    CHECK (priority_level BETWEEN 1 AND 10);
  END IF;
END $$;;

-- Rate limit violations log
-- Tracks when rate limits are exceeded for analysis and alerting
CREATE TABLE IF NOT EXISTS rate_limit_violations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  rate_limit_id uuid REFERENCES rate_limits(id) ON DELETE CASCADE,
  customer_id uuid REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE SET NULL,
  
  -- Violation details
  endpoint_called text NOT NULL,
  method text NOT NULL,
  attempted_requests integer NOT NULL DEFAULT 1,
  limit_exceeded integer NOT NULL,
  burst_exceeded boolean DEFAULT false,
  
  -- Request context
  user_agent text DEFAULT NULL,
  ip_address inet DEFAULT NULL,
  geographic_region text DEFAULT NULL,
  
  -- Response details
  response_code integer NOT NULL,
  retry_after_seconds integer DEFAULT NULL,
  
  -- Metadata
  violation_time timestamp DEFAULT now(),
  resolved_at timestamp DEFAULT NULL,
  
  -- Indexing for analytics
  created_at timestamp DEFAULT now()
);

-- Rate limit whitelist
-- Allows bypassing rate limits for trusted customers or emergency situations
CREATE TABLE IF NOT EXISTS rate_limit_whitelist (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE CASCADE,
  
  -- Whitelist configuration
  whitelist_type text NOT NULL CHECK (whitelist_type IN ('emergency', 'enterprise', 'testing', 'migration')),
  bypass_all_limits boolean DEFAULT false,
  specific_endpoints text[] DEFAULT NULL,
  
  -- Time restrictions
  valid_from timestamp DEFAULT now(),
  valid_until timestamp NOT NULL,
  
  -- Approval workflow
  requested_by uuid NOT NULL,
  approved_by uuid DEFAULT NULL,
  approval_reason text NOT NULL,
  
  -- Status
  is_active boolean DEFAULT true,
  created_at timestamp DEFAULT now(),
  updated_at timestamp DEFAULT now(),
  
  -- Constraints
  CONSTRAINT whitelist_scope_check CHECK (
    (customer_id IS NOT NULL AND api_key_id IS NULL) OR
    (customer_id IS NULL AND api_key_id IS NOT NULL)
  ),
  
  CONSTRAINT whitelist_time_check CHECK (valid_until > valid_from)
);

-- Rate limit analytics aggregation
-- Pre-computed analytics for dashboard performance
CREATE TABLE IF NOT EXISTS rate_limit_analytics (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Aggregation scope
  customer_id uuid REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id uuid REFERENCES api_keys(id) ON DELETE CASCADE,
  endpoint_pattern text DEFAULT NULL,
  
  -- Time window
  window_start timestamp NOT NULL,
  window_end timestamp NOT NULL,
  window_type text NOT NULL CHECK (window_type IN ('minute', 'hour', 'day', 'week', 'month')),
  
  -- Metrics
  total_requests integer NOT NULL DEFAULT 0,
  blocked_requests integer NOT NULL DEFAULT 0,
  burst_requests integer NOT NULL DEFAULT 0,
  avg_response_time_ms numeric(10,2) DEFAULT NULL,
  
  -- Rate limit efficiency
  limit_utilization_percent numeric(5,2) DEFAULT NULL,
  burst_utilization_percent numeric(5,2) DEFAULT NULL,
  
  -- Geographic distribution
  top_regions jsonb DEFAULT NULL, -- {'us-east': 1000, 'eu-west': 500}
  
  -- Computed at
  computed_at timestamp DEFAULT now(),
  
  -- Indexing
  UNIQUE(customer_id, api_key_id, endpoint_pattern, window_start, window_type)
);

-- Unique constraints for rate limits
CREATE UNIQUE INDEX IF NOT EXISTS idx_rate_limits_customer_unique 
ON rate_limits(customer_id, limit_type, algorithm_type) WHERE api_key_id IS NULL;

CREATE UNIQUE INDEX IF NOT EXISTS idx_rate_limits_api_key_unique 
ON rate_limits(api_key_id, limit_type, algorithm_type) WHERE customer_id IS NULL;

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_rate_limits_customer_active 
ON rate_limits(customer_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_rate_limits_api_key_active 
ON rate_limits(api_key_id, is_active) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_rate_limits_reset_time 
ON rate_limits(reset_at) WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_time 
ON rate_limit_violations(violation_time DESC);

CREATE INDEX IF NOT EXISTS idx_rate_limit_violations_customer 
ON rate_limit_violations(customer_id, violation_time DESC);

CREATE INDEX IF NOT EXISTS idx_rate_limit_whitelist_active 
ON rate_limit_whitelist(customer_id, api_key_id, is_active, valid_from, valid_until) 
WHERE is_active = true;

CREATE INDEX IF NOT EXISTS idx_rate_limit_analytics_window 
ON rate_limit_analytics(customer_id, window_start DESC, window_type);

-- RLS (Row Level Security) policies
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limit_violations ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limit_whitelist ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limit_analytics ENABLE ROW LEVEL SECURITY;

-- Admin full access policy
CREATE POLICY rate_limits_admin_access ON rate_limits
FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY rate_limit_violations_admin_access ON rate_limit_violations
FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY rate_limit_whitelist_admin_access ON rate_limit_whitelist
FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY rate_limit_analytics_admin_access ON rate_limit_analytics
FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Functions for rate limiting logic

-- Reset rate limit window
CREATE OR REPLACE FUNCTION reset_rate_limit_window(limit_id uuid)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  limit_record record;
  new_reset_time timestamp;
BEGIN
  SELECT * INTO limit_record FROM rate_limits WHERE id = limit_id;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Rate limit not found: %', limit_id;
  END IF;
  
  -- Calculate next reset time based on limit type
  CASE limit_record.limit_type
    WHEN 'per_minute' THEN
      new_reset_time := date_trunc('minute', now()) + interval '1 minute';
    WHEN 'per_hour' THEN
      new_reset_time := date_trunc('hour', now()) + interval '1 hour';
    WHEN 'per_day' THEN
      new_reset_time := date_trunc('day', now()) + interval '1 day';
    WHEN 'per_month' THEN
      new_reset_time := date_trunc('month', now()) + interval '1 month';
  END CASE;
  
  UPDATE rate_limits 
  SET 
    current_usage = 0,
    burst_usage = 0,
    window_start = now(),
    reset_at = new_reset_time,
    updated_at = now()
  WHERE id = limit_id;
END;
$$;

-- Check and update rate limit usage
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_customer_id uuid DEFAULT NULL,
  p_api_key_id uuid DEFAULT NULL,
  p_endpoint text DEFAULT NULL,
  p_requests_to_add integer DEFAULT 1
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  limit_record record;
  whitelist_record record;
  result jsonb := '{"allowed": true}';
  total_usage integer;
BEGIN
  -- Check for active whitelist
  SELECT * INTO whitelist_record
  FROM rate_limit_whitelist
  WHERE 
    ((p_customer_id IS NOT NULL AND customer_id = p_customer_id) OR
     (p_api_key_id IS NOT NULL AND api_key_id = p_api_key_id))
    AND is_active = true
    AND now() BETWEEN valid_from AND valid_until
    AND (bypass_all_limits = true OR 
         specific_endpoints IS NULL OR 
         p_endpoint = ANY(specific_endpoints));
  
  IF FOUND THEN
    RETURN jsonb_build_object(
      'allowed', true,
      'whitelisted', true,
      'whitelist_type', whitelist_record.whitelist_type
    );
  END IF;
  
  -- Check rate limits
  FOR limit_record IN
    SELECT rl.*, 
           CASE WHEN now() > rl.reset_at THEN true ELSE false END as needs_reset
    FROM rate_limits rl
    WHERE 
      ((p_customer_id IS NOT NULL AND rl.customer_id = p_customer_id) OR
       (p_api_key_id IS NOT NULL AND rl.api_key_id = p_api_key_id))
      AND rl.is_active = true
      AND (rl.endpoint_patterns IS NULL OR 
           p_endpoint ~ ANY(rl.endpoint_patterns))
    ORDER BY rl.priority_level DESC
  LOOP
    -- Reset window if needed
    IF limit_record.needs_reset THEN
      PERFORM reset_rate_limit_window(limit_record.id);
      -- Reload the record after reset
      SELECT * INTO limit_record FROM rate_limits WHERE id = limit_record.id;
    END IF;
    
    -- Calculate total usage including burst
    total_usage := limit_record.current_usage + limit_record.burst_usage;
    
    -- Check if limit would be exceeded
    IF total_usage + p_requests_to_add > limit_record.limit_value + limit_record.burst_allowance THEN
      -- Log violation
      INSERT INTO rate_limit_violations (
        rate_limit_id, customer_id, api_key_id,
        endpoint_called, method, attempted_requests,
        limit_exceeded, burst_exceeded, response_code,
        retry_after_seconds
      ) VALUES (
        limit_record.id, p_customer_id, p_api_key_id,
        COALESCE(p_endpoint, 'unknown'), 'POST', p_requests_to_add,
        limit_record.limit_value, 
        (total_usage > limit_record.limit_value),
        429,
        EXTRACT(EPOCH FROM (limit_record.reset_at - now()))::integer
      );
      
      RETURN jsonb_build_object(
        'allowed', false,
        'limit_type', limit_record.limit_type,
        'limit_value', limit_record.limit_value,
        'current_usage', total_usage,
        'reset_at', limit_record.reset_at,
        'retry_after', EXTRACT(EPOCH FROM (limit_record.reset_at - now()))::integer
      );
    END IF;
    
    -- Update usage (prefer burst usage up to burst allowance)
    IF limit_record.current_usage + p_requests_to_add <= limit_record.limit_value THEN
      -- Within normal limit
      UPDATE rate_limits 
      SET current_usage = current_usage + p_requests_to_add,
          updated_at = now()
      WHERE id = limit_record.id;
    ELSE
      -- Use burst capacity
      UPDATE rate_limits 
      SET burst_usage = burst_usage + p_requests_to_add,
          updated_at = now()
      WHERE id = limit_record.id;
    END IF;
    
  END LOOP;
  
  RETURN result;
END;
$$;

-- Audit logging triggers
CREATE OR REPLACE FUNCTION audit_rate_limit_changes()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO audit_logs (
    customer_id,
    resource_type,
    resource_id,
    action,
    details,
    admin_user_id
  ) VALUES (
    COALESCE(NEW.customer_id, OLD.customer_id),
    'rate_limit',
    COALESCE(NEW.id, OLD.id),
    TG_OP,
    jsonb_build_object(
      'old', to_jsonb(OLD),
      'new', to_jsonb(NEW)
    ),
    (current_setting('app.admin_user_id', true))::uuid
  );
  
  RETURN COALESCE(NEW, OLD);
END;
$$;

CREATE TRIGGER audit_rate_limits_changes
  AFTER INSERT OR UPDATE OR DELETE ON rate_limits
  FOR EACH ROW EXECUTE FUNCTION audit_rate_limit_changes();

CREATE TRIGGER audit_rate_limit_whitelist_changes
  AFTER INSERT OR UPDATE OR DELETE ON rate_limit_whitelist
  FOR EACH ROW EXECUTE FUNCTION audit_rate_limit_changes();

-- Comments for documentation
COMMENT ON TABLE rate_limits IS 'Multi-tier rate limiting configuration with burst capabilities and geographic restrictions';
COMMENT ON TABLE rate_limit_violations IS 'Log of rate limit violations for monitoring and analytics';
COMMENT ON TABLE rate_limit_whitelist IS 'Emergency and enterprise rate limit bypass configurations';
COMMENT ON TABLE rate_limit_analytics IS 'Pre-computed rate limiting analytics for dashboard performance';

COMMENT ON FUNCTION check_rate_limit IS 'Core rate limiting logic with multi-algorithm support and whitelist checking';
COMMENT ON FUNCTION reset_rate_limit_window IS 'Resets rate limit windows based on configured time periods';