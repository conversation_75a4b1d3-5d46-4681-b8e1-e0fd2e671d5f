-- Create processing_status table for tracking document processing pipeline
-- Issue: Missing table causing function failures

CREATE TABLE IF NOT EXISTS public.processing_status (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES public.documents(id) ON DELETE CASCADE,
  stage VARCHAR(50) NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_processing_status_document_id ON public.processing_status(document_id);
CREATE INDEX IF NOT EXISTS idx_processing_status_stage ON public.processing_status(stage);
CREATE INDEX IF NOT EXISTS idx_processing_status_status ON public.processing_status(status);
CREATE INDEX IF NOT EXISTS idx_processing_status_created_at ON public.processing_status(created_at);

-- Add RLS policies
ALTER TABLE public.processing_status ENABLE ROW LEVEL SECURITY;

-- Policy: Customers can only see their own document processing status
CREATE POLICY "processing_status_customer_isolation" ON public.processing_status
  FOR ALL
  USING (
    document_id IN (
      SELECT id FROM public.documents 
      WHERE customer_id = auth.uid()
    )
  );

-- Policy: Service role can access all processing status records
CREATE POLICY "processing_status_service_access" ON public.processing_status
  FOR ALL
  TO service_role
  USING (true);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_processing_status_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER processing_status_updated_at_trigger
  BEFORE UPDATE ON public.processing_status
  FOR EACH ROW
  EXECUTE FUNCTION update_processing_status_updated_at();

-- Add helpful comments
COMMENT ON TABLE public.processing_status IS 'Tracks the status of document processing pipeline stages';
COMMENT ON COLUMN public.processing_status.document_id IS 'Reference to the document being processed';
COMMENT ON COLUMN public.processing_status.stage IS 'Processing stage: text_extraction, ai_processing, validation, etc.';
COMMENT ON COLUMN public.processing_status.status IS 'Status: pending, in_progress, completed, failed';
COMMENT ON COLUMN public.processing_status.metadata IS 'Stage-specific metadata and progress information';