-- Agent Customization System Migration
-- Epic 3, Story 4: Agent Customization
-- 
-- This migration adds comprehensive agent customization capabilities including:
-- - Version control for agent modifications
-- - Change tracking and audit logging
-- - Processing configuration management
-- - Preview mode support

-- ================================================================================
-- 1. AGENT VERSIONS TABLE
-- ================================================================================
-- Stores historical versions of agent configurations for rollback capabilities

CREATE TABLE public.agent_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    version_name TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES public.customers(id),
    
    -- Agent configuration snapshot
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    system_prompt TEXT NOT NULL,
    output_schema JSONB NOT NULL,
    processing_config JSONB NOT NULL DEFAULT '{}',
    
    -- Change tracking
    changes_summary JSONB,
    parent_version_id UUID REFERENCES public.agent_versions(id),
    
    -- Metadata
    is_published BOOLEAN NOT NULL DEFAULT false,
    performance_metrics JSONB,
    
    UNIQUE(agent_id, version_name)
);

CREATE INDEX idx_agent_versions_agent_id ON public.agent_versions(agent_id);
CREATE INDEX idx_agent_versions_created_at ON public.agent_versions(created_at);
CREATE INDEX idx_agent_versions_created_by ON public.agent_versions(created_by);

COMMENT ON TABLE public.agent_versions IS 
'Version control system for agent configurations, enabling rollback and change tracking';

COMMENT ON COLUMN public.agent_versions.version_name IS 
'Human-readable version identifier (e.g., "v2.1", "production-2025-01")';

COMMENT ON COLUMN public.agent_versions.changes_summary IS 
'Summary of changes made in this version for audit purposes';

COMMENT ON COLUMN public.agent_versions.processing_config IS 
'Processing parameters: confidence_threshold, retry_attempts, model_preference, timeout_seconds';

-- ================================================================================
-- 2. AGENT CHANGE LOG TABLE
-- ================================================================================
-- Tracks all modifications to agents for collaborative editing and audit

CREATE TABLE public.agent_change_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES public.customers(id),
    
    -- Change details
    change_type TEXT NOT NULL CHECK (change_type IN (
        'created', 'customization', 'rollback', 'published', 'archived'
    )),
    changes JSONB NOT NULL,
    previous_values JSONB,
    
    -- Context
    ip_address INET,
    user_agent TEXT,
    session_id TEXT,
    
    -- Timestamps
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Validation results
    validation_passed BOOLEAN NOT NULL DEFAULT true,
    validation_results JSONB
);

CREATE INDEX idx_agent_change_log_agent_id ON public.agent_change_log(agent_id);
CREATE INDEX idx_agent_change_log_customer_id ON public.agent_change_log(customer_id);
CREATE INDEX idx_agent_change_log_timestamp ON public.agent_change_log(timestamp);
CREATE INDEX idx_agent_change_log_change_type ON public.agent_change_log(change_type);

COMMENT ON TABLE public.agent_change_log IS 
'Comprehensive audit log of all agent modifications for compliance and collaboration';

COMMENT ON COLUMN public.agent_change_log.changes IS 
'JSON object containing the specific fields that were modified';

COMMENT ON COLUMN public.agent_change_log.validation_results IS 
'Results from prompt injection checks, schema validation, and compatibility tests';

-- ================================================================================
-- 3. AGENT PREVIEW SESSIONS TABLE
-- ================================================================================
-- Stores preview mode testing sessions and results

CREATE TABLE public.agent_preview_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id UUID NOT NULL REFERENCES public.agents(id) ON DELETE CASCADE,
    customer_id UUID NOT NULL REFERENCES public.customers(id),
    
    -- Preview configuration
    preview_config JSONB NOT NULL,
    test_documents JSONB NOT NULL,
    
    -- Results
    preview_results JSONB NOT NULL,
    performance_metrics JSONB,
    
    -- Session info
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
    session_token TEXT UNIQUE NOT NULL DEFAULT encode(gen_random_bytes(32), 'hex')
);

CREATE INDEX idx_agent_preview_sessions_agent_id ON public.agent_preview_sessions(agent_id);
CREATE INDEX idx_agent_preview_sessions_customer_id ON public.agent_preview_sessions(customer_id);
CREATE INDEX idx_agent_preview_sessions_expires_at ON public.agent_preview_sessions(expires_at);
CREATE INDEX idx_agent_preview_sessions_token ON public.agent_preview_sessions(session_token);

COMMENT ON TABLE public.agent_preview_sessions IS 
'Stores preview mode test sessions allowing users to test customizations before saving';

COMMENT ON COLUMN public.agent_preview_sessions.preview_config IS 
'The customization being tested (prompt, schema, processing config)';

COMMENT ON COLUMN public.agent_preview_sessions.test_documents IS 
'Array of test documents used for preview validation';

-- ================================================================================
-- 4. ENHANCE EXISTING AGENTS TABLE
-- ================================================================================
-- Add new columns to support customization features

-- Add processing configuration support
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS processing_config JSONB NOT NULL DEFAULT '{}';

-- Add customization metadata
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS is_customizable BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN IF NOT EXISTS current_version TEXT,
ADD COLUMN IF NOT EXISTS last_customized_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_customized_by UUID REFERENCES public.customers(id);

-- Add performance tracking
ALTER TABLE public.agents 
ADD COLUMN IF NOT EXISTS performance_stats JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS usage_count INTEGER NOT NULL DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_used_at TIMESTAMPTZ;

COMMENT ON COLUMN public.agents.processing_config IS 
'JSON configuration: {"confidence_threshold": 0.8, "retry_attempts": 3, "model_preference": ["openai/gpt-4o"], "timeout_seconds": 30}';

COMMENT ON COLUMN public.agents.is_customizable IS 
'Whether this agent allows customer customization (false for locked system agents)';

COMMENT ON COLUMN public.agents.performance_stats IS 
'Aggregated performance metrics: processing times, success rates, confidence scores';

-- ================================================================================
-- 5. VALIDATION FUNCTIONS
-- ================================================================================
-- Functions to validate agent customizations

CREATE OR REPLACE FUNCTION public.validate_agent_processing_config(config JSONB)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    validation_results JSONB := '[]'::JSONB;
    confidence_threshold NUMERIC;
    retry_attempts INTEGER;
    timeout_seconds INTEGER;
BEGIN
    -- Validate confidence_threshold
    IF config ? 'confidence_threshold' THEN
        confidence_threshold := (config->>'confidence_threshold')::NUMERIC;
        IF confidence_threshold < 0 OR confidence_threshold > 1 THEN
            validation_results := validation_results || jsonb_build_object(
                'level', 'error',
                'field', 'confidence_threshold',
                'message', 'Confidence threshold must be between 0 and 1',
                'current_value', confidence_threshold
            );
        END IF;
    END IF;
    
    -- Validate retry_attempts
    IF config ? 'retry_attempts' THEN
        retry_attempts := (config->>'retry_attempts')::INTEGER;
        IF retry_attempts < 0 OR retry_attempts > 10 THEN
            validation_results := validation_results || jsonb_build_object(
                'level', 'error',
                'field', 'retry_attempts',
                'message', 'Retry attempts must be between 0 and 10',
                'current_value', retry_attempts
            );
        END IF;
    END IF;
    
    -- Validate timeout_seconds
    IF config ? 'timeout_seconds' THEN
        timeout_seconds := (config->>'timeout_seconds')::INTEGER;
        IF timeout_seconds < 1 OR timeout_seconds > 300 THEN
            validation_results := validation_results || jsonb_build_object(
                'level', 'error',
                'field', 'timeout_seconds',
                'message', 'Timeout must be between 1 and 300 seconds',
                'current_value', timeout_seconds
            );
        END IF;
    END IF;
    
    -- Validate model_preference array
    IF config ? 'model_preference' THEN
        IF jsonb_typeof(config->'model_preference') != 'array' THEN
            validation_results := validation_results || jsonb_build_object(
                'level', 'error',
                'field', 'model_preference',
                'message', 'Model preference must be an array of strings'
            );
        END IF;
    END IF;
    
    RETURN validation_results;
END;
$$;

COMMENT ON FUNCTION public.validate_agent_processing_config(JSONB) IS 
'Validates processing configuration parameters and returns validation errors/warnings';

-- ================================================================================
-- 6. VERSIONING FUNCTIONS
-- ================================================================================
-- Functions to manage agent versions

CREATE OR REPLACE FUNCTION public.create_agent_version(
    p_agent_id UUID,
    p_version_name TEXT,
    p_customer_id UUID,
    p_changes_summary JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    version_id UUID;
    current_agent RECORD;
BEGIN
    -- Get current agent state
    SELECT * INTO current_agent
    FROM public.agents
    WHERE id = p_agent_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Agent not found: %', p_agent_id;
    END IF;
    
    -- Create version record
    INSERT INTO public.agent_versions (
        agent_id,
        version_name,
        created_by,
        name,
        description,
        category,
        system_prompt,
        output_schema,
        processing_config,
        changes_summary
    )
    VALUES (
        p_agent_id,
        p_version_name,
        p_customer_id,
        current_agent.name,
        current_agent.description,
        current_agent.category,
        current_agent.system_prompt,
        current_agent.output_schema,
        current_agent.processing_config,
        p_changes_summary
    )
    RETURNING id INTO version_id;
    
    -- Update agent with current version
    UPDATE public.agents
    SET current_version = p_version_name,
        last_customized_at = NOW(),
        last_customized_by = p_customer_id
    WHERE id = p_agent_id;
    
    RETURN version_id;
END;
$$;

COMMENT ON FUNCTION public.create_agent_version(UUID, TEXT, UUID, JSONB) IS 
'Creates a new version snapshot of an agent configuration';

CREATE OR REPLACE FUNCTION public.rollback_agent_to_version(
    p_agent_id UUID,
    p_version_id UUID,
    p_customer_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    version_record RECORD;
    previous_config JSONB;
BEGIN
    -- Get version data
    SELECT * INTO version_record
    FROM public.agent_versions
    WHERE id = p_version_id
    AND agent_id = p_agent_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Version not found: %', p_version_id;
    END IF;
    
    -- Store current config for change log
    SELECT to_jsonb(a.*) INTO previous_config
    FROM public.agents a
    WHERE id = p_agent_id;
    
    -- Restore agent from version
    UPDATE public.agents
    SET 
        name = version_record.name,
        description = version_record.description,
        system_prompt = version_record.system_prompt,
        output_schema = version_record.output_schema,
        processing_config = version_record.processing_config,
        current_version = version_record.version_name,
        last_customized_at = NOW(),
        last_customized_by = p_customer_id
    WHERE id = p_agent_id;
    
    -- Log the rollback
    INSERT INTO public.agent_change_log (
        agent_id,
        customer_id,
        change_type,
        changes,
        previous_values
    )
    VALUES (
        p_agent_id,
        p_customer_id,
        'rollback',
        jsonb_build_object(
            'rolled_back_to_version', version_record.version_name,
            'version_id', p_version_id
        ),
        previous_config
    );
    
    RETURN true;
END;
$$;

COMMENT ON FUNCTION public.rollback_agent_to_version(UUID, UUID, UUID) IS 
'Rolls back an agent to a previous version configuration';

-- ================================================================================
-- 7. ROW LEVEL SECURITY
-- ================================================================================
-- Ensure customers can only access their own agent data

-- Agent versions security
ALTER TABLE public.agent_versions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "customers_own_agent_versions" ON public.agent_versions
    FOR ALL USING (
        agent_id IN (
            SELECT id FROM public.agents 
            WHERE customer_id = (auth.jwt() ->> 'customer_id')::UUID
            OR is_default = true
        )
    );

-- Agent change log security
ALTER TABLE public.agent_change_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "customers_own_agent_changes" ON public.agent_change_log
    FOR ALL USING (
        customer_id = (auth.jwt() ->> 'customer_id')::UUID
        OR agent_id IN (
            SELECT id FROM public.agents WHERE is_default = true
        )
    );

-- Preview sessions security
ALTER TABLE public.agent_preview_sessions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "customers_own_preview_sessions" ON public.agent_preview_sessions
    FOR ALL USING (
        customer_id = (auth.jwt() ->> 'customer_id')::UUID
    );

-- ================================================================================
-- 8. CLEANUP FUNCTIONS
-- ================================================================================
-- Functions to maintain system hygiene

CREATE OR REPLACE FUNCTION public.cleanup_expired_preview_sessions()
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.agent_preview_sessions
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;

COMMENT ON FUNCTION public.cleanup_expired_preview_sessions() IS 
'Removes expired preview sessions (run via cron job)';

-- ================================================================================
-- 9. PERFORMANCE INDEXES
-- ================================================================================
-- Additional indexes for performance optimization

-- Composite indexes for common queries (skip if already exists)
CREATE INDEX IF NOT EXISTS idx_agents_customer_customizable_v2 ON public.agents(customer_id, is_customizable) 
WHERE is_customizable = true;

CREATE INDEX idx_agent_versions_agent_created ON public.agent_versions(agent_id, created_at DESC);

-- Index for recent changes (without NOW() predicate)
CREATE INDEX idx_agent_change_log_recent ON public.agent_change_log(agent_id, timestamp DESC);

-- ================================================================================
-- 10. MIGRATION VALIDATION
-- ================================================================================
-- Verify migration was successful

DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
BEGIN
    -- Count new tables
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('agent_versions', 'agent_change_log', 'agent_preview_sessions');
    
    -- Count new functions
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN (
        'validate_agent_processing_config',
        'create_agent_version',
        'rollback_agent_to_version',
        'cleanup_expired_preview_sessions'
    );
    
    -- Validation
    IF table_count != 3 THEN
        RAISE EXCEPTION 'Migration failed: Expected 3 new tables, found %', table_count;
    END IF;
    
    IF function_count != 4 THEN
        RAISE EXCEPTION 'Migration failed: Expected 4 new functions, found %', function_count;
    END IF;
    
    RAISE NOTICE 'Migration successful: Agent customization system installed';
    RAISE NOTICE '  - % new tables created', table_count;
    RAISE NOTICE '  - % new functions created', function_count;
    RAISE NOTICE '  - RLS policies applied';
    RAISE NOTICE '  - Performance indexes created';
END;
$$;