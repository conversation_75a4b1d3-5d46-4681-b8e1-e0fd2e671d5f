-- Agent Cloning System Migration
-- GitHub Issue #15: Implements comprehensive agent cloning functionality
-- Date: 2025-09-22

-- ============================================================================
-- AGENT INHERITANCE TABLE
-- ============================================================================
-- Tracks parent-child relationships between agents
CREATE TABLE IF NOT EXISTS agent_inheritance (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  child_agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  parent_agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Ensure a child can only have one parent
  UNIQUE(child_agent_id),
  -- Prevent circular references (child cannot be parent of its ancestor)
  CHECK(child_agent_id != parent_agent_id)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_agent_inheritance_child ON agent_inheritance(child_agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_inheritance_parent ON agent_inheritance(parent_agent_id);

-- ============================================================================
-- AGENT CLONE CONFIGURATIONS TABLE
-- ============================================================================
-- Stores cloning configuration and metadata
CREATE TABLE IF NOT EXISTS agent_clone_configs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  
  -- Cloning metadata
  clone_source_type VARCHAR(20) NOT NULL DEFAULT 'default' CHECK(clone_source_type IN ('default', 'custom')),
  clone_operation_id UUID NOT NULL, -- Correlation ID for tracking
  clone_settings JSONB DEFAULT '{}',
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Each agent can only have one clone configuration
  UNIQUE(agent_id)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_agent_clone_configs_agent ON agent_clone_configs(agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_clone_configs_customer ON agent_clone_configs(customer_id);
CREATE INDEX IF NOT EXISTS idx_agent_clone_configs_operation ON agent_clone_configs(clone_operation_id);

-- ============================================================================
-- CUSTOMER CLONE LIMITS TABLE
-- ============================================================================
-- Tracks cloning limits and usage per customer tier
CREATE TABLE IF NOT EXISTS customer_clone_limits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  tier VARCHAR(20) NOT NULL,
  
  -- Limits
  max_clones_per_month INTEGER NOT NULL DEFAULT 2,
  max_bulk_clone_size INTEGER NOT NULL DEFAULT 1,
  
  -- Current usage
  clones_used_this_month INTEGER NOT NULL DEFAULT 0,
  current_month DATE NOT NULL DEFAULT date_trunc('month', CURRENT_DATE),
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Each customer can only have one limit record
  UNIQUE(customer_id)
);

-- Index for performance
CREATE INDEX IF NOT EXISTS idx_customer_clone_limits_customer ON customer_clone_limits(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_clone_limits_tier ON customer_clone_limits(tier);

-- ============================================================================
-- AGENT CLONING AUDIT LOG TABLE
-- ============================================================================
-- Comprehensive audit logging for all cloning operations
CREATE TABLE IF NOT EXISTS agent_cloning_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Operation details
  operation_type VARCHAR(20) NOT NULL CHECK(operation_type IN ('single_clone', 'bulk_clone')),
  operation_id UUID NOT NULL, -- Correlation ID
  
  -- Actors
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id UUID REFERENCES api_keys(id) ON DELETE SET NULL,
  
  -- Agent details
  source_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  target_agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  
  -- Operation metadata
  operation_status VARCHAR(20) NOT NULL CHECK(operation_status IN ('success', 'failure', 'partial')),
  operation_details JSONB DEFAULT '{}',
  error_message TEXT,
  
  -- Timestamps
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Indexes for performance and querying
CREATE INDEX IF NOT EXISTS idx_agent_cloning_audit_operation_id ON agent_cloning_audit_log(operation_id);
CREATE INDEX IF NOT EXISTS idx_agent_cloning_audit_customer ON agent_cloning_audit_log(customer_id);
CREATE INDEX IF NOT EXISTS idx_agent_cloning_audit_source_agent ON agent_cloning_audit_log(source_agent_id);
CREATE INDEX IF NOT EXISTS idx_agent_cloning_audit_operation_type ON agent_cloning_audit_log(operation_type);
CREATE INDEX IF NOT EXISTS idx_agent_cloning_audit_status ON agent_cloning_audit_log(operation_status);
CREATE INDEX IF NOT EXISTS idx_agent_cloning_audit_created_at ON agent_cloning_audit_log(created_at);

-- ============================================================================
-- ENHANCE AGENTS TABLE
-- ============================================================================
-- Add cloning-related fields to existing agents table
ALTER TABLE agents ADD COLUMN IF NOT EXISTS is_cloneable BOOLEAN DEFAULT true;
ALTER TABLE agents ADD COLUMN IF NOT EXISTS clone_permissions JSONB DEFAULT '{"public": true, "customers_only": false}';
ALTER TABLE agents ADD COLUMN IF NOT EXISTS clone_count INTEGER DEFAULT 0;
ALTER TABLE agents ADD COLUMN IF NOT EXISTS customization_url TEXT;

-- Index for cloneable agents
CREATE INDEX IF NOT EXISTS idx_agents_is_cloneable ON agents(is_cloneable) WHERE is_cloneable = true;

-- ============================================================================
-- ROW LEVEL SECURITY (RLS)
-- ============================================================================

-- Agent Inheritance: Customers can only see inheritance for their own agents
ALTER TABLE agent_inheritance ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Customers can view inheritance for their agents" ON agent_inheritance
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM agents a 
            WHERE a.id = child_agent_id 
            AND a.customer_id = auth.uid()::uuid
        )
    );

-- Agent Clone Configs: Customers can only see their own clone configs
ALTER TABLE agent_clone_configs ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Customers can view their own clone configs" ON agent_clone_configs
    FOR SELECT USING (customer_id = auth.uid()::uuid);

CREATE POLICY "Customers can modify their own clone configs" ON agent_clone_configs
    FOR ALL USING (customer_id = auth.uid()::uuid);

-- Customer Clone Limits: Customers can only see their own limits
ALTER TABLE customer_clone_limits ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Customers can view their own clone limits" ON customer_clone_limits
    FOR SELECT USING (customer_id = auth.uid()::uuid);

-- Agent Cloning Audit Log: Customers can only see their own audit logs
ALTER TABLE agent_cloning_audit_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Customers can view their own cloning audit logs" ON agent_cloning_audit_log
    FOR SELECT USING (customer_id = auth.uid()::uuid);

-- ============================================================================
-- HELPER FUNCTIONS
-- ============================================================================

-- Function to get clone limits for a customer
CREATE OR REPLACE FUNCTION get_customer_clone_limits(p_customer_id UUID)
RETURNS TABLE (
    max_clones_per_month INTEGER,
    max_bulk_clone_size INTEGER,
    clones_used_this_month INTEGER,
    can_clone BOOLEAN
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ccl.max_clones_per_month,
        ccl.max_bulk_clone_size,
        ccl.clones_used_this_month,
        (ccl.clones_used_this_month < ccl.max_clones_per_month) as can_clone
    FROM customer_clone_limits ccl
    WHERE ccl.customer_id = p_customer_id;
    
    -- If no record exists, return default free tier limits
    IF NOT FOUND THEN
        RETURN QUERY
        SELECT 2, 1, 0, true;
    END IF;
END;
$$;

-- Function to increment clone usage
CREATE OR REPLACE FUNCTION increment_clone_usage(p_customer_id UUID, p_increment INTEGER DEFAULT 1)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_month_date DATE := date_trunc('month', CURRENT_DATE);
BEGIN
    -- Insert or update clone limits
    INSERT INTO customer_clone_limits (customer_id, tier, current_month, clones_used_this_month)
    VALUES (p_customer_id, 'free', current_month_date, p_increment)
    ON CONFLICT (customer_id) DO UPDATE SET
        clones_used_this_month = CASE 
            WHEN customer_clone_limits.current_month = current_month_date 
            THEN customer_clone_limits.clones_used_this_month + p_increment
            ELSE p_increment
        END,
        current_month = current_month_date,
        updated_at = now();
    
    RETURN true;
END;
$$;

-- Function to check if agent can be cloned
CREATE OR REPLACE FUNCTION can_clone_agent(p_agent_id UUID, p_customer_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    agent_cloneable BOOLEAN;
    agent_customer_id UUID;
    clone_perms JSONB;
BEGIN
    SELECT is_cloneable, customer_id, clone_permissions
    INTO agent_cloneable, agent_customer_id, clone_perms
    FROM agents
    WHERE id = p_agent_id;
    
    -- Agent must exist and be cloneable
    IF NOT FOUND OR NOT agent_cloneable THEN
        RETURN false;
    END IF;
    
    -- Default agents (customer_id IS NULL) are always cloneable if marked as such
    IF agent_customer_id IS NULL THEN
        RETURN true;
    END IF;
    
    -- Custom agents can only be cloned by their owner
    IF agent_customer_id = p_customer_id THEN
        RETURN true;
    END IF;
    
    -- Check if other customers can clone this agent
    IF clone_perms->>'public' = 'true' THEN
        RETURN true;
    END IF;
    
    RETURN false;
END;
$$;

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Set up default clone limits for existing customers
INSERT INTO customer_clone_limits (customer_id, tier, max_clones_per_month, max_bulk_clone_size)
SELECT 
    c.id as customer_id,
    COALESCE(c.tier, 'free') as tier,
    CASE COALESCE(c.tier, 'free')
        WHEN 'free' THEN 2
        WHEN 'starter' THEN 10
        WHEN 'professional' THEN 25
        WHEN 'enterprise' THEN 999999
        ELSE 2
    END as max_clones_per_month,
    CASE COALESCE(c.tier, 'free')
        WHEN 'free' THEN 1
        WHEN 'starter' THEN 5
        WHEN 'professional' THEN 10
        WHEN 'enterprise' THEN 25
        ELSE 1
    END as max_bulk_clone_size
FROM customers c
WHERE NOT EXISTS (
    SELECT 1 FROM customer_clone_limits ccl 
    WHERE ccl.customer_id = c.id
);

-- Update existing default agents to be cloneable
UPDATE agents 
SET 
    is_cloneable = true,
    clone_permissions = '{"public": true, "customers_only": false}'
WHERE customer_id IS NULL AND is_cloneable IS NULL;

-- ============================================================================
-- GRANTS AND PERMISSIONS
-- ============================================================================

-- Grant necessary permissions for the functions
GRANT EXECUTE ON FUNCTION get_customer_clone_limits(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_clone_usage(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION can_clone_agent(UUID, UUID) TO authenticated;

-- Grant access to new tables for authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON agent_inheritance TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON agent_clone_configs TO authenticated;
GRANT SELECT, INSERT, UPDATE ON customer_clone_limits TO authenticated;
GRANT SELECT, INSERT ON agent_cloning_audit_log TO authenticated;

-- Grant access to sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Trigger to update clone count when inheritance is created/deleted
CREATE OR REPLACE FUNCTION update_agent_clone_count()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE agents 
        SET clone_count = clone_count + 1, updated_at = now()
        WHERE id = NEW.parent_agent_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE agents 
        SET clone_count = clone_count - 1, updated_at = now()
        WHERE id = OLD.parent_agent_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$;

DROP TRIGGER IF EXISTS trigger_update_agent_clone_count ON agent_inheritance;
CREATE TRIGGER trigger_update_agent_clone_count
    AFTER INSERT OR DELETE ON agent_inheritance
    FOR EACH ROW
    EXECUTE FUNCTION update_agent_clone_count();

-- Trigger to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER 
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$;

-- Apply updated_at triggers to relevant tables
DROP TRIGGER IF EXISTS trigger_agent_inheritance_updated_at ON agent_inheritance;
CREATE TRIGGER trigger_agent_inheritance_updated_at
    BEFORE UPDATE ON agent_inheritance
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_agent_clone_configs_updated_at ON agent_clone_configs;
CREATE TRIGGER trigger_agent_clone_configs_updated_at
    BEFORE UPDATE ON agent_clone_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_customer_clone_limits_updated_at ON customer_clone_limits;
CREATE TRIGGER trigger_customer_clone_limits_updated_at
    BEFORE UPDATE ON customer_clone_limits
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();