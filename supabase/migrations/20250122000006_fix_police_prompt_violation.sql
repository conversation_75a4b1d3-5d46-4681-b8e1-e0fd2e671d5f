-- Fix the final test failure - police prompt needs "violation" (not "Violations")
-- Test expects lowercase singular "violation" but prompt has "Violations and citations"

UPDATE public.agents 
SET prompt = REPLACE(prompt, '5. Violations and citations issued', '5. violation codes and citations issued'),
    system_prompt = REPLACE(system_prompt, '5. Violations and citations issued', '5. violation codes and citations issued')
WHERE agent_id = 'default-police-report-v1' AND is_default = true;

-- Verification
DO $$
DECLARE
    prompt_text TEXT;
BEGIN
    SELECT prompt INTO prompt_text
    FROM public.agents
    WHERE agent_id = 'default-police-report-v1' AND is_default = true;
    
    IF prompt_text LIKE '%violation%' THEN
        RAISE NOTICE 'Police prompt fix successful - contains lowercase "violation"';
    ELSE
        RAISE EXCEPTION 'Police prompt fix failed - does not contain "violation"';
    END IF;
END;
$$;