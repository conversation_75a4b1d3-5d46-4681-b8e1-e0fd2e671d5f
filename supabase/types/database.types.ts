export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          extensions?: Json
          operationName?: string
          query?: string
          variables?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      agent_clone_configs: {
        Row: {
          agent_id: string
          clone_description: string | null
          clone_generation: number
          clone_name: string
          cloned_at: string
          customer_id: string
          customization_count: number
          customizations: Json | null
          id: string
          is_customizable: boolean
          last_customized_at: string | null
        }
        Insert: {
          agent_id: string
          clone_description?: string | null
          clone_generation?: number
          clone_name: string
          cloned_at?: string
          customer_id: string
          customization_count?: number
          customizations?: Json | null
          id?: string
          is_customizable?: boolean
          last_customized_at?: string | null
        }
        Update: {
          agent_id?: string
          clone_description?: string | null
          clone_generation?: number
          clone_name?: string
          cloned_at?: string
          customer_id?: string
          customization_count?: number
          customizations?: Json | null
          id?: string
          is_customizable?: boolean
          last_customized_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_clone_configs_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: true
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_clone_configs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_clone_operations: {
        Row: {
          api_key_id: string
          completed_at: string | null
          correlation_id: string | null
          created_at: string
          customer_id: string
          error_details: Json | null
          failure_count: number
          id: string
          ip_address: unknown | null
          operation_status: string
          operation_type: string
          processing_time_ms: number | null
          request_payload: Json | null
          source_agent_ids: string[]
          success_count: number
          target_agent_ids: string[] | null
          user_agent: string | null
        }
        Insert: {
          api_key_id: string
          completed_at?: string | null
          correlation_id?: string | null
          created_at?: string
          customer_id: string
          error_details?: Json | null
          failure_count?: number
          id?: string
          ip_address?: unknown | null
          operation_status?: string
          operation_type: string
          processing_time_ms?: number | null
          request_payload?: Json | null
          source_agent_ids: string[]
          success_count?: number
          target_agent_ids?: string[] | null
          user_agent?: string | null
        }
        Update: {
          api_key_id?: string
          completed_at?: string | null
          correlation_id?: string | null
          created_at?: string
          customer_id?: string
          error_details?: Json | null
          failure_count?: number
          id?: string
          ip_address?: unknown | null
          operation_status?: string
          operation_type?: string
          processing_time_ms?: number | null
          request_payload?: Json | null
          source_agent_ids?: string[]
          success_count?: number
          target_agent_ids?: string[] | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_clone_operations_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_clone_operations_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_inheritance: {
        Row: {
          child_agent_id: string
          created_at: string
          id: string
          inheritance_depth: number
          inheritance_type: string
          parent_agent_id: string
          root_agent_id: string
        }
        Insert: {
          child_agent_id: string
          created_at?: string
          id?: string
          inheritance_depth?: number
          inheritance_type?: string
          parent_agent_id: string
          root_agent_id: string
        }
        Update: {
          child_agent_id?: string
          created_at?: string
          id?: string
          inheritance_depth?: number
          inheritance_type?: string
          parent_agent_id?: string
          root_agent_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_inheritance_child_agent_id_fkey"
            columns: ["child_agent_id"]
            isOneToOne: true
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_inheritance_parent_agent_id_fkey"
            columns: ["parent_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agent_inheritance_root_agent_id_fkey"
            columns: ["root_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_performance_metrics: {
        Row: {
          accuracy_score: number
          agent_id: string
          avg_processing_time_ms: number
          confidence_score: number
          id: string
          test_date: string
          test_document_count: number
          test_results: Json | null
        }
        Insert: {
          accuracy_score?: number
          agent_id: string
          avg_processing_time_ms?: number
          confidence_score?: number
          id?: string
          test_date?: string
          test_document_count?: number
          test_results?: Json | null
        }
        Update: {
          accuracy_score?: number
          agent_id?: string
          avg_processing_time_ms?: number
          confidence_score?: number
          id?: string
          test_date?: string
          test_document_count?: number
          test_results?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_performance_metrics_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_versions: {
        Row: {
          agent_id: string
          changelog: string | null
          created_at: string
          deprecation_date: string | null
          id: string
          is_current: boolean
          version_number: string
        }
        Insert: {
          agent_id: string
          changelog?: string | null
          created_at?: string
          deprecation_date?: string | null
          id?: string
          is_current?: boolean
          version_number: string
        }
        Update: {
          agent_id?: string
          changelog?: string | null
          created_at?: string
          deprecation_date?: string | null
          id?: string
          is_current?: boolean
          version_number?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_versions_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agents: {
        Row: {
          agent_id: string
          category: string
          clone_generation: number
          clone_source_id: string | null
          cloning_allowed: boolean
          created_at: string
          customer_id: string | null
          customization_locked: boolean
          description: string | null
          id: string
          is_customizable: boolean
          is_default: boolean
          json_schema: Json
          name: string
          parent_agent_id: string | null
          performance_metrics: Json | null
          prompt: string
          settings: Json | null
          status: string
          updated_at: string
          version: number
        }
        Insert: {
          agent_id: string
          category: string
          clone_generation?: number
          clone_source_id?: string | null
          cloning_allowed?: boolean
          created_at?: string
          customer_id?: string | null
          customization_locked?: boolean
          description?: string | null
          id?: string
          is_customizable?: boolean
          is_default?: boolean
          json_schema: Json
          name: string
          parent_agent_id?: string | null
          performance_metrics?: Json | null
          prompt: string
          settings?: Json | null
          status?: string
          updated_at?: string
          version?: number
        }
        Update: {
          agent_id?: string
          category?: string
          clone_generation?: number
          clone_source_id?: string | null
          cloning_allowed?: boolean
          created_at?: string
          customer_id?: string | null
          customization_locked?: boolean
          description?: string | null
          id?: string
          is_customizable?: boolean
          is_default?: boolean
          json_schema?: Json
          name?: string
          parent_agent_id?: string | null
          performance_metrics?: Json | null
          prompt?: string
          settings?: Json | null
          status?: string
          updated_at?: string
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "agents_clone_source_id_fkey"
            columns: ["clone_source_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agents_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agents_parent_agent_id_fkey"
            columns: ["parent_agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      api_keys: {
        Row: {
          created_at: string
          credits: number
          customer_id: string
          expires_at: string | null
          id: string
          key_hash: string
          key_prefix: string
          key_salt: string | null
          key_type: string
          last_used_at: string | null
          max_credits: number | null
          name: string
          permissions: Json
          rate_limits: Json
          revoked: boolean
          revoked_at: string | null
          revoked_reason: string | null
          status: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          credits?: number
          customer_id: string
          expires_at?: string | null
          id?: string
          key_hash: string
          key_prefix: string
          key_salt?: string | null
          key_type: string
          last_used_at?: string | null
          max_credits?: number | null
          name?: string
          permissions?: Json
          rate_limits?: Json
          revoked?: boolean
          revoked_at?: string | null
          revoked_reason?: string | null
          status?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          credits?: number
          customer_id?: string
          expires_at?: string | null
          id?: string
          key_hash?: string
          key_prefix?: string
          key_salt?: string | null
          key_type?: string
          last_used_at?: string | null
          max_credits?: number | null
          name?: string
          permissions?: Json
          rate_limits?: Json
          revoked?: boolean
          revoked_at?: string | null
          revoked_reason?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "api_keys_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      audit_logs: {
        Row: {
          action: string
          api_key_id: string | null
          correlation_id: string | null
          created_at: string
          customer_id: string | null
          error_message: string | null
          id: string
          ip_address: unknown | null
          metadata: Json | null
          new_values: Json | null
          old_values: Json | null
          request_id: string | null
          resource_id: string | null
          resource_type: string
          session_id: string | null
          success: boolean
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          action: string
          api_key_id?: string | null
          correlation_id?: string | null
          created_at?: string
          customer_id?: string | null
          error_message?: string | null
          id?: string
          ip_address?: unknown | null
          metadata?: Json | null
          new_values?: Json | null
          old_values?: Json | null
          request_id?: string | null
          resource_id?: string | null
          resource_type: string
          session_id?: string | null
          success?: boolean
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          action?: string
          api_key_id?: string | null
          correlation_id?: string | null
          created_at?: string
          customer_id?: string | null
          error_message?: string | null
          id?: string
          ip_address?: unknown | null
          metadata?: Json | null
          new_values?: Json | null
          old_values?: Json | null
          request_id?: string | null
          resource_id?: string | null
          resource_type?: string
          session_id?: string | null
          success?: boolean
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "audit_logs_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "audit_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      customer_clone_limits: {
        Row: {
          can_bulk_clone: boolean
          can_clone_custom_agents: boolean
          created_at: string
          id: string
          max_clone_depth: number
          max_clones: number
          tier: string
          updated_at: string
        }
        Insert: {
          can_bulk_clone?: boolean
          can_clone_custom_agents?: boolean
          created_at?: string
          id?: string
          max_clone_depth?: number
          max_clones: number
          tier: string
          updated_at?: string
        }
        Update: {
          can_bulk_clone?: boolean
          can_clone_custom_agents?: boolean
          created_at?: string
          id?: string
          max_clone_depth?: number
          max_clones?: number
          tier?: string
          updated_at?: string
        }
        Relationships: []
      }
      customers: {
        Row: {
          address: Json | null
          billing_info: Json | null
          company_name: string
          contact_name: string | null
          created_at: string
          email: string
          id: string
          phone: string | null
          settings: Json | null
          status: string
          tier: string
          updated_at: string
        }
        Insert: {
          address?: Json | null
          billing_info?: Json | null
          company_name: string
          contact_name?: string | null
          created_at?: string
          email: string
          id?: string
          phone?: string | null
          settings?: Json | null
          status?: string
          tier?: string
          updated_at?: string
        }
        Update: {
          address?: Json | null
          billing_info?: Json | null
          company_name?: string
          contact_name?: string | null
          created_at?: string
          email?: string
          id?: string
          phone?: string | null
          settings?: Json | null
          status?: string
          tier?: string
          updated_at?: string
        }
        Relationships: []
      }
      documents: {
        Row: {
          agent_id: string | null
          api_key_id: string
          created_at: string
          customer_id: string
          customer_price: number | null
          error_message: string | null
          expires_at: string | null
          extraction_confidence: number | null
          extraction_result: Json | null
          file_hash: string
          file_size: number
          file_type: string
          filename: string
          id: string
          metadata: Json | null
          model_used: string | null
          original_filename: string
          processing_completed_at: string | null
          processing_cost: number | null
          processing_started_at: string | null
          retention_policy: string
          retry_count: number
          status: string
          storage_path: string
          updated_at: string
        }
        Insert: {
          agent_id?: string | null
          api_key_id: string
          created_at?: string
          customer_id: string
          customer_price?: number | null
          error_message?: string | null
          expires_at?: string | null
          extraction_confidence?: number | null
          extraction_result?: Json | null
          file_hash: string
          file_size: number
          file_type: string
          filename: string
          id?: string
          metadata?: Json | null
          model_used?: string | null
          original_filename: string
          processing_completed_at?: string | null
          processing_cost?: number | null
          processing_started_at?: string | null
          retention_policy?: string
          retry_count?: number
          status?: string
          storage_path: string
          updated_at?: string
        }
        Update: {
          agent_id?: string | null
          api_key_id?: string
          created_at?: string
          customer_id?: string
          customer_price?: number | null
          error_message?: string | null
          expires_at?: string | null
          extraction_confidence?: number | null
          extraction_result?: Json | null
          file_hash?: string
          file_size?: number
          file_type?: string
          filename?: string
          id?: string
          metadata?: Json | null
          model_used?: string | null
          original_filename?: string
          processing_completed_at?: string | null
          processing_cost?: number | null
          processing_started_at?: string | null
          retention_policy?: string
          retry_count?: number
          status?: string
          storage_path?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "documents_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
        ]
      }
      job_queue: {
        Row: {
          actual_duration: number | null
          agent_id: string | null
          api_key_id: string
          completed_at: string | null
          correlation_id: string
          created_at: string
          customer_id: string
          document_id: string
          error_code: string | null
          error_message: string | null
          estimated_duration: number | null
          expires_at: string | null
          id: string
          job_data: Json
          job_type: string
          max_retries: number
          parent_job_id: string | null
          priority: Database["public"]["Enums"]["job_priority"]
          processing_node: string | null
          result: Json | null
          retry_count: number
          retry_delay_base: number
          scheduled_for: string
          stack_trace: string | null
          started_at: string | null
          status: Database["public"]["Enums"]["job_status"]
          webhook_url: string | null
        }
        Insert: {
          actual_duration?: number | null
          agent_id?: string | null
          api_key_id: string
          completed_at?: string | null
          correlation_id?: string
          created_at?: string
          customer_id: string
          document_id: string
          error_code?: string | null
          error_message?: string | null
          estimated_duration?: number | null
          expires_at?: string | null
          id?: string
          job_data?: Json
          job_type?: string
          max_retries?: number
          parent_job_id?: string | null
          priority?: Database["public"]["Enums"]["job_priority"]
          processing_node?: string | null
          result?: Json | null
          retry_count?: number
          retry_delay_base?: number
          scheduled_for?: string
          stack_trace?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["job_status"]
          webhook_url?: string | null
        }
        Update: {
          actual_duration?: number | null
          agent_id?: string | null
          api_key_id?: string
          completed_at?: string | null
          correlation_id?: string
          created_at?: string
          customer_id?: string
          document_id?: string
          error_code?: string | null
          error_message?: string | null
          estimated_duration?: number | null
          expires_at?: string | null
          id?: string
          job_data?: Json
          job_type?: string
          max_retries?: number
          parent_job_id?: string | null
          priority?: Database["public"]["Enums"]["job_priority"]
          processing_node?: string | null
          result?: Json | null
          retry_count?: number
          retry_delay_base?: number
          scheduled_for?: string
          stack_trace?: string | null
          started_at?: string | null
          status?: Database["public"]["Enums"]["job_status"]
          webhook_url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "job_queue_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_queue_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_queue_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_queue_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "job_queue_parent_job_id_fkey"
            columns: ["parent_job_id"]
            isOneToOne: false
            referencedRelation: "job_queue"
            referencedColumns: ["id"]
          },
        ]
      }
      rate_limits: {
        Row: {
          api_key_id: string
          created_at: string
          id: string
          request_count: number
          updated_at: string
          window_start: string
          window_type: string
        }
        Insert: {
          api_key_id: string
          created_at?: string
          id?: string
          request_count?: number
          updated_at?: string
          window_start: string
          window_type?: string
        }
        Update: {
          api_key_id?: string
          created_at?: string
          id?: string
          request_count?: number
          updated_at?: string
          window_start?: string
          window_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "rate_limits_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
        ]
      }
      usage_logs: {
        Row: {
          api_key_id: string
          created_at: string
          credits_used: number
          customer_id: string
          customer_price: number
          document_id: string | null
          error_code: string | null
          error_message: string | null
          id: string
          input_tokens: number | null
          ip_address: unknown | null
          metadata: Json | null
          model_cost: number
          model_used: string | null
          operation_type: string
          output_tokens: number | null
          processing_time_ms: number | null
          profit_margin: number | null
          request_id: string | null
          success: boolean
          user_agent: string | null
        }
        Insert: {
          api_key_id: string
          created_at?: string
          credits_used?: number
          customer_id: string
          customer_price?: number
          document_id?: string | null
          error_code?: string | null
          error_message?: string | null
          id?: string
          input_tokens?: number | null
          ip_address?: unknown | null
          metadata?: Json | null
          model_cost?: number
          model_used?: string | null
          operation_type: string
          output_tokens?: number | null
          processing_time_ms?: number | null
          profit_margin?: number | null
          request_id?: string | null
          success?: boolean
          user_agent?: string | null
        }
        Update: {
          api_key_id?: string
          created_at?: string
          credits_used?: number
          customer_id?: string
          customer_price?: number
          document_id?: string | null
          error_code?: string | null
          error_message?: string | null
          id?: string
          input_tokens?: number | null
          ip_address?: unknown | null
          metadata?: Json | null
          model_cost?: number
          model_used?: string | null
          operation_type?: string
          output_tokens?: number | null
          processing_time_ms?: number | null
          profit_margin?: number | null
          request_id?: string | null
          success?: boolean
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "usage_logs_api_key_id_fkey"
            columns: ["api_key_id"]
            isOneToOne: false
            referencedRelation: "api_keys"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_customer_id_fkey"
            columns: ["customer_id"]
            isOneToOne: false
            referencedRelation: "customers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "usage_logs_document_id_fkey"
            columns: ["document_id"]
            isOneToOne: false
            referencedRelation: "documents"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_document_expiry: {
        Args: { created_at?: string; retention_policy: string }
        Returns: string
      }
      calculate_priority_order: {
        Args: { priority: Database["public"]["Enums"]["job_priority"] }
        Returns: number
      }
      calculate_profit_margin: {
        Args: { customer_price: number; model_cost: number }
        Returns: number
      }
      calculate_retry_delay: {
        Args: { base_delay: number; retry_count: number }
        Returns: number
      }
      check_agent_name_conflict: {
        Args: { agent_name: string; customer_uuid: string }
        Returns: boolean
      }
      check_cron_jobs_health: {
        Args: Record<PropertyKey, never>
        Returns: {
          healthy: boolean
          job_name: string
          last_run: string
          next_run: string
          status: string
        }[]
      }
      check_table_rls: {
        Args: { table_name: string }
        Returns: boolean
      }
      cleanup_job_queue: {
        Args: Record<PropertyKey, never>
        Returns: {
          dead_letter_archived: number
          expired_jobs_deleted: number
          old_completed_deleted: number
        }[]
      }
      cleanup_old_rate_limits: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      complete_job: {
        Args: { p_job_id: string; p_processing_node: string; p_result: Json }
        Returns: boolean
      }
      create_agent_inheritance: {
        Args: {
          child_agent_uuid: string
          inheritance_type_param?: string
          parent_agent_uuid: string
        }
        Returns: string
      }
      current_customer_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      deduct_api_key_credits: {
        Args: { p_credits_to_deduct: number; p_key_id: string }
        Returns: {
          message: string
          remaining_credits: number
          success: boolean
        }[]
      }
      enqueue_job: {
        Args: {
          p_agent_id?: string
          p_api_key_id: string
          p_customer_id: string
          p_document_id: string
          p_job_data?: Json
          p_job_type?: string
          p_max_retries?: number
          p_priority?: Database["public"]["Enums"]["job_priority"]
          p_webhook_url?: string
        }
        Returns: string
      }
      explain_query: {
        Args: { query: string }
        Returns: string
      }
      extract_key_type: {
        Args: { api_key: string }
        Returns: string
      }
      fail_job: {
        Args: {
          p_error_code?: string
          p_error_message: string
          p_job_id: string
          p_processing_node?: string
          p_stack_trace?: string
        }
        Returns: boolean
      }
      generate_api_key: {
        Args: {
          p_credits?: number
          p_customer_id: string
          p_key_type: string
          p_name?: string
        }
        Returns: {
          credits: number
          expires_at: string
          key_hash: string
          key_id: string
          message: string
          raw_key: string
          success: boolean
        }[]
      }
      generate_api_key_secure: {
        Args: {
          p_credits?: number
          p_customer_id: string
          p_key_type: string
          p_name?: string
        }
        Returns: {
          credits: number
          expires_at: string
          key_hash: string
          key_id: string
          key_salt: string
          message: string
          raw_key: string
          success: boolean
        }[]
      }
      generate_salt: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      generate_unique_agent_name: {
        Args: { base_name: string; customer_uuid: string }
        Returns: string
      }
      get_cron_job_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          avg_duration_ms: number
          failed_runs: number
          job_name: string
          last_error: string
          success_rate: number
          successful_runs: number
          total_runs: number
        }[]
      }
      get_current_agent_version: {
        Args: { agent_uuid: string }
        Returns: string
      }
      get_current_customer: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_customer_clone_count: {
        Args: { customer_uuid: string }
        Returns: number
      }
      get_customer_clone_limits: {
        Args: { customer_tier: string }
        Returns: {
          can_bulk_clone: boolean
          can_clone_custom_agents: boolean
          max_clone_depth: number
          max_clones: number
        }[]
      }
      get_customer_job_priority: {
        Args: { customer_tier: string }
        Returns: Database["public"]["Enums"]["job_priority"]
      }
      get_customer_usage_summary: {
        Args: { customer_id: string; end_date: string; start_date: string }
        Returns: {
          avg_profit_margin: number
          successful_requests: number
          total_cost: number
          total_price: number
          total_requests: number
        }[]
      }
      get_default_agents_by_category: {
        Args: { agent_category?: string }
        Returns: {
          accuracy_score: number
          agent_id: string
          category: string
          created_at: string
          current_version: string
          description: string
          name: string
          version: number
        }[]
      }
      get_monthly_usage_summary: {
        Args: { days_back?: number }
        Returns: {
          customer_id: string
          total_cost: number
          total_price: number
        }[]
      }
      get_next_job: {
        Args: { processing_node_id: string }
        Returns: {
          agent_id: string
          correlation_id: string
          customer_id: string
          document_id: string
          job_data: Json
          job_id: string
          job_type: string
          priority: Database["public"]["Enums"]["job_priority"]
          retry_count: number
        }[]
      }
      get_queue_metrics: {
        Args: Record<PropertyKey, never>
        Returns: {
          avg_processing_time_seconds: number
          high_queued: number
          low_queued: number
          normal_queued: number
          oldest_queued_age_minutes: number
          total_dead_letter: number
          total_failed: number
          total_processing: number
          total_queued: number
          urgent_queued: number
        }[]
      }
      get_rate_limit_status: {
        Args: { p_api_key_id: string; p_window_type?: string }
        Returns: {
          current_count: number
          limit_configured: number
          window_start: string
        }[]
      }
      hash_api_key: {
        Args: { raw_key: string }
        Returns: string
      }
      hash_api_key_pbkdf2: {
        Args: { raw_key: string; salt_hex: string }
        Returns: string
      }
      increment_rate_limit: {
        Args: { p_api_key_id: string; p_window_type?: string }
        Returns: {
          limit_exceeded: boolean
          new_count: number
        }[]
      }
      list_rls_policies: {
        Args: { schema_name?: string }
        Returns: {
          policy_cmd: string
          policy_name: string
          table_name: string
        }[]
      }
      log_clone_operation: {
        Args: {
          api_key_uuid: string
          correlation_id_param?: string
          customer_uuid: string
          error_details_param?: Json
          failure_count_param?: number
          ip_address_param?: unknown
          operation_status_param?: string
          operation_type_param: string
          processing_time_ms_param?: number
          request_payload_param?: Json
          source_agent_ids_param: string[]
          success_count_param?: number
          target_agent_ids_param?: string[]
          user_agent_param?: string
        }
        Returns: string
      }
      migrate_existing_keys_to_pbkdf2: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      pause_queue_processing: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      record_agent_performance: {
        Args: {
          accuracy: number
          agent_uuid: string
          avg_time_ms: number
          confidence: number
          test_count: number
          test_results_json?: Json
        }
        Returns: string
      }
      reset_stuck_jobs: {
        Args: { stuck_threshold_minutes?: number }
        Returns: number
      }
      resume_queue_processing: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      set_current_customer: {
        Args: { customer_id: string }
        Returns: undefined
      }
      update_api_key_status: {
        Args: { p_key_id: string; p_new_status: string; p_reason?: string }
        Returns: {
          key_id: string
          message: string
          new_status: string
          old_status: string
          success: boolean
        }[]
      }
      validate_agent_clone_permission: {
        Args: {
          customer_tier: string
          customer_uuid: string
          source_agent_uuid: string
        }
        Returns: {
          can_clone: boolean
          error_message: string
        }[]
      }
      validate_agent_schema: {
        Args: { schema_json: Json }
        Returns: boolean
      }
      validate_api_key_auth: {
        Args: { raw_key: string }
        Returns: {
          credits: number
          customer_id: string
          is_expired: boolean
          key_id: string
          key_type: string
          rate_limits: Json
          status: string
        }[]
      }
      validate_api_key_auth_secure: {
        Args: { raw_key: string }
        Returns: {
          credits: number
          customer_id: string
          is_expired: boolean
          key_id: string
          key_type: string
          rate_limits: Json
          status: string
        }[]
      }
      validate_api_key_format: {
        Args: { api_key: string }
        Returns: boolean
      }
      verify_api_key_hash: {
        Args: { raw_key: string; stored_hash: string; stored_salt: string }
        Returns: boolean
      }
      verify_cron_setup: {
        Args: Record<PropertyKey, never>
        Returns: {
          check_name: string
          details: string
          status: string
        }[]
      }
    }
    Enums: {
      job_priority: "urgent" | "high" | "normal" | "low" | "background"
      job_status:
        | "queued"
        | "processing"
        | "completed"
        | "failed"
        | "dead_letter"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {
      job_priority: ["urgent", "high", "normal", "low", "background"],
      job_status: [
        "queued",
        "processing",
        "completed",
        "failed",
        "dead_letter",
      ],
    },
  },
} as const

