-- Seed data for testing
-- Create test customers and API keys for validation tests

-- Insert test customers matching test-config.json expectations
INSERT INTO customers (id, customer_id, name, email, tier, credits_available, status, created_at) VALUES
  ('00000000-0000-0000-0000-000000000001', 'TEST-CUSTOMER-001', 'Test Company', '<EMAIL>', 'professional', 10000, 'active', NOW()),
  ('550e8400-e29b-41d4-a716-446655440001', 'TEST-CUSTOMER-002', 'Test Company 2', '<EMAIL>', 'professional', 5000, 'active', NOW()),
  ('550e8400-e29b-41d4-a716-446655440002', 'RLS-TEST-CUSTOMER', 'RLS Test Company', '<EMAIL>', 'starter', 500, 'active', NOW())
ON CONFLICT (id) DO NOTHING;

-- Create test API keys matching test-config.json expectations
-- Test key from config: skt_c30a93caa4f2e88d116253f12d7adff8
INSERT INTO api_keys (
  id, 
  customer_id, 
  key_hash, 
  key_prefix,
  key_type,
  name, 
  credits_allocated,
  credits_used,
  rate_limit_per_minute,
  rate_limit_per_hour,
  is_active,
  created_at
) VALUES
  (
    '450e8400-e29b-41d4-a716-446655440000',
    '00000000-0000-0000-0000-000000000001',
    'eb0dac7482d07b32717d20953cc09604cf4e1b5bc5c8592714d62768c5fa00a9',
    'skt_',
    'test',
    'Test API Key from Config',
    10000,
    0,
    1000,
    10000,
    true,
    NOW()
  ),
  (
    '450e8400-e29b-41d4-a716-446655440004',
    '00000000-0000-0000-0000-000000000001',
    '15298a172d2a57ddc363ccd888b1ce095d2be2ef5f256bf001a3a04befbbc16d',
    'skt_',
    'test',
    'Integration Test API Key',
    10000,
    0,
    1000,
    10000,
    true,
    NOW()
  ),
  (
    '450e8400-e29b-41d4-a716-446655440001',
    '00000000-0000-0000-0000-000000000001',
    encode(digest('skp_production_key_example_12345678', 'sha256'), 'hex'),
    'skp_',
    'production',
    'Prod API Key 1',
    5000,
    0,
    200,
    2000,
    true,
    NOW()
  ),
  (
    '450e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    encode(digest('skt_abcdef1234567890abcdef1234567890', 'sha256'), 'hex'),
    'skt_',
    'test',
    'Test API Key 2',
    2000,
    0,
    100,
    1000,
    true,
    NOW()
  ),
  (
    '450e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440002',
    encode(digest('skt_rls_test123456789012345678901234', 'sha256'), 'hex'),
    'skt_',
    'test',
    'RLS Test API Key',
    500,
    0,
    50,
    500,
    true,
    NOW()
  )
ON CONFLICT (id) DO NOTHING;

-- Insert test custom agents for RLS testing (not default agents)
INSERT INTO agents (
  id,
  customer_id,
  agent_id,
  name,
  description,
  category,
  system_prompt,
  prompt,
  output_schema,
  json_schema,
  processing_config,
  is_default,
  is_customizable,
  status,
  created_at
) VALUES
  (
    '350e8400-e29b-41d4-a716-446655440001',
    '00000000-0000-0000-0000-000000000001',
    'custom_invoice',
    'Custom Invoice Agent',
    'Customer-specific invoice agent',
    'invoice',
    'Extract custom invoice data including vendor, amounts, and line items.',
    'Extract custom invoice data including vendor, amounts, and line items.',
    '{"type": "object", "properties": {"total": {"type": "number"}}}',
    '{"type": "object", "properties": {"total": {"type": "number"}}}',
    '{}',
    false,
    true,
    'active',
    NOW()
  ),
  (
    '350e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440002',
    'rls_test_agent',
    'RLS Test Agent',
    'Agent for RLS testing',
    'contract',
    'Extract contract data including parties and terms.',
    'Extract contract data including parties and terms.',
    '{"type": "object", "properties": {"parties": {"type": "array"}}}',
    '{"type": "object", "properties": {"parties": {"type": "array"}}}',
    '{}',
    false,
    true,
    'active',
    NOW()
  )
ON CONFLICT (id) DO NOTHING;

-- Create some test documents for RLS testing
INSERT INTO documents (
  id,
  customer_id,
  document_hash,
  original_filename,
  file_size,
  mime_type,
  storage_path,
  status,
  retention_expires_at,
  created_at
) VALUES
  (
    '250e8400-e29b-41d4-a716-446655440000',
    '00000000-0000-0000-0000-000000000001',
    'abc123def456',
    'test_invoice_1.pdf',
    45678,
    'application/pdf',
    '/storage/test_invoice_1.pdf',
    'processed',
    NOW() + INTERVAL '7 days',
    NOW()
  ),
  (
    '250e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    'def456ghi789',
    'test_contract_1.pdf',
    67890,
    'application/pdf',
    '/storage/test_contract_1.pdf',
    'processed',
    NOW() + INTERVAL '30 days',
    NOW()
  ),
  (
    '250e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440002',
    'ghi789jkl012',
    'rls_test_doc.pdf',
    12345,
    'application/pdf',
    '/storage/rls_test_doc.pdf',
    'processed',
    NOW() + INTERVAL '7 days',
    NOW()
  )
ON CONFLICT (id) DO NOTHING;

-- Create usage logs for testing
INSERT INTO usage_logs (
  id,
  customer_id,
  api_key_id,
  endpoint,
  method,
  input_tokens,
  output_tokens,
  model_used,
  model_cost_usd,
  customer_price_usd,
  credits_used,
  processing_time_ms,
  status_code,
  success,
  created_at
) VALUES
  (
    '150e8400-e29b-41d4-a716-446655440000',
    '00000000-0000-0000-0000-000000000001',
    '450e8400-e29b-41d4-a716-446655440000',
    '/extract',
    'POST',
    1500,
    800,
    'gpt-4o-mini',
    0.05,
    0.075,
    10,
    3200,
    200,
    true,
    NOW()
  ),
  (
    '150e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    '450e8400-e29b-41d4-a716-446655440002',
    '/extract',
    'POST',
    2000,
    1000,
    'claude-3-haiku',
    0.08,
    0.12,
    15,
    2800,
    200,
    true,
    NOW()
  )
ON CONFLICT (id) DO NOTHING;