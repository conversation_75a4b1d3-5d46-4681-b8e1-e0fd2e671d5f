-- Setup pg_cron for Automatic Queue Processing
-- Issue #12: Queue System for Large Documents (Epic 2, Story 6) - Cron Configuration
-- Date: 2025-09-22
-- Description: Configure pg_cron to automatically process queue jobs every 10 seconds

-- =============================================================================
-- CRON JOB CONFIGURATION
-- =============================================================================

-- Schedule queue processor to run every 10 seconds
-- This provides near real-time processing while preventing overlapping executions
SELECT cron.schedule(
  'process-document-queue',
  '*/10 * * * * *',  -- Every 10 seconds
  'SELECT net.http_post(
    url := current_setting(''app.functions_url'') || ''/queue-processor'',
    headers := jsonb_build_object(
      ''Authorization'', ''Bearer '' || current_setting(''app.service_role_key''),
      ''Content-Type'', ''application/json''
    ),
    body := jsonb_build_object(
      ''action'', ''process_batch'',
      ''batchSize'', 3,
      ''timeout'', 50000
    )
  );'
);

-- Schedule cleanup job to run every hour
-- Cleans up expired jobs, old completed jobs, and archives dead letter jobs
SELECT cron.schedule(
  'cleanup-job-queue',
  '0 * * * *',  -- Every hour at minute 0
  'SELECT public.cleanup_job_queue();'
);

-- Schedule stuck job reset to run every 30 minutes
-- Resets jobs that have been stuck in processing state
SELECT cron.schedule(
  'reset-stuck-jobs',
  '*/30 * * * *',  -- Every 30 minutes
  'SELECT public.reset_stuck_jobs(60);'  -- Reset jobs stuck for more than 60 minutes
);

-- Schedule queue metrics logging every 5 minutes
-- Logs queue health metrics for monitoring
SELECT cron.schedule(
  'log-queue-metrics',
  '*/5 * * * *',  -- Every 5 minutes
  'INSERT INTO audit_logs (action, resource_type, new_values, success, metadata)
   SELECT ''METRICS'', ''job_queue'', to_jsonb(metrics.*), TRUE,
          jsonb_build_object(''source'', ''cron_metrics'', ''timestamp'', NOW())
   FROM public.get_queue_metrics() metrics;'
);

-- =============================================================================
-- CRON JOB MONITORING FUNCTIONS
-- =============================================================================

-- Function to check cron job health
CREATE OR REPLACE FUNCTION public.check_cron_jobs_health()
RETURNS TABLE(
  job_name TEXT,
  last_run TIMESTAMPTZ,
  next_run TIMESTAMPTZ,
  status TEXT,
  healthy BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cj.jobname::TEXT as job_name,
    cj.last_run,
    cj.next_run,
    CASE 
      WHEN cj.active THEN 'active'
      ELSE 'inactive'
    END as status,
    CASE 
      WHEN cj.active AND cj.last_run > NOW() - INTERVAL '1 hour' THEN TRUE
      WHEN cj.active AND cj.jobname = 'process-document-queue' AND cj.last_run > NOW() - INTERVAL '2 minutes' THEN TRUE
      ELSE FALSE
    END as healthy
  FROM cron.job cj
  WHERE cj.jobname IN (
    'process-document-queue',
    'cleanup-job-queue', 
    'reset-stuck-jobs',
    'log-queue-metrics'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.check_cron_jobs_health IS 'Check health status of queue-related cron jobs';

-- Function to get cron job statistics
CREATE OR REPLACE FUNCTION public.get_cron_job_stats()
RETURNS TABLE(
  job_name TEXT,
  total_runs BIGINT,
  successful_runs BIGINT,
  failed_runs BIGINT,
  success_rate NUMERIC,
  avg_duration_ms NUMERIC,
  last_error TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    cjr.jobname::TEXT as job_name,
    COUNT(*) as total_runs,
    COUNT(*) FILTER (WHERE cjr.return_message IS NULL OR cjr.return_message = '') as successful_runs,
    COUNT(*) FILTER (WHERE cjr.return_message IS NOT NULL AND cjr.return_message != '') as failed_runs,
    ROUND(
      (COUNT(*) FILTER (WHERE cjr.return_message IS NULL OR cjr.return_message = '') * 100.0) / 
      NULLIF(COUNT(*), 0), 
      2
    ) as success_rate,
    ROUND(AVG(EXTRACT(EPOCH FROM (cjr.end_time - cjr.start_time)) * 1000), 2) as avg_duration_ms,
    (
      SELECT cjr2.return_message 
      FROM cron.job_run_details cjr2 
      WHERE cjr2.jobname = cjr.jobname 
        AND cjr2.return_message IS NOT NULL 
        AND cjr2.return_message != ''
      ORDER BY cjr2.start_time DESC 
      LIMIT 1
    ) as last_error
  FROM cron.job_run_details cjr
  WHERE cjr.jobname IN (
    'process-document-queue',
    'cleanup-job-queue',
    'reset-stuck-jobs',
    'log-queue-metrics'
  )
  AND cjr.start_time >= NOW() - INTERVAL '24 hours'  -- Last 24 hours
  GROUP BY cjr.jobname;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.get_cron_job_stats IS 'Get performance statistics for queue-related cron jobs';

-- =============================================================================
-- CRON JOB MANAGEMENT FUNCTIONS
-- =============================================================================

-- Function to pause queue processing (for maintenance)
CREATE OR REPLACE FUNCTION public.pause_queue_processing()
RETURNS BOOLEAN AS $$
BEGIN
  -- Unschedule the main processing job
  PERFORM cron.unschedule('process-document-queue');
  
  -- Log the pause action
  INSERT INTO audit_logs (
    action,
    resource_type,
    metadata,
    success
  ) VALUES (
    'PAUSE_QUEUE_PROCESSING',
    'system',
    jsonb_build_object(
      'reason', 'Manual pause',
      'timestamp', NOW(),
      'user', current_user
    ),
    TRUE
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.pause_queue_processing IS 'Pause automatic queue processing for maintenance';

-- Function to resume queue processing
CREATE OR REPLACE FUNCTION public.resume_queue_processing()
RETURNS BOOLEAN AS $$
BEGIN
  -- Re-schedule the main processing job
  PERFORM cron.schedule(
    'process-document-queue',
    '*/10 * * * * *',
    'SELECT net.http_post(
      url := current_setting(''app.functions_url'') || ''/queue-processor'',
      headers := jsonb_build_object(
        ''Authorization'', ''Bearer '' || current_setting(''app.service_role_key''),
        ''Content-Type'', ''application/json''
      ),
      body := jsonb_build_object(
        ''action'', ''process_batch'',
        ''batchSize'', 3,
        ''timeout'', 50000
      )
    );'
  );
  
  -- Log the resume action
  INSERT INTO audit_logs (
    action,
    resource_type,
    metadata,
    success
  ) VALUES (
    'RESUME_QUEUE_PROCESSING',
    'system',
    jsonb_build_object(
      'reason', 'Manual resume',
      'timestamp', NOW(),
      'user', current_user
    ),
    TRUE
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.resume_queue_processing IS 'Resume automatic queue processing after maintenance';

-- =============================================================================
-- ENVIRONMENT CONFIGURATION SETUP
-- =============================================================================

-- Set default configuration values for cron jobs
-- These should be updated with actual values during deployment

-- Default to local development URLs
-- In production, these will be set via environment variables
DO $$
BEGIN
  -- Set functions URL for local development
  IF current_setting('app.functions_url', true) IS NULL THEN
    PERFORM set_config('app.functions_url', 'http://localhost:54321/functions/v1', false);
  END IF;
  
  -- Set service role key placeholder
  -- This should be set to actual key during deployment
  IF current_setting('app.service_role_key', true) IS NULL THEN
    PERFORM set_config('app.service_role_key', 'REPLACE_WITH_ACTUAL_SERVICE_ROLE_KEY', false);
  END IF;
END $$;

-- =============================================================================
-- INITIAL SETUP VERIFICATION
-- =============================================================================

-- Function to verify cron setup
CREATE OR REPLACE FUNCTION public.verify_cron_setup()
RETURNS TABLE(
  check_name TEXT,
  status TEXT,
  details TEXT
) AS $$
BEGIN
  -- Check if pg_cron extension is available
  RETURN QUERY
  SELECT 
    'pg_cron_extension'::TEXT,
    CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_cron') 
      THEN 'OK' 
      ELSE 'MISSING' 
    END,
    'pg_cron extension must be installed for queue processing'::TEXT;
  
  -- Check if pg_net extension is available
  RETURN QUERY
  SELECT 
    'pg_net_extension'::TEXT,
    CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pg_net') 
      THEN 'OK' 
      ELSE 'MISSING' 
    END,
    'pg_net extension must be installed for HTTP requests'::TEXT;
  
  -- Check if cron jobs are scheduled
  RETURN QUERY
  SELECT 
    'scheduled_jobs'::TEXT,
    CASE WHEN EXISTS (
      SELECT 1 FROM cron.job 
      WHERE jobname IN ('process-document-queue', 'cleanup-job-queue', 'reset-stuck-jobs', 'log-queue-metrics')
    ) THEN 'OK' ELSE 'MISSING' END,
    'Queue processing cron jobs should be scheduled'::TEXT;
  
  -- Check configuration settings
  RETURN QUERY
  SELECT 
    'functions_url_config'::TEXT,
    CASE WHEN current_setting('app.functions_url', true) IS NOT NULL 
      THEN 'OK' 
      ELSE 'MISSING' 
    END,
    'Functions URL must be configured for HTTP requests'::TEXT;
  
  RETURN QUERY
  SELECT 
    'service_role_key_config'::TEXT,
    CASE WHEN current_setting('app.service_role_key', true) IS NOT NULL 
         AND current_setting('app.service_role_key', true) != 'REPLACE_WITH_ACTUAL_SERVICE_ROLE_KEY'
      THEN 'OK' 
      ELSE 'NEEDS_UPDATE' 
    END,
    'Service role key must be configured for authenticated HTTP requests'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.verify_cron_setup IS 'Verify that cron setup is correct and complete';

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant necessary permissions for cron job management
GRANT EXECUTE ON FUNCTION public.check_cron_jobs_health() TO service_role;
GRANT EXECUTE ON FUNCTION public.get_cron_job_stats() TO service_role;
GRANT EXECUTE ON FUNCTION public.pause_queue_processing() TO service_role;
GRANT EXECUTE ON FUNCTION public.resume_queue_processing() TO service_role;
GRANT EXECUTE ON FUNCTION public.verify_cron_setup() TO service_role;

-- Allow authenticated users to check cron health (read-only)
GRANT EXECUTE ON FUNCTION public.check_cron_jobs_health() TO authenticated;
GRANT EXECUTE ON FUNCTION public.verify_cron_setup() TO authenticated;

-- =============================================================================
-- INITIAL VERIFICATION
-- =============================================================================

-- Run initial verification to check setup
SELECT * FROM public.verify_cron_setup();