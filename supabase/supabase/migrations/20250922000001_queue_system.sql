-- Queue System for Large Documents
-- Issue #12: Queue System for Large Documents (Epic 2, Story 6)
-- Date: 2025-09-22
-- Description: PostgreSQL-based queue system with pg_cron for processing large documents (>10MB) and complex files

-- =============================================================================
-- ENABLE REQUIRED EXTENSIONS
-- =============================================================================

-- Enable pg_cron extension for scheduled jobs
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Enable pg_net for HTTP requests from database
CREATE EXTENSION IF NOT EXISTS pg_net;

-- =============================================================================
-- CREATE CUSTOM TYPES
-- =============================================================================

-- Job status enum type
CREATE TYPE job_status AS ENUM (
  'queued',       -- Job is waiting to be processed
  'processing',   -- Job is currently being processed
  'completed',    -- Job completed successfully
  'failed',       -- Job failed but can be retried
  'dead_letter'   -- Job failed permanently after max retries
);

COMMENT ON TYPE job_status IS 'Status values for queue job lifecycle';

-- Job priority enum type
CREATE TYPE job_priority AS ENUM (
  'urgent',       -- 1: Critical priority (enterprise customers)
  'high',         -- 2: High priority (premium customers)  
  'normal',       -- 3: Normal priority (standard customers)
  'low',          -- 4: Low priority (free tier)
  'background'    -- 5: Background/cleanup jobs
);

COMMENT ON TYPE job_priority IS 'Priority levels for queue job processing';

-- =============================================================================
-- JOB QUEUE TABLE
-- =============================================================================

CREATE TABLE job_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id UUID NOT NULL REFERENCES api_keys(id) ON DELETE CASCADE,
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
  
  -- Job metadata
  job_type TEXT NOT NULL DEFAULT 'document_processing' CHECK (job_type IN ('document_processing', 'agent_management', 'data_export', 'cleanup')),
  priority job_priority NOT NULL DEFAULT 'normal',
  status job_status NOT NULL DEFAULT 'queued',
  
  -- Job configuration
  job_data JSONB NOT NULL DEFAULT '{}', -- Job-specific parameters
  webhook_url TEXT, -- Optional webhook for completion notification
  max_retries INTEGER NOT NULL DEFAULT 3,
  retry_delay_base INTEGER NOT NULL DEFAULT 60, -- Base delay in seconds (exponential backoff)
  
  -- Processing tracking
  retry_count INTEGER NOT NULL DEFAULT 0,
  processing_node TEXT, -- Which processor instance is handling this job
  estimated_duration INTEGER, -- Estimated processing time in seconds
  actual_duration INTEGER, -- Actual processing time in seconds
  
  -- Results and errors
  result JSONB, -- Job result data
  error_message TEXT,
  error_code TEXT,
  stack_trace TEXT,
  
  -- Timestamps
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  scheduled_for TIMESTAMPTZ NOT NULL DEFAULT NOW(), -- When job should be processed
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ, -- When job expires and should be cleaned up
  
  -- Correlation tracking
  correlation_id TEXT NOT NULL DEFAULT gen_random_uuid()::TEXT,
  parent_job_id UUID REFERENCES job_queue(id), -- For job dependencies
  
  -- Constraints
  CONSTRAINT job_queue_retry_count_valid CHECK (retry_count >= 0 AND retry_count <= max_retries),
  CONSTRAINT job_queue_processing_times_valid CHECK (
    (started_at IS NULL OR started_at >= created_at) AND
    (completed_at IS NULL OR (started_at IS NOT NULL AND completed_at >= started_at))
  ),
  CONSTRAINT job_queue_status_timestamps CHECK (
    CASE status
      WHEN 'queued' THEN started_at IS NULL AND completed_at IS NULL
      WHEN 'processing' THEN started_at IS NOT NULL AND completed_at IS NULL
      WHEN 'completed' THEN started_at IS NOT NULL AND completed_at IS NOT NULL AND result IS NOT NULL
      WHEN 'failed' THEN error_message IS NOT NULL
      WHEN 'dead_letter' THEN error_message IS NOT NULL AND retry_count >= max_retries
      ELSE TRUE
    END
  )
);

-- Comprehensive table documentation
COMMENT ON TABLE job_queue IS 'Asynchronous job queue for large document processing with retry logic and prioritization';
COMMENT ON COLUMN job_queue.customer_id IS 'Customer who submitted this job';
COMMENT ON COLUMN job_queue.api_key_id IS 'API key used to submit this job';
COMMENT ON COLUMN job_queue.document_id IS 'Document to be processed';
COMMENT ON COLUMN job_queue.agent_id IS 'Agent to use for processing (null for default)';
COMMENT ON COLUMN job_queue.job_type IS 'Type of job: document_processing, agent_management, data_export, cleanup';
COMMENT ON COLUMN job_queue.priority IS 'Job priority based on customer tier and urgency';
COMMENT ON COLUMN job_queue.status IS 'Current job status in the processing lifecycle';
COMMENT ON COLUMN job_queue.job_data IS 'Job-specific configuration and parameters';
COMMENT ON COLUMN job_queue.webhook_url IS 'Optional webhook URL for job completion notification';
COMMENT ON COLUMN job_queue.max_retries IS 'Maximum number of retry attempts';
COMMENT ON COLUMN job_queue.retry_delay_base IS 'Base delay in seconds for exponential backoff';
COMMENT ON COLUMN job_queue.retry_count IS 'Number of retry attempts so far';
COMMENT ON COLUMN job_queue.processing_node IS 'Identifier of the processing instance handling this job';
COMMENT ON COLUMN job_queue.estimated_duration IS 'Estimated processing time in seconds';
COMMENT ON COLUMN job_queue.actual_duration IS 'Actual processing time in seconds';
COMMENT ON COLUMN job_queue.result IS 'Job result data (extraction results, etc.)';
COMMENT ON COLUMN job_queue.error_message IS 'Human-readable error message for failures';
COMMENT ON COLUMN job_queue.error_code IS 'Machine-readable error code for failures';
COMMENT ON COLUMN job_queue.stack_trace IS 'Full stack trace for debugging failures';
COMMENT ON COLUMN job_queue.scheduled_for IS 'When job should be processed (for delayed jobs)';
COMMENT ON COLUMN job_queue.expires_at IS 'When job expires and should be cleaned up';
COMMENT ON COLUMN job_queue.correlation_id IS 'Unique identifier for request tracing';
COMMENT ON COLUMN job_queue.parent_job_id IS 'Reference to parent job for job dependencies';

-- =============================================================================
-- PERFORMANCE INDEXES
-- =============================================================================

-- Primary queue processing index (CRITICAL: <50ms job selection)
CREATE INDEX idx_job_queue_processing ON job_queue(status, priority, scheduled_for, created_at)
  WHERE status = 'queued';

-- Customer job tracking index
CREATE INDEX idx_job_queue_customer ON job_queue(customer_id, created_at DESC);

-- Correlation tracking index
CREATE INDEX idx_job_queue_correlation ON job_queue(correlation_id);

-- Cleanup and maintenance indexes
CREATE INDEX idx_job_queue_expires_at ON job_queue(expires_at)
  WHERE expires_at IS NOT NULL;

-- Monitoring and metrics indexes
CREATE INDEX idx_job_queue_status_created ON job_queue(status, created_at);
CREATE INDEX idx_job_queue_retry_tracking ON job_queue(status, retry_count)
  WHERE status IN ('failed', 'dead_letter');

-- Performance monitoring index
CREATE INDEX idx_job_queue_duration_tracking ON job_queue(job_type, status, actual_duration)
  WHERE status = 'completed' AND actual_duration IS NOT NULL;

-- Parent-child job relationship index
CREATE INDEX idx_job_queue_parent_child ON job_queue(parent_job_id)
  WHERE parent_job_id IS NOT NULL;

-- =============================================================================
-- QUEUE MANAGEMENT FUNCTIONS
-- =============================================================================

-- Function to calculate priority order value (lower = higher priority)
CREATE OR REPLACE FUNCTION public.calculate_priority_order(priority job_priority)
RETURNS INTEGER AS $$
BEGIN
  CASE priority
    WHEN 'urgent' THEN RETURN 1;
    WHEN 'high' THEN RETURN 2;
    WHEN 'normal' THEN RETURN 3;
    WHEN 'low' THEN RETURN 4;
    WHEN 'background' THEN RETURN 5;
    ELSE RETURN 3; -- Default to normal
  END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION public.calculate_priority_order IS 'Convert priority enum to numeric order for sorting';

-- Function to determine job priority based on customer tier
CREATE OR REPLACE FUNCTION public.get_customer_job_priority(customer_tier TEXT)
RETURNS job_priority AS $$
BEGIN
  CASE customer_tier
    WHEN 'enterprise' THEN RETURN 'high'::job_priority;
    WHEN 'premium' THEN RETURN 'normal'::job_priority;
    WHEN 'standard' THEN RETURN 'normal'::job_priority;
    ELSE RETURN 'low'::job_priority;
  END CASE;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION public.get_customer_job_priority IS 'Determine job priority based on customer tier';

-- Function to calculate exponential backoff delay
CREATE OR REPLACE FUNCTION public.calculate_retry_delay(base_delay INTEGER, retry_count INTEGER)
RETURNS INTEGER AS $$
BEGIN
  -- Exponential backoff: base_delay * (2 ^ retry_count)
  -- Cap at 1 hour (3600 seconds)
  RETURN LEAST(base_delay * (2 ^ retry_count), 3600);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

COMMENT ON FUNCTION public.calculate_retry_delay IS 'Calculate exponential backoff delay for job retries';

-- Function to enqueue a new job
CREATE OR REPLACE FUNCTION public.enqueue_job(
  p_customer_id UUID,
  p_api_key_id UUID,
  p_document_id UUID,
  p_agent_id UUID DEFAULT NULL,
  p_job_type TEXT DEFAULT 'document_processing',
  p_job_data JSONB DEFAULT '{}',
  p_webhook_url TEXT DEFAULT NULL,
  p_max_retries INTEGER DEFAULT 3,
  p_priority job_priority DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  v_job_id UUID;
  v_customer_tier TEXT;
  v_calculated_priority job_priority;
  v_expires_at TIMESTAMPTZ;
BEGIN
  -- Get customer tier for priority calculation
  SELECT tier INTO v_customer_tier
  FROM customers
  WHERE id = p_customer_id;

  -- Calculate priority if not provided
  v_calculated_priority := COALESCE(p_priority, public.get_customer_job_priority(v_customer_tier));

  -- Set expiration (jobs expire after 24 hours by default)
  v_expires_at := NOW() + INTERVAL '24 hours';

  -- Insert job into queue
  INSERT INTO job_queue (
    customer_id,
    api_key_id,
    document_id,
    agent_id,
    job_type,
    priority,
    job_data,
    webhook_url,
    max_retries,
    expires_at
  ) VALUES (
    p_customer_id,
    p_api_key_id,
    p_document_id,
    p_agent_id,
    p_job_type,
    v_calculated_priority,
    p_job_data,
    p_webhook_url,
    p_max_retries,
    v_expires_at
  ) RETURNING id INTO v_job_id;

  -- Log job creation
  INSERT INTO audit_logs (
    customer_id,
    api_key_id,
    action,
    resource_type,
    resource_id,
    new_values,
    success,
    metadata
  ) VALUES (
    p_customer_id,
    p_api_key_id,
    'CREATE',
    'job_queue',
    v_job_id,
    jsonb_build_object(
      'job_type', p_job_type,
      'priority', v_calculated_priority,
      'document_id', p_document_id
    ),
    TRUE,
    jsonb_build_object(
      'function', 'enqueue_job',
      'timestamp', NOW()
    )
  );

  RETURN v_job_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.enqueue_job IS 'Add a new job to the processing queue with automatic priority calculation';

-- Function to get next job for processing
CREATE OR REPLACE FUNCTION public.get_next_job(processing_node_id TEXT)
RETURNS TABLE(
  job_id UUID,
  customer_id UUID,
  document_id UUID,
  agent_id UUID,
  job_type TEXT,
  job_data JSONB,
  priority job_priority,
  retry_count INTEGER,
  correlation_id TEXT
) AS $$
DECLARE
  v_job_id UUID;
BEGIN
  -- Get the highest priority job that's ready to process
  SELECT jq.id INTO v_job_id
  FROM job_queue jq
  WHERE jq.status = 'queued'
    AND jq.scheduled_for <= NOW()
    AND (jq.expires_at IS NULL OR jq.expires_at > NOW())
  ORDER BY 
    public.calculate_priority_order(jq.priority),
    jq.scheduled_for,
    jq.created_at
  LIMIT 1
  FOR UPDATE SKIP LOCKED; -- Prevent concurrent processing

  -- If no job found, return empty result
  IF v_job_id IS NULL THEN
    RETURN;
  END IF;

  -- Mark job as processing
  UPDATE job_queue
  SET 
    status = 'processing',
    started_at = NOW(),
    processing_node = processing_node_id
  WHERE id = v_job_id;

  -- Return job details
  RETURN QUERY
  SELECT 
    jq.id,
    jq.customer_id,
    jq.document_id,
    jq.agent_id,
    jq.job_type,
    jq.job_data,
    jq.priority,
    jq.retry_count,
    jq.correlation_id
  FROM job_queue jq
  WHERE jq.id = v_job_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.get_next_job IS 'Get the next highest priority job for processing with locking';

-- Function to complete a job successfully
CREATE OR REPLACE FUNCTION public.complete_job(
  p_job_id UUID,
  p_result JSONB,
  p_processing_node TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
  v_started_at TIMESTAMPTZ;
  v_duration INTEGER;
BEGIN
  -- Get job start time for duration calculation
  SELECT started_at INTO v_started_at
  FROM job_queue
  WHERE id = p_job_id AND processing_node = p_processing_node;

  -- Calculate actual processing duration
  v_duration := EXTRACT(EPOCH FROM (NOW() - v_started_at))::INTEGER;

  -- Update job as completed
  UPDATE job_queue
  SET 
    status = 'completed',
    completed_at = NOW(),
    result = p_result,
    actual_duration = v_duration,
    error_message = NULL,
    error_code = NULL
  WHERE id = p_job_id 
    AND processing_node = p_processing_node
    AND status = 'processing';

  -- Return whether update was successful
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.complete_job IS 'Mark a job as successfully completed with results';

-- Function to fail a job with retry logic
CREATE OR REPLACE FUNCTION public.fail_job(
  p_job_id UUID,
  p_error_message TEXT,
  p_error_code TEXT DEFAULT NULL,
  p_stack_trace TEXT DEFAULT NULL,
  p_processing_node TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  v_job_record RECORD;
  v_new_retry_count INTEGER;
  v_retry_delay INTEGER;
  v_new_status job_status;
  v_scheduled_for TIMESTAMPTZ;
BEGIN
  -- Get current job details
  SELECT 
    retry_count,
    max_retries,
    retry_delay_base,
    processing_node
  INTO v_job_record
  FROM job_queue
  WHERE id = p_job_id
    AND (p_processing_node IS NULL OR processing_node = p_processing_node)
    AND status = 'processing';

  -- Return false if job not found or not in processing state
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;

  -- Calculate new retry count
  v_new_retry_count := v_job_record.retry_count + 1;

  -- Determine if job should be retried or moved to dead letter
  IF v_new_retry_count >= v_job_record.max_retries THEN
    v_new_status := 'dead_letter';
    v_scheduled_for := NULL;
  ELSE
    v_new_status := 'failed';
    v_retry_delay := public.calculate_retry_delay(v_job_record.retry_delay_base, v_new_retry_count);
    v_scheduled_for := NOW() + (v_retry_delay || ' seconds')::INTERVAL;
  END IF;

  -- Update job with failure information
  UPDATE job_queue
  SET 
    status = v_new_status,
    retry_count = v_new_retry_count,
    error_message = p_error_message,
    error_code = p_error_code,
    stack_trace = p_stack_trace,
    scheduled_for = v_scheduled_for,
    completed_at = CASE WHEN v_new_status = 'dead_letter' THEN NOW() ELSE NULL END,
    processing_node = NULL -- Release the job from this node
  WHERE id = p_job_id;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.fail_job IS 'Mark a job as failed with automatic retry scheduling or dead letter handling';

-- Function to get queue metrics
CREATE OR REPLACE FUNCTION public.get_queue_metrics()
RETURNS TABLE(
  total_queued BIGINT,
  total_processing BIGINT,
  total_failed BIGINT,
  total_dead_letter BIGINT,
  urgent_queued BIGINT,
  high_queued BIGINT,
  normal_queued BIGINT,
  low_queued BIGINT,
  avg_processing_time_seconds NUMERIC,
  oldest_queued_age_minutes NUMERIC
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) FILTER (WHERE status = 'queued') AS total_queued,
    COUNT(*) FILTER (WHERE status = 'processing') AS total_processing,
    COUNT(*) FILTER (WHERE status = 'failed') AS total_failed,
    COUNT(*) FILTER (WHERE status = 'dead_letter') AS total_dead_letter,
    COUNT(*) FILTER (WHERE status = 'queued' AND priority = 'urgent') AS urgent_queued,
    COUNT(*) FILTER (WHERE status = 'queued' AND priority = 'high') AS high_queued,
    COUNT(*) FILTER (WHERE status = 'queued' AND priority = 'normal') AS normal_queued,
    COUNT(*) FILTER (WHERE status = 'queued' AND priority = 'low') AS low_queued,
    AVG(actual_duration) FILTER (WHERE status = 'completed' AND actual_duration IS NOT NULL) AS avg_processing_time_seconds,
    EXTRACT(EPOCH FROM (NOW() - MIN(created_at) FILTER (WHERE status = 'queued'))) / 60 AS oldest_queued_age_minutes
  FROM job_queue
  WHERE created_at >= NOW() - INTERVAL '24 hours'; -- Focus on recent jobs
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.get_queue_metrics IS 'Get comprehensive queue health and performance metrics';

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC QUEUE MANAGEMENT
-- =============================================================================

-- Trigger to automatically set job expiration and initial scheduling
CREATE OR REPLACE FUNCTION public.set_job_defaults()
RETURNS TRIGGER AS $$
BEGIN
  -- Set correlation_id if not provided
  IF NEW.correlation_id IS NULL OR NEW.correlation_id = '' THEN
    NEW.correlation_id := gen_random_uuid()::TEXT;
  END IF;

  -- Set expires_at if not provided (24 hours default)
  IF NEW.expires_at IS NULL THEN
    NEW.expires_at := NEW.created_at + INTERVAL '24 hours';
  END IF;

  -- Set scheduled_for if not provided (immediate processing)
  IF NEW.scheduled_for IS NULL THEN
    NEW.scheduled_for := NEW.created_at;
  END IF;

  -- Ensure processing timestamps are consistent
  IF NEW.status = 'processing' AND NEW.started_at IS NULL THEN
    NEW.started_at := NOW();
  END IF;

  IF NEW.status = 'completed' AND NEW.completed_at IS NULL THEN
    NEW.completed_at := NOW();
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER job_queue_set_defaults
  BEFORE INSERT OR UPDATE ON job_queue
  FOR EACH ROW
  EXECUTE FUNCTION public.set_job_defaults();

-- Trigger to update document status when job status changes
CREATE OR REPLACE FUNCTION public.sync_job_document_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update for document processing jobs
  IF NEW.job_type != 'document_processing' THEN
    RETURN NEW;
  END IF;

  -- Update document status based on job status
  CASE NEW.status
    WHEN 'processing' THEN
      UPDATE documents 
      SET status = 'processing', processing_started_at = NOW()
      WHERE id = NEW.document_id;
      
    WHEN 'completed' THEN
      UPDATE documents 
      SET 
        status = 'completed',
        processing_completed_at = NOW(),
        extraction_result = NEW.result,
        model_used = COALESCE((NEW.result->>'model')::TEXT, 'queue_processor')
      WHERE id = NEW.document_id;
      
    WHEN 'failed', 'dead_letter' THEN
      UPDATE documents 
      SET 
        status = 'failed',
        processing_completed_at = NOW(),
        error_message = NEW.error_message,
        retry_count = NEW.retry_count
      WHERE id = NEW.document_id;
  END CASE;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER job_queue_sync_document_status
  AFTER UPDATE ON job_queue
  FOR EACH ROW
  WHEN (OLD.status IS DISTINCT FROM NEW.status)
  EXECUTE FUNCTION public.sync_job_document_status();

-- =============================================================================
-- CLEANUP AND MAINTENANCE FUNCTIONS
-- =============================================================================

-- Function to clean up expired and old completed jobs
CREATE OR REPLACE FUNCTION public.cleanup_job_queue()
RETURNS TABLE(
  expired_jobs_deleted BIGINT,
  old_completed_deleted BIGINT,
  dead_letter_archived BIGINT
) AS $$
DECLARE
  v_expired_count BIGINT;
  v_completed_count BIGINT;
  v_dead_letter_count BIGINT;
BEGIN
  -- Delete expired jobs
  DELETE FROM job_queue
  WHERE expires_at <= NOW()
    AND status IN ('queued', 'failed');
  
  GET DIAGNOSTICS v_expired_count = ROW_COUNT;

  -- Delete old completed jobs (older than 7 days)
  DELETE FROM job_queue
  WHERE status = 'completed'
    AND completed_at <= NOW() - INTERVAL '7 days';
  
  GET DIAGNOSTICS v_completed_count = ROW_COUNT;

  -- Archive old dead letter jobs (older than 30 days)
  DELETE FROM job_queue
  WHERE status = 'dead_letter'
    AND completed_at <= NOW() - INTERVAL '30 days';
  
  GET DIAGNOSTICS v_dead_letter_count = ROW_COUNT;

  RETURN QUERY SELECT v_expired_count, v_completed_count, v_dead_letter_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.cleanup_job_queue IS 'Clean up expired and old jobs from the queue';

-- Function to reset stuck jobs (processing for too long)
CREATE OR REPLACE FUNCTION public.reset_stuck_jobs(stuck_threshold_minutes INTEGER DEFAULT 60)
RETURNS BIGINT AS $$
DECLARE
  v_reset_count BIGINT;
BEGIN
  -- Reset jobs that have been processing for too long
  UPDATE job_queue
  SET 
    status = 'failed',
    error_message = 'Job timeout - stuck in processing state for ' || stuck_threshold_minutes || ' minutes',
    error_code = 'TIMEOUT',
    processing_node = NULL,
    retry_count = retry_count + 1,
    scheduled_for = CASE 
      WHEN retry_count + 1 >= max_retries THEN NULL
      ELSE NOW() + (public.calculate_retry_delay(retry_delay_base, retry_count + 1) || ' seconds')::INTERVAL
    END
  WHERE status = 'processing'
    AND started_at <= NOW() - (stuck_threshold_minutes || ' minutes')::INTERVAL;

  GET DIAGNOSTICS v_reset_count = ROW_COUNT;

  -- Update status to dead_letter for jobs that exceeded max retries
  UPDATE job_queue
  SET 
    status = 'dead_letter',
    completed_at = NOW()
  WHERE status = 'failed'
    AND retry_count >= max_retries
    AND error_code = 'TIMEOUT';

  RETURN v_reset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.reset_stuck_jobs IS 'Reset jobs that have been stuck in processing state';

-- =============================================================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================================================

-- Enable RLS on job_queue table
ALTER TABLE job_queue ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for customer isolation
CREATE POLICY "job_queue_customer_isolation" ON job_queue
  FOR ALL USING (customer_id = public.current_customer_id());

-- =============================================================================
-- GRANT PERMISSIONS
-- =============================================================================

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON job_queue TO authenticated, service_role;
GRANT USAGE ON TYPE job_status TO authenticated, service_role;
GRANT USAGE ON TYPE job_priority TO authenticated, service_role;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated, service_role;

-- Grant pg_cron permissions to service role only
GRANT USAGE ON SCHEMA cron TO service_role;
GRANT ALL ON ALL TABLES IN SCHEMA cron TO service_role;