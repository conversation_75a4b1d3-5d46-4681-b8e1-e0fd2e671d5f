# Source Tree & Project Structure Guide

## 📁 Project Overview

The IDP Platform is an **API-first document processing service** built on Supabase that transforms unstructured documents into structured JSON data. Per the PRD, this platform delivers cost-effective document processing (reducing costs from $0.10-$1.00 to competitive pricing) with 99.5% uptime through multi-model AI fallbacks and customizable extraction agents. This guide maps every significant file and directory, explaining their purpose, relationships, and importance.

**Project Root**: `/Users/<USER>/Developer/GPT/IDP-Platform`

---

## 🏗️ Directory Structure Overview

```
idp-platform/
├── 📦 supabase/              # Supabase backend configuration
├── 📚 docs/                  # Project documentation
├── 🔧 types/                 # TypeScript type definitions
├── 🧪 tests/                 # Test suites (unit, integration, manual)
├── 📝 Configuration Files    # Root config files (.env, package.json, tsconfig.json)
└── 🔐 Security Files        # .gitignore, environment templates
```

---

## 📦 Core Application Structure

### `/supabase` - Backend Infrastructure

```
supabase/
├── config.toml                 # Supabase local development config
├── functions/                  # Edge Functions (Deno)
│   ├── health/                # Health check endpoint
│   │   └── index.ts          # Simple health check for monitoring
│   ├── validate-api-key/     # API key validation
│   │   └── index.ts          # Customer authentication
│   ├── extract/              # Document extraction
│   │   └── index.ts          # Main document processing pipeline
│   ├── agents/               # Agent management
│   │   └── index.ts          # List/clone/update agents
│   └── deno.json            # Deno configuration for functions
└── migrations/               # Database schema migrations
    └── (to be created)       # Initial schema for API platform
```

#### **config.toml**
- **Purpose**: Configures local Supabase development environment
- **Key Settings**:
  - API port: `14321`
  - Database port: `14322`
  - Studio port: `14323`
  - Deno version: `1.43.0` (Edge Runtime compatible)
  - Extensions: `pgcrypto` (for API key hashing)

#### **functions/extract/index.ts**
- **Purpose**: Main document processing API endpoint
- **Features**:
  - Multi-model AI fallback (OpenAI → Claude → LlamaParse)
  - API key authentication
  - File upload validation
  - Cost tracking and credit management
  - CORS handling for API access
- **Environment Variables**: `OPENAI_API_KEY`, `CLAUDE_API_KEY`, `LLAMAPARSE_API_KEY`

#### **functions/validate-api-key/index.ts**
- **Purpose**: API key validation and customer identification
- **Features**:
  - SHA-256 key hashing
  - Rate limiting enforcement
  - Credit balance checking
  - Audit logging

#### **migrations/**
- **Purpose**: PostgreSQL database schema versioning
- **Core Tables** (Per PRD Requirements):
  1. `customers` - Customer accounts with tier management (Epic 1 & 4)
  2. `api_keys` - SHA-256 hashed keys with dual credit system (skt_/skp_) (Epic 1)
  3. `agents` - Versioned default agents + customer clones (Epic 3)
  4. `documents` - Processed documents with retention policies (Epic 2)
  5. `usage_logs` - Dual metrics tracking (cost/price) (Epic 2 & 4)
  6. `audit_logs` - Comprehensive security event logging (Epic 5)

---

### `/docs` - Documentation Hub

```
docs/
├── architecture/
│   ├── coding-standards.md    # Development best practices
│   ├── tech-stack.md          # Technology deep dive
│   └── source-tree.md         # This file
├── product-brief.md           # High-level product vision and requirements
└── stories/                   # Feature development documentation
    └── (to be created)        # User stories and sprint planning
```

#### **Key Documentation Files**:

- **coding-standards.md**: API-first development patterns, security standards, testing approach
- **tech-stack.md**: Deep dive into Supabase, Edge Functions, AI integration
- **product-brief.md**: Complete product vision, user journeys, and MVP scope

---

### `/types` - TypeScript Definitions

```
types/
└── database.types.ts          # Auto-generated from DB schema
```

- **Generated by**: `npm run gen:types`
- **Contains**: Complete TypeScript interfaces for all database tables
- **Usage**: Import for type-safe database operations

---

### `/tests` - API Testing Suite

```
tests/
├── unit/                    # Unit tests (Bun)
│   └── api-validation.test.ts    # API key validation logic
├── integration/            # Integration tests
│   ├── manual/            # Manual API test scripts (Node.js)
│   │   ├── test-auth.js           # API key authentication
│   │   ├── test-api.js            # API endpoint testing
│   │   ├── test-database.js       # Database operations
│   │   └── test-integration.js    # End-to-end workflows
│   └── *.test.ts         # Automated integration tests
├── performance/           # Performance benchmarks
│   └── api-load.bench.ts  # API load testing
├── setup.ts              # Test utilities
├── run-manual-tests.js   # Interactive test runner
└── README.md             # Test documentation
```

**Test Organization**:
- **Unit tests**: Isolated component testing with Bun
- **Integration tests**: End-to-end API workflow validation
- **Manual tests**: Interactive API testing and debugging
- **Performance tests**: API load and throughput testing

**Running Tests**:
```bash
npm test                         # Quick test suite
npm run test:all                # Complete test suite
npm run test:manual             # Interactive test runner
npm run test:manual:auth        # API authentication tests
npm run test:manual:api         # API endpoint tests
npm run test:manual:database    # Database operation tests
npm run test:manual:integration # End-to-end workflow tests
```

### `/types` - TypeScript Definitions

```
types/
└── database.types.ts          # Auto-generated from DB schema
```

- **Generated by**: `npm run db:types`
- **Contains**: Complete TypeScript interfaces for all database tables
- **Usage**: Import for type-safe API operations and Edge Functions

---

## 🤖 BMAD Framework Structure

### `/.bmad-core` - Business Modeling & Development

```
.bmad-core/
├── core-config.yaml          # BMAD configuration
├── agents/                   # AI agent personas
│   ├── architect.md         # Winston - System Architect
│   ├── dev.md              # James - Developer
│   ├── pm.md               # Project Manager
│   ├── po.md               # Product Owner
│   ├── qa.md               # Quality Assurance
│   └── ...
├── tasks/                   # Executable workflows
│   ├── create-doc.md
│   ├── document-project.md
│   ├── shard-doc.md
│   └── ...
├── templates/               # Document templates
│   ├── architecture-tmpl.yaml
│   ├── prd-tmpl.yaml
│   ├── story-tmpl.yaml
│   └── ...
├── checklists/             # Process checklists
├── workflows/              # Development workflows
└── data/                   # Reference data
```

#### **Key BMAD Components**:

- **core-config.yaml**: Central configuration linking all BMAD resources
- **agents/**: Personality-driven AI assistants for different roles
- **tasks/**: Reusable task definitions for common operations
- **templates/**: Structured templates for consistent documentation

---

## 🔧 Configuration Files (Root)

### Essential Configuration

```
📄 package.json              # NPM dependencies & scripts
📄 CLAUDE.md                 # Claude AI context document
📄 README.md                 # Project introduction
📄 .env                      # Environment variables (local)
📄 .env.example              # Environment template
📄 .gitignore                # Git exclusions
📄 bun.lock                  # Bun package lock
📄 setup.md                  # Quick setup notes
```

#### **package.json Scripts**:
```json
{
  "dev": "supabase start",
  "db:reset": "supabase db reset",
  "db:push": "supabase db push",
  "gen:types": "supabase gen types typescript",
  "functions:serve": "supabase functions serve",
  "functions:deploy": "supabase functions deploy",
  "test": "npm run test:functions && npm run test:db",
  "lint": "deno lint supabase/functions/",
  "format": "deno fmt supabase/functions/"
}
```

#### **Environment Variables** (.env):
- `SUPABASE_URL`: Project API endpoint
- `SUPABASE_ANON_KEY`: Public API key
- `SUPABASE_PROJECT_REF`: Project identifier
- `OPENROUTER_API_KEY`: AI gateway authentication
- `LLAMAPARSE_API_KEY`: PDF parsing service

---

## 🧪 Test Files

### Test Suite Organization

Tests are now organized in the `/tests` directory with the following structure:

```
tests/
├── unit/                    # Jest unit tests
├── integration/            # Integration tests
│   ├── *.test.ts          # Automated tests
│   └── manual/            # Manual test scripts
├── performance/           # Benchmarks
└── run-manual-tests.js    # Interactive runner
```

#### **Test Execution**:
```bash
# Quick test suite
npm test

# Complete suite
npm run test:all

# Interactive manual tests
npm run test:manual

# Specific manual tests
npm run test:manual:extraction
npm run test:manual:comparison
npm run test:manual:billing
npm run test:manual:selector
npm run test:manual:response
npm run test:manual:fixes
```

#### **Test Patterns**:
- Direct API testing with OpenRouter
- Cost analysis per extraction
- Model performance comparison
- Response structure validation
- Customer billing integration
- QA validation scripts

---

## 🛠️ Development Tools

### `/.vscode` - VS Code Configuration

```
.vscode/
├── extensions.json          # Recommended extensions
└── settings.json           # Workspace settings
```

**Recommended Extensions**:
- Deno for VS Code
- PostgreSQL syntax
- Supabase snippets

### `/.claude` & `/.gemini` - AI Assistant Configs

```
.claude/commands/BMad/      # Claude command mappings
.gemini/commands/BMad/      # Gemini command mappings
```

These directories mirror BMAD tasks and agents for AI integration.

---

## 📊 File Relationships & Data Flow

### Critical Path Files

```mermaid
graph TD
    A[.env] --> B[supabase/config.toml]
    B --> C[Edge Functions]
    B --> D[PostgreSQL]
    
    E[migrations/*.sql] --> D
    D --> F[types/database.types.ts]
    
    C --> G[OpenRouter API]
    C --> D
    
    H[test-*.js] --> G
    I[examples/client.ts] --> C
    
    J[CLAUDE.md] --> K[AI Context]
    L[docs/*.md] --> K
```

### Key Dependencies

1. **Database Schema** → **TypeScript Types**
   - Migrations define schema
   - `gen:types` creates TypeScript interfaces
   - Client code uses types for safety

2. **Edge Functions** → **AI Services**
   - Functions call OpenRouter/OpenAI
   - Results stored in PostgreSQL
   - Embeddings cached for similarity

3. **BMAD Framework** → **Development Workflow**
   - Agents guide development process
   - Templates ensure consistency
   - Tasks automate common operations

---

## 🚀 Quick Navigation Guide

### For Different Roles:

#### **Backend Developer (James)**
Focus on these directories:
- `/supabase/functions/` - Edge Function code
- `/supabase/migrations/` - Database schema
- `/types/` - TypeScript types
- `/docs/architecture/coding-standards.md` - Best practices

#### **Frontend Developer**
Key files:
- `/examples/client.ts` - SDK usage patterns
- `/types/database.types.ts` - API interfaces
- `/docs/architecture/tech-stack.md` - Technology overview

#### **DevOps Engineer**
Important configs:
- `/supabase/config.toml` - Infrastructure settings
- `/.env.example` - Environment setup
- `/package.json` - Deployment scripts

#### **Product Manager**
Documentation:
- `/docs/prd.md` - Product requirements
- `/docs/stories/` - User stories
- `/docs/supplement/` - Technical analysis

#### **QA Engineer**
Testing resources:
- `/test-*.js` - Integration tests
- `/.bmad-core/checklists/` - QA checklists
- `/docs/stories/` - Acceptance criteria

---

## 🚨 **CRITICAL: Dependency Management**

### **Deno Lock File Coordination Rules**

**MAJOR ISSUE**: Multiple agents creating lock files in different locations causes Edge Function boot failures.

#### **Lock File Locations (CRITICAL)**
```
✅ ALLOWED:     supabase/functions/deno.lock    # Primary functions cache
❌ FORBIDDEN:   deno.lock                       # Project root (conflicts with CLI)
❌ FORBIDDEN:   .deno/.deno.lock               # Cache conflicts
❌ FORBIDDEN:   Multiple lock files             # Version hell
```

#### **MANDATORY Rules for ALL Agents:**

1. **ALWAYS work from the functions directory:**
   ```bash
   # ✅ CORRECT - All Deno commands from here
   cd supabase/functions
   deno cache index.ts
   deno test --allow-all
   deno lint **/*.ts
   
   # ❌ WRONG - Creates conflicts
   deno cache supabase/functions/index.ts  # From root = conflict!
   ```

2. **Before ANY Deno command, check for conflicts:**
   ```bash
   # Check for multiple lock files
   find . -name "deno.lock*" -o -name ".deno.lock"
   
   # Should see ONLY: ./supabase/functions/deno.lock
   # If you see multiple files, STOP and clean them up
   ```

3. **Clean up conflicts immediately:**
   ```bash
   # Remove all conflicting lock files
   rm deno.lock  # Root level (shouldn't exist)
   rm -rf supabase/functions/node_modules/.deno/.deno.lock
   
   # Recreate from correct location
   cd supabase/functions
   deno cache index.ts  # This creates the ONE correct lock file
   ```

4. **Use consistent import patterns:**
   ```typescript
   // ✅ ALWAYS use exact versions
   import "jsr:@supabase/functions-js/edge-runtime.d.ts";
   import { createClient } from "jsr:@supabase/supabase-js@2.57.4";
   
   // ❌ NEVER mix version styles - causes conflicts
   // import { createClient } from "jsr:@supabase/supabase-js@2";
   // import { createClient } from "../../../node_modules/...";
   ```

#### **Emergency Lock File Recovery**

If Edge Functions fail to start or show version conflicts:

```bash
# 1. Stop all running services
supabase stop

# 2. Remove ALL lock files
rm deno.lock 2>/dev/null || true
rm supabase/functions/deno.lock 2>/dev/null || true
rm -rf supabase/functions/node_modules/.deno/ 2>/dev/null || true

# 3. Clear Deno cache
deno cache --reload --lock-write supabase/functions/index.ts

# 4. Restart services
supabase start
supabase functions serve
```

#### **Agent Coordination Protocol**

When multiple agents work on the project:

1. **Check current state FIRST:**
   ```bash
   find . -name "*lock*" | grep -E "(deno|lock)"
   ```

2. **If you find multiple lock files, announce and clean:**
   ```bash
   echo "🚨 DEPENDENCY CONFLICT DETECTED - Cleaning lock files"
   # Clean and recreate as shown above
   ```

3. **Always announce Deno operations:**
   ```bash
   echo "🔧 Running Deno cache from functions directory"
   cd supabase/functions && deno cache index.ts
   ```

#### **File Structure Impact**

The lock file location affects the entire project structure:

```
IDP/
├── supabase/
│   └── functions/
│       ├── deno.lock          # ✅ THE ONLY LOCK FILE
│       ├── index.ts           # Entry points
│       └── [function-dirs]/
│           └── index.ts
├── deno.lock                  # ❌ SHOULD NOT EXIST
└── .deno/
    └── .deno.lock            # ❌ SHOULD NOT EXIST
```

**Remember**: Lock file conflicts create version hell that breaks the entire Edge Functions runtime. Prevention is critical.

---

## 📝 File Naming Conventions

### Patterns Used:

1. **Migrations**: `[timestamp]_[description].sql`
   - Example: `20250119000001_initial_schema.sql`

2. **Stories**: `IDP-[number]-[feature].md`
   - Example: `IDP-001-model-routing-schema.md`

3. **Templates**: `[type]-tmpl.yaml`
   - Example: `architecture-tmpl.yaml`

4. **Tests**: `test-[feature].js`
   - Example: `test-extraction.js`

5. **BMAD Files**: `[role].md` or `[task].md`
   - Examples: `architect.md`, `create-doc.md`

---

## 🔄 Development Workflow

### Typical Development Cycle:

1. **Setup Environment**
   ```bash
   cp .env.example .env
   npm install
   npm run dev
   ```

2. **Database Changes**
   ```bash
   npm run migrate:new feature_name
   # Edit migration file
   npm run db:push
   npm run gen:types
   ```

3. **Edge Function Development**
   ```bash
   # Create function
   supabase functions new my-function
   # Edit supabase/functions/my-function/index.ts
   npm run functions:serve
   ```

4. **Testing**
   ```bash
   npm test
   node test-extraction.js
   ```

5. **Documentation**
   - Update relevant `.md` files in `/docs`
   - Run BMAD tasks for structured docs

---

## 🎯 Key Takeaways

### Most Important Files:

1. **`supabase/config.toml`** - Defines entire local environment
2. **`supabase/migrations/*.sql`** - Database source of truth
3. **`types/database.types.ts`** - TypeScript contract with DB
4. **`.env`** - Runtime configuration
5. **`CLAUDE.md`** - AI assistant context

### Core Relationships:

- **Migrations** → **Types** → **Code** (type safety flow)
- **Config** → **Services** → **Functions** (infrastructure flow)
- **BMAD** → **Docs** → **Stories** (development flow)
- **Tests** → **Functions** → **AI** (validation flow)

### Best Practices:

1. Always run `gen:types` after migration changes
2. Use BMAD templates for consistent documentation
3. Test with cheapest models first (`test-extraction.js`)
4. Document API changes in `/docs/supplement/`
5. Follow naming conventions for discoverability

---

## 🔗 Related Documentation

- [Coding Standards](./coding-standards.md)
- [Tech Stack Guide](./tech-stack.md)
- [Product Requirements](../prd.md)
- [Sprint Overview](../stories/sprint-1/SPRINT_1_OVERVIEW.md)
- [CLAUDE Context](../../CLAUDE.md)

---

## ⚡ **WORKING vs BROKEN - NO MIDDLE GROUND**

**CRITICAL POLICY**: There are only TWO acceptable status reports:

### ✅ **WORKING**
- Feature/function operates completely as specified
- All tests pass (100% success rate)
- No errors, warnings, or exceptions
- Performance meets requirements
- Ready for production use

### ❌ **BROKEN**
- Any deviation from expected behavior
- Any failing test (even 1 out of 100)
- Any error, warning, or exception
- Performance below requirements
- Not ready for production

### 🚫 **FORBIDDEN STATUS REPORTS**
**NEVER use these terms** - they indicate incomplete work:
- "Mostly working"
- "Partially working"
- "Almost done"
- "Nearly complete"
- "Working with minor issues"
- "Functional but needs tweaks"
- "95% working"
- "Works except for..."
- "Generally working"
- "Basically functional"

**If it's not WORKING, it's BROKEN. Fix it completely or report it as BROKEN.**

---

*This document serves as the authoritative guide to the IDP project structure. Keep it updated as the project evolves.*

*Last Updated: September 21, 2025*
*Version: 1.2 - Aligned with PRD v1.0 API-First Document Processing Platform*