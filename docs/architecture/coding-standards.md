# Coding Standards and Best Practices

## Overview
This document establishes coding standards and patterns for the IDP Platform project. These standards ensure code quality, maintainability, and consistency across all components.

---

## 🚨 **CRITICAL MANDATORY DIRECTIVE - NO REGRESSION POLICY**

**ABSOLUTE REQUIREMENT**: When encountering ANY issue, bug, error, or problem:

### ✅ **ALWAYS DO** (Mandatory):
- **FIX THE ROOT CAUSE** with proper solution
- **IMPLEMENT COMPREHENSIVE SOLUTION** that eliminates the problem
- **MAINTAIN OR IMPROVE CODE QUALITY** in all changes
- **ASK FOR GUIDANCE** if unsure about proper solution approach
- **PAUSE WORK** if unable to implement proper fix

### ❌ **NEVER DO** (Forbidden):
- **Disable functionality** to bypass problems
- **Comment out code** to "temporarily" resolve issues  
- **Hack or workaround** problems with quick fixes
- **Introduce regressions** that break existing functionality
- **Compromise security** for convenience
- **Reduce code quality** to meet deadlines
- **Ignore warnings or errors** assuming they're "minor"

### 🛑 **When to PAUSE and ASK**:
- Root cause is unclear or complex
- Proper solution requires architectural decisions
- Fix might impact other systems
- Security implications are involved
- Performance impact is uncertain

**VIOLATION CONSEQUENCES**: Any agent that disables, hacks, or workarounds instead of properly fixing issues violates project integrity and must immediately cease work.

---

## 🎯 Core Principles

### 1. **Type Safety First**
- Use TypeScript for all new code
- Leverage Deno's built-in TypeScript support for Edge Functions
- Generate types from database schema using `npm run gen:types`

### 2. **Error Handling Excellence**
- Always handle errors gracefully with try-catch blocks
- Return meaningful error messages with appropriate HTTP status codes
- Log errors with context for debugging

### 3. **Performance Optimization**
- Implement multi-layer caching strategies
- Optimize database queries with proper indexing
- Use efficient data structures and algorithms

### 4. **Security by Design**
- Never expose sensitive credentials in code
- Use environment variables for all secrets
- Implement Row Level Security (RLS) on all tables
- Validate all inputs before processing

---

## 🏗️ IDP Platform Architecture

### **API-First Document Processing Platform**

This platform is built as an **API-first document processing service** with these core architectural principles:

**Platform Overview**:
- **API-Only Service**: No user interface, pure backend API platform
- **Dual API Key System**: Test keys (`skt_`) vs Production keys (`skp_`) with different retention and billing
- **Multi-Model AI Fallbacks**: OpenAI ↔ Claude ↔ LlamaParse for reliability
- **Agent-Based Processing**: Customizable extraction templates per customer
- **Enterprise API Management**: Rate limiting, usage tracking, audit logging

### **Critical Platform Concepts for Developers**:

#### **1. API Key Architecture**
```typescript
// ✅ CORRECT: Always validate API key first
async function processDocument(req: Request) {
  const apiKey = req.headers.get('Authorization')?.replace('Bearer ', '');
  
  // Validate and get customer context
  const { customerId, keyType, credits, limits } = await validateApiKey(apiKey);
  
  if (!customerId) {
    return new Response('Invalid API key', { status: 401 });
  }
  
  // Process with customer context
  return await processWithCustomer(customerId, keyType, credits);
}

// ❌ WRONG: Processing without API key validation
async function processDocument(req: Request) {
  const data = await req.json();
  // Direct processing without authentication - FORBIDDEN!
}
```

#### **2. Document Processing Pipeline Standards**
```typescript
interface ProcessingResult {
  success: boolean;
  extractedData?: any;
  confidence: number;
  model: 'openai' | 'claude' | 'llamaparse';
  processingTime: number;
  cost: number;
  creditsUsed: number;
  retentionExpiry?: Date; // Based on key type
}

// ✅ CORRECT: Multi-model fallback pattern
async function extractData(document: Document, agent: Agent, keyType: string) {
  try {
    // Try primary model (OpenAI)
    return await processWithOpenAI(document, agent);
  } catch (error) {
    console.log('OpenAI failed, trying Claude fallback');
    try {
      return await processWithClaude(document, agent);
    } catch (error) {
      console.log('Claude failed, trying LlamaParse fallback');
      return await processWithLlamaParse(document, agent);
    }
  }
}
```

#### **3. Agent System Patterns**
```typescript
interface Agent {
  id: string;
  customerId: string | null; // null for default agents
  name: string;
  prompt: string;
  schema: JSONSchema;
  version: number;
  isDefault: boolean;
  parentAgentId?: string; // For cloned agents
}

// ✅ CORRECT: Agent access control
async function getAvailableAgents(customerId: string) {
  const { data } = await supabase
    .from('agents')
    .select('*')
    .or(`customer_id.eq.${customerId},is_default.eq.true`);
  
  return data;
}

// ❌ WRONG: No access control on agents
async function getAvailableAgents() {
  const { data } = await supabase.from('agents').select('*');
  return data; // Exposes all customer agents!
}
```

#### **4. Cost and Credit Tracking**
```typescript
interface CostTracking {
  customerId: string;
  apiKeyId: string;
  inputTokens: number;
  outputTokens: number;
  modelCost: number;      // What we pay AI provider
  customerPrice: number;  // What customer pays us
  creditsUsed: number;
  model: string;
  timestamp: Date;
}

// ✅ REQUIRED: Track both cost and price
async function recordUsage(processing: ProcessingResult, customer: Customer) {
  await supabase.from('usage_logs').insert({
    customer_id: customer.id,
    api_key_id: customer.currentKeyId,
    model_cost: processing.cost,           // Our cost
    customer_price: processing.cost * 1.5, // Customer price (markup)
    credits_used: processing.creditsUsed,
    model: processing.model,
    // ... other fields
  });
}
```

---

## 🏗️ Foundation Architecture

### **JWT-Based Multitenancy Foundation**
This project uses JWT-based multitenancy as its core security and performance foundation:

**Foundation Documentation**:
- `docs/architecture/foundation-migrations/critical-security-multitenancy-fix.md` - SEC-001 elimination
- `docs/architecture/foundation-migrations/high-performance-jwt-architecture.md` - Sub-50ms implementation
- `docs/architecture/foundation-migrations/patterns/` - Reusable JWT and security patterns

### **API Key-Based Authentication Patterns**:

**CRITICAL**: This platform uses API key authentication, NOT JWT multitenancy. All code must follow API key patterns:

```typescript
// ✅ CORRECT: API key-based customer isolation
const { data } = await supabase
  .from('documents')
  .select('*')
  .eq('customer_id', customerIdFromApiKey);

// ✅ CORRECT: Get customer from API key validation
const { customerId } = await validateApiKey(apiKey);
const { data } = await supabase
  .from('agents')
  .select('*')
  .eq('customer_id', customerId);

// ❌ WRONG: JWT-based patterns (not applicable to this platform)
const { data } = await supabase
  .from('documents')
  .eq('customer_id', await supabase.rpc('auth_tenant_id'));
```

### **RLS Policy Standards for API Key Platform**:
```sql
-- ✅ CORRECT: API key-based customer isolation
-- Note: RLS may not be primary security mechanism with API key architecture
CREATE POLICY "customer_isolation" ON documents
  FOR ALL USING (
    customer_id = current_setting('app.customer_id', true)::uuid
  );

-- ✅ CORRECT: Admin access patterns
CREATE POLICY "admin_full_access" ON documents
  FOR ALL USING (
    current_setting('app.user_role', true) = 'admin'
  );
```

---

## 📁 Project Structure

```
idp-platform/
├── supabase/
│   ├── functions/          # Edge Functions (Deno/TypeScript)
│   │   ├── [function-name]/
│   │   │   └── index.ts    # Function entry point
│   │   └── tests/         # Deno test files
│   └── migrations/         # Database migrations (SQL)
│       └── [timestamp]_[description].sql
├── src/                    # Frontend application source
│   ├── components/        # React components
│   ├── pages/            # Next.js pages/routes
│   ├── lib/              # Utility functions
│   └── types/            # TypeScript type definitions
├── tests/                  # All project tests
│   ├── unit/              # Unit tests (Bun)
│   ├── integration/       # Integration tests
│   │   ├── *.test.ts     # Automated tests
│   │   └── manual/       # Manual test scripts
│   └── performance/       # Performance benchmarks
├── types/                  # Shared TypeScript definitions
│   └── database.types.ts  # Auto-generated DB types
└── docs/
    ├── architecture/      # Architecture documentation
    │   ├── coding-standards.md     # This file
    │   └── tech-stack.md          # Technology decisions
    └── stories/           # Feature development documentation
```

---

## 🚀 Supabase Edge Functions Standards

### Deno Configuration

Create a `deno.json` file in `supabase/functions/` directory:

```json
{
  "imports": {
    "@supabase/functions-js": "jsr:@supabase/functions-js@2.4.0",
    "@supabase/supabase-js": "https://esm.sh/@supabase/supabase-js@2.57.4",
    "@std/assert": "jsr:@std/assert@1.0.8",
    "@std/testing": "jsr:@std/testing@1.0.7",
    "openai": "https://esm.sh/openai@4.79.1",
    "fast-xml-parser": "https://esm.sh/fast-xml-parser@4.5.1"
  },
  "lint": {
    "include": ["**/*.ts"],
    "rules": {
      "tags": ["recommended"],
      "include": ["no-explicit-any", "no-unused-vars", "no-import-prefix"]
    }
  },
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
```

### Import Guidelines

```typescript
// ✅ Use bare specifiers with import map
import "@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from "@supabase/supabase-js";
import { assertEquals } from "@std/assert";

// ❌ Avoid versioned/prefixed imports
// import "jsr:@supabase/functions-js/edge-runtime.d.ts";
// import { createClient } from "https://esm.sh/@supabase/supabase-js@2.57.4";
```

### Function Structure
```typescript
// Import Deno types for Edge Runtime
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

// Environment variables at top
const API_KEY = Deno.env.get('API_KEY')!;
const SERVICE_URL = Deno.env.get('SERVICE_URL') || 'default-url';

// Type definitions
interface RequestPayload {
  field: string;
  optional?: string;
}

interface ResponsePayload {
  success: boolean;
  data?: any;
  error?: string;
}

// Main handler with proper CORS
Deno.serve(async (req: Request): Promise<Response> => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  }

  try {
    // Parse and validate request
    const payload = await req.json() as RequestPayload;
    
    if (!payload.field) {
      return new Response(
        JSON.stringify({ error: 'Field is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    // Process request
    const result = await processRequest(payload);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('Error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
});
```

### Type Safety Standards

1. **Avoid `any` Types**
   ```typescript
   // ❌ Avoid
   function processData(data: any) { }
   
   // ✅ Preferred - Use specific types
   interface ProcessData {
     id: string;
     content: string;
   }
   function processData(data: ProcessData) { }
   
   // ✅ When type is unknown, use `unknown`
   function handleUnknownData(data: unknown) {
     // Type guards to narrow the type
     if (typeof data === 'object' && data !== null) {
       // Process safely
     }
   }
   ```

2. **Create Type Definition Files**
   ```typescript
   // types.ts for each Edge Function
   export interface RequestPayload {
     field: string;
     optional?: string;
   }
   
   export type Database = {
     public: {
       Tables: {
         model_routing: {
           Row: ModelRouting;
         };
       };
     };
   };
   
   export type TypedSupabaseClient = SupabaseClient<Database>;
   ```

### Unused Variables

1. **Prefix with Underscore for Intentionally Unused**
   ```typescript
   // ❌ Lint error: 'error' is never used
   const { data, error } = await supabase.from('table').select();
   
   // ✅ Correct if not using error
   const { data, error: _error } = await supabase.from('table').select();
   
   // ✅ Or omit entirely if not needed
   const { data } = await supabase.from('table').select();
   ```

2. **Remove Unused Imports**
   ```typescript
   // ❌ Avoid unused imports
   import { assertEquals, assertExists } from "@std/assert";
   // Only using assertEquals
   
   // ✅ Import only what you need
   import { assertEquals } from "@std/assert";
   ```

### Best Practices for Edge Functions

1. **Environment Variables**
   - Use `Deno.env.get()` for required variables with `!` assertion
   - Provide defaults for optional variables with `||` operator
   - Never hardcode credentials

2. **Type Safety**
   - Define interfaces for all request/response payloads
   - Use type assertions with `as` keyword when parsing JSON
   - Leverage auto-generated database types

3. **Error Handling**
   - Always wrap main logic in try-catch
   - Return structured error responses
   - Log errors with context using `console.error()`

4. **Response Format**
   - Always include `Content-Type: application/json`
   - Add CORS headers for browser compatibility
   - Include timestamps in responses for debugging

5. **Performance**
   - Keep functions lightweight and focused
   - Use streaming for large responses
   - Implement request validation early to fail fast

---

## 🚨 **CRITICAL DATABASE SCHEMA RULES**

### **FORBIDDEN: Functions in auth Schema**

**NEVER create functions in the `auth` schema - this causes migration failures and permission errors.**

```sql
-- ❌ FORBIDDEN - Will cause permission denied errors
CREATE OR REPLACE FUNCTION auth.validate_session_context(...)
CREATE OR REPLACE FUNCTION auth.get_session_context(...)
CREATE OR REPLACE FUNCTION auth.cleanup_expired_sessions(...)

-- ✅ REQUIRED - Always use public schema for custom functions
CREATE OR REPLACE FUNCTION public.validate_session_context(...)
CREATE OR REPLACE FUNCTION public.get_session_context(...)
CREATE OR REPLACE FUNCTION public.cleanup_expired_user_sessions(...)  -- Note: unique names to avoid conflicts
```

### **Critical Schema Rules:**

1. **Public Schema Only for Custom Functions**
   - All custom functions MUST go in `public` schema
   - The `auth` schema is managed by Supabase and has restricted permissions
   - Attempting to create functions in `auth` schema will fail during migration

2. **Function Naming to Avoid Conflicts**
   - Check for existing function names before creating new ones
   - Use descriptive prefixes when needed (e.g., `cleanup_expired_user_sessions` vs `cleanup_expired_sessions`)
   - Document function purposes clearly

3. **RLS Policy References**
   ```sql
   -- ✅ CORRECT - Use auth.uid() in policies (this is allowed)
   CREATE POLICY "user_access" ON user_sessions
     FOR SELECT USING (user_id = auth.uid());
   
   -- ❌ WRONG - Don't try to create custom auth functions
   CREATE FUNCTION auth.custom_uid() RETURNS UUID AS...  -- FORBIDDEN!
   ```

4. **Migration Validation**
   - Always test migrations with `npm run db:reset` before committing
   - If migration fails with "permission denied for schema auth", move functions to public schema
   - Update all references and grants accordingly

### **Common Auth Schema Violations:**

```sql
-- These patterns ALWAYS fail - fix immediately:
CREATE OR REPLACE FUNCTION auth.anything(...)  -- FORBIDDEN
GRANT EXECUTE ON FUNCTION auth.custom_func TO ...  -- FORBIDDEN  
COMMENT ON FUNCTION auth.my_function IS ...  -- FORBIDDEN
```

### **Emergency Fix Pattern:**

If you encounter auth schema permission errors:

1. **Move function to public schema:**
   ```sql
   CREATE OR REPLACE FUNCTION public.function_name(...)  -- Change auth. to public.
   ```

2. **Update all references:**
   ```sql
   -- In other migrations or function calls
   SELECT public.function_name(...);  -- Change auth. to public.
   ```

3. **Update grants and comments:**
   ```sql
   GRANT EXECUTE ON FUNCTION public.function_name TO service_role;
   COMMENT ON FUNCTION public.function_name IS '...';
   ```

---

## 💾 Database Standards

### Migration Naming Convention
```
[timestamp]_[action]_[description].sql
```
Examples:
- `20250119000001_initial_schema.sql`
- `20250119000002_add_model_registry.sql`
- `20250119000003_add_comprehensive_comments.sql`

### SQL Best Practices

```sql
-- 1. Always add comprehensive comments
COMMENT ON TABLE users IS
'Application users with profile information and preferences.';

COMMENT ON COLUMN users.user_id IS
'Human-readable unique identifier for the user';

-- 2. Use proper constraints
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT UNIQUE NOT NULL,
  email TEXT NOT NULL,
  -- ...
);

-- 3. Create appropriate indexes
CREATE INDEX idx_users_email 
ON users(email);

-- 4. Enable RLS where needed
ALTER TABLE user_data ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own data" ON user_data
  FOR SELECT USING (auth.uid() = user_id);

-- 5. Use ENUMs for fixed values
CREATE TYPE user_status AS ENUM (
  'active', 'inactive', 'suspended', 'pending'
);

-- 6. Implement trigger functions for automation
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```



---

## 🧪 Testing Standards - BUN TEST FRAMEWORK

### TypeScript Quality Standards

**ZERO TOLERANCE for `any` types**
- All data structures must have specific interfaces
- Use `unknown` for truly unknown data, then type guard
- Prefer union types over `any`
- Enable strict TypeScript compilation

### Test Framework: Bun Test

**Why Bun Test:**
- 50% faster execution than Jest
- Native TypeScript support
- Zero configuration needed
- Consistent with project's Bun ecosystem

### Test Organization

```
tests/
├── unit/                     # Unit tests (Bun Test/TypeScript)
├── integration/             # Integration tests (Bun Test)
│   ├── *.test.ts           # Automated integration tests
│   └── manual/             # Manual integration scripts (Node.js)
├── performance/            # Performance benchmarks
├── setup.ts               # Test environment configuration
├── run-manual-tests.js    # Interactive test runner
└── README.md              # Test documentation

supabase/functions/tests/  # Edge Function tests (Deno)
```

### Running Tests

```bash
# Quick test suite
npm test                    # Functions + Database

# Complete test suite
npm run test:all           # Unit + Integration + Functions + DB

# Specific test types
npm run test:unit          # Bun unit tests (--timeout 30000)
npm run test:integration   # Bun integration tests (--timeout 30000)
npm run test:functions     # Deno Edge Function tests
npm run test:db           # Database tests

# Manual integration tests (Node.js)
npm run test:manual        # Interactive menu
npm run test:manual:auth           # Authentication flows
npm run test:manual:api            # API endpoints
npm run test:manual:database       # Database operations
npm run test:manual:integration    # End-to-end workflows
```

### Bun Test File Structure (REQUIRED PATTERN)

```typescript
// ✅ CORRECT: Bun test imports
import { describe, it, expect, beforeEach, afterEach, mock } from 'bun:test';

// ✅ CORRECT: Specific type imports
import { createClient } from '@supabase/supabase-js';
import type { Database } from '../../types/database.types';

// ✅ CORRECT: Proper interface definitions (NO ANY TYPES)
interface TestData {
  id: string;
  content: string;
  metadata: Record<string, string>; // NOT any!
}

// ✅ CORRECT: Mock setup for Bun
const createMockChain = () => ({
  from: mock(() => createMockChain()),
  select: mock(() => createMockChain()),
  eq: mock(() => createMockChain()),
  single: mock(() => Promise.resolve({ data: null, error: null }))
});

describe('Feature Name', () => {
  beforeEach(() => {
    // Reset mocks for each test
  });

  it('should describe specific behavior', async () => {
    // Arrange: Setup test data with proper types
    const testData: TestData = {
      id: 'test-123',
      content: 'test content',
      metadata: { key: 'value' }
    };

    // Act: Execute the function under test
    const result = await functionUnderTest(testData);

    // Assert: Verify expected behavior
    expect(result.success).toBe(true);
    expect(result.data).toEqual(expect.objectContaining({
      id: testData.id,
      content: testData.content
    }));
  });
});
```

### ❌ FORBIDDEN PATTERNS (Will Break CI/CD)

```typescript
// ❌ NEVER USE: any types
function processData(data: any): any { // FORBIDDEN!

// ❌ NEVER USE: Jest imports
import { describe, it, expect } from '@jest/globals'; // OUTDATED!

// ❌ NEVER USE: Untyped mocks
const mockSupabase = {
  from: jest.fn().mockReturnThis() // BROKEN WITH BUN!
};

// ❌ NEVER USE: Missing type definitions
const result = await someFunction(undefined); // UNACCEPTABLE!
```

### ✅ REQUIRED PATTERNS (Enforce in All Tests)

```typescript
// ✅ REQUIRED: Specific interface for every data structure
interface UserValidation {
  isValid: boolean;
  userId?: string;
  email?: string;
  status: 'active' | 'inactive' | 'pending'; // NOT string!
  permissions?: string[];
  lastLogin?: Date;
  error?: string;
}

// ✅ REQUIRED: Proper Supabase mock chain for Bun
const mockSupabase = {
  from: mock(() => ({
    select: mock(() => ({
      eq: mock(() => ({
        single: mock(() => Promise.resolve({
          data: null as UserRecord | null,
          error: null as Error | null
        }))
      }))
    }))
  }))
};

// ✅ REQUIRED: Type guards for runtime validation
function isUserRecord(data: unknown): data is UserRecord {
  return typeof data === 'object' &&
         data !== null &&
         'id' in data &&
         'email' in data;
}

// ✅ REQUIRED: Proper error handling with types
try {
  const result = await validateUser(userData);
  expect(result.isValid).toBe(true);
} catch (error: unknown) {
  if (error instanceof Error) {
    expect(error.message).toMatch(/Invalid user/);
  } else {
    throw new Error('Unexpected error type');
  }
}
```

### CRITICAL: Environment Setup for Tests

```typescript
// ✅ REQUIRED: Environment validation before tests
beforeAll(async () => {
  // Ensure Supabase is running
  const supabaseUrl = process.env.SUPABASE_URL;
  if (!supabaseUrl || !supabaseUrl.includes('localhost')) {
    throw new Error('SUPABASE_URL not configured for local testing');
  }

  // Verify Edge Functions are accessible
  const functionsCheck = await fetch(`${supabaseUrl}/functions/v1/health`);
  if (!functionsCheck.ok) {
    throw new Error('Edge Functions not running. Run: supabase functions serve');
  }

  // Validate API keys are real (not demo keys)
  const anonKey = process.env.SUPABASE_ANON_KEY;
  if (!anonKey || anonKey.includes('demo')) {
    throw new Error('Invalid SUPABASE_ANON_KEY. Use real local key from: supabase status');
  }
});
```

### Test Quality Checklist

**Before Committing Any Test:**
- [ ] Uses `bun:test` imports
- [ ] Zero `any` types - all data properly typed
- [ ] Proper mock setup for Bun test framework
- [ ] Environment validation in beforeAll
- [ ] Descriptive test names and clear AAA pattern
- [ ] Proper error handling with typed catch blocks
- [ ] No hardcoded API keys or demo credentials
- [ ] All tests pass with `bun test --timeout 30000`
- [ ] No lint violations when run with `npm run lint`
- [ ] TypeScript compiles cleanly with `npm run typecheck`
// tests/integration/manual/test-extraction.js
const API_KEY = process.env.API_KEY || 'test-key';

async function testFeature() {
  // Setup
  const testData = prepareTestData();

  try {
    // Execute
    const result = await performAction(testData);

    // Verify
    console.log('✅ Test passed:', result);

    // Cleanup
    await cleanup();

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests
testFeature().catch(console.error);
```

### Testing Best Practices

1. **Use descriptive console output**
   ```javascript
   console.log('🚀 Starting extraction test...');
   console.log('─'.repeat(50));
   console.log('✅ Extraction successful');
   console.log('❌ Validation failed');
   ```

2. **Include performance metrics for operations**
   ```javascript
   const executionTime = endTime - startTime;
   console.log(`⏱️ Execution time: ${executionTime}ms`);
   console.log(`📊 Throughput: ${Math.floor(1000/executionTime)} ops/sec`);
   ```

3. **Test both success and failure paths**
4. **Use realistic test data**
5. **Clean up test artifacts**

---

## 📦 TypeScript/JavaScript Patterns

### Async/Await Over Promises

```typescript
// ✅ Preferred
async function fetchData() {
  try {
    const response = await fetch(url);
    const data = await response.json();
    return data;
  } catch (error) {
    handleError(error);
  }
}

// ❌ Avoid
function fetchData() {
  return fetch(url)
    .then(response => response.json())
    .then(data => data)
    .catch(handleError);
}
```

### Null Safety

```typescript
// Use optional chaining
const value = data?.nested?.property ?? 'default';

// Use nullish coalescing
const port = process.env.PORT ?? 3000;

// Guard clauses
if (!requiredParam) {
  throw new Error('Required parameter missing');
}
```

### Object Destructuring

```typescript
// Function parameters
async function processDocument({ 
  url, 
  agentId, 
  customPrompt 
}: ProcessOptions) {
  // ...
}

// Response handling
const { data, error } = await supabase
  .from('agents')
  .select('*')
  .single();
```

---

## 📅 Date Usage in Documentation

### Important Date Guidelines

When creating or updating documentation:

1. **Always Use the Current Date**
   - When adding "Last Updated" timestamps, use the actual current date
   - Do not use placeholder or future dates
   - Format: `Month Day, Year` (e.g., "September 20, 2025")

2. **Documentation Headers**
   ```markdown
   # Document Title
   
   *Last Updated: [Use Current Date]*
   *Author: [Your Name/Team]*
   ```

3. **Changelog Entries**
   ```markdown
   ## Changelog
   
   ### [Current Date]
   - Added feature X
   - Fixed bug Y
   ```

4. **Code Comments**
   ```typescript
   // Updated: [Current Date] - Fixed validation logic
   // TODO: [Current Date] - Implement caching
   ```

**Remember**: Using incorrect dates creates confusion and undermines the reliability of documentation timestamps. Always verify the current date before adding timestamps.

---

## 📝 Naming Conventions

### **Meaningful Names Policy**

**CRITICAL RULE**: Always use descriptive, purpose-driven names that reflect the actual functionality, not temporary artifacts or sprint references.

#### **✅ CORRECT Naming Patterns:**
```typescript
// Files and functions
auth-validation.test.ts         // Clear purpose
user-profile-management.ts      // Describes functionality
database-connection-pool.ts     // Specific technical purpose

// Database tables
user_profiles                   // Clear entity
authentication_tokens          // Specific purpose
api_rate_limits                // Business function

// Test descriptions
describe('User authentication validation', () => {
  it('should reject invalid email formats', () => {
```

#### **❌ FORBIDDEN Naming Patterns:**
```typescript
// ❌ Sprint/temporary references
sprint-1-jwt-extraction.ts      // FORBIDDEN
temp-auth-fix.ts               // FORBIDDEN
quick-db-patch.sql             // FORBIDDEN
test-spike-feature.test.ts     // FORBIDDEN

// ❌ Vague or meaningless names
stuff.ts                       // FORBIDDEN
helpers.ts                     // TOO VAGUE
utils.ts                       // TOO GENERIC
data.sql                       // MEANINGLESS

// ❌ Implementation details in names
jwt-with-redis-cache.ts        // FORBIDDEN - exposes implementation
postgres-specific-queries.ts   // FORBIDDEN - ties to specific tech
```

#### **Naming Guidelines:**

1. **Files & Modules**: Use kebab-case, describe purpose
   ```
   user-authentication.ts
   payment-processing.ts
   email-notifications.ts
   ```

2. **Database Tables**: Use snake_case, plural nouns
   ```sql
   users
   user_sessions
   api_keys
   rate_limit_configs
   ```

3. **Test Files**: Match functionality + .test.ts
   ```
   user-authentication.test.ts
   payment-processing.test.ts
   database-migrations.test.ts
   ```

4. **Functions & Variables**: Use camelCase, action-oriented
   ```typescript
   validateUserCredentials()
   processPaymentRequest()
   generateSecureToken()
   ```

5. **Constants**: Use SCREAMING_SNAKE_CASE
   ```typescript
   MAX_LOGIN_ATTEMPTS
   DEFAULT_SESSION_TIMEOUT
   API_RATE_LIMIT_WINDOW
   ```

#### **Renaming Legacy Code:**

If you encounter poorly named artifacts:
1. **Rename immediately** - don't propagate bad names
2. **Update all references** - ensure consistency
3. **Document the change** - explain the rename in commit message

**Remember**: Code is read 10x more than it's written. Names should be instantly understandable to any developer.

---

## 🔧 Development Workflow

### 1. **Before Starting Development**
```bash
# Pull latest changes
git pull origin main

# Update dependencies
npm install

# Start local Supabase
npm run dev

# Generate latest types
npm run gen:types
```

### 2. **During Development**
```bash
# Test Edge Functions locally
npm run functions:serve

# Run database migrations
npm run db:push

# Open Studio for database inspection
npm run studio

# Lint Edge Functions (aim for 0 errors)
npm run lint

# Common lint fixes:
# - Replace `any` with specific types or `unknown`
# - Prefix unused vars with underscore: `_unusedVar`
# - Use import map in deno.json for dependencies

# Format code automatically
npm run format

# Type check for type safety
npm run typecheck
```

### 3. **Before Committing**
```bash
# Run all tests
npm test

# Check formatting
npm run format

# Verify types
npm run typecheck

# Test migrations with reset
npm run db:reset
```

---

## 🔐 Security Standards for Document Processing Platform

### **API Key Security (CRITICAL)**

**MANDATORY SECURITY PRACTICES** for handling API keys and document processing:

#### **1. API Key Handling**
```typescript
// ✅ CORRECT: Secure API key validation
async function validateApiKey(key: string): Promise<ApiKeyValidation> {
  if (!key || !key.match(/^sk[tp]_live_[a-zA-Z0-9]{32,}$/)) {
    throw new Error('Invalid API key format');
  }
  
  // Hash before database lookup
  const keyHash = await hashApiKey(key);
  
  const { data } = await supabase
    .from('api_keys')
    .select('customer_id, key_type, credits, rate_limits, revoked')
    .eq('key_hash', keyHash)
    .single();
    
  if (data?.revoked) {
    throw new Error('API key revoked');
  }
  
  return {
    customerId: data.customer_id,
    keyType: data.key_type,
    credits: data.credits,
    rateLimit: data.rate_limits
  };
}

// ❌ FORBIDDEN: Storing raw API keys
async function storeApiKey(rawKey: string) {
  await supabase.from('api_keys').insert({
    raw_key: rawKey // NEVER STORE RAW KEYS!
  });
}
```

#### **2. Prompt Injection Protection**
```typescript
// ✅ REQUIRED: Sanitize all user inputs
import DOMPurify from 'dompurify';

async function processCustomPrompt(userPrompt: string, document: string) {
  // 1. Sanitize HTML/script content
  const cleanPrompt = DOMPurify.sanitize(userPrompt);
  
  // 2. Check for prompt injection patterns
  const injectionPatterns = [
    /ignore.{0,10}previous.{0,10}instructions/i,
    /system.{0,10}prompt/i,
    /assistant.{0,10}mode/i,
    /\/\*.*\*\//gs, // Block comments
    /<script.*?>.*?<\/script>/gis
  ];
  
  for (const pattern of injectionPatterns) {
    if (pattern.test(cleanPrompt)) {
      throw new Error('Potential prompt injection detected');
    }
  }
  
  // 3. Limit prompt length
  if (cleanPrompt.length > 4000) {
    throw new Error('Custom prompt too long (max 4000 chars)');
  }
  
  return cleanPrompt;
}

// ❌ FORBIDDEN: Direct user input to AI models
async function processDocument(userPrompt: string) {
  return await openai.complete({
    prompt: userPrompt // DANGEROUS! No validation
  });
}
```

#### **3. File Upload Security**
```typescript
// ✅ REQUIRED: Validate all uploaded files
async function validateUpload(file: File, customerId: string): Promise<ValidationResult> {
  // 1. Check file size against customer limits
  const { maxFileSize } = await getCustomerLimits(customerId);
  if (file.size > maxFileSize) {
    throw new Error(`File too large. Max: ${maxFileSize} bytes`);
  }
  
  // 2. Validate file type
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 
                       'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  if (!allowedTypes.includes(file.type)) {
    throw new Error('Unsupported file type');
  }
  
  // 3. Scan file header (magic numbers)
  const header = await file.slice(0, 8).arrayBuffer();
  if (!isValidFileHeader(header, file.type)) {
    throw new Error('File header mismatch - possible malicious file');
  }
  
  // 4. Virus scanning (if available)
  if (process.env.VIRUS_SCAN_ENABLED) {
    await scanForMalware(file);
  }
  
  return { valid: true, sanitizedFile: file };
}

// ❌ FORBIDDEN: Processing files without validation
async function processUpload(file: File) {
  return await extractText(file); // No security checks!
}
```

#### **4. Rate Limiting Implementation**
```typescript
// ✅ REQUIRED: Implement per-key rate limiting
async function checkRateLimit(apiKeyId: string): Promise<boolean> {
  const windowStart = new Date(Date.now() - 60000); // 1 minute window
  
  const { count } = await supabase
    .from('api_requests')
    .select('*', { count: 'exact' })
    .eq('api_key_id', apiKeyId)
    .gte('created_at', windowStart.toISOString());
    
  const { rateLimit } = await getKeyLimits(apiKeyId);
  
  if (count >= rateLimit) {
    throw new Error(`Rate limit exceeded: ${count}/${rateLimit} requests per minute`);
  }
  
  // Log this request
  await supabase.from('api_requests').insert({
    api_key_id: apiKeyId,
    created_at: new Date().toISOString()
  });
  
  return true;
}
```

#### **5. Audit Logging Requirements**
```typescript
// ✅ MANDATORY: Log all security-relevant events
interface AuditLog {
  customerId: string;
  apiKeyId: string;
  action: 'document_upload' | 'extraction_request' | 'agent_clone' | 'key_validation';
  status: 'success' | 'failure';
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  correlationId: string;
}

async function logSecurityEvent(event: AuditLog) {
  await supabase.from('audit_logs').insert(event);
  
  // Also log to external service for compliance
  if (process.env.EXTERNAL_AUDIT_ENDPOINT) {
    await fetch(process.env.EXTERNAL_AUDIT_ENDPOINT, {
      method: 'POST',
      body: JSON.stringify(event)
    });
  }
}

// ✅ REQUIRED: Use correlation IDs for request tracing
function generateCorrelationId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}
```

---

## 🔒 **Dependency Management (CRITICAL)**

### **Deno Lock File Management - NO LOCKFILE POLICY**

**CRITICAL UPDATE (September 21, 2025)**: Due to Supabase Edge Runtime compatibility issues with Deno lockfile version formats, we now use a **NO LOCKFILE** approach.

#### **Root Cause Analysis:**
- Local Deno 2.5.1+ creates lockfile version 5
- Supabase Edge Runtime uses Deno 1.43.0 which only supports older lockfile formats
- This version mismatch causes "Unsupported lockfile version" errors and deployment failures

#### **NEW MANDATORY POLICY:**

1. **NEVER create or maintain lockfiles:**
   ```bash
   # ✅ CORRECT - Remove all lockfiles permanently
   find . -name "*lock*" -type f | grep -E "(deno|lock)" | grep -v node_modules | xargs rm -f

   # ✅ CORRECT - All Deno commands use --no-lock flag
   deno check --no-lock ./supabase/functions/*/index.ts
   deno cache --no-lock --reload ./supabase/functions/health/index.ts
   deno test --no-lock --allow-all
   deno fmt --no-lock
   deno lint --no-lock
   ```

2. **Dependency management through import maps and version pinning:**
   ```json
   // supabase/functions/deno.json
   {
     "lock": false,  // CRITICAL: Disable lockfile generation
     "imports": {
       "@supabase/functions-js": "jsr:@supabase/functions-js@2.5.0",
       "@supabase/supabase-js": "jsr:@supabase/supabase-js@2.57.4"
     },
     "tasks": {
       "test": "deno test --allow-all --no-lock --unstable",
       "lint": "deno lint --no-lock",
       "fmt": "deno fmt --no-lock",
       "check": "deno check --no-lock **/*.ts"
     }
   }
   ```

3. **Always use versioned imports for reproducibility:**
   ```typescript
   // ✅ CORRECT - Exact version pinning
   import "jsr:@supabase/functions-js@2.5.0/edge-runtime.d.ts";
   import { createClient } from "jsr:@supabase/supabase-js@2.57.4";

   // ❌ WRONG - Version ranges cause conflicts
   // import { createClient } from "jsr:@supabase/supabase-js@2";
   ```

#### **Emergency Cleanup Protocol:**

If lockfiles appear or Edge Functions fail to start:

```bash
# Step 1: Complete lockfile removal
cd /path/to/project/root
find . -name "*lock*" -type f | grep -E "(deno|lock)" | grep -v node_modules | xargs rm -f

# Step 2: Verify deno.json configuration
cat supabase/functions/deno.json | grep '"lock": false'  # Should exist

# Step 3: Test functions serve
supabase stop
supabase start
supabase functions serve  # Should start without lockfile errors
```

#### **Why This Works:**
- **No Version Conflicts**: Without lockfiles, no format compatibility issues
- **Runtime Compatibility**: Supabase Edge Runtime handles dependency resolution internally
- **Development Flexibility**: Use latest Deno locally while deploying to stable runtime
- **Deterministic Builds**: Import maps + exact versioning provide reproducibility

#### **Forbidden Patterns:**
```bash
# ❌ NEVER create lockfiles
deno cache index.ts  # Creates deno.lock - FORBIDDEN!

# ❌ NEVER run Deno commands without --no-lock
deno test --allow-all  # Will try to create/use lockfile - FORBIDDEN!

# ❌ NEVER commit lockfiles to repository
git add deno.lock  # FORBIDDEN!
```

#### **Required Patterns:**
```bash
# ✅ ALWAYS use --no-lock for all Deno operations
deno cache --no-lock --reload index.ts
deno test --no-lock --allow-all
deno check --no-lock **/*.ts

# ✅ ALWAYS verify no lockfiles exist before development
find . -name "deno.lock" | wc -l  # Should return 0
```

This approach eliminates the persistent lockfile version compatibility issue and aligns with Supabase's deployment architecture.

---

## 🌟 Code Quality Checklist

### For Every Pull Request:

- [ ] **Linting & Code Quality**
  - Zero or minimal lint errors (`npm run lint`)
  - No `any` types in production code
  - Unused variables prefixed with `_` or removed
  - Imports use bare specifiers from deno.json

- [ ] **TypeScript/Type Safety**
  - All new code uses TypeScript
  - Interfaces defined for data structures
  - Specific types used instead of `any`
  - Type definition files created for complex functions

- [ ] **Error Handling**
  - All async operations have try-catch
  - Meaningful error messages returned
  - Errors logged with context

- [ ] **Security**
  - No hardcoded credentials
  - Input validation implemented
  - RLS policies considered

- [ ] **Performance**
  - Database queries optimized
  - Appropriate indexes created
  - Caching strategy considered

- [ ] **Documentation**
  - Functions have clear comments
  - Complex logic explained
  - API changes documented

- [ ] **Testing**
  - New features have tests
  - Edge cases covered
  - Cost implications analyzed

---

## 📚 Additional Resources

### Deno & Edge Functions
- [Deno Runtime API](https://deno.land/api)
- [Supabase Edge Functions Guide](https://supabase.com/docs/guides/functions)
- [JSR Package Registry](https://jsr.io/@supabase/functions-js)

### PostgreSQL & pgvector
- [pgvector Documentation](https://github.com/pgvector/pgvector)
- [PostgreSQL Best Practices](https://wiki.postgresql.org/wiki/Don%27t_Do_This)
- [HNSW Index Tuning](https://github.com/pgvector/pgvector#hnsw)

### TypeScript
- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook)
- [Type Safety Patterns](https://www.typescriptlang.org/docs/handbook/2/narrowing.html)

---

## ⚡ **WORKING vs BROKEN - NO MIDDLE GROUND**

**CRITICAL POLICY**: There are only TWO acceptable status reports:

### ✅ **WORKING**
- Feature/function operates completely as specified
- All tests pass (100% success rate)
- No errors, warnings, or exceptions
- Performance meets requirements
- Ready for production use

### ❌ **BROKEN**
- Any deviation from expected behavior
- Any failing test (even 1 out of 100)
- Any error, warning, or exception
- Performance below requirements
- Not ready for production

### 🚫 **FORBIDDEN STATUS REPORTS**
**NEVER use these terms** - they indicate incomplete work:
- "Mostly working"
- "Partially working"
- "Almost done"
- "Nearly complete"
- "Working with minor issues"
- "Functional but needs tweaks"
- "95% working"
- "Works except for..."
- "Generally working"
- "Basically functional"

**If it's not WORKING, it's BROKEN. Fix it completely or report it as BROKEN.**

---

## 🎯 Summary

These coding standards ensure:
1. **Consistency** - Uniform code style across the project
2. **Maintainability** - Easy to understand and modify
3. **Reliability** - Proper error handling and testing
4. **Performance** - Optimized for speed and cost
5. **Security** - Protected against common vulnerabilities

Remember: **Good code is written for humans to read, and only incidentally for machines to execute.**

---

*Last Updated: September 21, 2025*
*Aligned with PRD v1.0 - API-First Document Processing Platform*
