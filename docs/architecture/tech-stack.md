# Technology Stack & Architecture Guide

## 🎯 Executive Summary

This document provides comprehensive technical context for the IDP Platform - an API-first document processing service. It covers all technologies, their interactions, best practices, and practical usage patterns to enable efficient development.

**Core Philosophy**: Build a pure API backend service using Supabase Edge Functions with intelligent multi-model AI fallbacks (OpenAI ↔ Claude ↔ LlamaParse), achieving 60%+ profit margins while reducing customer costs from $0.10-$1.00 per document to competitive pricing with 99.5% uptime and <5 second processing times (per PRD goals).

---

## 🏗️ High-Level Architecture

```mermaid
graph TB
    subgraph "API Clients"
        DEV[Developer Applications]
        CURL[Direct API Calls]
        SDK[Custom SDKs]
    end

    subgraph "Supabase Platform"
        KONG[Kong Gateway]
        EF[Edge Functions<br/>Deno Runtime]
        DB[(PostgreSQL)]
        AUTH[API Key Auth]
    end

    subgraph "AI Processing"
        OPENAI[OpenAI API]
        CLAUDE[Claude API]
        LLAMA[LlamaParse API]
    end

    subgraph "Core Services"
        VALIDATE[API Key Validation]
        EXTRACT[Document Extraction]
        AGENT[Agent Management]
        AUDIT[Audit Logging]
    end

    DEV --> KONG
    CURL --> KONG
    SDK --> KONG
    
    KONG --> EF
    EF --> VALIDATE
    EF --> EXTRACT
    EF --> AGENT
    EF --> AUDIT
    
    EXTRACT --> OPENAI
    EXTRACT --> CLAUDE
    EXTRACT --> LLAMA
    
    EF --> DB
    AUTH --> DB
```

---

## 🛠️ Core Technologies

### 1. **Supabase Platform** (Backend-as-a-Service)

Supabase provides a complete backend platform built on open-source tools:

#### **Key Components:**
- **PostgreSQL 17**: Core database with extensions
- **PostgREST**: Auto-generated REST APIs
- **GoTrue**: JWT-based authentication
- **Realtime**: WebSocket server for live updates
- **Storage**: S3-compatible object storage
- **Kong**: API gateway for routing and auth

#### **Why Supabase for API Platform?**
- **Open Source**: No vendor lock-in, self-hostable
- **Edge Functions**: Perfect for API-only architecture
- **Postgres-First**: Full SQL power for complex queries
- **Auto APIs**: Instant REST APIs from schema (admin endpoints)
- **Authentication**: JWT handling for API keys
- **Serverless**: Pay-per-use scaling for API workloads

#### **Platform Features We Use:**
```javascript
// Auto-generated REST API
const { data } = await supabase
  .from('agents')
  .select('*')
  .eq('category', 'invoice')

// Real-time subscriptions
supabase
  .channel('jobs')
  .on('postgres_changes', { 
    event: 'UPDATE', 
    schema: 'public',
    table: 'job_queue',
    filter: `id=eq.${jobId}`
  }, handleUpdate)
  .subscribe()

// Row Level Security (RLS)
-- Users only see their own jobs
CREATE POLICY "user_jobs" ON job_queue
  FOR SELECT USING (auth.uid() = account_id);
```

---

### 2. **PostgreSQL Extensions** (Database Layer)

#### **pgvector** - Vector Similarity Search
```sql
-- Store embeddings for semantic search
CREATE TABLE document_embeddings (
  embedding vector(1536),  -- OpenAI dimension
  -- HNSW index for fast similarity
  INDEX USING hnsw (embedding vector_cosine_ops)
);

-- Find similar documents
SELECT * FROM document_embeddings
WHERE embedding <=> $1::vector < 0.3
ORDER BY embedding <=> $1::vector
LIMIT 10;
```

**Use Cases:**
- Document deduplication (>95% similarity)
- Smart agent selection
- Template matching
- Semantic search

#### **pg_cron** - Scheduled Jobs
```sql
-- Process queue every 10 seconds
SELECT cron.schedule('process-queue', '10 seconds', 
  $$SELECT process_embedding_queue()$$);
```

#### **pg_net** - HTTP from Postgres
```sql
-- Call Edge Functions from database
SELECT net.http_post(
  url := 'https://project.supabase.co/functions/v1/extract',
  headers := '{"Authorization": "Bearer token"}'::jsonb,
  body := '{"document": "..."}'::jsonb
);
```

#### **pgmq** - Message Queue
```sql
-- Reliable job queueing
SELECT pgmq.send('extraction_jobs', 
  jsonb_build_object('doc_id', 123, 'agent', 'invoice'));
```

---

### 3. **Deno Runtime** (Edge Functions)

#### **Why Deno?**
- **TypeScript Native**: No compilation step
- **Secure by Default**: Explicit permissions
- **Web Standards**: Fetch API, Web Crypto
- **NPM Compatible**: Use Node packages
- **Fast Cold Starts**: ~50ms initialization

#### **Edge Function Structure:**
```typescript
// supabase/functions/extract/index.ts
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

Deno.serve(async (req: Request): Promise<Response> => {
  // CORS handling
  if (req.method === 'OPTIONS') {
    return new Response(null, { 
      status: 200,
      headers: corsHeaders 
    })
  }

  try {
    // Type-safe request parsing
    const payload = await req.json() as ExtractRequest
    
    // Process with error handling
    const result = await processDocument(payload)
    
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        ...corsHeaders 
      }
    })
  } catch (error) {
    console.error('Processing error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500 }
    )
  }
})
```

#### **Key Patterns:**
- Always handle CORS for browser compatibility
- Use environment variables via `Deno.env.get()`
- Implement proper error boundaries
- Return structured JSON responses
- Log errors for debugging

---

### 4. **OpenRouter** (AI Gateway)

#### **What is OpenRouter?**
OpenRouter is an AI model gateway that provides:
- **Unified API**: Single interface for 100+ models
- **Smart Routing**: Automatic failover and load balancing
- **Cost Optimization**: Route to cheapest capable model
- **No Rate Limits**: Handles throttling automatically

#### **Model Tiers in Our System:**
```typescript
// Model selection by cost/capability (configured in project)
const MODEL_TIERS = {
  fast: [
    'google/gemini-flash-1.5-8b',     // $0.0375/1M tokens
    'meta-llama/llama-3.1-8b-instruct' // $0.18/1M tokens
  ],
  balanced: [
    'google/gemini-flash-1.5',         // $0.075/1M tokens
    'anthropic/claude-3-haiku'         // $0.25/1M tokens
  ],
  smart: [
    'anthropic/claude-3.5-sonnet',     // $3/1M tokens
    'openai/gpt-4o'                    // $2.50/1M tokens
  ],
  vision: [
    'google/gemini-1.5-pro',           // With image support
    'openai/gpt-4o-mini'               // Cheaper vision model
  ]
}
```

#### **Integration Pattern:**
```typescript
const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
    'HTTP-Referer': 'https://your-project-ref.supabase.co',
    'X-Title': 'IDP Extraction'
  },
  body: JSON.stringify({
    model: selectModelByComplexity(document),
    messages: [
      { role: 'system', content: agent.system_prompt },
      { role: 'user', content: documentContent }
    ],
    temperature: 0.1,  // Low for consistency
    response_format: { type: "json_object" }  // Structured output
  })
})
```

---

### 5. **TypeScript** (Type Safety)

#### **Database Types Generation:**
```bash
# Generate types from database schema
npm run gen:types
```

```typescript
// types/database.types.ts (auto-generated)
export interface Database {
  public: {
    Tables: {
      agents: {
        Row: {
          id: string
          agent_id: string
          name: string
          category: 'invoice' | 'contract' | 'insurance'
          // ... all columns with types
        }
        Insert: { /* ... */ }
        Update: { /* ... */ }
      }
    }
  }
}

// Usage with full type safety
const { data, error } = await supabase
  .from('agents')
  .select('*')
  .eq('category', 'invoice')
  .single()

// TypeScript knows data.category is 'invoice'
```

#### **Custom Type Patterns:**
```typescript
// Discriminated unions for job status
type JobStatus = 
  | { status: 'queued'; queuedAt: Date }
  | { status: 'processing'; startedAt: Date }
  | { status: 'completed'; result: ExtractionResult }
  | { status: 'failed'; error: string; retriesLeft: number }

// Branded types for IDs
type AgentId = string & { __brand: 'AgentId' }
type JobId = string & { __brand: 'JobId' }

// Result types with error handling
type Result<T, E = Error> = 
  | { ok: true; value: T }
  | { ok: false; error: E }
```

---

## 🔄 Data Flow & Processing Pipeline

### Document Processing Lifecycle:

```mermaid
sequenceDiagram
    participant C as Client
    participant EF as Edge Function
    participant DB as PostgreSQL
    participant Q as Job Queue
    participant AI as OpenRouter/AI
    participant RT as Realtime

    C->>EF: POST /extract
    EF->>DB: Check embedding cache
    alt Cache Hit
        DB-->>EF: Return cached result
        EF-->>C: Immediate response
    else Cache Miss
        EF->>Q: Enqueue job
        Q-->>EF: Job ID
        EF-->>C: 202 Accepted + Job ID
        
        loop Process Queue
            Q->>EF: Process job
            EF->>AI: Generate extraction
            AI-->>EF: Structured data
            EF->>DB: Store result + embedding
            EF->>RT: Broadcast update
            RT-->>C: WebSocket notification
        end
    end
```

### Key Processing Patterns:

1. **Async Queue Pattern**
   - Non-blocking API responses
   - Retry logic for failures
   - Priority-based processing

2. **Caching Strategy**
   - Vector similarity for near-duplicates
   - Hash-based exact matching
   - TTL for cache expiration

3. **Real-time Updates**
   - WebSocket subscriptions for job status
   - Broadcast pattern for multi-client updates
   - Presence for collaborative features

---

## 💡 Development Patterns & Best Practices

### Database Patterns

#### 1. **Always Use Migrations**
```sql
-- supabase/migrations/20250119000001_add_feature.sql
-- Never modify schema directly in production
CREATE TABLE new_feature (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  -- Always add comments for documentation
  created_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE new_feature IS 'Feature description';
```

#### 2. **RLS for Security**
```sql
-- Enable RLS on all user-facing tables
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- Create policies for access control
CREATE POLICY "Users see own documents" ON documents
  FOR ALL USING (auth.uid() = user_id);
```

#### 3. **Optimistic UI Updates**
```typescript
// Update UI immediately, rollback on error
const optimisticUpdate = (job: Job) => {
  // Update local state
  setJobs(prev => [...prev, job])
  
  // Make API call
  supabase.from('jobs').insert(job)
    .then(({ error }) => {
      if (error) {
        // Rollback on failure
        setJobs(prev => prev.filter(j => j.id !== job.id))
        toast.error('Failed to create job')
      }
    })
}
```

### Edge Function Patterns

#### 1. **Environment Configuration**
```typescript
// Defensive environment variable access
const getEnvVar = (key: string, fallback?: string): string => {
  const value = Deno.env.get(key)
  if (!value && !fallback) {
    throw new Error(`Missing required env var: ${key}`)
  }
  return value || fallback!
}

const API_KEY = getEnvVar('OPENROUTER_API_KEY')
const MODEL = getEnvVar('DEFAULT_MODEL', 'google/gemini-flash-1.5')
```

#### 2. **Error Boundaries**
```typescript
// Wrap all async operations
const safeExecute = async <T>(
  fn: () => Promise<T>,
  fallback: T
): Promise<T> => {
  try {
    return await fn()
  } catch (error) {
    console.error('Execution failed:', error)
    return fallback
  }
}
```

#### 3. **Request Validation**
```typescript
// Use Zod for runtime validation
import { z } from 'npm:zod'

const RequestSchema = z.object({
  document: z.string().min(1).max(50000),
  agent_id: z.string().uuid(),
  options: z.object({
    priority: z.number().min(0).max(10).optional(),
    webhook: z.string().url().optional()
  }).optional()
})

// Validate with helpful errors
const payload = RequestSchema.parse(await req.json())
```

### Performance Optimization

#### 1. **Batch Operations**
```typescript
// Process multiple documents efficiently
const batchProcess = async (documents: Document[]) => {
  // Group by agent type
  const grouped = documents.reduce((acc, doc) => {
    const key = doc.agent_id
    acc[key] = acc[key] || []
    acc[key].push(doc)
    return acc
  }, {} as Record<string, Document[]>)

  // Process in parallel by group
  const results = await Promise.all(
    Object.entries(grouped).map(([agent, docs]) =>
      processGroup(agent, docs)
    )
  )

  return results.flat()
}
```

#### 2. **Connection Pooling**
```typescript
// Reuse database connections
import postgres from 'https://deno.land/x/postgresjs/mod.js'

// Singleton connection
let sql: ReturnType<typeof postgres>

const getDb = () => {
  if (!sql) {
    sql = postgres(Deno.env.get('DATABASE_URL')!, {
      max: 10,  // Connection pool size
      idle_timeout: 20,
      connect_timeout: 10
    })
  }
  return sql
}
```

#### 3. **Caching Strategy**
```typescript
// Multi-layer caching
class DocumentCache {
  // In-memory cache (L1)
  private memory = new Map<string, CachedResult>()
  
  // Vector similarity cache (L2)
  async findSimilar(embedding: number[]): Promise<CachedResult | null> {
    const { data } = await supabase.rpc('find_similar_documents', {
      query_embedding: embedding,
      similarity_threshold: 0.95
    })
    return data?.[0] || null
  }

  // Hash-based cache (L3)
  async findExact(hash: string): Promise<CachedResult | null> {
    const { data } = await supabase
      .from('document_embeddings')
      .select('*')
      .eq('document_hash', hash)
      .single()
    return data
  }
}
```

---

## 🚀 Local Development Setup

### Project-Specific Configuration

**Supabase Project Details:**
- **Project URL**: Configure in `.env` as `SUPABASE_URL_PROD=https://your-project-ref.supabase.co`
- **Project Reference**: Set your actual project reference from Supabase dashboard
- **Local Studio**: `http://localhost:54323`
- **Local API**: `http://localhost:54321`
- **Local Database**: `postgresql://postgres:postgres@localhost:54322/postgres`

**Configured Ports (from supabase/config.toml):**
- API Gateway (Kong): `54321`
- Database: `54322`
- Studio: `54323`
- Inbucket (Email): `54324`
- Storage: `54321/storage`
- Auth: `54321/auth`

### Prerequisites Installation

```bash
# Install Supabase CLI
npm install -g supabase

# Install Deno (for Edge Functions)
curl -fsSL https://deno.land/install.sh | sh

# Install project dependencies
npm install
```

### Environment Setup

```bash
# 1. Copy environment template
cp .env.example .env

# 2. Configure your .env with actual values:
# SUPABASE_URL_PROD=https://your-project-ref.supabase.co
# SUPABASE_PROJECT_ID=your_actual_project_id
# OPENROUTER_API_KEY=sk-or-v1-xxxxx (your key)
# OPENROUTER_REFERER=https://your-project-ref.supabase.co
# LLAMAPARSE_API_KEY=llx-xxxxx (for PDF parsing)

# 3. Start Supabase locally
npm run dev
# This starts: Postgres, Studio, Edge Functions, Realtime on configured ports

# 4. Apply database migrations
npm run db:push

# 5. Generate TypeScript types
npm run gen:types

# 6. Serve Edge Functions locally
npm run functions:serve

# 7. Open Supabase Studio
npm run studio
# Available at http://localhost:54323
```

### Development Workflow

```bash
# Terminal 1: Supabase services
npm run dev

# Terminal 2: Edge Functions with hot reload
npm run functions:serve

# Terminal 3: Run tests
npm test

# Before committing:
npm run lint        # Lint Edge Functions
npm run format      # Format code
npm run typecheck   # Type verification
```

---

## 🔍 Debugging & Monitoring

### Debugging Edge Functions

```typescript
// Enhanced logging for development
const debugLog = (stage: string, data: any) => {
  if (Deno.env.get('ENVIRONMENT') === 'development') {
    console.log(`[${new Date().toISOString()}] ${stage}:`, 
      JSON.stringify(data, null, 2))
  }
}

// Track performance
const timer = performance.now()
const result = await processDocument(doc)
debugLog('Processing time', performance.now() - timer)
```

### SQL Query Monitoring

```sql
-- Monitor slow queries
CREATE EXTENSION pg_stat_statements;

SELECT query, mean_exec_time, calls
FROM pg_stat_statements
WHERE mean_exec_time > 100  -- queries over 100ms
ORDER BY mean_exec_time DESC;

-- Check index usage
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan;  -- Find unused indexes
```

### Cost Tracking

```typescript
// Track AI costs per request
interface CostTracking {
  model: string
  input_tokens: number
  output_tokens: number
  cost_usd: number
  timestamp: Date
}

const trackCost = async (cost: CostTracking) => {
  // Store in database
  await supabase.from('cost_metrics').insert(cost)
  
  // Check budget
  const { data: total } = await supabase
    .from('cost_metrics')
    .select('cost_usd.sum()')
    .gte('timestamp', startOfMonth())
    .single()
  
  if (total.sum > MONTHLY_BUDGET) {
    await alertAdmins('Budget exceeded!')
  }
}
```

---

## 🎓 Learning Resources

### Supabase Deep Dives
- [Supabase Architecture](https://supabase.com/docs/guides/getting-started/architecture)
- [Edge Functions Guide](https://supabase.com/docs/guides/functions)
- [Vector Embeddings & pgvector](https://supabase.com/docs/guides/ai/vector-embeddings)
- [Realtime Subscriptions](https://supabase.com/docs/guides/realtime)

### Deno & TypeScript
- [Deno Manual](https://deno.land/manual)
- [TypeScript Deep Dive](https://basarat.gitbook.io/typescript)
- [Deno KV for Caching](https://deno.com/kv)

### PostgreSQL & Extensions
- [pgvector Documentation](https://github.com/pgvector/pgvector)
- [PostgreSQL Patterns](https://www.postgresql.org/docs/current/tutorial.html)
- [HNSW Index Tuning](https://github.com/pgvector/pgvector#hnsw)

### AI & Embeddings
- [OpenRouter Models](https://openrouter.ai/models)
- [OpenAI Embeddings](https://platform.openai.com/docs/guides/embeddings)
- [Semantic Search Best Practices](https://www.pinecone.io/learn/vector-search/)

---

## 🏁 Quick Reference

### Common Commands
```bash
# Database
npm run db:reset        # Reset database (includes seed data)
npm run db:push         # Apply migrations to local/remote
npm run gen:types       # Generate types to ./types/database.types.ts

# Development
npm run dev             # Start all services
npm run functions:serve # Serve Edge Functions
npm run studio          # Open database UI

# Testing & Quality
npm test                # Run all tests
npm run lint            # Lint code
npm run format          # Format code
npm run typecheck       # Check types

# Deployment
npm run functions:deploy # Deploy Edge Functions
npm run push            # Push to production
```

### Key Environment Variables
```bash
# Required (Production Values)
SUPABASE_URL_PROD=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY_PROD       # Public API key (from dashboard)
SUPABASE_SERVICE_ROLE_KEY_PROD # Admin key (from dashboard)
SUPABASE_PROJECT_ID          # Project ID from dashboard
OPENROUTER_API_KEY          # sk-or-v1-xxxxx (your key)

# Configured Defaults
OPENROUTER_REFERER=https://your-project-ref.supabase.co
DEFAULT_MODEL_TIER=balanced  # Using balanced tier by default
AUTO_SELECT_MODEL=true       # AI selects optimal model
MAX_COST_PER_DOCUMENT=0.10  # $0.10 max per doc
OPENROUTER_BUDGET_LIMIT=100.00  # $100/month budget

# Optional but recommended
LLAMAPARSE_API_KEY      # llx-xxxxx for advanced PDF parsing
```

### Performance Targets (Per PRD Requirements)
- **API Response**: < 500ms for non-processing endpoints (NFR4)
- **Document Processing**: < 5s for standard docs (NFR1)
- **Platform Uptime**: 99.5% with fallback systems (NFR2)
- **Extraction Accuracy**: >95% for structured documents (NFR5)
- **Profit Margins**: 60%+ through intelligent model routing (NFR3)
- **Scalability**: 1000+ API calls per customer per month (NFR6)

---

## 💪 Summary

This tech stack provides:

1. **Scalability**: Serverless Edge Functions + PostgreSQL
2. **Cost Efficiency**: Smart model routing + caching
3. **Developer Experience**: TypeScript + auto-generated APIs
4. **Performance**: Vector search + optimized queries
5. **Reliability**: Queue system + retry logic
6. **Security**: RLS + JWT auth + input validation

The combination of Supabase's integrated platform with AI processing through OpenRouter creates a powerful, production-ready document processing pipeline that scales from prototype to enterprise.

---

*Last Updated: September 21, 2025*
*Architecture Version: 2.1 - Aligned with PRD v1.0*
*Maintained by: BMAD Development Team*