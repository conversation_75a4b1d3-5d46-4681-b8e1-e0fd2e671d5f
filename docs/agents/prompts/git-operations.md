# Git Operations Command Prompts

## /git-commit [target]

**Prompt:**
```
I need you to create a git commit for the specified changes. Follow these steps:

1. **Analyze changes:**
   - Run git status to see all modified/added/deleted files
   - Run git diff to understand the nature of changes
   - If target specified: Focus on changes related to that file/folder/issue

2. **Generate commit message:**
   - Follow conventional commit format: type(scope): description
   - Types: feat, fix, docs, style, refactor, test, chore
   - Scope: component/module affected (auth, api, ui, etc.)
   - Description: concise summary of what changed (imperative mood)
   - If issue specified: Include "Closes #123" or "Refs #123"

3. **Stage appropriate files:**
   - If target is file: Stage only that file
   - If target is folder: Stage all changes in that folder
   - If target is issue: Stage files related to the issue
   - If no target: Review all changes and stage appropriately

4. **Create commit:**
   - Stage the files using git add
   - Create commit with generated message
   - Confirm commit was created successfully

**Examples:**
- `/git-commit src/auth/` → Commit all auth-related changes
- `/git-commit #45` → Commit changes that resolve issue #45
- `/git-commit` → Analyze all changes and create appropriate commit
```

## /git-branch [action] [name]

**Prompt:**
```
I need you to perform git branch operations. Follow these steps:

1. **Understand the action:**
   - create: Create new branch from current branch
   - switch/checkout: Switch to existing branch
   - delete: Delete a branch (safely)
   - list: Show all branches with status

2. **Branch naming conventions:**
   - For new branches: Use format "type/description" or "story/epic-X-story-Y-description"
   - Check project's branch naming conventions
   - If related to issue: Include issue number in branch name

3. **Execute branch operation:**
   - For create: `git checkout -b [branch-name]`
   - For switch: `git checkout [branch-name]`
   - For delete: Verify branch is merged before `git branch -d [branch-name]`
   - For list: `git branch -a` with status indicators

4. **Report results:**
   - Confirm branch operation completed
   - Show current branch status
   - For new branches: Confirm clean working directory
   - For deletions: Confirm branch was safely removed

**Examples:**
- `/git-branch create feature/user-authentication`
- `/git-branch switch main`
- `/git-branch delete old-feature`
```

## /git-push [target]

**Prompt:**
```
I need you to push changes to the remote repository. Follow these steps:

1. **Pre-push checks:**
   - Verify current branch and its tracking status
   - Check if there are uncommitted changes
   - Run git status to confirm clean state or pending commits
   - Check if branch has upstream set

2. **Determine push strategy:**
   - If target is branch name: Push that specific branch
   - If target is issue: Push branch related to that issue
   - If no target: Push current branch
   - Check if this is first push (needs -u origin branch-name)

3. **Execute push:**
   - If first push: `git push -u origin [branch-name]`
   - If regular push: `git push` or `git push origin [branch-name]`
   - Handle any conflicts or authentication issues

4. **Report results:**
   - Confirm push was successful
   - Show remote branch status
   - If new branch: Provide link to create PR if applicable
   - Alert if there are any warnings or conflicts
```

## /git-pull [target]

**Prompt:**
```
I need you to pull latest changes from remote repository. Follow these steps:

1. **Pre-pull analysis:**
   - Check current branch and its remote tracking
   - Verify working directory is clean or stash changes if needed
   - Check if there are any merge conflicts from previous operations

2. **Determine pull strategy:**
   - If target is branch: Pull from that specific branch
   - If no target: Pull current branch from its upstream
   - Check if fast-forward is possible or merge is needed

3. **Execute pull:**
   - Run git pull origin [branch] or git pull
   - Handle any merge conflicts if they occur
   - If conflicts: Show conflicted files and guide resolution

4. **Report results:**
   - Show summary of changes pulled
   - List any new commits received
   - If conflicts occurred: Provide conflict resolution guidance
   - Confirm working directory status after pull
```

## /create-pr [title] [target-branch]

**Prompt:**
```
I need you to create a pull request. Follow these steps:

1. **Analyze current state:**
   - Verify current branch has commits to merge
   - Check if current branch is pushed to remote
   - Identify target branch (usually main/master if not specified)
   - Review commits that will be included in PR

2. **Generate PR content:**
   - Create descriptive title (use provided title or generate from commits)
   - Generate PR description including:
     - Summary of changes
     - Related issue numbers (Closes #123)
     - Testing instructions
     - Any breaking changes or special considerations

3. **Use appropriate tool:**
   - If GitHub CLI available: Use `gh pr create`
   - Otherwise: Provide GitHub web URL to create PR manually
   - Include all necessary flags and options

4. **Report PR creation:**
   - Provide PR URL once created
   - Show PR summary and reviewers (if auto-assigned)
   - Mention any required checks or approvals needed
   - Confirm issue linking if applicable

**Examples:**
- `/create-pr "Add user authentication" main`
- `/create-pr` → Auto-generate title and use default target branch
```