# Code Quality Command Prompts

## /lint [target]

**Prompt:**
```
I need you to run linting on the specified target. Follow these steps:

1. **Identify linting tools:**
   - Check package.json for lint scripts and dependencies (eslint, prettier, etc.)
   - Look for config files (.eslintrc.*, prettier.config.*, etc.)
   - Identify the lint command and any custom scripts

2. **Target analysis:**
   - If file: Lint the specific file and its dependencies
   - If folder: Lint all lintable files in the folder recursively
   - If issue (#123): Read issue to identify relevant files to lint
   - If no target: Lint entire project according to project settings

3. **Execute linting:**
   - Run the appropriate lint command with proper targeting
   - Capture both errors and warnings
   - Try auto-fix options if available (--fix flag)

4. **Report results:**
   - Show linting errors grouped by file and rule type
   - For each error: show line number, rule name, and suggested fix
   - If auto-fixes were applied, show what was changed
   - Provide summary of remaining manual fixes needed

**Examples:**
- `/lint src/components/` → Lint all files in components folder
- `/lint src/auth/login.ts` → Lint specific file
- `/lint #67` → Lint files related to issue #67
```

## /format [target]

**Prompt:**
```
I need you to format code according to project standards. Follow these steps:

1. **Identify formatting tools:**
   - Check for prettier, eslint --fix, or other formatting tools
   - Look for formatting config files (prettier.config.js, .prettierrc, etc.)
   - Check package.json for format scripts

2. **Target formatting:**
   - If file: Format the specific file
   - If folder: Format all supported files in folder
   - If issue: Format files mentioned in the issue
   - If no target: Format entire project

3. **Execute formatting:**
   - Run prettier or equivalent formatting tool
   - Apply project-specific formatting rules
   - Handle different file types appropriately (.ts, .js, .json, .md, etc.)

4. **Report changes:**
   - Show which files were modified
   - Highlight any formatting conflicts or errors
   - Confirm all files now meet formatting standards
```

## /type-check [target]

**Prompt:**
```
I need you to run TypeScript type checking on the target. Follow these steps:

1. **Setup type checking:**
   - Locate tsconfig.json and understand project TypeScript setup
   - Check for multiple TypeScript configs (tsconfig.build.json, etc.)
   - Identify TypeScript version and compiler options

2. **Target analysis:**
   - If file: Type check the file and its dependencies
   - If folder: Type check all TypeScript files in folder
   - If issue: Type check files related to the issue
   - If no target: Run full project type check

3. **Execute type checking:**
   - Run tsc --noEmit or equivalent type-only compilation
   - Capture all type errors with file locations and descriptions
   - Check for missing type definitions

4. **Report type issues:**
   - Group errors by type (missing types, type mismatches, etc.)
   - Show file locations and specific error messages
   - Suggest fixes for common type errors
   - Identify any missing @types packages needed
```

## /security-scan [target]

**Prompt:**
```
I need you to run security scanning on the target. Follow these steps:

1. **Identify security tools:**
   - Check for npm audit, snyk, or other security scanning tools
   - Look for security-related scripts in package.json
   - Check for .snyk files or security configurations

2. **Run security scans:**
   - Execute npm audit or yarn audit for dependency vulnerabilities
   - Run any configured security scanning tools
   - Check for hardcoded secrets or sensitive data in code

3. **Analyze target-specific security:**
   - If file: Scan for security issues in that specific file
   - If folder: Check all files in folder for security patterns
   - If issue: Focus on security aspects of the issue requirements

4. **Report security findings:**
   - List all vulnerabilities with severity levels
   - Show affected packages and suggested remediation
   - Highlight any critical security issues requiring immediate attention
   - Provide upgrade paths for vulnerable dependencies
```