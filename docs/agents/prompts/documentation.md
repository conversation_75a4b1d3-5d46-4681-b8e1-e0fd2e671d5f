# Documentation Command Prompts

## /generate-docs [target]

**Prompt:**
```
I need you to generate documentation for the specified target. Follow these steps:

1. **Analyze target scope:**
   - If file: Generate docs for functions, classes, and exports in that file
   - If folder: Create overview docs for the module/package
   - If issue: Generate docs for features implemented in that issue
   - If no target: Generate high-level project documentation

2. **Code analysis:**
   - Read and understand the code structure and functionality
   - Identify public APIs, interfaces, and main functionality
   - Extract existing comments and docstrings
   - Understand parameter types, return values, and side effects

3. **Generate appropriate documentation:**
   - For functions: Parameter descriptions, return values, examples, exceptions
   - For classes: Constructor parameters, public methods, usage examples
   - For modules: Overview, main exports, usage patterns, dependencies
   - For APIs: Endpoints, request/response formats, authentication

4. **Format documentation:**
   - Use appropriate format (JSDoc, TypeDoc, Markdown, etc.)
   - Include code examples and usage patterns
   - Add cross-references to related functions/modules
   - Ensure documentation follows project conventions

**Examples:**
- `/generate-docs src/auth/login.ts` → Generate docs for login functionality
- `/generate-docs src/api/` → Generate module documentation for API folder
```

## /update-readme [section]

**Prompt:**
```
I need you to update the project README file. Follow these steps:

1. **Analyze current README:**
   - Read existing README.md to understand current structure
   - Identify outdated or missing sections
   - Check for broken links or incorrect information

2. **Determine updates needed:**
   - If section specified: Focus on that specific section
   - If no section: Identify what needs updating based on recent changes
   - Check package.json for new dependencies or scripts
   - Verify installation and setup instructions are current

3. **Generate updated content:**
   - Update installation instructions if dependencies changed
   - Refresh usage examples with current API
   - Update feature lists with new functionality
   - Fix any broken links or references
   - Add new sections if needed (Contributing, API Reference, etc.)

4. **Maintain README quality:**
   - Keep language clear and beginner-friendly
   - Ensure examples are working and tested
   - Add badges for build status, coverage, etc. if missing
   - Follow README best practices for the project type

**Examples:**
- `/update-readme installation` → Update installation section only
- `/update-readme` → Comprehensive README review and update
```

## /api-docs [target]

**Prompt:**
```
I need you to generate API documentation for the target. Follow these steps:

1. **Identify API endpoints:**
   - If target is file: Extract API routes/endpoints from that file
   - If target is folder: Find all API definitions in the folder
   - If target is issue: Focus on API changes in that issue
   - Parse route definitions, middleware, and handlers

2. **Extract API details:**
   - HTTP methods (GET, POST, PUT, DELETE, etc.)
   - URL paths and parameters (path params, query params)
   - Request body schemas and content types
   - Response schemas and status codes
   - Authentication/authorization requirements

3. **Generate comprehensive API docs:**
   - Use OpenAPI/Swagger format if project supports it
   - Include request/response examples with real data
   - Document error responses and status codes
   - Add authentication examples (headers, tokens, etc.)
   - Include rate limiting and usage guidelines

4. **Organize and format:**
   - Group endpoints by functionality/resource
   - Provide clear endpoint descriptions and use cases
   - Include code examples in multiple languages if applicable
   - Add links to related documentation or tutorials

**Examples:**
- `/api-docs src/routes/users.ts` → Generate docs for user API endpoints
- `/api-docs src/api/` → Generate complete API documentation
```

## /changelog [version]

**Prompt:**
```
I need you to generate or update the project changelog. Follow these steps:

1. **Analyze recent changes:**
   - Review git commits since last changelog entry
   - Group commits by type (features, fixes, breaking changes, etc.)
   - Extract meaningful changes (ignore internal refactoring unless significant)
   - Check closed issues and merged PRs for context

2. **Categorize changes:**
   - **Added**: New features and functionality
   - **Changed**: Changes in existing functionality
   - **Deprecated**: Soon-to-be removed features
   - **Removed**: Removed features
   - **Fixed**: Bug fixes
   - **Security**: Security improvements

3. **Generate changelog entry:**
   - If version specified: Create entry for that version
   - If no version: Create entry for next logical version
   - Use semantic versioning principles (major.minor.patch)
   - Include date and version number in header
   - Write user-facing descriptions (not technical implementation details)

4. **Format and update:**
   - Follow Keep a Changelog format
   - Add entry to CHANGELOG.md (create if doesn't exist)
   - Include links to issues and PRs where relevant
   - Highlight breaking changes prominently

**Examples:**
- `/changelog 1.2.0` → Generate changelog for version 1.2.0
- `/changelog` → Generate changelog for next version based on recent changes
```

## /code-comments [target]

**Prompt:**
```
I need you to add or improve code comments for the target. Follow these steps:

1. **Analyze code complexity:**
   - Identify complex algorithms, business logic, or non-obvious code
   - Find areas lacking documentation or explanation
   - Locate public APIs that need better documentation
   - Check for TODO/FIXME comments that need attention

2. **Determine comment strategy:**
   - For functions: Add JSDoc/docstring with parameters and return values
   - For complex logic: Add inline comments explaining the "why"
   - For public APIs: Comprehensive documentation with examples
   - For classes: Constructor documentation and usage examples

3. **Write effective comments:**
   - Explain "why" not "what" for business logic
   - Use clear, concise language
   - Include examples for complex usage patterns
   - Add type information where helpful
   - Follow project's comment style and conventions

4. **Validate and format:**
   - Ensure comments add value and aren't stating the obvious
   - Check that examples in comments are accurate and working
   - Format according to documentation tool requirements (JSDoc, etc.)
   - Update related documentation if comments reveal gaps

**Examples:**
- `/code-comments src/utils/crypto.ts` → Add comments to crypto utility functions
- `/code-comments src/auth/` → Improve comments throughout auth module
```