# AI Command Prompts

This folder contains detailed implementation prompts for AI assistants to execute common development commands. Each prompt provides step-by-step instructions that any AI assistant (<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, etc.) can follow to perform the requested operation.

## How to Use These Prompts

1. **Find the relevant command** in the category files below
2. **Copy the entire prompt** including the steps and examples
3. **Paste it to your AI assistant** along with any specific target (file, folder, issue)
4. **The AI will follow the structured steps** to complete the operation

## Available Command Categories

### [Testing Commands](./testing.md)
- `/run-tests` - Run tests for specific targets
- `/test-unit` - Run unit tests only
- `/test-integration` - Run integration tests with proper setup

### [Code Quality Commands](./code-quality.md)
- `/lint` - Run linting with auto-fix options
- `/format` - Format code according to project standards
- `/type-check` - Run TypeScript type checking
- `/security-scan` - Scan for security vulnerabilities

### [Git Operations Commands](./git-operations.md)
- `/git-commit` - Create conventional commits with proper messages
- `/git-branch` - Manage branches (create, switch, delete, list)
- `/git-push` - Push changes with proper upstream handling
- `/git-pull` - Pull latest changes with conflict resolution
- `/create-pr` - Create pull requests with auto-generated descriptions

### [Project Management Commands](./project-management.md)
- `/analyze-issue` - Comprehensive issue analysis with implementation planning
- `/work-on-issue` - Start working on GitHub issues with proper setup
- `/update-progress` - Update issue progress and project board status
- `/estimate-effort` - Generate detailed effort estimates for tasks
- `/close-issue` - Properly close issues with verification

### [Documentation Commands](./documentation.md)
- `/generate-docs` - Generate comprehensive code documentation
- `/update-readme` - Update README with current project state
- `/api-docs` - Generate API documentation from code
- `/changelog` - Create changelog entries from git history
- `/code-comments` - Add or improve inline code comments

## Command Structure

All commands follow this general pattern:

```
/command-name [target]
```

Where `target` can be:
- **File path**: `src/auth/login.ts`
- **Folder path**: `src/components/`
- **Issue number**: `#123`
- **Pattern**: `*.test.ts` or `src/**/*.spec.js`
- **No target**: Command applies to entire project

## Example Usage

### With Claude
```
/run-tests src/auth/

[Paste the /run-tests prompt from testing.md here]
```

### With Cursor
```
/lint #45

[Paste the /lint prompt from code-quality.md here]
```

### With Any AI Assistant
```
/work-on-issue 67

[Paste the /work-on-issue prompt from project-management.md here]
```

## Benefits

- **Consistency**: Same commands work across different AI assistants
- **Reliability**: Detailed prompts ensure proper execution
- **Learning**: Prompts teach best practices for development workflows
- **Efficiency**: Reduces cognitive overhead for common tasks
- **Quality**: Built-in checks and validations prevent errors

## Customization

You can customize these prompts for your specific project by:

1. **Modifying tool detection logic** for your specific build tools
2. **Adding project-specific conventions** (branch naming, commit formats)
3. **Including custom scripts** from your package.json
4. **Adapting for your testing framework** (Jest, Vitest, etc.)
5. **Adding project-specific quality gates** and checks