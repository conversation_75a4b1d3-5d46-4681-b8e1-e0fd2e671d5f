# Project Management Command Prompts

## /analyze-issue [issue-number]

**Prompt:**
```
I need you to analyze a GitHub issue and provide comprehensive context. Follow these steps:

1. **Fetch issue details:**
   - Use GitHub CLI or API to get full issue information
   - Extract title, description, labels, assignees, and comments
   - Identify linked PRs, related issues, or project board status
   - Note issue state (open/closed) and timeline

2. **Parse technical requirements:**
   - Break down the issue description into specific technical tasks
   - Identify affected code areas, files, or modules
   - Extract acceptance criteria or definition of done
   - Note any technical constraints or dependencies

3. **Analyze codebase context:**
   - Search for related code files mentioned in the issue
   - Find existing implementations that might need modification
   - Identify test files that need updates
   - Check for related documentation that needs changes

4. **Provide actionable summary:**
   - List specific files/folders that need modification
   - Suggest implementation approach and order of tasks
   - Identify potential risks or complexity areas
   - Recommend testing strategy for the changes

**Examples:**
- `/analyze-issue 45` → Full analysis of issue #45 with implementation plan
```

## /work-on-issue [issue-number]

**Prompt:**
```
I need you to start working on a specific GitHub issue. Follow these steps:

1. **Issue context gathering:**
   - Fetch and analyze the complete issue (use /analyze-issue workflow)
   - Understand requirements, acceptance criteria, and scope
   - Identify all affected code areas and dependencies

2. **Branch setup:**
   - Create appropriately named branch following project conventions
   - Use format: story/epic-X-story-Y-description or feature/issue-45-description
   - Switch to the new branch and confirm clean state

3. **Implementation planning:**
   - Break down the issue into specific coding tasks
   - Identify files to create, modify, or delete
   - Plan the order of implementation (dependencies first)
   - Consider testing requirements and documentation updates

4. **Begin implementation:**
   - Start with the first task in your implementation plan
   - Make incremental commits with clear messages
   - Test changes as you implement them
   - Keep the issue updated with progress comments

**Examples:**
- `/work-on-issue 67` → Full workflow from analysis to implementation start
```

## /update-progress [issue-number] [status]

**Prompt:**
```
I need you to update progress on a GitHub issue. Follow these steps:

1. **Gather current state:**
   - Check current branch and recent commits
   - Review what has been implemented so far
   - Identify remaining tasks from the original issue requirements

2. **Determine progress status:**
   - If status provided: Use that status (in-progress, testing, review-ready, blocked)
   - If no status: Analyze commits and determine current state automatically
   - Calculate percentage completion based on acceptance criteria

3. **Update issue:**
   - Add progress comment to the GitHub issue
   - Include summary of completed work
   - List remaining tasks with estimated effort
   - Mention any blockers or challenges encountered
   - Update labels if appropriate (in-progress, needs-review, etc.)

4. **Update project board:**
   - If using GitHub Projects, move issue to appropriate column
   - Update any custom fields (progress, effort estimate, etc.)
   - Notify relevant stakeholders if major milestone reached

**Examples:**
- `/update-progress 45 testing` → Update issue #45 status to testing phase
- `/update-progress 67` → Auto-determine and update progress on issue #67
```

## /estimate-effort [target]

**Prompt:**
```
I need you to estimate development effort for the target. Follow these steps:

1. **Analyze scope:**
   - If issue: Parse requirements and acceptance criteria thoroughly
   - If file/folder: Analyze complexity of changes needed in that area
   - If feature description: Break down into component tasks

2. **Technical complexity assessment:**
   - Identify complexity factors (new technology, refactoring, testing, etc.)
   - Check for external dependencies or integrations required
   - Assess impact on existing code and potential for breaking changes
   - Consider documentation and testing requirements

3. **Generate effort estimate:**
   - Break down into subtasks with individual estimates
   - Use story points or hour estimates based on project preference
   - Include time for code review, testing, and documentation
   - Add buffer for unexpected complications

4. **Provide detailed breakdown:**
   - Show effort estimate for each subtask
   - Explain complexity factors that drive the estimate
   - Identify highest risk/complexity areas
   - Suggest ways to reduce effort or mitigate risks

**Examples:**
- `/estimate-effort #78` → Detailed effort estimate for issue #78
- `/estimate-effort src/auth/` → Estimate effort for auth module changes
```

## /close-issue [issue-number] [resolution]

**Prompt:**
```
I need you to properly close a GitHub issue. Follow these steps:

1. **Verify completion:**
   - Check that all acceptance criteria have been met
   - Verify related PR has been merged (if applicable)
   - Confirm all tests are passing
   - Ensure documentation has been updated

2. **Final validation:**
   - Run relevant tests to confirm functionality works
   - Check that no regressions were introduced
   - Verify deployment/release status if applicable

3. **Close issue properly:**
   - Add final comment summarizing what was completed
   - Include links to related PRs, commits, or documentation
   - Thank contributors and reviewers
   - Close the issue with appropriate reason (completed, duplicate, won't fix, etc.)

4. **Project cleanup:**
   - Update project board status to "Done"
   - Archive related branches if appropriate
   - Update any related issues or epics
   - Notify stakeholders of completion

**Examples:**
- `/close-issue 45 completed` → Close issue #45 as completed
- `/close-issue 67 duplicate` → Close issue #67 as duplicate
```