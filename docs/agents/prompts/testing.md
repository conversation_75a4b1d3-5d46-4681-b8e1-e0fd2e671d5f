# Testing Command Prompts

## /run-tests [target]

**Prompt:**
```
I need you to run tests for the specified target. Follow these steps:

1. **Analyze the target:**
   - If it's a file: Find related test files (*.test.*, *.spec.*, __tests__/*)
   - If it's a folder: Find all test files within that folder
   - If it's an issue number (#123): Read the issue to understand what code areas need testing
   - If no target: Run all tests

2. **Determine test framework:**
   - Check package.json for test scripts and dependencies
   - Look for jest.config.js, vitest.config.js, or similar config files
   - Identify the test command (npm test, bun test, yarn test, etc.)

3. **Execute tests:**
   - Run the appropriate test command with proper file/folder targeting
   - For specific files: Use framework-specific file patterns
   - Capture both stdout and stderr

4. **Report results:**
   - Show test summary (passed/failed/skipped)
   - For failures: Show specific error messages and file locations
   - Suggest fixes for common test failures
   - If all pass: Confirm coverage and completion

**Examples:**
- `/run-tests src/auth/` → Run all tests for auth module
- `/run-tests src/utils/validation.ts` → Run tests for validation utility
- `/run-tests #45` → Run tests related to issue #45
```

## /test-unit [target]

**Prompt:**
```
I need you to specifically run unit tests for the target. Follow these steps:

1. **Identify unit tests:**
   - Look for *.unit.test.*, *.unit.spec.* files
   - Check test file naming conventions in the project
   - If target is an issue, identify the specific functions/modules to unit test

2. **Run isolated unit tests:**
   - Use test framework flags to run only unit tests (exclude integration/e2e)
   - Target specific test suites or files
   - Ensure no external dependencies are called

3. **Analyze coverage:**
   - Check unit test coverage for the target code
   - Identify untested code paths
   - Suggest additional unit tests if coverage is low

4. **Report:**
   - Show unit test results separately from integration tests
   - Highlight any missing unit test coverage
   - Suggest mock implementations for external dependencies
```

## /test-integration [target]

**Prompt:**
```
I need you to run integration tests for the target. Follow these steps:

1. **Setup integration environment:**
   - Check if database/external services need to be running
   - Look for docker-compose.test.yml or similar setup files
   - Verify test environment configuration

2. **Identify integration tests:**
   - Look for *.integration.test.*, *.int.test.* files
   - Find tests that interact with databases, APIs, or file systems
   - Check test folders like __tests__/integration/

3. **Run integration tests:**
   - Start necessary services (databases, mock servers)
   - Execute integration test suite with proper setup/teardown
   - Monitor for external dependency failures

4. **Clean up and report:**
   - Ensure test artifacts are cleaned up
   - Report on service interactions and data flow
   - Identify any external service reliability issues
```