# AI Assistant Commands for IDP Platform

*Last Updated: September 21, 2025*  
*Version: 1.0*  
*Compatible with: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, GitHub Copilot, and other AI coding assistants*

## 🎯 Overview

This document provides a comprehensive set of commands that work with files, folders, and GitHub issues. These commands are designed to be flexible and work with any AI coding assistant by providing specific targets for operations.

**Command Pattern**: `/command [target]` where target can be:
- **File**: `src/utils/auth.ts`
- **Folder**: `supabase/functions/auth/`
- **Issue**: `#123`
- **Pattern**: `src/**/*.test.ts`
- **Feature**: `auth-system`

---

## 🧪 Testing & Quality Commands

### Test Execution
```
/run-tests [file/folder/issue/pattern]
Run tests for specific targets with intelligent test discovery.

Examples:
/run-tests src/utils/auth.test.ts
/run-tests supabase/functions/auth/
/run-tests #123
/run-tests src/**/*.test.ts
/run-tests auth-system

What it does:
- Analyzes target and finds relevant test files
- Runs appropriate test command (bun test, deno test, etc.)
- Shows coverage and performance metrics
- Reports failures with actionable guidance
```

```
/test-unit [file/folder/pattern]
Run unit tests for specific code with coverage analysis.

Examples:
/test-unit src/components/auth/
/test-unit utils/validation.ts
/test-unit **/*auth*.test.ts

What it does:
- Finds and runs unit tests for target
- Generates coverage report
- Identifies missing test cases
- Suggests test improvements
```

```
/test-integration [feature/issue/endpoint]
Run integration tests for features, stories, or API endpoints.

Examples:
/test-integration auth-flow
/test-integration #123
/test-integration /api/v1/extract
/test-integration document-processing

What it does:
- Maps feature to integration test files
- Runs end-to-end API tests
- Validates against story acceptance criteria
- Tests service interactions
```

```
/test-watch [pattern/folder]
Start test watcher for continuous feedback during development.

Examples:
/test-watch src/auth/
/test-watch **/*.test.ts
/test-watch supabase/functions/extract/

What it does:
- Monitors file changes in target
- Automatically runs relevant tests
- Provides real-time feedback
- Shows coverage changes
```

```
/test-performance [endpoint/file/feature]
Run performance tests and benchmarks for specific targets.

Examples:
/test-performance /api/v1/extract
/test-performance supabase/functions/auth/index.ts
/test-performance document-processing

What it does:
- Executes performance benchmarks
- Validates against SLA requirements (<5s processing, <500ms API)
- Measures memory usage and throughput
- Compares against historical performance
```

### Code Quality & Linting
```
/lint [file/folder/issue]
Run linter on specific targets with intelligent error reporting.

Examples:
/lint supabase/functions/
/lint src/utils/validation.ts
/lint #123
/lint .

What it does:
- Runs ESLint, Deno lint, or appropriate linter
- Groups errors by type and severity
- Suggests auto-fixes vs manual fixes
- Shows impact on story/issue code
```

```
/lint-fix [file/folder]
Run linter with auto-fix enabled for specific targets.

Examples:
/lint-fix src/components/
/lint-fix supabase/functions/auth/
/lint-fix package.json

What it does:
- Applies automatic lint fixes
- Reports what was fixed vs what needs manual attention
- Validates fixes don't break functionality
- Commits changes if requested
```

```
/format [file/folder/pattern]
Format code according to project standards.

Examples:
/format src/
/format supabase/functions/extract/index.ts
/format **/*.ts
/format .

What it does:
- Applies Prettier, Deno fmt, or project formatter
- Maintains consistent code style
- Preserves functionality while improving readability
- Shows formatting statistics
```

```
/type-check [file/folder/issue]
Run TypeScript type checking with detailed analysis.

Examples:
/type-check src/utils/
/type-check supabase/functions/auth/
/type-check #123

What it does:
- Runs tsc --noEmit or equivalent
- Analyzes type errors by category
- Suggests fixes for common type issues
- Validates against architecture requirements
```

---

## 🔧 Development & Build Commands

### Code Analysis
```
/analyze [file/folder/issue]
Perform comprehensive code analysis and quality assessment.

Examples:
/analyze src/auth/
/analyze supabase/functions/extract/index.ts
/analyze #123

What it does:
- Code complexity analysis
- Security vulnerability scanning
- Performance bottleneck identification
- Architecture compliance checking
- Technical debt assessment
```

```
/deps-check [file/folder]
Analyze dependencies, vulnerabilities, and update recommendations.

Examples:
/deps-check package.json
/deps-check supabase/functions/deno.json
/deps-check .

What it does:
- Scans for known vulnerabilities
- Identifies outdated dependencies
- Suggests compatible updates
- Analyzes dependency tree conflicts
```

```
/build [target/issue]
Build specific parts of the project or issue-related code.

Examples:
/build supabase/functions/auth/
/build #123
/build .

What it does:
- Runs appropriate build command
- Validates build artifacts
- Reports build errors with context
- Measures build performance
```

### Documentation Generation
```
/doc-generate [file/folder/feature]
Generate documentation for specific code or features.

Examples:
/doc-generate src/utils/auth.ts
/doc-generate supabase/functions/extract/
/doc-generate auth-system

What it does:
- Generates JSDoc/TSDoc comments
- Creates README sections for features
- Documents API endpoints and schemas
- Updates architecture documentation
```

```
/doc-api [endpoint/file]
Generate API documentation for specific endpoints or function files.

Examples:
/doc-api /api/v1/extract
/doc-api supabase/functions/auth/index.ts

What it does:
- Creates OpenAPI specifications
- Documents request/response schemas
- Generates usage examples
- Updates API documentation files
```

---

## 📋 Issue & Story Management Commands

### Issue Analysis
```
/analyze-issue [issue-number]
Comprehensive analysis of GitHub issue requirements and impact.

Examples:
/analyze-issue #123
/analyze-issue #456

What it does:
- Parses issue requirements and acceptance criteria
- Identifies affected files and components
- Analyzes dependencies and blockers
- Estimates effort and complexity
- Suggests implementation approach
```

```
/code-for-issue [issue-number]
Find all code files related to a specific GitHub issue.

Examples:
/code-for-issue #123
/code-for-issue #456

What it does:
- Searches for issue references in code
- Identifies files modified in related PRs
- Maps issue to affected components
- Shows related test files
```

```
/test-issue [issue-number]
Run all tests related to a specific GitHub issue.

Examples:
/test-issue #123
/test-issue #456

What it does:
- Finds tests for issue-related code
- Runs story-specific test scenarios
- Validates acceptance criteria
- Reports test coverage for issue scope
```

### Story Validation
```
/validate-story [issue-number]
Validate story implementation against acceptance criteria.

Examples:
/validate-story #123
/validate-story #456

What it does:
- Checks each acceptance criterion
- Runs story-specific tests
- Validates business requirements
- Reports completion status
- Identifies missing requirements
```

```
/story-impact [issue-number]
Analyze the impact of implementing a specific story.

Examples:
/story-impact #123
/story-impact #456

What it does:
- Identifies affected files and components
- Analyzes breaking changes
- Estimates performance impact
- Reviews security implications
- Assesses customer impact
```

---

## 🚀 Deployment & Environment Commands

### Environment Management
```
/env-start [service/component]
Start development environment or specific services.

Examples:
/env-start
/env-start supabase
/env-start auth-service

What it does:
- Starts Supabase services (DB, Edge Functions, Studio)
- Validates environment configuration
- Checks service health and connectivity
- Updates TypeScript types if needed
```

```
/env-check [service/config]
Check environment health and configuration.

Examples:
/env-check
/env-check supabase
/env-check .env

What it does:
- Validates all service connections
- Checks environment variables
- Tests database connectivity
- Verifies API endpoints
- Reports configuration issues
```

```
/deploy-check [target/issue]
Validate deployment readiness for specific targets.

Examples:
/deploy-check staging
/deploy-check #123
/deploy-check auth-feature

What it does:
- Runs pre-deployment quality checks
- Validates environment compatibility
- Checks for breaking changes
- Verifies monitoring and alerting
- Assesses rollback procedures
```

### Database Operations
```
/db-migrate [migration/issue]
Run database migrations or create migrations for issues.

Examples:
/db-migrate
/db-migrate #123
/db-migrate auth-tables

What it does:
- Applies pending migrations
- Creates new migrations for story requirements
- Validates schema changes
- Tests migration rollback procedures
```

```
/db-reset [environment]
Reset database to clean state for testing.

Examples:
/db-reset
/db-reset local
/db-reset test

What it does:
- Resets database to clean state
- Applies all migrations
- Runs seed data scripts
- Updates TypeScript types
- Validates schema integrity
```

---

## 🔍 Code Search & Navigation Commands

### Code Discovery
```
/find-code [pattern/feature]
Search for code patterns, functions, or features across the codebase.

Examples:
/find-code "API key validation"
/find-code auth functions
/find-code error handling

What it does:
- Searches across all project files
- Finds functions, classes, and patterns
- Shows usage examples and context
- Identifies related code and tests
```

```
/find-usage [function/variable/file]
Find all usages of specific functions, variables, or imports.

Examples:
/find-usage validateApiKey
/find-usage src/utils/auth.ts
/find-usage createClient

What it does:
- Searches for function/variable references
- Shows import/export relationships
- Identifies potential breaking changes
- Maps dependency relationships
```

```
/find-similar [file/function]
Find similar code patterns or implementations.

Examples:
/find-similar src/auth/validate.ts
/find-similar validateInput

What it does:
- Identifies similar code patterns
- Finds potential code duplication
- Suggests refactoring opportunities
- Shows architectural patterns
```

### Architecture Analysis
```
/map-architecture [feature/issue]
Generate architecture map for specific features or issues.

Examples:
/map-architecture auth-system
/map-architecture #123
/map-architecture document-processing

What it does:
- Maps component relationships
- Shows data flow diagrams
- Identifies integration points
- Documents API dependencies
```

---

## 🛡️ Security & Compliance Commands

### Security Scanning
```
/security-scan [file/folder/issue]
Run security analysis on specific targets.

Examples:
/security-scan supabase/functions/
/security-scan src/auth/
/security-scan #123

What it does:
- Scans for security vulnerabilities
- Checks for credential exposure
- Validates input sanitization
- Reviews authentication logic
- Tests authorization mechanisms
```

```
/audit-permissions [file/feature]
Audit permissions and access control for specific code.

Examples:
/audit-permissions src/auth/
/audit-permissions user-management
/audit-permissions #123

What it does:
- Reviews RLS policies
- Validates API key permissions
- Checks authorization logic
- Tests access control mechanisms
```

### Compliance Checking
```
/compliance-check [feature/issue]
Check compliance requirements for specific features.

Examples:
/compliance-check data-retention
/compliance-check #123
/compliance-check user-data

What it does:
- Validates GDPR compliance
- Checks data retention policies
- Reviews audit logging
- Verifies encryption requirements
```

---

## 💰 Cost & Performance Analysis Commands

### Cost Analysis
```
/cost-analyze [feature/endpoint/issue]
Analyze operational costs for specific features or endpoints.

Examples:
/cost-analyze /api/v1/extract
/cost-analyze document-processing
/cost-analyze #123

What it does:
- Calculates AI model usage costs
- Estimates infrastructure costs
- Analyzes profit margins
- Suggests cost optimizations
```

```
/perf-profile [file/endpoint/feature]
Profile performance for specific targets.

Examples:
/perf-profile /api/v1/extract
/perf-profile supabase/functions/auth/index.ts
/perf-profile document-processing

What it does:
- Measures execution time and memory
- Identifies performance bottlenecks
- Validates against SLA requirements
- Suggests optimization strategies
```

---

## 🎯 Workflow Integration Commands

### Git Integration
```
/git-status [issue]
Show git status with context for specific issue.

Examples:
/git-status #123
/git-status

What it does:
- Shows current branch and changes
- Maps changes to story requirements
- Identifies files needing commits
- Suggests commit groupings
```

```
/commit-prepare [issue/message]
Prepare commit for specific issue with intelligent message generation.

Examples:
/commit-prepare #123
/commit-prepare "fix auth validation"

What it does:
- Analyzes staged changes
- Generates conventional commit message
- Validates changes against story requirements
- Suggests commit improvements
```

### PR Management
```
/pr-prepare [issue]
Prepare pull request for specific issue.

Examples:
/pr-prepare #123
/pr-prepare

What it does:
- Generates PR title and description
- Creates checklist from acceptance criteria
- Identifies reviewers based on code changes
- Runs pre-PR quality checks
```

```
/review-pr [pr-url/pr-number]
Comprehensive pull request review.

Examples:
/review-pr #456
/review-pr https://github.com/org/repo/pull/456

What it does:
- Reviews code quality and standards
- Checks security and performance
- Validates acceptance criteria
- Suggests improvements
```

---

## 📊 Usage Examples by Target Type

### File-Specific Operations
```
Target: supabase/functions/auth/index.ts

/lint supabase/functions/auth/index.ts
/test-unit supabase/functions/auth/index.ts
/security-scan supabase/functions/auth/index.ts
/doc-generate supabase/functions/auth/index.ts
/perf-profile supabase/functions/auth/index.ts
```

### Folder Operations
```
Target: src/utils/

/run-tests src/utils/
/lint-fix src/utils/
/format src/utils/
/security-scan src/utils/
/find-usage src/utils/
```

### Issue-Based Operations
```
Target: #123 (GitHub Issue)

/analyze-issue #123
/test-issue #123
/validate-story #123
/code-for-issue #123
/commit-prepare #123
/pr-prepare #123
```

### Pattern-Based Operations
```
Target: **/*.test.ts

/run-tests **/*.test.ts
/format **/*.test.ts
/lint **/*.test.ts
```

### Feature Operations
```
Target: auth-system

/test-integration auth-system
/map-architecture auth-system
/cost-analyze auth-system
/deploy-check auth-system
```

---

## 🔧 Platform-Specific Usage

### Claude AI
```
You: /run-tests #123
Claude: I'll analyze issue #123 and run all related tests...

Found 3 test files related to API key authentication:
✅ auth.test.ts - 5/5 tests passing
✅ validation.test.ts - 3/3 tests passing  
❌ integration.test.ts - 2/4 tests failing

The failing tests are related to rate limiting functionality...
```

### Cursor
```
You: /lint src/auth/
Cursor: Running ESLint on auth directory...

Found 3 issues:
- unused variable in validateKey function
- missing return type annotation
- prefer const assertion for error codes

Shall I apply auto-fixes?
```

### GitHub Copilot
```
You: /security-scan supabase/functions/
Copilot: Scanning Edge Functions for security issues...

🔍 Found potential issues:
- API key stored in plaintext (line 23)
- Missing input validation (line 45)
- SQL injection risk (line 67)

Suggestions: [detailed security recommendations]
```

---

## 🚀 Advanced Command Combinations

### Story Development Workflow
```
1. /analyze-issue #123
2. /find-code related-functionality  
3. /run-tests #123
4. [make changes]
5. /lint-fix .
6. /test-issue #123
7. /commit-prepare #123
8. /pr-prepare #123
```

### Code Quality Audit
```
1. /security-scan src/
2. /perf-profile /api/v1/extract
3. /cost-analyze document-processing
4. /compliance-check data-retention
5. /deps-check package.json
```

### Deployment Preparation
```
1. /validate-story #123
2. /test-integration auth-system
3. /deploy-check staging
4. /security-scan supabase/functions/
5. /perf-profile production-endpoints
```

---

*This command reference provides flexible, target-specific operations that work with any AI coding assistant. Commands are designed to be practical, actionable, and aligned with the IDP Platform development workflow.*

---

*Last Updated: September 21, 2025*  
*AI Commands Version: 1.0*  
*Compatible with: Claude, Gemini, Cursor, GitHub Copilot, and other AI coding assistants*