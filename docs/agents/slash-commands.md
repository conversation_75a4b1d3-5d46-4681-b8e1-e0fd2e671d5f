# AI Agent Slash Commands Reference

*Last Updated: September 21, 2025*  
*Version: 1.0*  
*Compatible with: <PERSON>, <PERSON>, <PERSON>urs<PERSON>, GitHub Copilot, and other AI coding assistants*

## 🎯 Overview

This document provides a standardized set of slash commands that can be used with any AI coding assistant to streamline development workflows on the IDP Platform. These commands are designed to be copy-pastable and work across different AI platforms.

**Usage**: Copy the command you need and paste it into your AI assistant (<PERSON>, <PERSON>, Cursor, etc.)

---

## 📋 Command Categories

### 🔧 Git & Version Control Commands

#### Commit Operations
```
/git-commit
Create an intelligent commit message based on my staged changes and current story context. Include the story reference and follow conventional commit format.

/git-commit-lint  
Run linting first, fix any auto-fixable issues, then commit only if all lint checks pass. Reject and show errors if linting fails.

/git-commit-test
Run all relevant tests first, then commit only if tests pass. Show detailed failure information if tests don't pass.

/git-commit-qa
Run comprehensive quality checks: lint + tests + type check + security scan. Only commit if everything passes. Provide detailed report.

/git-commit-wip
Create a work-in-progress commit with timestamp and current progress description for checkpoint saving.

/git-commit-fix
Analyze recent commits and create a proper fix commit message with reference to what's being fixed.

/git-amend
Add current staged changes to the last commit and update the commit message appropriately.
```

#### Branch Operations
```
/git-branch-create
Create a properly named branch based on current story context using the pattern: story/epic-X-story-Y-description

/git-branch-sync  
Sync current branch with main branch, handle any merge conflicts intelligently, and provide guidance.

/git-branch-clean
List and delete merged branches, prune remote tracking branches, and clean up the workspace.

/git-switch-main
Switch to main branch, pull latest changes, and show status of what's new.

/git-switch-story [issue-number]
Switch to the story branch for the given issue number, or create it if it doesn't exist.
```

#### Push & Pull Operations
```
/git-push
Push current branch to GitHub with proper upstream tracking and verify the push succeeded.

/git-push-force-safe
Perform a force push with lease to avoid overwriting others' work, with safety checks.

/git-pull-rebase
Pull latest changes with rebase to maintain clean history and handle conflicts.

/git-sync-upstream
Sync fork with upstream repository and update all relevant branches.
```

### 🧪 Testing & Quality Assurance Commands

#### Test Execution
```
/test-unit
Run unit tests relevant to current changes with coverage report and detailed results.

/test-integration  
Run integration tests for the current story/feature with comprehensive output.

/test-e2e
Run end-to-end tests for complete user workflows affected by current changes.

/test-performance
Run performance benchmarks and validate against SLA requirements defined in architecture.

/test-security
Run security scans, vulnerability checks, and validate against security requirements.

/test-all
Run comprehensive test suite: unit + integration + e2e + security + performance.

/test-watch
Start test watcher for continuous feedback during development session.
```

#### Quality Gates
```
/qa-check
Perform full quality assessment: tests + lint + types + security + performance against project standards.

/qa-story
Validate current work against the story's acceptance criteria and definition of done.

/qa-epic
Check if current changes align with epic requirements and don't break epic integration.

/qa-pre-commit
Run pre-commit quality gate with auto-fixes where possible, report what needs manual attention.

/qa-pre-pr
Run comprehensive pre-PR quality gate and generate detailed report for review.

/qa-production-ready
Check production readiness: monitoring, alerts, documentation, security, performance.
```

#### Code Quality
```
/lint
Run project linter with auto-fix for simple issues, report what needs manual fixes.

/lint-strict
Run strict linting with no auto-fixes, provide detailed error reporting and fix suggestions.

/format
Format code according to project standards (Prettier, ESLint, Deno fmt, etc.).

/type-check
Run TypeScript type checking with detailed error analysis and suggestions.

/deps-check
Scan dependencies for vulnerabilities and provide update recommendations.
```

### 📋 Project Management Commands

#### Story Management
```
/story-status
Show current story progress against acceptance criteria with completion percentage.

/story-next
Analyze available stories and suggest the next logical story to work on.

/story-block [reason]
Mark current story as blocked with detailed reason and suggest alternatives.

/story-complete
Validate story completion against acceptance criteria and prepare for PR creation.

/story-handoff [username]
Prepare comprehensive handoff documentation for story transfer to another developer.
```

#### Epic Tracking
```
/epic-progress
Show epic completion status with visual progress and remaining work estimate.

/epic-blockers
Identify current blockers preventing epic completion and suggest resolution paths.

/epic-velocity
Calculate team velocity and provide epic completion time estimates.

/epic-risk
Assess risks for epic timeline: dependencies, complexity, resource availability.
```

#### Planning
```
/plan-day
Create a daily work plan based on story priorities, dependencies, and estimated capacity.

/plan-sprint
Assist with sprint planning: story prioritization, capacity planning, risk assessment.

/plan-dependencies
Analyze story dependencies and provide optimal sequencing recommendations.
```

### 🔄 Pull Request & Review Commands

#### PR Creation
```
/pr-create
Create PR with auto-generated title, description, and checklist based on story requirements.

/pr-draft
Create draft PR for early feedback with clear indication of what's still in progress.

/pr-ready
Mark draft PR as ready for review after running final quality checks.

/pr-update
Update existing PR description and title based on latest changes and story evolution.
```

#### Review Process
```
/pr-self-review
Run comprehensive self-review checklist before requesting team reviews.

/pr-review [pr-url]
Provide structured code review analysis with security, performance, and quality checks.

/pr-approve [pr-url]
Approve PR after validating all quality gates and acceptance criteria.

/pr-request-changes [pr-url]
Generate structured change request with specific, actionable feedback.
```

### 🚀 Environment & DevOps Commands

#### Environment Management
```
/env-start
Start complete development environment (Supabase services, database, Edge Functions).

/env-status
Check health status of all development services and dependencies.

/env-reset
Reset development environment to clean state with fresh database and cleared caches.

/env-config
Validate environment configuration against requirements and fix common issues.

/env-troubleshoot
Diagnose and provide solutions for common development environment problems.
```

#### Deployment
```
/deploy-staging
Deploy current branch to staging environment with validation and smoke tests.

/deploy-production
Execute production deployment with comprehensive safety checks and rollback plan.

/deploy-rollback
Perform intelligent rollback to last known good state with impact assessment.

/deploy-status
Show deployment status across all environments with health indicators.

/deploy-smoke-test
Run post-deployment smoke tests to verify system functionality.
```

### 📚 Documentation & Knowledge Commands

#### Auto-Documentation
```
/doc-api
Generate comprehensive API documentation for current changes and new endpoints.

/doc-code
Add intelligent inline code comments and function documentation.

/doc-readme
Update project README with new feature information and usage examples.

/doc-changelog
Create detailed changelog entries for current release with impact analysis.

/doc-troubleshooting
Generate troubleshooting guide for complex features and common issues.
```

#### Knowledge Capture
```
/knowledge-capture
Document architectural decisions, trade-offs, and implementation reasoning.

/knowledge-share
Create team knowledge sharing document with lessons learned and best practices.

/knowledge-search [topic]
Search existing project knowledge base and documentation for relevant information.
```

### 📊 Performance & Monitoring Commands

#### Performance Analysis
```
/perf-profile
Profile current code for performance bottlenecks and optimization opportunities.

/perf-benchmark
Run performance benchmarks with historical comparison and trend analysis.

/perf-optimize
Analyze code and suggest specific performance optimizations with impact estimates.

/perf-memory
Perform memory usage analysis and detect potential memory leaks.

/perf-api
Test API endpoint performance and validate against response time requirements.
```

#### Monitoring Setup
```
/monitor-setup
Configure monitoring and alerting for new features and endpoints.

/monitor-alerts
Set up intelligent alerts based on error rates, performance thresholds, and business metrics.

/monitor-dashboard
Create comprehensive monitoring dashboard for feature health and performance.

/monitor-health
Implement health checks and readiness probes for current feature.
```

### 💰 Business & Cost Commands

#### Cost Analysis
```
/cost-estimate
Calculate operational costs for current implementation including AI model usage.

/cost-optimize
Analyze and suggest cost optimization strategies while maintaining quality requirements.

/cost-ai
Detailed AI model usage cost analysis with optimization recommendations.

/cost-infrastructure
Infrastructure cost analysis with scaling and optimization recommendations.
```

#### Business Impact
```
/impact-customer
Analyze how current changes will affect existing customers and their workflows.

/impact-business
Assess business value and ROI of current implementation and feature set.

/impact-risk
Comprehensive risk assessment for current implementation and deployment.

/metrics-business
Set up business metrics tracking and success measurement for new features.
```

### 🚨 Emergency & Crisis Commands

#### Crisis Management
```
/emergency-branch
Create emergency hotfix branch with proper naming and immediate context setup.

/emergency-fix
Implement minimum viable fix for critical production issues with safety validation.

/emergency-deploy
Execute emergency deployment with accelerated but safe deployment process.

/emergency-rollback
Perform emergency rollback with impact assessment and communication templates.

/emergency-communicate
Generate stakeholder communication templates for incident management.
```

#### Incident Response
```
/incident-analyze
Perform root cause analysis for production issues with systematic investigation.

/incident-timeline
Create detailed incident timeline for post-mortem analysis and learning.

/incident-lessons
Capture lessons learned and create prevention strategies for similar issues.

/incident-postmortem
Generate comprehensive post-mortem document with action items and improvements.
```

### 🎓 Learning & Development Commands

#### Code Learning
```
/learn-pattern [pattern-name]
Explain architectural patterns used in codebase with examples and best practices.

/learn-best-practices
Show current technology stack best practices with code examples and reasoning.

/learn-anti-patterns
Identify and explain anti-patterns to avoid with better alternatives.

/learn-optimize
Provide teaching-focused code optimization suggestions with explanation of concepts.
```

#### Skill Development
```
/skill-assess
Assess current code quality against skill development goals and industry standards.

/skill-recommend
Recommend learning resources and practice exercises based on current work and gaps.

/skill-practice
Suggest specific practice exercises to improve skills relevant to current project.
```

### 🔧 Advanced Workflow Commands

#### Multi-Story Management
```
/work-switch [story-id]
Switch development context between multiple active stories with proper state management.

/work-status
Show comprehensive status of all active work streams with progress indicators.

/work-prioritize
Help prioritize multiple concurrent tasks based on dependencies and business value.

/work-checkpoint
Create development checkpoint across all active work for safe context switching.
```

#### Team Coordination
```
/team-standup
Generate comprehensive standup update covering all active work and blockers.

/team-handoff [username]
Prepare detailed work handoff documentation with context and next steps.

/team-help-request
Create structured help request with full context and specific questions.

/team-knowledge-share
Document and share solutions, patterns, and learnings with the development team.
```

---

## 🎯 Usage Examples by AI Platform

### Claude AI
```
You: /git-commit-qa
Claude: I'll run comprehensive quality checks before committing...
✅ Lint passed ✅ Tests passed ✅ Types valid ✅ Security scan clean
🔄 Creating commit: "feat(auth): implement SHA-256 API key hashing with rate limiting"
```

### GitHub Copilot Chat
```
You: /test-unit
Copilot: Running unit tests for your current changes...
Found 3 new test files needed. Shall I create them?
```

### Cursor AI
```
You: /story-status  
Cursor: Analyzing story #123 against acceptance criteria...
✅ 4/6 criteria complete ⏳ 2 remaining: error handling, audit logging
```

### Gemini/Bard
```
You: /perf-benchmark
Gemini: Running performance benchmarks...
API response time: 245ms (target: <500ms) ✅
Database query time: 12ms (target: <50ms) ✅
```

---

## 🔧 Implementation Tips

### For AI Assistant Configuration

**Claude Projects**: Add this document to your project knowledge
**Cursor**: Save these as custom commands in settings
**GitHub Copilot**: Use in chat interface with @ mentions
**Gemini**: Reference this doc in conversation context

### Customization Guidelines

**Adapt commands to your AI platform's syntax:**
- Some platforms prefer `/command` format
- Others work better with natural language
- Adjust based on your AI's strengths

**Team Standardization:**
- Choose a subset of commands that work best for your team
- Document any team-specific modifications
- Train new team members on the standardized set

### Integration with Project Tools

**GitHub Integration:**
- Commands should reference GitHub issues, PRs, and project boards
- Include links and issue numbers in outputs

**CI/CD Integration:**
- Commands should trigger or check CI/CD pipeline status
- Validate against automated quality gates

**Monitoring Integration:**
- Include monitoring setup and dashboard creation
- Reference existing alerting and metrics systems

---

## 📋 Command Checklist for New Team Members

### Essential Commands to Learn First:
- [ ] `/git-commit-qa` - Quality-controlled commits
- [ ] `/story-status` - Track story progress  
- [ ] `/test-unit` - Run relevant tests
- [ ] `/pr-create` - Create pull requests
- [ ] `/env-start` - Start development environment

### Advanced Commands for Experienced Developers:
- [ ] `/perf-optimize` - Performance optimization
- [ ] `/security-audit` - Security validation
- [ ] `/cost-analyze` - Cost impact analysis
- [ ] `/monitor-setup` - Production monitoring
- [ ] `/incident-response` - Emergency procedures

---

## 🚀 Contributing New Commands

### Command Design Principles:
1. **Single Responsibility**: Each command does one thing well
2. **Consistent Naming**: Use verb-noun pattern (`/git-commit`, `/test-unit`)
3. **Clear Output**: Provide actionable feedback and next steps
4. **Error Handling**: Graceful failure with helpful error messages
5. **Context Aware**: Use current story/epic context when relevant

### Adding New Commands:
1. Follow the established format and categories
2. Include clear description and expected behavior
3. Provide usage examples for different AI platforms
4. Test with multiple AI assistants before merging
5. Update this documentation with new additions

---

*This slash commands reference provides a standardized interface for AI-assisted development that works across multiple platforms and coding assistants. Choose the commands that work best for your workflow and AI platform.*

---

*Last Updated: September 21, 2025*  
*Command Reference Version: 1.0*  
*Compatible with: Claude, Gemini, Cursor, GitHub Copilot, and other AI coding assistants*