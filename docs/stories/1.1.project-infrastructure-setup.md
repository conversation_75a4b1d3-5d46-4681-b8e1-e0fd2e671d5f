# Story 1.1: Project Infrastructure Setup

## Status
Draft

## Story
**As a** Platform Administrator,  
**I want** a properly configured Supabase project with Edge Functions,  
**so that** I have a scalable foundation for the document processing platform.

## Acceptance Criteria

1. Supabase project created with PostgreSQL database configured
2. Edge Functions runtime environment setup with Deno configuration
3. Environment variables configured for development and production
4. Git repository initialized with proper .gitignore and structure
5. Basic CI/CD pipeline configured for Edge Function deployment
6. Health check endpoint (`GET /api/v1/health`) returns system status
7. Database connection pooling configured for concurrent requests

## Tasks / Subtasks

- [ ] **Task 1: Initialize Supabase Project** (AC: 1, 2)
  - [ ] Run `supabase init` in project root
  - [ ] Configure `supabase/config.toml` with correct ports (API: 54321, DB: 54322, Studio: 54323)
  - [ ] Create initial Edge Function structure in `supabase/functions/`
  - [ ] Set up `supabase/functions/deno.json` with proper imports and configuration
  - [ ] Enable required PostgreSQL extensions (pgcrypto, uuid-ossp)

- [ ] **Task 2: Environment Configuration** (AC: 3)
  - [ ] Create `.env.example` template with all required variables
  - [ ] Configure local `.env` with Supabase keys and AI service keys
  - [ ] Set up environment variable validation in Edge Functions
  - [ ] Configure separate environments for development/staging/production

- [ ] **Task 3: Repository Setup** (AC: 4)
  - [ ] Initialize Git repository with comprehensive `.gitignore`
  - [ ] Create proper directory structure per source-tree.md
  - [ ] Set up `package.json` with NPM scripts for development workflow
  - [ ] Configure TypeScript settings for Edge Functions

- [ ] **Task 4: CI/CD Pipeline** (AC: 5)
  - [ ] Create GitHub Actions workflow for automated testing
  - [ ] Set up automated Edge Function deployment pipeline
  - [ ] Configure environment variable management for CI/CD
  - [ ] Add automated linting and type checking

- [ ] **Task 5: Health Check Implementation** (AC: 6, 7)
  - [ ] Create `supabase/functions/health/index.ts` endpoint
  - [ ] Implement database connectivity check
  - [ ] Add AI service connectivity validation (OpenAI, Claude)
  - [ ] Configure connection pooling settings in Supabase config
  - [ ] Return comprehensive system status JSON response

## Dev Notes

### Technical Context
This story establishes the foundational infrastructure for the **IDP Platform - API-first document processing service**. Per the PRD, this platform will:
- Reduce customer costs from $0.10-$1.00 per document to competitive pricing
- Achieve 60%+ profit margins through intelligent AI model routing
- Maintain 99.5% uptime with <5 second processing times
- Support dual API key architecture (skt_ test keys, skp_ production keys)

### Architecture Foundation
**Core Technology Stack** [Source: docs/architecture/tech-stack.md]:
- **Runtime**: Deno within Supabase Edge Functions for TypeScript-native serverless execution
- **Database**: PostgreSQL 17 with Row-Level Security policies and comprehensive indexing
- **AI Integration**: Multi-model fallback chain (OpenAI → Claude → LlamaParse) via OpenRouter gateway
- **Authentication**: Supabase Auth with custom API key validation for customer endpoints

### Project Structure Requirements
**Directory Structure** [Source: docs/architecture/source-tree.md]:
```
idp-platform/
├── supabase/
│   ├── functions/          # Edge Functions (Deno/TypeScript)
│   │   ├── health/         # Health check endpoint
│   │   └── deno.json      # Deno configuration with imports
│   ├── migrations/         # Database migrations (SQL)
│   └── config.toml        # Supabase local development config
├── types/                  # Shared TypeScript definitions
│   └── database.types.ts  # Auto-generated DB types
├── tests/                  # Test suites (unit, integration, manual)
└── docs/                   # Project documentation
```

### Environment Variables Required
**Critical Environment Setup** [Source: docs/architecture/tech-stack.md]:
```bash
# Supabase Configuration
SUPABASE_URL_PROD=https://your-project-ref.supabase.co
SUPABASE_ANON_KEY_PROD       # Public API key (from dashboard)
SUPABASE_SERVICE_ROLE_KEY_PROD # Admin key (from dashboard) 
SUPABASE_PROJECT_ID          # Project ID from dashboard

# AI Service Integration
OPENROUTER_API_KEY      # sk-or-v1-xxxxx (AI gateway)
OPENROUTER_REFERER=https://your-project-ref.supabase.co
LLAMAPARSE_API_KEY      # llx-xxxxx (PDF parsing)      # llx-xxxxx (PDF parsing)
```

### Performance & Reliability Requirements
**System Requirements** [Source: docs/prd.md - NFRs]:
- API Response Times: <500ms for non-processing endpoints (NFR4)
- Document Processing: <5 seconds for standard documents (NFR1)
- Platform Uptime: 99.5% with fallback systems operational (NFR2)
- Concurrent Processing: Support within Edge Function 55-second timeout limits
- Connection Pooling: Efficient database resource management for 1000+ API calls per customer per month

### Supabase Configuration Details
**config.toml Settings** [Source: docs/architecture/tech-stack.md]:
```toml
[api]
port = 54321

[db]
port = 54322

[studio] 
port = 54323

[functions]
verify_jwt = false  # Using custom API key auth
```

### Edge Function Standards
**Deno Configuration** [Source: docs/architecture/coding-standards.md]:
```json
{
  "imports": {
    "@supabase/functions-js": "jsr:@supabase/functions-js@2.5.0",
    "@supabase/supabase-js": "jsr:@supabase/supabase-js@2.57.4"
  },
  "lint": {
    "rules": {
      "tags": ["recommended"],
      "include": ["no-explicit-any", "no-unused-vars"]
    }
  },
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true
  }
}
```

### Health Check Endpoint Specification
**Required Implementation**:
```typescript
// GET /api/v1/health response format
{
  "status": "healthy" | "degraded" | "unhealthy",
  "timestamp": "2025-09-21T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": { "status": "healthy", "latency_ms": 15 },
    "openai": { "status": "healthy", "latency_ms": 245 },
    "claude": { "status": "healthy", "latency_ms": 189 },
    "llamaparse": { "status": "healthy", "latency_ms": 432 }
  },
  "performance": {
    "response_time_ms": 45,
    "active_connections": 12,
    "edge_function_cold_start": false
  }
}
```

## Testing

### Testing Standards [Source: docs/architecture/coding-standards.md]
**Framework**: Bun Test for unit tests, Deno test for Edge Functions
**Test Locations**:
- Unit tests: `tests/unit/infrastructure.test.ts`
- Edge Function tests: `supabase/functions/health/health.test.ts`
- Integration tests: `tests/integration/infrastructure.test.ts`

**Required Test Coverage**:
1. Health endpoint returns proper JSON structure
2. Database connectivity validation
3. AI service connectivity checks
4. Environment variable validation
5. Edge Function runtime initialization
6. Connection pooling performance under load

**Test Execution Commands**:
```bash
npm test                    # Quick test suite
npm run test:functions     # Deno Edge Function tests
npm run test:integration   # Infrastructure integration tests
```

### Quality Standards
- Zero `any` types in TypeScript code
- All environment variables validated at startup
- Comprehensive error handling with structured responses
- CORS headers configured for API access
- Correlation IDs for request tracing

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-09-21 | 1.0 | Initial story creation from Epic 1 requirements | Scrum Master |

## Dev Agent Record

*This section will be populated during implementation*

## QA Results

*This section will be populated after QA review*