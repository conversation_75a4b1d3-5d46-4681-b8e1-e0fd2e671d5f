# Project Brief: IDP Platform MVP
## API-First Document Processing Platform

**Product Brief | September 21, 2025**

---

## Executive Summary

We're building a focused **API-first Document Processing Platform** that enables companies to extract structured data from documents using AI models. The MVP delivers core document processing capabilities through REST APIs, allowing customers to process PDFs, documents, spreadsheets, and images into structured JSON output using customizable AI agents.

**Key Value Proposition:** Cost-effective document extraction with fallback AI models, customizable agents, and enterprise-grade API management - delivered as pure API service without UI complexity.

---

## Problem Statement

### Current State Pain Points
- **High Document Processing Costs**: Companies paying $0.10-$1.00 per document with existing solutions
- **Limited Customization**: One-size-fits-all extraction that doesn't match company-specific document formats
- **Single Point of Failure**: Solutions dependent on single AI provider create risk when APIs are down
- **Complex Integration**: Existing platforms require extensive UI integration when customers just need API access

### Why Now
- AI model costs have dropped significantly, making competitive pricing possible
- Multiple AI providers (OpenAI, Claude, LlamaParse) enable redundancy and cost optimization
- Growing demand for document automation in businesses of all sizes
- API-first approach reduces complexity and time-to-market

---

## Proposed Solution

### Core Concept
An **API-first platform** built on Supabase Edge Functions that provides:
- **Document Processing APIs** for PDF, DOCX, XLSX, images
- **AI Agent System** with default templates and customer customization
- **Multi-Model Fallbacks** (OpenAI ↔ Claude, LlamaParse for complex PDFs)
- **Enterprise API Management** with usage tracking, rate limiting, and security

### Key Differentiators
- **API-Only Approach**: Zero UI complexity, pure backend service
- **Agent Customization**: Clone and modify extraction templates
- **AI Redundancy**: Automatic fallbacks when primary models are unavailable
- **Transparent Pricing**: Separate cost/price tracking for admin oversight
- **Dual Key Architecture**: Test keys (`skt_`) with credits and 7-day retention, production keys (`skp_`) with configurable billing

---

## Target Users

### Primary User Segment: Software Development Teams
- **Profile**: Companies building document processing into their own applications
- **Current Behavior**: Building custom document extraction or using expensive third-party APIs
- **Pain Points**: High costs, limited customization, integration complexity
- **Goals**: Reliable document processing APIs they can integrate quickly

### Secondary User Segment: Business Operations Teams
- **Profile**: Companies needing programmatic document processing for workflows
- **Current Behavior**: Manual document processing or basic OCR tools
- **Pain Points**: Manual effort, inconsistent extraction quality
- **Goals**: Automated document processing with custom field extraction

---

## User Journey Maps

### Journey 1: Platform Admin (Internal User)
**Persona:** Platform administrator managing customers and system operations

**Goal:** Onboard new customer and monitor platform health

#### Phase 1: Customer Onboarding
1. **Login to Admin Portal**
   - Authenticate with admin credentials
   - Access dashboard with platform metrics

2. **Create New Customer Account**
   - Navigate to customer management
   - Enter company details (name, contact info)
   - Configure initial settings (file size limit, rate limits)

3. **Generate API Key Pair**
   - Create test key (`skt_live_...`) with configurable initial credits and rate limits
   - Create production key (`skp_live_...`) with separate credit allocation or billing setup
   - Set retention policies (7-day for test, configurable for prod)
   - Assign agent access permissions to both keys
   - Share keys securely with customer

4. **Setup Monitoring**
   - Configure usage alerts for customer
   - Set cost/revenue tracking parameters
   - Enable audit logging for customer account

#### Phase 2: Ongoing Management
5. **Monitor Usage & Credits**
   - Review real-time usage dashboard per API key
   - Track cost vs price metrics and remaining credit balances
   - Identify customers approaching rate limits or low credit balances
   - Add credits to API keys when requested by customers

6. **Handle Support Issues**
   - Review API error logs and patterns
   - Investigate processing failures
   - Adjust rate limits or file size limits as needed

7. **Credit & Key Management**
   - Monitor credit balances across all customer API keys
   - Handle customer requests for additional credits
   - Add credits to specific API keys (`POST /api/admin/keys/{keyId}/credits`)
   - Adjust rate limits based on customer needs
   - Track credit consumption patterns and cost optimization

8. **Platform Maintenance**
   - Monitor AI model availability (OpenAI, Claude, LlamaParse)
   - Review fallback system performance
   - Update default agent versions

**Pain Points Addressed:**
- Single dashboard for all customer management
- Real-time visibility into costs and revenue
- Proactive monitoring prevents customer issues

### Journey 2: Developer (Customer API User)
**Persona:** Software developer integrating document processing into their application

**Goal:** Integrate document extraction API and customize agents for specific use case

#### Phase 1: API Integration & Testing
1. **Receive API Credentials**
   - Get test key (`skt_live_...`) and production key (`skp_live_...`) from admin
   - Receive initial credits allocated to test key for experimentation
   - Review API documentation and endpoints
   - Set up development environment with test key

2. **Initial API Testing**
   - Test authentication with test key (`skt_live_...`)
   - Use `GET /api/v1/agents` to see available agents
   - Try `POST /api/v1/extract` with sample document using test credits

3. **Choose and Test Agents**
   - Browse default agents (invoice, contract, receipt, etc.)
   - Test various agents with sample documents
   - Evaluate accuracy and JSON output format

#### Phase 2: Agent Customization
4. **Clone Agent for Customization**
   - Use `POST /api/v1/agents/clone` to copy default agent
   - Receive custom agent ID for modification

5. **Customize Extraction Fields**
   - Modify agent prompt for specific document format
   - Add custom field extractions needed for application
   - Test customized agent with real documents

6. **Iterate and Refine**
   - Use test key credits to refine agent with transparent LLM costs
   - Monitor credit usage while testing different prompt variations
   - Adjust prompts based on accuracy results
   - Save final customized agent with `PUT /api/v1/agents/{id}`

#### Phase 3: Production Integration
7. **Switch to Production**
   - Switch from test key (`skt_`) to production key (`skp_`) in code
   - Implement `POST /api/v1/extract` with production key
   - Add error handling for API failures and credit limits
   - Set up retry logic for processing delays

8. **Monitor and Optimize**
   - Track API usage, costs, and remaining credit balances per key
   - Monitor processing accuracy in production
   - Request additional credits from admin when needed
   - Optimize agent prompts based on real usage

**Pain Points Addressed:**
- Clear API documentation and testing flow
- Agent customization without starting from scratch
- Test mode prevents unexpected costs during development

### Journey 3: End User (Customer's Customer)
**Persona:** Business user interacting with developer's application that uses our API

**Goal:** Upload invoice and get extracted data for expense management

#### User's Application Interface Journey
1. **Document Upload**
   - User uploads invoice PDF through customer's web app
   - Application validates file type and size
   - User sees "Processing..." indicator

2. **Behind the Scenes (Our API)**
   - Customer's app calls `POST /api/v1/extract`
   - Document queued for processing
   - AI model processes with fallback if needed
   - Structured JSON data returned to customer's app

3. **Results Display**
   - Customer's app receives extracted data:
     ```json
     {
       "invoice_number": "INV-2025-001",
       "vendor": "Office Supply Co",
       "total": 145.67,
       "date": "2025-09-21",
       "line_items": [...]
     }
     ```
   - User sees pre-filled expense form
   - User reviews and confirms extracted data

4. **Error Handling**
   - If processing fails, user sees "Please try again" message
   - If accuracy is low, user can manually correct fields
   - All corrections help improve future extractions

#### Alternative Flow: Batch Processing
1. **Bulk Upload**
   - User uploads multiple documents at once
   - Customer's app uses queue system for batch processing
   - User receives email when processing complete

2. **Review Results**
   - User reviews batch processing results
   - Flags any accuracy issues for manual review
   - Exports data to accounting system

**Pain Points Addressed:**
- Fast, reliable document processing
- High accuracy reduces manual corrections
- Transparent error handling maintains user trust

---

## User Journey Success Metrics

### Admin Journey Success
- **Onboarding Time**: <15 minutes to setup new customer with API key pair
- **Credit Management**: <5 minutes to add credits or adjust limits per API key
- **Issue Resolution**: <2 hours average response to customer problems
- **Platform Visibility**: Real-time cost/revenue tracking accuracy >99%

### Developer Journey Success  
- **Integration Time**: <4 hours from test key to first successful extraction
- **Customization Success**: >90% of developers successfully customize agents using test credits
- **Production Adoption**: >80% of test key users upgrade to production key within 30 days

### End User Journey Success
- **Processing Speed**: <5 seconds for standard documents
- **Accuracy Satisfaction**: >95% of extractions require no manual correction
- **User Experience**: <2 clicks from upload to extracted data display

---

## Goals & Success Metrics

### Business Objectives
- **Revenue Target**: $50K ARR within 6 months of MVP launch
- **Customer Acquisition**: 25 paying customers by month 6
- **Cost Efficiency**: Maintain 60%+ profit margins through intelligent model routing
- **Reliability**: 99.5% uptime with fallback systems

### User Success Metrics
- **Processing Accuracy**: >95% for structured documents
- **API Response Time**: <5 seconds for standard documents
- **Customer Retention**: >80% month-over-month retention
- **API Adoption**: Average 1000+ API calls per customer per month

### Key Performance Indicators (KPIs)
- **Monthly Recurring Revenue (MRR)**: Track subscription and usage growth
- **API Usage Volume**: Documents processed per month across platform
- **Processing Cost vs Revenue**: Maintain healthy profit margins
- **Customer Support Tickets**: <5% of API calls generating support requests

---

## MVP Scope

### Core Features (Must Have)

- **Admin Authentication System**: Secure admin portal for platform management
- **API Key Management**: Generate test keys (`skt_`) and production keys (`skp_`) with configurable credit balances, rate limits, and retention policies per key
- **Usage Tracking**: Dual metrics (cost/price) with admin visibility to both, customer visibility to price only
- **Document Processing Pipeline**: 
  - Support PDF, DOCX, XLSX, images (up to configurable file size per API key)
  - Multi-format text extraction with structure preservation
  - AI model routing with automatic fallbacks (OpenAI ↔ Claude, LlamaParse when available)
- **Agent System**:
  - Versioned default agents (customer cannot edit originals)
  - Clone default agents for customization
  - Save custom agents per customer
  - JSON schema validation for outputs
- **Core API Endpoints**:
  - `GET /api/v1/agents` - List available agents
  - `POST /api/v1/agents/clone` - Clone default agent
  - `PUT /api/v1/agents/{id}` - Update custom agent
  - `POST /api/v1/extract` - Process document (retention based on key type)
- **Admin Management Endpoints**:
  - `GET /api/admin/customers/{id}/keys` - View customer's API keys and credit balances
  - `POST /api/admin/keys/{keyId}/credits` - Add credits to specific API key
  - `PUT /api/admin/keys/{keyId}/limits` - Modify rate limits for API key
  - `GET /api/admin/usage/credits` - Monitor credit usage across all customers
- **Security Features**:
  - Prompt injection protection
  - Input sanitization and validation
  - Rate limiting per API key
  - Comprehensive audit logging
- **Queue System**: Asynchronous processing for large documents using Supabase capabilities
- **Database Design**: Fully commented tables/fields for OpenAPI and GraphQL spec generation

### Out of Scope for MVP
- User Interface / Dashboard
- Multi-user authentication within companies
- Payment processing / billing system
- Agent marketplace or sharing
- Webhook notifications
- Advanced analytics dashboard
- Multi-organization support
- White-label capabilities

### MVP Success Criteria
- **Functional**: All core endpoints operational with <5 second response times
- **Reliable**: 99.5% uptime with fallback systems working
- **Profitable**: 60%+ profit margins maintained
- **Adoptable**: 5+ pilot customers processing 100+ documents each

---

## Post-MVP Vision

### Phase 2 Features
- **Payment Integration**: Stripe integration for automatic billing
- **Advanced Rate Limiting**: Subscription tiers with different limits
- **Webhook System**: Event notifications for document processing completion
- **Basic Analytics Dashboard**: Usage insights for customers
- **Customer Agent Versioning**: Version control for custom agents

### Long-term Vision
- **Advanced AI Features**: Multi-model ensemble processing
- **Enterprise Features**: SSO, SCIM, compliance certifications
- **Global Scale**: Multi-region deployment and optimization

### Expansion Opportunities
- **Industry-Specific Agents**: Pre-built templates for healthcare, legal, finance
- **White-Label Solution**: Partner API offerings
- **Advanced Document Types**: Video, audio transcription capabilities
- **Workflow Integration**: Zapier, Make.com connectors

---

## Technical Considerations

### Platform Requirements
- **Target Environment**: Cloud-native, serverless architecture
- **API Standards**: RESTful APIs with OpenAPI 3.0 specification
- **Performance Requirements**: <5s document processing, <500ms API responses
- **File Size Limits**: Configurable per API key (default 50MB)

### Technology Stack
- **Backend**: Supabase Edge Functions (Deno runtime)
- **Database**: PostgreSQL with Row-Level Security
- **AI Integration**: OpenAI API, Claude API, LlamaParse API
- **Authentication**: Supabase Auth with custom JWT handling
- **Queue System**: PostgreSQL + pg_cron for job processing

### Architecture Considerations
- **Repository Structure**: Monorepo with clear separation of Edge Functions
- **Service Architecture**: Microservices via Edge Functions
- **Security Requirements**:
  - DOMPurify or equivalent for prompt injection protection
  - SHA-256 API key hashing
  - Comprehensive input validation
  - Audit trail for all operations
- **Integration Patterns**: 
  - Circuit breaker pattern for AI model fallbacks
  - Retry logic with exponential backoff
  - Comprehensive error handling and logging

### Best Practices Implementation
- **Schema Validation**: JSON Schema for agent output validation
- **Prompt Security**: Multi-layer prompt injection protection
- **Queue Management**: Supabase-optimized async processing
- **Database Documentation**: All tables/fields commented for auto-generated specs
- **Logging & Auditing**: Structured logging with correlation IDs

---

## Constraints & Assumptions

### Constraints
- **Budget**: MVP development within existing Supabase infrastructure costs
- **Timeline**: 4-6 weeks for MVP completion
- **Resources**: Single full-stack developer with AI/ML experience
- **Technical**: Must work within Supabase Edge Functions limitations

### Key Assumptions
- OpenAI and Claude APIs maintain current pricing and availability
- Supabase Edge Functions can handle document processing workloads
- Customer demand exists for API-first document processing
- 60%+ profit margins achievable through intelligent model routing
- LlamaParse provides sufficient value for complex document fallback

---

## Risks & Open Questions

### Key Risks
- **AI API Costs**: Model pricing changes could impact profit margins
- **Processing Limits**: Large documents may exceed Edge Function timeouts
- **Competition**: Major cloud providers adding similar capabilities
- **Customer Adoption**: API-first approach may limit some customer segments

### Open Questions
- What's the optimal default file size limit balancing cost and usability?
- How should we handle customer agent versioning complexity?
- What specific JSON schema standards should we implement?
- How do we balance security vs usability in prompt injection protection?

### Areas Needing Further Research
- Optimal queue system architecture within Supabase constraints
- Best practices for Edge Function document processing optimization
- Competitive pricing analysis for API-based document processing
- Customer onboarding flow for API-first products

---

## Next Steps

### Immediate Actions
1. **Technical Architecture Review**: Validate Edge Functions can handle document processing requirements
2. **Database Schema Design**: Create fully commented schema with RLS policies
3. **API Specification**: Draft OpenAPI 3.0 spec for all endpoints
4. **Security Research**: Implement prompt injection protection best practices
5. **Pilot Customer Identification**: Find 3-5 potential early adopters

### PM Handoff
This Project Brief provides the complete context for the IDP Platform MVP. The next step is creating a detailed PRD that translates these requirements into specific technical implementation details, API specifications, and development milestones. Please review this brief and suggest any clarifications or improvements before proceeding to PRD creation.

---

*© 2025 IDP Platform MVP. Built for API-first document processing excellence.*