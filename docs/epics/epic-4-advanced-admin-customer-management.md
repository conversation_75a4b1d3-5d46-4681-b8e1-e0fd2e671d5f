# Epic 4: Advanced Admin & Customer Management

## Epic Goal
Complete enterprise-grade administrative capabilities including comprehensive customer lifecycle management, advanced API key operations, and sophisticated usage analytics.

## Technical Context
- **Platform:** Supabase Edge Functions (Deno/TypeScript)
- **Database:** PostgreSQL with RLS policies
- **Authentication:** Supabase Auth + JWT for admin
- **Dependencies:** Epics 1-3 must be completed (basic auth, API keys, agents)

## Success Criteria
- [ ] Admin can manage complete customer lifecycle
- [ ] Advanced API key operations with granular controls
- [ ] Sophisticated credit management system
- [ ] Configurable rate limiting with burst capabilities
- [ ] Comprehensive usage analytics dashboard
- [ ] Integrated customer support tools

---

## Story 4.1: Comprehensive Customer Management

**As a Platform Administrator,**  
**I want complete customer lifecycle management capabilities,**  
**so that I can efficiently onboard, manage, and support customers.**

### Implementation Tasks

#### 4.1.1 Customer CRUD Operations
```typescript
// supabase/functions/admin-customers/index.ts
interface CustomerProfile {
  id: string;
  company_name: string;
  contact_email: string;
  status: 'active' | 'suspended' | 'trial' | 'enterprise';
  tier_settings: {
    max_api_keys: number;
    default_credit_limit: number;
    rate_limit_multiplier: number;
  };
  created_at: string;
  updated_at: string;
}
```

**API Endpoints to implement:**
- `POST /api/admin/customers` - Create customer
- `GET /api/admin/customers` - List with pagination/filtering
- `GET /api/admin/customers/{id}` - Get customer details
- `PUT /api/admin/customers/{id}` - Update customer
- `DELETE /api/admin/customers/{id}` - Soft delete with retention

#### 4.1.2 Customer Status Management
```sql
-- Add to existing customer table
ALTER TABLE customers ADD COLUMN IF NOT EXISTS status text DEFAULT 'trial';
ALTER TABLE customers ADD COLUMN IF NOT EXISTS tier_settings jsonb DEFAULT '{}';
```

#### 4.1.3 Audit Trail Implementation
```typescript
// Track all customer changes
await supabase.from('audit_logs').insert({
  customer_id: customerId,
  action: 'customer_updated',
  details: { old_values, new_values },
  admin_user_id: adminUserId,
  correlation_id: correlationId
});
```

### Acceptance Criteria
- [ ] Customer creation with company details validation
- [ ] Customer status management (active/suspended/trial/enterprise)
- [ ] Search and filter customers by multiple criteria
- [ ] Customer deletion with data retention compliance
- [ ] Full audit trail for all customer operations
- [ ] Admin permissions validation for all operations

---

## Story 4.2: Advanced API Key Operations

**As a Platform Administrator,**  
**I want comprehensive API key management with granular controls,**  
**so that I can provide flexible access and prevent abuse.**

### Implementation Tasks

#### 4.2.1 Enhanced API Key Table
```sql
-- Extend existing api_keys table
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS expires_at timestamp;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS scope_restrictions jsonb DEFAULT '{}';
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS suspended_at timestamp;
ALTER TABLE api_keys ADD COLUMN IF NOT EXISTS suspension_reason text;
```

#### 4.2.2 Key Management Endpoints
```typescript
// supabase/functions/admin-api-keys/index.ts
interface ApiKeyUpdate {
  expires_at?: string;
  scope_restrictions?: {
    allowed_endpoints?: string[];
    allowed_agents?: string[];
    max_file_size?: number;
  };
  suspended?: boolean;
  suspension_reason?: string;
}
```

**Endpoints:**
- `POST /api/admin/keys` - Generate with custom settings
- `PUT /api/admin/keys/{keyId}/suspend` - Suspend key
- `PUT /api/admin/keys/{keyId}/activate` - Reactivate key
- `PUT /api/admin/keys/{keyId}/scope` - Update scope restrictions
- `POST /api/admin/keys/{keyId}/rotate` - Rotate key
- `GET /api/admin/keys/{keyId}/usage` - Detailed usage analytics

#### 4.2.3 Bulk Operations
```typescript
// Bulk key management for enterprise customers
interface BulkKeyOperation {
  key_ids: string[];
  operation: 'suspend' | 'activate' | 'update_limits';
  parameters?: Record<string, any>;
}
```

### Acceptance Criteria
- [ ] Custom expiration dates for API keys
- [ ] Key suspension/reactivation without deletion
- [ ] Scope limitations (endpoints, agents, file sizes)
- [ ] Bulk operations for enterprise customers
- [ ] Key rotation with transition periods
- [ ] Emergency revocation with immediate effect

---

## Story 4.3: Credit Management System

**As a Platform Administrator,**  
**I want sophisticated credit management and billing preparation,**  
**so that I can handle various customer payment models.**

### Implementation Tasks

#### 4.3.1 Credit Operations API
```typescript
// supabase/functions/admin-credits/index.ts
interface CreditTransaction {
  customer_id: string;
  api_key_id?: string;
  amount: number;
  transaction_type: 'purchase' | 'deduction' | 'refund' | 'adjustment';
  payment_reference?: string;
  admin_notes?: string;
}
```

**Endpoints:**
- `POST /api/admin/customers/{id}/credits` - Add credits
- `GET /api/admin/customers/{id}/credits/history` - Credit history
- `POST /api/admin/customers/{id}/credits/refund` - Process refund
- `PUT /api/admin/alerts/credits` - Configure low balance alerts

#### 4.3.2 Credit Pooling for Enterprise
```sql
-- Add credit pooling support
CREATE TABLE IF NOT EXISTS credit_pools (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id uuid REFERENCES customers(id),
  pool_name text NOT NULL,
  total_credits integer DEFAULT 0,
  allocated_credits integer DEFAULT 0,
  created_at timestamp DEFAULT now()
);
```

#### 4.3.3 Automated Alerts
```typescript
// Background function to check credit balances
export async function checkCreditBalances() {
  const lowBalanceKeys = await supabase
    .from('api_keys')
    .select('*, customers(*)')
    .lt('credits', 100); // Configurable threshold
    
  for (const key of lowBalanceKeys) {
    await sendCreditAlert(key);
  }
}
```

### Acceptance Criteria
- [ ] Credit addition with payment reference tracking
- [ ] Automated low balance alerts (configurable thresholds)
- [ ] Credit pooling for enterprise customers
- [ ] Refund and adjustment workflows with approval
- [ ] Credit expiration handling with grace periods
- [ ] Usage-based credit calculation with transparent pricing

---

## Story 4.4: Advanced Rate Limiting

**As a Platform Administrator,**  
**I want configurable rate limiting with burst capabilities,**  
**so that I can optimize resource usage and prevent abuse.**

### Implementation Tasks

#### 4.4.1 Enhanced Rate Limiting Table
```sql
CREATE TABLE IF NOT EXISTS rate_limits (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  api_key_id uuid REFERENCES api_keys(id),
  customer_id uuid REFERENCES customers(id),
  limit_type text NOT NULL, -- 'per_minute', 'per_hour', 'per_day', 'per_month'
  limit_value integer NOT NULL,
  burst_allowance integer DEFAULT 0,
  current_usage integer DEFAULT 0,
  reset_at timestamp NOT NULL,
  created_at timestamp DEFAULT now()
);
```

#### 4.4.2 Rate Limiting Logic
```typescript
// supabase/functions/rate-limiter/index.ts
interface RateLimitCheck {
  api_key_id: string;
  endpoint: string;
  current_time: string;
}

export async function checkRateLimit(check: RateLimitCheck): Promise<{
  allowed: boolean;
  limit: number;
  remaining: number;
  reset_at: string;
  burst_used?: number;
}> {
  // Multi-tier rate limit checking
  const limits = await getRateLimits(check.api_key_id);
  
  for (const limit of limits) {
    const result = await checkSpecificLimit(limit, check);
    if (!result.allowed) return result;
  }
  
  return { allowed: true, limit: 0, remaining: 0, reset_at: '' };
}
```

#### 4.4.3 Rate Limit Management API
**Endpoints:**
- `PUT /api/admin/keys/{keyId}/limits` - Update rate limits
- `GET /api/admin/keys/{keyId}/limits/usage` - Current usage
- `POST /api/admin/limits/whitelist` - Add to whitelist
- `DELETE /api/admin/limits/violations/{id}` - Clear violations

### Acceptance Criteria
- [ ] Multi-tier rate limiting (minute/hour/day/month)
- [ ] Burst allowance for occasional high-volume usage
- [ ] Dynamic rate limiting based on historical patterns
- [ ] Rate limit inheritance from customer tier settings
- [ ] Violation tracking and automatic responses
- [ ] Whitelist capability for trusted customers

---

## Story 4.5: Usage Analytics Dashboard

**As a Platform Administrator,**  
**I want comprehensive usage analytics and reporting,**  
**so that I can make data-driven decisions about platform operations.**

### Implementation Tasks

#### 4.5.1 Analytics Aggregation
```sql
-- Create materialized views for performance
CREATE MATERIALIZED VIEW daily_usage_summary AS
SELECT 
  customer_id,
  api_key_id,
  DATE(created_at) as usage_date,
  COUNT(*) as total_requests,
  SUM(credits_used) as total_credits,
  SUM(model_cost) as total_cost,
  SUM(customer_price) as total_revenue,
  AVG(processing_time_ms) as avg_processing_time
FROM usage_logs
GROUP BY customer_id, api_key_id, DATE(created_at);

-- Refresh daily
SELECT cron.schedule('refresh_daily_usage', '0 1 * * *', 'REFRESH MATERIALIZED VIEW daily_usage_summary;');
```

#### 4.5.2 Analytics API
```typescript
// supabase/functions/admin-analytics/index.ts
interface UsageAnalytics {
  date_range: { start: string; end: string };
  customer_id?: string;
  api_key_id?: string;
  group_by: 'day' | 'week' | 'month';
}

// Endpoints:
// GET /api/admin/analytics/usage - Usage analytics
// GET /api/admin/analytics/revenue - Revenue analytics  
// GET /api/admin/analytics/performance - Performance metrics
// GET /api/admin/analytics/customers/top - Top customers by usage
```

#### 4.5.3 Real-time Dashboard Data
```typescript
export async function getDashboardMetrics() {
  return {
    active_customers: await getActiveCustomerCount(),
    total_api_calls_today: await getTodayApiCalls(),
    revenue_today: await getTodayRevenue(),
    profit_margin: await calculateProfitMargin(),
    ai_service_health: await getAiServiceStatus(),
    top_customers: await getTopCustomersByUsage(5)
  };
}
```

### Acceptance Criteria
- [ ] Real-time dashboard with KPIs
- [ ] Customer usage trends and pattern analysis
- [ ] Cost vs revenue analysis with profit margins
- [ ] AI service performance comparison
- [ ] Predictive analytics for capacity planning
- [ ] Exportable reports (CSV, JSON) for BI integration

---

## Story 4.6: Customer Support Tools

**As a Platform Administrator,**  
**I want integrated customer support and troubleshooting tools,**  
**so that I can quickly resolve customer issues and maintain satisfaction.**

### Implementation Tasks

#### 4.6.1 Customer Activity Timeline
```typescript
// supabase/functions/admin-support/index.ts
export async function getCustomerTimeline(customerId: string) {
  const timeline = await supabase
    .from('audit_logs')
    .select(`
      created_at,
      action,
      status,
      details,
      correlation_id,
      api_keys(key_prefix),
      usage_logs(model_used, credits_used)
    `)
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false })
    .limit(100);
    
  return formatTimeline(timeline.data);
}
```

#### 4.6.2 Error Aggregation
```sql
-- Create error summary view
CREATE VIEW customer_errors AS
SELECT 
  customer_id,
  DATE(created_at) as error_date,
  details->>'error_code' as error_code,
  COUNT(*) as error_count,
  array_agg(correlation_id) as correlation_ids
FROM audit_logs 
WHERE status = 'error'
GROUP BY customer_id, DATE(created_at), details->>'error_code';
```

#### 4.6.3 Support Tools API
**Endpoints:**
- `GET /api/admin/support/customers/{id}/timeline` - Activity timeline
- `GET /api/admin/support/customers/{id}/errors` - Error summary
- `GET /api/admin/support/errors/{correlationId}` - Detailed error info
- `POST /api/admin/support/customers/{id}/impersonate` - Safe impersonation
- `POST /api/admin/support/notifications/{customerId}` - Send notification

### Acceptance Criteria
- [ ] Customer activity timeline with all interactions
- [ ] Error log aggregation per customer with filtering
- [ ] Processing failure analysis with root cause
- [ ] Customer impersonation for issue reproduction
- [ ] Automated issue detection with proactive alerts
- [ ] Communication templates and automated responses

---

## Implementation Order

1. **Start with Story 4.1** - Customer management foundation
2. **Story 4.2** - Build on existing API key system
3. **Story 4.3** - Add credit management capabilities
4. **Story 4.4** - Enhance rate limiting system  
5. **Story 4.5** - Build analytics on usage data
6. **Story 4.6** - Complete with support tools

## Testing Requirements

### Unit Tests
```typescript
// tests/admin/customer-management.test.ts
describe('Customer Management', () => {
  it('should create customer with proper validation', async () => {
    const customer = await createCustomer(validCustomerData);
    expect(customer.status).toBe('trial');
    expect(customer.tier_settings).toBeDefined();
  });
});
```

### Integration Tests
- Admin authentication flow
- Customer CRUD operations
- API key management workflow
- Credit operations
- Rate limiting enforcement
- Analytics data accuracy

## Definition of Done

- [ ] All 6 stories implemented with acceptance criteria met
- [ ] Unit tests passing for all new functionality
- [ ] Integration tests covering admin workflows
- [ ] API documentation updated
- [ ] Admin portal UI can consume all endpoints
- [ ] Performance testing completed
- [ ] Security review passed
- [ ] Error handling and logging implemented