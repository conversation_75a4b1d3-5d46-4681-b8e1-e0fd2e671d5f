# Developer Workflow Guide

*Last Updated: September 21, 2025*  
*Version: 1.0*  
*Author: Winston <PERSON> <PERSON> Architect*

## 🎯 Overview

This guide provides the complete developer workflow for working on the IDP Platform using GitHub Projects + Claude AI integration. Designed for minimal cognitive overhead and maximum productivity.

**Project Board**: https://github.com/orgs/GPT-Integrators/projects/3/views/1

---

## 🚀 Quick Start

### Daily Developer Flow (30 seconds)

1. **Check Project Board** → Pick available story from Backlog
2. **Assign to <PERSON>** → <PERSON> auto-moves to "In Progress"  
3. **Tell <PERSON>**: `"work on github issue [URL]"`
4. **Code** → <PERSON> provides full context + creates branch
5. **Create PR** → Story auto-moves to "PR Review"
6. **Get Review** → Story moves to "Done" when merged

**That's it!** The automation handles project management, you focus on coding.

---

## 📋 Detailed Workflow

### Step 1: Story Selection

#### Finding Work

**Option A: Project Board**
1. Go to https://github.com/orgs/GPT-Integrators/projects/3/views/1
2. Look at "📋 Backlog" column
3. Pick a story that matches your skills/interests

**Option B: Epic View**
1. Filter by <PERSON><PERSON> (Epic 1, Epic 2, etc.)
2. See all stories for current epic
3. Pick next logical story to work on

**Option C: GitHub Issues**
1. Go to repository Issues tab
2. Filter by `label:story` and `is:open`
3. Sort by milestone or priority

#### Story Selection Criteria

**Good Stories to Pick:**
- ✅ Marked as `priority-high` for current epic
- ✅ Dependencies already completed (check "Depends on" section)
- ✅ Clear acceptance criteria defined
- ✅ Your skill level matches effort estimate (S/M/L)

**Stories to Avoid:**
- ❌ Blocked by incomplete dependencies
- ❌ Missing acceptance criteria
- ❌ Already assigned to someone else
- ❌ In future epic (unless current epic is done)

---

### Step 2: Claim Your Work

#### Assign the Issue

**Method 1: GitHub UI**
1. Open the issue/story
2. Click "Assignees" in right sidebar
3. Select yourself
4. **Auto-magic**: Story moves from "Backlog" → "In Progress"

**Method 2: Project Board**
1. Drag story from "Backlog" to "In Progress"
2. Click on story card
3. Assign to yourself

#### Branch Naming Convention

**Automatic (Recommended)**
When you tell Claude to work on an issue, it will create a branch following this pattern:
```bash
story/epic-[X]-story-[Y]-[short-description]
```

**Manual (If needed)**
```bash
# Epic 1 Examples
story/epic-1-story-1-database-schema
story/epic-1-story-2-api-key-auth
story/epic-1-story-3-health-check

# Epic 2 Examples  
story/epic-2-story-1-document-extract
story/epic-2-story-2-ai-integration
story/epic-2-story-3-usage-tracking
```

---

### Step 3: Claude Integration (The Magic ✨)

#### Primary Command

```
work on github issue https://github.com/GPT-Integrators/IDP-Platform/issues/[NUMBER]
```

#### What Claude Does Automatically

1. **Parses Issue**: Extracts epic, story details, acceptance criteria
2. **Loads Context**: Pulls relevant architecture docs, coding standards, PRD sections
3. **Creates Branch**: `git checkout -b story/epic-X-story-Y-description`
4. **Sets Up Environment**: Ensures development services are running
5. **Provides Guidance**: Full development context and implementation plan

#### Example Interaction

```
You: work on github issue https://github.com/GPT-Integrators/IDP-Platform/issues/15

Claude: I'll help you work on Epic 1 Story 2: "API Key Authentication System". 

Let me set up your development environment:

✅ Issue parsed: Epic 1, Medium effort, High priority
✅ Branch created: story/epic-1-story-2-api-key-auth  
✅ Context loaded: Authentication patterns, security requirements
✅ Architecture guidance: SHA-256 hashing, Edge Functions patterns

This story requires implementing:
- SHA-256 API key hashing
- API key validation Edge Function  
- Rate limiting middleware
- Authentication error handling
- Audit logging

Based on the architecture document, here's your implementation plan:
[Detailed implementation guidance follows...]

Ready to start coding? I have full context of the requirements and can guide you through each step.
```

---

### Step 4: Development Process

#### Working with Claude

**Start Development**
```
Claude, let's implement the API key hashing function first.
```

**Get Specific Help**
```
Claude, how should I structure the Edge Function for API validation according to our coding standards?

Claude, show me the database schema for the api_keys table.

Claude, what's the rate limiting pattern we decided on?
```

**Review Progress**
```
Claude, let's review what we've built so far against the acceptance criteria.
```

#### Development Best Practices

**Before You Start:**
- [ ] Supabase services running (`bun run dev`)
- [ ] Latest types generated (`bun run db:types`)
- [ ] Tests passing (`bun test`)

**During Development:**
- [ ] Follow acceptance criteria exactly
- [ ] Write tests as you go (TDD approach)
- [ ] Use TypeScript strictly (no `any` types)
- [ ] Follow security guidelines from coding standards
- [ ] Test with real API calls frequently

**Before Creating PR:**
- [ ] All acceptance criteria met
- [ ] Unit tests written and passing
- [ ] Integration tests pass
- [ ] Code follows style guidelines
- [ ] Performance requirements met

#### Key Development Commands

```bash
# Start development environment
bun run dev                    # Starts all Supabase services

# Generate types after schema changes
bun run db:types               # Updates TypeScript types

# Run tests
bun test                       # Unit tests  
bun run test:integration       # API tests
bun run test:manual            # Interactive testing

# Code quality
bun run lint                   # Check code style
bun run format                 # Format code
bun run type-check             # TypeScript validation

# Database operations
bun run db:reset               # Fresh database with seed data
bun run db:push                # Apply new migrations
```

---

### Step 5: Creating Pull Requests

#### When to Create a PR

**Ready Indicators:**
- ✅ All acceptance criteria completed
- ✅ Tests written and passing  
- ✅ Code reviewed by Claude for quality
- ✅ Manual testing completed
- ✅ No lint errors or type issues

#### PR Creation Process

**1. Push Your Branch**
```bash
git add .
git commit -m "Implement API key authentication system

- Add SHA-256 key hashing function
- Create API validation Edge Function
- Implement rate limiting middleware
- Add comprehensive error handling
- Include audit logging for security events

Closes #[issue-number]"

git push -u origin story/epic-1-story-2-api-key-auth
```

**2. Create PR via GitHub**
- GitHub will suggest creating a PR
- Click "Create Pull Request"

**3. PR Template Usage**

```markdown
## 🔗 Linked Issue
Closes #[issue-number]

## 📋 Summary
Brief description of what this PR implements.

## ✅ Acceptance Criteria Completed
- [x] SHA-256 API key hashing implemented
- [x] API key validation Edge Function created
- [x] Rate limiting middleware operational  
- [x] Authentication error handling with proper HTTP codes
- [x] Audit logging for all authentication events

## 🧪 Testing
- [x] Unit tests written and passing
- [x] Integration tests pass
- [x] Manual testing completed
- [x] Performance requirements met

## 🔍 Review Notes
- Focus on security implementation in `auth-validation/index.ts`
- Check rate limiting logic in middleware
- Verify audit logging captures all required events

## 📸 Screenshots/Evidence
[If applicable, add screenshots of working functionality]
```

**4. Auto-Magic Happens**
- **Issue moves**: "In Progress" → "PR Review"
- **Reviewers notified**: Based on CODEOWNERS or manual assignment
- **Checks run**: Automated tests and linting

---

### Step 6: Code Review Process

#### As Author (Getting Your PR Reviewed)

**What to Expect:**
- Reviewer will check code quality, security, performance
- May request changes or ask questions
- Usually 1-2 review cycles for most PRs

**Responding to Reviews:**
```bash
# Make requested changes
git add .
git commit -m "Address review feedback: improve error handling"
git push

# PR automatically updates, re-request review
```

**Review Etiquette:**
- ✅ Respond to all feedback (even if just "acknowledged")
- ✅ Ask questions if feedback is unclear
- ✅ Test suggested changes thoroughly
- ❌ Don't take feedback personally
- ❌ Don't argue without technical reasoning

#### As Reviewer (Reviewing Others' PRs)

**Review Checklist:**
- [ ] **Acceptance Criteria**: All items completed?
- [ ] **Code Quality**: Follows coding standards?
- [ ] **Security**: No vulnerabilities introduced?
- [ ] **Performance**: Meets response time requirements?
- [ ] **Tests**: Adequate coverage and quality?
- [ ] **Documentation**: Comments where needed?

**Review Types:**
- **✅ Approve**: Ready to merge, no issues
- **📝 Comment**: Suggestions but not blocking
- **❌ Request Changes**: Issues must be fixed before merge

**Review Response Time:**
- **High Priority**: Same day
- **Medium Priority**: Within 24 hours  
- **Low Priority**: Within 48 hours

---

### Step 7: Merge and Complete

#### Successful Merge

**What Happens:**
1. **Reviewer approves** PR
2. **Author or reviewer merges** (squash merge preferred)
3. **Auto-magic**: Issue moves "PR Review" → "Done"
4. **Branch deleted**: Automatically cleaned up
5. **Deployment**: Changes go to staging automatically

#### Rejected/Closed PR

**What Happens:**
1. **Reviewer closes** PR without merging
2. **Auto-magic**: Issue moves "PR Review" → "Backlog"
3. **Branch preserved**: For rework if needed
4. **New PR**: Create fresh PR when ready

#### After Merge

**Celebration Time! 🎉**
- [ ] Story marked as "Done"
- [ ] Epic progress updated
- [ ] Team metrics improved
- [ ] Pick next story from backlog

---

## 🎯 Advanced Workflows

### Epic Coordination

#### Starting New Epic

**Before Epic Kickoff:**
1. **Epic Planning**: Review all stories in milestone
2. **Dependency Check**: Ensure prerequisites are met
3. **Team Assignment**: Distribute stories based on skills
4. **Kick-off Meeting**: Discuss approach and timeline

#### Epic Progress Tracking

**Daily Standups:**
- What story are you working on?
- Any blockers or dependencies?
- When do you expect to complete current story?

**Epic Review:**
- Weekly epic progress review
- Identify bottlenecks or risks
- Adjust story priorities if needed

### Handling Blockers

#### Dependency Issues

**Story depends on incomplete work:**
1. **Comment on blocked story**: "Blocked by #[issue-number]"
2. **Pick different story**: From same epic if possible
3. **Help unblock**: Offer assistance to blocker owner
4. **Escalate**: If blocker affects multiple people

#### Technical Blockers

**Unclear requirements:**
1. **Comment on issue**: Ask specific questions
2. **Tag product owner**: For business requirement clarification
3. **Tag architect**: For technical architecture questions
4. **Schedule discussion**: If complex, schedule focused meeting

**Environment issues:**
1. **Check setup docs**: Review environment setup guide
2. **Ask in team chat**: Someone may have hit same issue
3. **Document solution**: Update docs when resolved

### Quality Gates

#### Definition of Done

**Story is not done until:**
- [ ] All acceptance criteria met
- [ ] Unit tests written and passing
- [ ] Integration tests pass
- [ ] Code review completed and approved
- [ ] Manual testing completed
- [ ] Performance requirements validated
- [ ] Security requirements met
- [ ] Documentation updated (if needed)

#### Epic Completion

**Epic is not done until:**
- [ ] All stories completed and merged
- [ ] Epic-level integration testing passed
- [ ] Epic demo completed
- [ ] Epic retrospective held
- [ ] Next epic dependencies satisfied

---

## 🛠️ Tools and Shortcuts

### Useful GitHub Features

#### Quick Issue Creation

**Create from Project Board:**
1. Click "+" in any column
2. Select "Create new issue"
3. Use story template automatically

**Create via URL:**
```
https://github.com/GPT-Integrators/IDP-Platform/issues/new?template=user-story.md&milestone=[epic-number]&labels=story,epic-X
```

#### Keyboard Shortcuts

- `g i` → Go to Issues
- `g p` → Go to Pull Requests  
- `c` → Create new issue/PR
- `/` → Search
- `?` → Show all shortcuts

#### GitHub CLI Commands

```bash
# Install GitHub CLI
brew install gh

# Create issue
gh issue create --title "[Epic 1] Story Title" --milestone "Epic 1" --label "story,epic-1"

# Create PR
gh pr create --title "Implement feature" --body "Closes #123"

# Check status
gh pr status
gh issue list --assignee @me
```

### Claude Integration Tips

#### Core Development Commands

**Getting Context:**
```
Claude, explain the requirements for story #123

Claude, show me the architecture section relevant to authentication

Claude, what coding standards apply to Edge Functions?

Claude, what's the current status of this story? Show me what's been done and what's left.

Claude, refresh the context for this issue if anything seems out of date
```

**Implementation Help:**
```
Claude, implement the SHA-256 hashing function according to our security standards

Claude, create the Edge Function structure for API validation

Claude, write unit tests for the authentication middleware

Claude, show me the correct database schema for this feature

Claude, what's the proper error handling pattern for this endpoint?
```

**Quality Checks:**
```
Claude, review this code against our acceptance criteria

Claude, check if this meets our performance requirements

Claude, validate this follows our security guidelines

Claude, run through the acceptance criteria and tell me what's complete vs missing

Claude, what potential issues do you see with this implementation?
```

#### Git & Branch Management

**Branch Operations:**
```
Claude, create a new branch for issue #123

Claude, switch to the main branch and pull latest changes

Claude, switch back to my feature branch

Claude, delete this feature branch after I'm done with it

Claude, show me what branch I'm currently on

Claude, list all my local branches
```

**Commit Management:**
```
Claude, commit the current changes with a proper commit message for this story

Claude, make a checkpoint commit with the work I've done so far

Claude, amend the last commit to include these new changes

Claude, show me what changes I have staged vs unstaged

Claude, stage all the files I've been working on for this story
```

**Push & Sync Operations:**
```
Claude, push my current branch to GitHub

Claude, push my changes and set upstream tracking

Claude, pull the latest changes from main and merge them into my branch

Claude, rebase my branch on the latest main

Claude, sync my branch with the remote version
```

#### Pull Request Management

**PR Creation:**
```
Claude, create a pull request for this story

Claude, push my changes and create a PR that closes issue #123

Claude, generate a proper PR title and description for this work

Claude, create a draft PR so I can get early feedback

Claude, mark my draft PR as ready for review
```

**PR Updates:**
```
Claude, push my latest changes to update the existing PR

Claude, address the review comments and update the PR

Claude, add a comment to the PR explaining the changes I made

Claude, request a re-review after addressing feedback
```

**PR Completion:**
```
Claude, merge this PR using squash merge

Claude, delete the feature branch after the PR is merged

Claude, check if my PR has been approved and is ready to merge
```

#### Issue & Project Management

**Issue Operations:**
```
Claude, add a comment to issue #123 with my current progress

Claude, update the issue with any blockers I've encountered

Claude, mark this issue as blocked by issue #456

Claude, assign issue #789 to myself

Claude, move this issue to "In Progress" on the project board
```

**Story Navigation:**
```
Claude, show me the next story I should work on after this one

Claude, find issues that are blocked waiting for this story to complete

Claude, list all the open stories for Epic 2

Claude, show me which stories in Epic 1 are still available to work on
```

#### Testing & Quality Assurance

**Test Commands:**
```
Claude, run the unit tests for the code I just wrote

Claude, run all tests and show me if anything is failing

Claude, create integration tests for this API endpoint

Claude, run the manual test script for this feature

Claude, check if my changes pass the linting rules
```

**Quality Validation:**
```
Claude, validate this code follows our TypeScript standards

Claude, check if this meets our security requirements

Claude, verify this implementation handles all the edge cases

Claude, make sure this code is properly documented

Claude, ensure this follows our API response format standards
```

#### Environment & Setup

**Development Environment:**
```
Claude, start my development environment (Supabase services)

Claude, check if all my development services are running

Claude, reset my database to a clean state for testing

Claude, generate fresh TypeScript types from the database schema

Claude, restart the Edge Functions if they're not responding
```

**Dependency Management:**
```
Claude, install any new dependencies this story needs

Claude, update the project dependencies to latest versions

Claude, check if there are any conflicting dependencies

Claude, clean and reinstall all node modules
```

#### Advanced Git Operations

**Branch Cleanup:**
```
Claude, show me which branches I can safely delete

Claude, delete all merged feature branches

Claude, clean up old branches that are no longer needed

Claude, prune remote tracking branches that no longer exist
```

**Conflict Resolution:**
```
Claude, help me resolve these merge conflicts

Claude, show me what files have conflicts and help me fix them

Claude, abort this merge and let me try a different approach

Claude, rebase my branch and help with any conflicts that arise
```

**History & Recovery:**
```
Claude, show me the git history for this file

Claude, find when a specific change was made to this code

Claude, help me recover some code I accidentally deleted

Claude, reset my branch to match the remote version

Claude, create a backup of my current work before I make risky changes
```

#### Context Management

**Session Management:**
```
work on github issue [URL]

I'm done with issue #123, now work on github issue #124

I'm continuing work on github issue #123 where we left off

Claude, save the current context and switch to issue #456

Claude, remind me what I was working on yesterday

Claude, summarize the progress on all my active stories
```

**Multi-Story Juggling:**
```
Claude, pause work on this story and switch to urgent issue #789

Claude, what stories do I have in progress right now?

Claude, help me finish up story #123 so I can move to the next one

Claude, show me which of my stories are closest to completion
```

#### Team Collaboration

**Communication:**
```
Claude, help me write a status update for the daily standup

Claude, draft a message asking for help with this technical challenge

Claude, create a handoff document for this story if someone else needs to take over

Claude, write a comment explaining the approach I took for this implementation
```

**Knowledge Sharing:**
```
Claude, document the solution to this problem for the team

Claude, create a troubleshooting guide for this issue

Claude, write up the lessons learned from implementing this feature

Claude, help me create documentation for this new API endpoint
```

#### Troubleshooting & Debugging

**Problem Diagnosis:**
```
Claude, help me debug why this test is failing

Claude, analyze this error message and suggest solutions

Claude, check if this performance issue violates our requirements

Claude, help me figure out why the CI pipeline is failing

Claude, diagnose why my Edge Function isn't starting correctly
```

**System Health:**
```
Claude, check if all our services are healthy

Claude, verify that my local environment matches the requirements

Claude, test if the API endpoints are working correctly

Claude, validate that the database schema is correct

Claude, make sure my development setup is properly configured
```

#### Epic & Milestone Tracking

**Progress Tracking:**
```
Claude, show me the progress on Epic 1

Claude, what stories are left to complete this milestone?

Claude, check if there are any stories blocking Epic 2

Claude, estimate how much work is left in the current epic

Claude, identify any dependencies that might delay the milestone
```

**Planning & Coordination:**
```
Claude, help me plan which story to work on next

Claude, check if any of my stories are blocking other team members

Claude, find stories that are ready to be worked on

Claude, suggest the optimal order to complete the remaining stories
```

#### Code Generation & Scaffolding

**Quick Scaffolding:**
```
Claude, create the basic Edge Function structure for this story

Claude, generate the database migration for this feature

Claude, scaffold the test files I'll need for this story

Claude, create the TypeScript interfaces based on the API requirements

Claude, generate the CRUD operations for this new table

Claude, create a basic Supabase function template

Claude, set up the folder structure for this new feature
```

**Code Generation:**
```
Claude, write the boilerplate code for this API endpoint

Claude, generate the validation schema for this form

Claude, create the error handling wrapper for this function

Claude, write the SQL queries I need for this feature

Claude, generate the mock data for testing this feature

Claude, create the API response types for this endpoint
```

#### Performance & Optimization

**Performance Analysis:**
```
Claude, check if this code meets our performance requirements

Claude, analyze the API response times for this endpoint

Claude, suggest optimizations for this database query

Claude, profile this function to see if it's efficient enough

Claude, check if this implementation will scale with our load requirements

Claude, identify potential performance bottlenecks in this code
```

**Cost Optimization:**
```
Claude, calculate the AI model costs for this processing workflow

Claude, suggest cheaper model alternatives that still meet quality requirements

Claude, estimate the credit usage for this feature

Claude, check if this implementation stays within our profit margin targets

Claude, optimize this AI prompt to reduce token costs
```

#### Security & Compliance

**Security Validation:**
```
Claude, audit this code for security vulnerabilities

Claude, check if this API endpoint has proper authentication

Claude, validate that user inputs are properly sanitized

Claude, ensure this follows our data encryption standards

Claude, verify this implementation doesn't leak sensitive information

Claude, check if this meets our API key security requirements

Claude, scan for potential SQL injection vulnerabilities
```

**Compliance Checks:**
```
Claude, ensure this feature complies with our data retention policies

Claude, check if this handles GDPR requirements correctly

Claude, validate the audit logging for this operation

Claude, verify this follows our user consent requirements
```

#### Documentation & Knowledge Management

**Auto-Documentation:**
```
Claude, generate API documentation for this endpoint

Claude, create inline code comments for this complex function

Claude, write a README section for this new feature

Claude, document the deployment steps for this change

Claude, create troubleshooting guides for common issues with this feature

Claude, write user-facing documentation for this API change

Claude, generate changelog entries for this release
```

**Knowledge Capture:**
```
Claude, document the decision-making process for this implementation

Claude, create a technical debt note for future refactoring

Claude, write a post-mortem for this bug fix

Claude, document the performance benchmarks for this feature

Claude, create a runbook for operating this new service
```

#### DevOps & Deployment

**Environment Management:**
```
Claude, check if my environment variables are properly configured

Claude, validate the deployment configuration for this feature

Claude, test the database migrations in the staging environment

Claude, verify all services are properly connected

Claude, check if the production environment is ready for this deployment

Claude, validate the CI/CD pipeline configuration
```

**Monitoring & Alerts:**
```
Claude, set up monitoring for this new API endpoint

Claude, create alerts for error rates on this feature

Claude, configure performance monitoring for this service

Claude, set up cost tracking for this AI integration

Claude, create dashboards for monitoring this feature's health
```

#### Customer & Business Impact

**Impact Analysis:**
```
Claude, estimate how this change will affect existing customers

Claude, identify which API clients might be impacted by this change

Claude, calculate the expected cost savings from this optimization

Claude, predict the performance improvement from this change

Claude, assess the risk level of deploying this feature
```

**Business Metrics:**
```
Claude, help me track the business impact of this feature

Claude, set up metrics to measure the success of this implementation

Claude, calculate the ROI of this optimization

Claude, measure how this affects our profit margins

Claude, track customer adoption of this new feature
```

#### Learning & Skill Development

**Code Review & Learning:**
```
Claude, explain the architectural patterns used in this codebase

Claude, teach me the best practices for this type of implementation

Claude, review my code and suggest improvements for future stories

Claude, explain why this approach is better than alternatives

Claude, help me understand the trade-offs in this design decision

Claude, show me examples of similar implementations in our codebase
```

**Skill Building:**
```
Claude, suggest resources to learn more about this technology

Claude, recommend practice exercises for improving my skills in this area

Claude, help me understand the broader context of this feature

Claude, explain the business reasoning behind these technical requirements
```

#### Crisis Management & Hotfixes

**Emergency Response:**
```
Claude, help me quickly identify the root cause of this production issue

Claude, create a hotfix branch for this critical bug

Claude, implement the minimum viable fix for this urgent issue

Claude, prepare an emergency deployment for this critical patch

Claude, help me roll back this problematic deployment

Claude, create an incident response document for this issue
```

**Damage Control:**
```
Claude, assess the blast radius of this production issue

Claude, identify which customers are affected by this bug

Claude, prepare customer communication about this incident

Claude, calculate the business impact of this outage

Claude, create a timeline of events for this incident
```

#### AI & Machine Learning Operations

**Model Management:**
```
Claude, test this prompt with different AI models to compare results

Claude, optimize this AI prompt for better accuracy

Claude, implement fallback logic for when AI models are unavailable

Claude, track the confidence scores from AI model responses

Claude, benchmark the performance of different model configurations

Claude, implement A/B testing for different AI approaches
```

**Quality Assurance:**
```
Claude, validate the accuracy of AI-generated responses

Claude, implement quality gates for AI model outputs

Claude, create test cases for edge cases in AI processing

Claude, monitor for AI model drift or degradation

Claude, implement human review workflows for AI decisions
```

#### Database & Data Management

**Data Operations:**
```
Claude, create database indexes to optimize this query

Claude, implement data migration scripts for this schema change

Claude, set up data backup and recovery procedures

Claude, optimize database performance for this workload

Claude, implement data validation rules for this table

Claude, create database maintenance scripts
```

**Data Analysis:**
```
Claude, analyze the data patterns for this feature

Claude, identify data quality issues in this dataset

Claude, create reports on database performance metrics

Claude, monitor data growth and storage usage

Claude, implement data archiving strategies
```

---

## 🐛 Troubleshooting

### Common Issues

#### Story Not Moving on Project Board

**Symptoms**: Assigned issue stays in "Backlog"

**Solutions:**
1. **Check issue is on project board**: Add manually if missing
2. **Verify automation**: Check GitHub Actions completed successfully
3. **Manual move**: Drag story to "In Progress" manually
4. **Check permissions**: Ensure you have write access

#### Branch Creation Fails

**Symptoms**: Can't create branch or Claude reports errors

**Solutions:**
```bash
# Check current branch
git branch

# Ensure you're on main
git checkout main

# Pull latest changes
git pull origin main

# Try manual branch creation
git checkout -b story/epic-X-story-Y-description
```

#### Tests Failing

**Symptoms**: `bun test` returns errors

**Solutions:**
1. **Check environment**: Ensure Supabase services running
2. **Update types**: Run `bun run db:types`
3. **Reset database**: Run `bun run db:reset` for clean state
4. **Check dependencies**: Run `bun install` to update packages

#### Claude Context Issues

**Symptoms**: Claude doesn't understand story or architecture

**Solutions:**
1. **Reload context**: Restart Claude session and re-run issue command
2. **Verify issue URL**: Ensure URL is correct and accessible
3. **Manual context**: Share issue text directly if URL parsing fails
4. **Check documentation**: Ensure architecture docs are up to date

### Getting Help

#### Self-Service

1. **Check this guide**: Most common scenarios covered
2. **Review setup docs**: Verify configuration is correct
3. **Search existing issues**: Someone may have had same problem
4. **Check GitHub Actions**: Look for automation failures

#### Team Support

1. **Team Chat**: Quick questions and immediate help
2. **GitHub Issues**: Create issue with `question` label
3. **Pair Programming**: Screen share for complex problems
4. **Team Meeting**: Escalate persistent blockers

#### Escalation

1. **Technical Architect**: For architecture or design questions
2. **Product Owner**: For business requirement clarification
3. **Project Manager**: For process or timeline issues

---

## 📊 Metrics and Improvement

### Personal Metrics

**Track Your Progress:**
- Stories completed per week
- Average time per story type (S/M/L)
- Review turnaround time
- Defect rate (bugs found in your code)

**View in GitHub:**
- Go to your profile
- Check contribution graph
- Review PR history
- Track issue assignments

### Team Metrics

**Epic Progress:**
- Stories completed vs. planned
- Epic completion timeline
- Bottleneck identification
- Velocity trends

**Quality Metrics:**
- Test coverage percentage
- Bug rate per epic
- Review cycle time
- Deployment success rate

### Continuous Improvement

**Weekly Retrospectives:**
- What went well?
- What can be improved?
- What should we start/stop/continue?
- Process adjustments needed?

**Process Evolution:**
- Suggest workflow improvements
- Share effective practices
- Document solutions to common problems
- Update this guide based on learnings

---

## 🎯 Success Tips

### Productivity Maximizers

**Start of Day:**
- [ ] Check project board for updates
- [ ] Review any PR feedback received
- [ ] Pick next story if current one is done
- [ ] Ensure development environment is ready

**During Development:**
- [ ] Use Claude for context and guidance
- [ ] Test frequently with real API calls
- [ ] Follow TDD (write tests first)
- [ ] Commit small, logical changes
- [ ] Ask questions early when blocked

**End of Day:**
- [ ] Commit all work (even if incomplete)
- [ ] Update story with progress notes
- [ ] Review tomorrow's planned work
- [ ] Check for any blocking issues

### Quality Maximizers

**Code Quality:**
- Use TypeScript strictly (no `any` types)
- Follow established patterns from architecture
- Write self-documenting code
- Include error handling for all edge cases

**Security:**
- Never commit secrets or API keys
- Follow SHA-256 hashing for sensitive data
- Validate all inputs thoroughly
- Implement proper rate limiting

**Performance:**
- Test API response times regularly
- Optimize database queries
- Use caching appropriately
- Monitor resource usage

### Collaboration Maximizers

**Communication:**
- Update stories with progress regularly
- Ask questions in public channels when possible
- Share discoveries and solutions
- Help others when you have expertise

**Code Reviews:**
- Review others' PRs promptly
- Provide constructive feedback
- Learn from others' approaches
- Be open to feedback on your code

**Team Support:**
- Offer help when others are blocked
- Share useful tools or techniques
- Contribute to team documentation
- Participate in retrospectives actively

---

*This developer workflow guide provides everything you need for efficient, high-quality development on the IDP Platform. Focus on code quality, help your teammates, and ship great features!*

---

*Last Updated: September 21, 2025*  
*Workflow Version: 1.0*  
*Compatible with: GitHub Projects (Beta), Claude AI, Supabase Edge Functions*