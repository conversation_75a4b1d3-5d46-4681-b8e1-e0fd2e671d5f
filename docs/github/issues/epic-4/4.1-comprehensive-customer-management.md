**Title:** [Epic 4] Comprehensive Customer Management

**Milestone:** Epic 4: Advanced Admin & Customer Management
**Labels:** story, epic-4, priority-medium, effort-M

## 🎯 User Story
As a **Platform Administrator**,
I want **complete customer lifecycle management capabilities**,
So that **I can efficiently onboard, manage, and support customers**.

## 📋 Requirements
Implement comprehensive customer management system covering the complete customer lifecycle from onboarding through offboarding, with tier management, contact tracking, and audit trails for enterprise customer support.

## ✅ Acceptance Criteria
- [ ] Customer creation with company details and contact information
- [ ] Customer status management (active, suspended, trial, enterprise)
- [ ] Customer profile editing with audit trail
- [ ] Customer deletion with data retention compliance
- [ ] Customer search and filtering by various criteria
- [ ] Customer notes and communication history tracking
- [ ] Customer tier management with different service levels
- [ ] Customer onboarding workflow automation
- [ ] Bulk customer operations for enterprise management
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Customer Lifecycle States:**
```typescript
type CustomerStatus =
  | 'trial'        // 30-day trial period
  | 'active'       // Paying customer
  | 'suspended'    // Temporarily disabled
  | 'cancelled'    // Account closed, data retained
  | 'enterprise'   // Special enterprise treatment
  | 'churned';     // Left platform, data archived

interface Customer {
  id: string;
  company_name: string;
  primary_contact: ContactInfo;
  billing_contact?: ContactInfo;
  technical_contact?: ContactInfo;
  status: CustomerStatus;
  tier: 'free' | 'starter' | 'professional' | 'enterprise';
  created_at: Date;
  trial_ends_at?: Date;
  last_login_at?: Date;
  total_documents_processed: number;
  total_spend_usd: number;
  settings: CustomerSettings;
  metadata: Record<string, any>;
}
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/admin/customers/index.ts` - Customer CRUD operations
- Function: `supabase/functions/admin/customers/bulk/index.ts` - Bulk operations
- Utils: `customer-manager.ts` - Core customer management logic
- Utils: `customer-lifecycle.ts` - Lifecycle state management
- Types: Customer management interfaces and status types
- Tests: `tests/integration/customer-management.test.ts`

**Customer Management API Implementation:**
```typescript
// POST /admin/customers - Create new customer
interface CreateCustomerRequest {
  company_name: string;
  primary_contact: {
    name: string;
    email: string;
    phone?: string;
    title?: string;
  };
  billing_contact?: ContactInfo;
  technical_contact?: ContactInfo;
  tier: 'starter' | 'professional' | 'enterprise';
  trial_duration_days?: number; // Default 30
  initial_credits?: number;
  metadata?: Record<string, any>;
}

const createCustomer = async (req: Request): Promise<CustomerResponse> => {
  await validateAdminAccess(req);
  const customerData: CreateCustomerRequest = await req.json();

  // Validate company name uniqueness
  await validateUniqueCompanyName(customerData.company_name);

  const customerId = generateCustomerId();
  const now = new Date();

  const customer: Customer = {
    id: customerId,
    company_name: customerData.company_name,
    primary_contact: customerData.primary_contact,
    billing_contact: customerData.billing_contact,
    technical_contact: customerData.technical_contact,
    status: 'trial',
    tier: customerData.tier,
    created_at: now,
    trial_ends_at: new Date(now.getTime() + (customerData.trial_duration_days || 30) * 24 * 60 * 60 * 1000),
    total_documents_processed: 0,
    total_spend_usd: 0,
    settings: getDefaultCustomerSettings(customerData.tier),
    metadata: customerData.metadata || {}
  };

  // Create customer record
  const { data: createdCustomer } = await supabase
    .from('customers')
    .insert(customer)
    .select()
    .single();

  // Generate initial API key pair
  const apiKeys = await generateInitialApiKeys(customerId, customerData.tier);

  // Log customer creation
  await logCustomerEvent(customerId, 'customer_created', {
    tier: customerData.tier,
    trial_duration: customerData.trial_duration_days || 30,
    created_by: await getAdminUser(req)
  });

  // Trigger onboarding workflow
  await triggerOnboardingWorkflow(customer, apiKeys);

  return {
    customer: createdCustomer,
    api_keys: apiKeys,
    onboarding_status: 'initiated'
  };
};
```

**Customer Status Management:**
```typescript
// PUT /admin/customers/{id}/status
interface UpdateStatusRequest {
  status: CustomerStatus;
  reason: string;
  notify_customer?: boolean;
  scheduled_date?: Date; // For future status changes
}

const updateCustomerStatus = async (
  customerId: string,
  statusUpdate: UpdateStatusRequest,
  adminUserId: string
): Promise<StatusUpdateResult> => {
  const customer = await getCustomer(customerId);
  const oldStatus = customer.status;

  // Validate status transition
  const transition = validateStatusTransition(oldStatus, statusUpdate.status);
  if (!transition.valid) {
    throw new Error(`Invalid status transition: ${oldStatus} -> ${statusUpdate.status}`);
  }

  // Apply status-specific logic
  const updateData = await prepareStatusUpdate(customer, statusUpdate);

  // Update customer status
  const { data: updatedCustomer } = await supabase
    .from('customers')
    .update({
      status: statusUpdate.status,
      ...updateData,
      updated_at: new Date()
    })
    .eq('id', customerId)
    .select()
    .single();

  // Log status change
  await logCustomerEvent(customerId, 'status_changed', {
    old_status: oldStatus,
    new_status: statusUpdate.status,
    reason: statusUpdate.reason,
    changed_by: adminUserId
  });

  // Notify customer if requested
  if (statusUpdate.notify_customer) {
    await notifyCustomerStatusChange(customer, statusUpdate);
  }

  // Execute status-specific actions
  await executeStatusActions(customer, statusUpdate.status);

  return {
    customer: updatedCustomer,
    transition: transition,
    actions_executed: await getExecutedActions(statusUpdate.status)
  };
};

const validateStatusTransition = (from: CustomerStatus, to: CustomerStatus): TransitionValidation => {
  const validTransitions: Record<CustomerStatus, CustomerStatus[]> = {
    trial: ['active', 'cancelled', 'suspended'],
    active: ['suspended', 'cancelled', 'enterprise'],
    suspended: ['active', 'cancelled'],
    cancelled: ['active'], // Can reactivate cancelled customers
    enterprise: ['active', 'suspended', 'cancelled'],
    churned: [] // Terminal state
  };

  return {
    valid: validTransitions[from]?.includes(to) || false,
    from,
    to,
    reason: validTransitions[from]?.includes(to)
      ? 'Valid transition'
      : `Transition from ${from} to ${to} not allowed`
  };
};
```

**Customer Search and Filtering:**
```typescript
// GET /admin/customers
interface CustomerSearchParams {
  q?: string;              // Search query
  status?: CustomerStatus[];
  tier?: string[];
  created_after?: Date;
  created_before?: Date;
  last_login_after?: Date;
  min_spend?: number;
  max_spend?: number;
  has_active_trial?: boolean;
  sort_by?: 'created_at' | 'company_name' | 'total_spend' | 'last_login';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

const searchCustomers = async (params: CustomerSearchParams): Promise<CustomerSearchResult> => {
  let query = supabase
    .from('customers')
    .select(`
      id, company_name, primary_contact, status, tier,
      created_at, trial_ends_at, last_login_at,
      total_documents_processed, total_spend_usd,
      api_keys!inner(count)
    `, { count: 'exact' });

  // Apply text search
  if (params.q) {
    query = query.or(
      `company_name.ilike.%${params.q}%,primary_contact->>email.ilike.%${params.q}%`
    );
  }

  // Apply filters
  if (params.status?.length) {
    query = query.in('status', params.status);
  }

  if (params.tier?.length) {
    query = query.in('tier', params.tier);
  }

  if (params.created_after) {
    query = query.gte('created_at', params.created_after.toISOString());
  }

  if (params.created_before) {
    query = query.lte('created_at', params.created_before.toISOString());
  }

  if (params.min_spend) {
    query = query.gte('total_spend_usd', params.min_spend);
  }

  if (params.max_spend) {
    query = query.lte('total_spend_usd', params.max_spend);
  }

  if (params.has_active_trial !== undefined) {
    if (params.has_active_trial) {
      query = query.eq('status', 'trial').gte('trial_ends_at', new Date().toISOString());
    } else {
      query = query.neq('status', 'trial');
    }
  }

  // Apply sorting
  const sortBy = params.sort_by || 'created_at';
  const sortOrder = params.sort_order || 'desc';
  query = query.order(sortBy, { ascending: sortOrder === 'asc' });

  // Apply pagination
  const page = params.page || 1;
  const limit = Math.min(params.limit || 50, 100);
  const offset = (page - 1) * limit;
  query = query.range(offset, offset + limit - 1);

  const { data: customers, error, count } = await query;

  if (error) throw error;

  return {
    customers: customers || [],
    pagination: {
      current_page: page,
      total_pages: Math.ceil((count || 0) / limit),
      total_count: count || 0,
      page_size: limit
    },
    filters_applied: params
  };
};
```

**Customer Tier Management:**
```typescript
const TIER_CONFIGURATIONS = {
  free: {
    max_api_keys: 2,
    max_documents_per_month: 100,
    max_agents: 5,
    support_level: 'community',
    features: ['basic_extraction', 'default_agents']
  },
  starter: {
    max_api_keys: 5,
    max_documents_per_month: 2500,
    max_agents: 25,
    support_level: 'email',
    features: ['basic_extraction', 'default_agents', 'agent_cloning']
  },
  professional: {
    max_api_keys: 15,
    max_documents_per_month: 10000,
    max_agents: 100,
    support_level: 'priority_email',
    features: ['all_extraction', 'custom_agents', 'webhooks', 'analytics']
  },
  enterprise: {
    max_api_keys: -1, // Unlimited
    max_documents_per_month: -1, // Unlimited
    max_agents: -1, // Unlimited
    support_level: 'dedicated',
    features: ['all_features', 'custom_models', 'sla_guarantee', 'dedicated_support']
  }
};

// PUT /admin/customers/{id}/tier
const updateCustomerTier = async (
  customerId: string,
  newTier: string,
  adminUserId: string
): Promise<TierUpdateResult> => {
  const customer = await getCustomer(customerId);
  const oldTierConfig = TIER_CONFIGURATIONS[customer.tier];
  const newTierConfig = TIER_CONFIGURATIONS[newTier];

  // Validate tier change doesn't violate current usage
  const currentUsage = await getCustomerUsage(customerId);
  const violations = validateTierLimits(currentUsage, newTierConfig);

  if (violations.length > 0 && newTierConfig.max_api_keys !== -1) {
    throw new Error(`Tier change would violate limits: ${violations.join(', ')}`);
  }

  // Update customer tier
  const { data: updatedCustomer } = await supabase
    .from('customers')
    .update({
      tier: newTier,
      settings: {
        ...customer.settings,
        ...getDefaultCustomerSettings(newTier)
      },
      updated_at: new Date()
    })
    .eq('id', customerId)
    .select()
    .single();

  // Update API key limits
  await updateApiKeyLimits(customerId, newTierConfig);

  // Log tier change
  await logCustomerEvent(customerId, 'tier_changed', {
    old_tier: customer.tier,
    new_tier: newTier,
    changed_by: adminUserId
  });

  return {
    customer: updatedCustomer,
    old_tier: customer.tier,
    new_tier: newTier,
    configuration: newTierConfig
  };
};
```

**Customer Notes and Communication Tracking:**
```typescript
// POST /admin/customers/{id}/notes
interface CustomerNote {
  id: string;
  customer_id: string;
  author_id: string;
  type: 'general' | 'support' | 'billing' | 'technical';
  title: string;
  content: string;
  visibility: 'internal' | 'customer';
  created_at: Date;
  updated_at: Date;
}

const addCustomerNote = async (
  customerId: string,
  note: CreateNoteRequest,
  authorId: string
): Promise<CustomerNote> => {
  const noteData = {
    customer_id: customerId,
    author_id: authorId,
    type: note.type,
    title: note.title,
    content: note.content,
    visibility: note.visibility || 'internal',
    created_at: new Date(),
    updated_at: new Date()
  };

  const { data: createdNote } = await supabase
    .from('customer_notes')
    .insert(noteData)
    .select()
    .single();

  // Auto-tag based on content
  const tags = await analyzeNoteContent(note.content);
  if (tags.length > 0) {
    await addNoteTags(createdNote.id, tags);
  }

  return createdNote;
};
```

## 🧪 Testing Requirements
- [ ] Customer creation generates proper initial setup
- [ ] Status transitions follow valid state machine rules
- [ ] Search and filtering return accurate results
- [ ] Tier management enforces appropriate limits
- [ ] Audit trails capture all customer changes
- [ ] Bulk operations handle errors gracefully

## 🔗 Dependencies
**Depends on**: #[1.4 Basic API Key Management], #[2.5 Usage Tracking & Credit System]
**Blocks**: #[4.3 Credit Management System], #[4.6 Customer Support Tools]

## 📊 Metadata
- **Epic**: Epic 4
- **Effort**: M (Medium: 1-3 days)
- **Priority**: Medium

---

### 💡 Development Notes
This customer management foundation provides the enterprise administrative capabilities that enable senior developers to implement any customer relationship or lifecycle management feature with comprehensive audit trails and tier-based access controls.