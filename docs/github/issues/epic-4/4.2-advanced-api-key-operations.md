**Title:** [Epic 4] Advanced API Key Operations

**Milestone:** Epic 4: Advanced Admin & Customer Management
**Labels:** story, epic-4, priority-medium, effort-S

## 🎯 User Story
As a **Platform Administrator**,
I want **comprehensive API key management with granular controls**,
So that **I can provide flexible access and prevent abuse**.

## 📋 Requirements
Implement advanced API key management capabilities including granular scope controls, expiration management, key rotation, usage analytics, and emergency response features for enterprise customer support.

## ✅ Acceptance Criteria
- [ ] API key generation with custom expiration dates
- [ ] Key suspension and reactivation without deletion
- [ ] Key scope limitation (specific endpoints, agent access)
- [ ] Bulk key operations for enterprise customers
- [ ] Key rotation with seamless transition periods
- [ ] Key usage analytics with detailed breakdowns
- [ ] Emergency key revocation with immediate effect
- [ ] Key inheritance for team management
- [ ] Automated key lifecycle management
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Advanced API Key Features:**
```typescript
interface AdvancedApiKey extends ApiKey {
  scopes: KeyScope[];
  expires_at?: Date;
  last_used_at?: Date;
  usage_limits: UsageLimits;
  rotation_policy?: RotationPolicy;
  team_access?: TeamAccess;
  emergency_revoked: boolean;
  metadata: Record<string, any>;
}

interface KeyScope {
  resource: 'documents' | 'agents' | 'analytics' | 'admin';
  actions: string[]; // ['read', 'write', 'delete']
  restrictions?: Record<string, any>;
}

interface UsageLimits {
  requests_per_minute?: number;
  requests_per_hour?: number;
  requests_per_day?: number;
  requests_per_month?: number;
  concurrent_requests?: number;
  data_transfer_mb?: number;
}
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/admin/api-keys/advanced/index.ts`
- Utils: `advanced-key-manager.ts` - Advanced key operations
- Utils: `key-scope-validator.ts` - Scope validation and enforcement
- Utils: `key-rotation-manager.ts` - Automated rotation logic
- Types: Advanced API key interfaces and scope types
- Tests: `tests/integration/advanced-api-keys.test.ts`

**Advanced Key Generation:**
```typescript
// POST /admin/api-keys/advanced
interface CreateAdvancedKeyRequest {
  customer_id: string;
  name: string;
  description?: string;
  key_type: 'test' | 'production';
  expires_at?: Date;
  scopes: KeyScope[];
  usage_limits?: UsageLimits;
  rotation_policy?: {
    auto_rotate: boolean;
    rotation_interval_days?: number;
    advance_notice_days?: number;
  };
  team_access?: {
    shared: boolean;
    allowed_users?: string[];
    permissions?: TeamPermission[];
  };
  metadata?: Record<string, any>;
}

const createAdvancedApiKey = async (req: Request): Promise<AdvancedApiKeyResponse> => {
  await validateAdminAccess(req);
  const keyRequest: CreateAdvancedKeyRequest = await req.json();

  // Validate customer exists and tier supports advanced features
  const customer = await validateCustomerTier(keyRequest.customer_id, ['professional', 'enterprise']);

  // Validate scope permissions
  await validateScopePermissions(keyRequest.scopes, customer.tier);

  // Generate secure API key
  const rawKey = generateSecureApiKey(keyRequest.key_type);
  const keyHash = await hashApiKey(rawKey);
  const keyId = generateKeyId();

  const advancedKey: AdvancedApiKey = {
    id: keyId,
    customer_id: keyRequest.customer_id,
    name: keyRequest.name,
    description: keyRequest.description,
    key_type: keyRequest.key_type,
    key_hash: keyHash,
    scopes: keyRequest.scopes,
    expires_at: keyRequest.expires_at,
    usage_limits: keyRequest.usage_limits || getDefaultUsageLimits(customer.tier),
    rotation_policy: keyRequest.rotation_policy,
    team_access: keyRequest.team_access,
    emergency_revoked: false,
    status: 'active',
    created_at: new Date(),
    metadata: keyRequest.metadata || {}
  };

  // Store key in database
  const { data: createdKey } = await supabase
    .from('api_keys')
    .insert(advancedKey)
    .select()
    .single();

  // Set up rotation if configured
  if (keyRequest.rotation_policy?.auto_rotate) {
    await scheduleKeyRotation(keyId, keyRequest.rotation_policy);
  }

  // Log key creation
  await logKeyEvent(keyId, 'advanced_key_created', {
    scopes: keyRequest.scopes,
    created_by: await getAdminUser(req)
  });

  return {
    key: createdKey,
    raw_key: rawKey, // Only returned once
    warnings: await validateKeyConfiguration(advancedKey)
  };
};
```

**Scope Validation and Enforcement:**
```typescript
class ScopeValidator {
  // Validate API request against key scopes
  async validateRequest(apiKey: AdvancedApiKey, request: ApiRequest): Promise<ScopeValidationResult> {
    const endpoint = this.parseEndpoint(request.path);
    const method = request.method;

    // Check if key has access to this resource
    const resourceScope = apiKey.scopes.find(scope => scope.resource === endpoint.resource);
    if (!resourceScope) {
      return {
        allowed: false,
        reason: `No access to resource: ${endpoint.resource}`
      };
    }

    // Check if key has permission for this action
    const requiredAction = this.mapMethodToAction(method);
    if (!resourceScope.actions.includes(requiredAction)) {
      return {
        allowed: false,
        reason: `No permission for action: ${requiredAction} on ${endpoint.resource}`
      };
    }

    // Check resource-specific restrictions
    if (resourceScope.restrictions) {
      const restrictionCheck = await this.validateRestrictions(
        resourceScope.restrictions,
        request
      );
      if (!restrictionCheck.allowed) {
        return restrictionCheck;
      }
    }

    return { allowed: true };
  }

  private parseEndpoint(path: string): { resource: string; operation: string } {
    // Parse API endpoint to determine resource and operation
    const pathParts = path.split('/').filter(p => p);

    if (pathParts.includes('extract')) {
      return { resource: 'documents', operation: 'process' };
    } else if (pathParts.includes('agents')) {
      return { resource: 'agents', operation: pathParts.includes('clone') ? 'clone' : 'read' };
    } else if (pathParts.includes('analytics')) {
      return { resource: 'analytics', operation: 'read' };
    }

    return { resource: 'unknown', operation: 'unknown' };
  }

  private async validateRestrictions(
    restrictions: Record<string, any>,
    request: ApiRequest
  ): Promise<ScopeValidationResult> {
    // Agent-specific restrictions
    if (restrictions.allowed_agents) {
      const agentId = request.body?.agent_id || request.params?.agent_id;
      if (agentId && !restrictions.allowed_agents.includes(agentId)) {
        return {
          allowed: false,
          reason: `Access denied to agent: ${agentId}`
        };
      }
    }

    // Document type restrictions
    if (restrictions.allowed_document_types) {
      const documentType = await this.detectDocumentType(request);
      if (documentType && !restrictions.allowed_document_types.includes(documentType)) {
        return {
          allowed: false,
          reason: `Access denied to document type: ${documentType}`
        };
      }
    }

    // Time-based restrictions
    if (restrictions.allowed_hours) {
      const currentHour = new Date().getHours();
      if (!restrictions.allowed_hours.includes(currentHour)) {
        return {
          allowed: false,
          reason: `Access denied outside allowed hours`
        };
      }
    }

    return { allowed: true };
  }
}
```

**Key Rotation Management:**
```typescript
class KeyRotationManager {
  // Automatic key rotation
  async rotateKey(keyId: string): Promise<KeyRotationResult> {
    const currentKey = await getApiKey(keyId);
    if (!currentKey.rotation_policy?.auto_rotate) {
      throw new Error('Key does not have auto-rotation enabled');
    }

    // Generate new key
    const newRawKey = generateSecureApiKey(currentKey.key_type);
    const newKeyHash = await hashApiKey(newRawKey);
    const newKeyId = generateKeyId();

    // Create new key with same configuration
    const newKey: AdvancedApiKey = {
      ...currentKey,
      id: newKeyId,
      key_hash: newKeyHash,
      created_at: new Date(),
      parent_key_id: keyId, // Link to previous key
      rotation_generation: (currentKey.rotation_generation || 0) + 1
    };

    // Start transaction for seamless rotation
    const { data: createdKey } = await supabase
      .from('api_keys')
      .insert(newKey)
      .select()
      .single();

    // Mark old key as rotated (but keep it active for transition period)
    await supabase
      .from('api_keys')
      .update({
        status: 'rotated',
        rotated_at: new Date(),
        replacement_key_id: newKeyId
      })
      .eq('id', keyId);

    // Schedule old key deactivation after transition period
    const transitionPeriod = currentKey.rotation_policy.transition_period_hours || 24;
    await scheduleKeyDeactivation(keyId, transitionPeriod);

    // Notify customer of rotation
    await notifyKeyRotation(currentKey.customer_id, {
      old_key_id: keyId,
      new_key_id: newKeyId,
      new_key: newRawKey,
      transition_period_hours: transitionPeriod
    });

    // Log rotation
    await logKeyEvent(newKeyId, 'key_rotated', {
      previous_key_id: keyId,
      rotation_generation: newKey.rotation_generation
    });

    return {
      old_key_id: keyId,
      new_key: createdKey,
      new_raw_key: newRawKey,
      transition_period_hours: transitionPeriod,
      deactivation_scheduled_at: new Date(Date.now() + transitionPeriod * 60 * 60 * 1000)
    };
  }

  // Schedule automatic rotation
  async scheduleKeyRotation(keyId: string, policy: RotationPolicy): Promise<void> {
    const nextRotation = new Date();
    nextRotation.setDate(nextRotation.getDate() + policy.rotation_interval_days);

    await supabase.from('scheduled_rotations').insert({
      key_id: keyId,
      scheduled_at: nextRotation,
      advance_notice_days: policy.advance_notice_days || 7,
      status: 'scheduled'
    });

    // Set up advance notice
    if (policy.advance_notice_days) {
      const noticeDate = new Date(nextRotation);
      noticeDate.setDate(noticeDate.getDate() - policy.advance_notice_days);

      await scheduleRotationNotice(keyId, noticeDate);
    }
  }
}
```

**Bulk Key Operations:**
```typescript
// POST /admin/api-keys/bulk
interface BulkKeyOperation {
  operation: 'suspend' | 'activate' | 'extend_expiration' | 'update_limits' | 'add_scope';
  key_ids: string[];
  parameters?: Record<string, any>;
  reason: string;
}

const performBulkKeyOperation = async (
  operation: BulkKeyOperation,
  adminUserId: string
): Promise<BulkOperationResult> => {
  const results = [];
  const errors = [];

  for (const keyId of operation.key_ids) {
    try {
      let result;

      switch (operation.operation) {
        case 'suspend':
          result = await suspendApiKey(keyId, operation.reason);
          break;
        case 'activate':
          result = await activateApiKey(keyId, operation.reason);
          break;
        case 'extend_expiration':
          result = await extendKeyExpiration(keyId, operation.parameters?.days);
          break;
        case 'update_limits':
          result = await updateKeyLimits(keyId, operation.parameters?.limits);
          break;
        case 'add_scope':
          result = await addKeyScope(keyId, operation.parameters?.scope);
          break;
        default:
          throw new Error(`Unknown operation: ${operation.operation}`);
      }

      results.push({ key_id: keyId, success: true, result });

    } catch (error) {
      errors.push({ key_id: keyId, error: error.message });
      results.push({ key_id: keyId, success: false, error: error.message });
    }
  }

  // Log bulk operation
  await logBulkKeyOperation(operation, adminUserId, results);

  return {
    operation: operation.operation,
    total_keys: operation.key_ids.length,
    successful: results.filter(r => r.success).length,
    failed: errors.length,
    results: results,
    errors: errors
  };
};
```

**Key Usage Analytics:**
```typescript
// GET /admin/api-keys/{id}/analytics
const getKeyAnalytics = async (keyId: string, timeframe: string): Promise<KeyAnalytics> => {
  const timeframeDays = { day: 1, week: 7, month: 30, quarter: 90 }[timeframe] || 7;
  const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

  const [usageStats, endpointBreakdown, errorAnalysis] = await Promise.all([
    getKeyUsageStats(keyId, startDate),
    getEndpointBreakdown(keyId, startDate),
    getErrorAnalysis(keyId, startDate)
  ]);

  return {
    key_id: keyId,
    timeframe: timeframe,
    period: { start: startDate, end: new Date() },
    usage_stats: usageStats,
    endpoint_breakdown: endpointBreakdown,
    error_analysis: errorAnalysis,
    performance_metrics: await getKeyPerformanceMetrics(keyId, startDate),
    cost_analysis: await getKeyCostAnalysis(keyId, startDate)
  };
};

const getKeyUsageStats = async (keyId: string, startDate: Date): Promise<UsageStats> => {
  const { data: logs } = await supabase
    .from('usage_logs')
    .select('*')
    .eq('api_key_id', keyId)
    .gte('created_at', startDate.toISOString());

  return {
    total_requests: logs?.length || 0,
    successful_requests: logs?.filter(l => l.success).length || 0,
    avg_response_time_ms: logs?.reduce((sum, l) => sum + l.processing_time_ms, 0) / (logs?.length || 1),
    total_cost_usd: logs?.reduce((sum, l) => sum + l.model_cost_usd, 0) || 0,
    peak_requests_per_hour: await calculatePeakUsage(logs || []),
    unique_documents_processed: await countUniqueDocuments(logs || [])
  };
};
```

## 🧪 Testing Requirements
- [ ] Advanced key generation creates proper scope restrictions
- [ ] Scope validation correctly enforces access controls
- [ ] Key rotation maintains seamless API access during transition
- [ ] Bulk operations handle partial failures gracefully
- [ ] Usage analytics provide accurate metrics
- [ ] Emergency revocation takes immediate effect

## 🔗 Dependencies
**Depends on**: #[4.1 Comprehensive Customer Management]
**Blocks**: #[4.6 Customer Support Tools]

## 📊 Metadata
- **Epic**: Epic 4
- **Effort**: S (Small: <1 day)
- **Priority**: Medium

---

### 💡 Development Notes
This advanced API key system provides the enterprise-grade access control foundation that enables senior developers to implement any customer access pattern or security requirement with granular permissions and automated lifecycle management.