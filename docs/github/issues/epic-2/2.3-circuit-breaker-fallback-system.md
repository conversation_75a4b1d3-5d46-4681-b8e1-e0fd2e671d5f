**Title:** [Epic 2] Circuit Breaker & Fallback System

**Milestone:** Epic 2: Document Processing & Usage Tracking
**Labels:** story, epic-2, priority-high, effort-L

## 🎯 User Story
As a **Platform Administrator**,
I want **automatic fallback between AI models when services fail**,
So that **document processing maintains high availability**.

## 📋 Requirements
Implement comprehensive circuit breaker and fallback system to achieve 99.5% uptime by automatically routing between OpenAI, Claude, and LlamaParse when services are unavailable or degraded.

## ✅ Acceptance Criteria
- [ ] Circuit breaker implementation with configurable failure thresholds
- [ ] Automatic fallback from OpenAI → Claude → LlamaParse
- [ ] Service health monitoring with automatic recovery detection
- [ ] Fallback decision logging for operational visibility
- [ ] Performance metrics for each AI service (latency, success rate)
- [ ] Manual circuit breaker override for maintenance
- [ ] Exponential backoff retry logic for temporary failures
- [ ] Service degradation detection (slow responses)
- [ ] Cost optimization during fallback scenarios
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Circuit Breaker Configuration:**
```typescript
interface CircuitBreakerConfig {
  failureThreshold: number;     // Failures before opening circuit
  timeout: number;              // Wait time before retry (ms)
  resetTimeout: number;         // Full reset time (ms)
  successThreshold: number;     // Successes needed to close circuit
  degradationThreshold: number; // Response time considered degraded
}

const circuitBreakerConfig = {
  openai: {
    failureThreshold: 5,
    timeout: 60000,        // 1 minute
    resetTimeout: 300000,  // 5 minutes
    successThreshold: 3,
    degradationThreshold: 10000 // 10 seconds
  },
  claude: {
    failureThreshold: 3,   // More sensitive for secondary
    timeout: 120000,       // 2 minutes
    resetTimeout: 600000,  // 10 minutes
    successThreshold: 2,
    degradationThreshold: 15000 // 15 seconds
  }
};
```

**Fallback Priority Chain:**
1. **Primary**: OpenAI GPT-4o (best cost/performance ratio)
2. **Secondary**: Claude 3.5 Sonnet (high quality, higher cost)
3. **Tertiary**: LlamaParse (specialized PDF processing, highest cost)

**Key Files to Modify/Create:**
- Utils: `circuit-breaker.ts` - Circuit breaker implementation
- Service: `ai-service-manager.ts` - Fallback orchestration
- Monitor: `service-health-monitor.ts` - Continuous health checking
- Types: Circuit breaker state and health interfaces
- Tests: `tests/unit/circuit-breaker.test.ts`

**Circuit Breaker Implementation:**
```typescript
class CircuitBreaker {
  private state: 'closed' | 'open' | 'half-open' = 'closed';
  private failures = 0;
  private nextAttempt = 0;
  private successCount = 0;

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'open') {
      if (Date.now() < this.nextAttempt) {
        throw new Error('Circuit breaker is OPEN');
      }
      this.state = 'half-open';
      this.successCount = 0;
    }

    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess() {
    this.failures = 0;
    if (this.state === 'half-open') {
      this.successCount++;
      if (this.successCount >= this.config.successThreshold) {
        this.state = 'closed';
      }
    }
  }

  private onFailure() {
    this.failures++;
    if (this.failures >= this.config.failureThreshold) {
      this.state = 'open';
      this.nextAttempt = Date.now() + this.config.timeout;
    }
  }
}
```

**Service Health Monitoring:**
```typescript
class ServiceHealthMonitor {
  private healthChecks = new Map<string, ServiceHealth>();

  async checkServiceHealth(service: string): Promise<ServiceHealth> {
    const start = performance.now();
    try {
      await this.pingService(service);
      const latency = performance.now() - start;

      const health: ServiceHealth = {
        service,
        status: latency > this.getDegradationThreshold(service) ? 'degraded' : 'healthy',
        latency_ms: latency,
        last_check: new Date(),
        success_rate: this.calculateSuccessRate(service)
      };

      this.healthChecks.set(service, health);
      return health;
    } catch (error) {
      const health: ServiceHealth = {
        service,
        status: 'unhealthy',
        latency_ms: performance.now() - start,
        last_check: new Date(),
        error: error.message,
        success_rate: this.calculateSuccessRate(service)
      };

      this.healthChecks.set(service, health);
      return health;
    }
  }

  // Check all services every 30 seconds
  startMonitoring() {
    setInterval(() => {
      ['openai', 'claude', 'llamaparse'].forEach(service => {
        this.checkServiceHealth(service);
      });
    }, 30000);
  }
}
```

**Fallback Orchestration:**
```typescript
const processWithFallback = async (document: Document, agent: Agent): Promise<ProcessingResult> => {
  const fallbackChain = ['openai', 'claude', 'llamaparse'];

  for (const service of fallbackChain) {
    try {
      const circuitBreaker = getCircuitBreaker(service);
      const result = await circuitBreaker.execute(() =>
        processWithService(service, document, agent)
      );

      // Log successful fallback usage
      await logFallbackUsage(service, document.id, 'success');
      return result;

    } catch (error) {
      console.warn(`Service ${service} failed: ${error.message}`);
      await logFallbackUsage(service, document.id, 'failure', error.message);

      // Continue to next service in chain
      if (service === fallbackChain[fallbackChain.length - 1]) {
        throw new Error('All AI services unavailable');
      }
    }
  }
};
```

**Cost Optimization During Fallbacks:**
```typescript
const optimizeFallbackCosts = async (customer: Customer, usedService: string) => {
  // Track increased costs from fallbacks
  const costIncrease = calculateCostIncrease(usedService);

  // Alert customer to higher costs if using expensive fallbacks
  if (costIncrease > 0.5) { // >50% cost increase
    await notifyCustomer(customer.id, {
      message: `Using ${usedService} due to primary service unavailability. Costs may be higher.`,
      cost_impact: `+${(costIncrease * 100).toFixed(0)}%`
    });
  }

  // Switch to lower-cost models if budget constraints
  if (customer.monthly_spend_usd > customer.budget_limit * 0.9) {
    await enforceEconomyMode(customer.id);
  }
};
```

## 🧪 Testing Requirements
- [ ] Circuit breaker opens/closes correctly based on thresholds
- [ ] Fallback chain executes in proper order
- [ ] Service health monitoring detects failures and recovery accurately
- [ ] Exponential backoff prevents service overload during failures
- [ ] Manual overrides work correctly for maintenance scenarios
- [ ] Performance metrics collected accurately across all services

## 🔗 Dependencies
**Depends on**: #[2.2 AI Model Integration]
**Blocks**: #[2.4 Basic Document Processing], #[2.5 Usage Tracking & Credit System]

## 📊 Metadata
- **Epic**: Epic 2
- **Effort**: L (Large: 3-5 days)
- **Priority**: High

---

### 💡 Development Notes
This reliability foundation ensures senior developers can implement any document processing feature knowing that high availability and automatic failover are handled at the infrastructure level.