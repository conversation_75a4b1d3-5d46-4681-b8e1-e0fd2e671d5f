**Title:** [Epic 2] Basic Document Processing

**Milestone:** Epic 2: Document Processing & Usage Tracking
**Labels:** story, epic-2, priority-high, effort-M

## 🎯 User Story
As a **Developer**,
I want **to extract structured data from documents using default agents**,
So that **I can evaluate platform capabilities for my use case**.

## 📋 Requirements
Implement core document processing pipeline with text extraction, AI-powered data extraction, and structured JSON output generation achieving >95% accuracy for standard document types.

## ✅ Acceptance Criteria
- [ ] Text extraction from PDF files with formatting preservation
- [ ] Document content preprocessing for AI model optimization
- [ ] Default extraction agent with basic field detection
- [ ] JSON output generation with consistent schema structure
- [ ] Processing status tracking (queued, processing, completed, failed)
- [ ] Error handling for unsupported document formats
- [ ] Processing time limits with timeout handling (55s max for Edge Functions)
- [ ] Document content caching for duplicate detection
- [ ] Processing results storage with retention policies
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Processing Pipeline Flow:**
1. **Text Extraction**: Extract raw text from uploaded document
2. **Content Preprocessing**: Clean and format content for AI processing
3. **AI Processing**: Use selected model with appropriate agent prompt
4. **Output Formatting**: Standardize response to JSON schema
5. **Storage**: Save results with appropriate retention policy

**Key Files to Modify/Create:**
- Function: `supabase/functions/extract/index.ts` - Main processing endpoint
- Utils: `text-extraction.ts` - Document text extraction utilities
- Utils: `content-preprocessor.ts` - Content cleaning and formatting
- Utils: `output-formatter.ts` - Response standardization
- Types: Processing pipeline interfaces and schemas
- Tests: `tests/integration/document-processing.test.ts`

**Text Extraction Implementation:**
```typescript
const extractTextFromDocument = async (file: File): Promise<ExtractedContent> => {
  const fileType = file.type;

  switch (fileType) {
    case 'application/pdf':
      return await extractFromPdf(file);
    case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      return await extractFromDocx(file);
    case 'image/jpeg':
    case 'image/png':
      return await extractFromImage(file);
    default:
      throw new Error(`Unsupported file type: ${fileType}`);
  }
};

const extractFromPdf = async (file: File): Promise<ExtractedContent> => {
  // Use pdf-parse or similar library for text extraction
  const buffer = await file.arrayBuffer();
  const text = await pdfParse(buffer);

  return {
    text: text.text,
    pages: text.numpages,
    metadata: {
      title: text.info?.Title,
      author: text.info?.Author,
      creation_date: text.info?.CreationDate
    }
  };
};
```

**Content Preprocessing:**
```typescript
const preprocessContent = (content: ExtractedContent, agent: Agent): string => {
  let processedText = content.text;

  // 1. Clean common OCR artifacts
  processedText = processedText.replace(/\s+/g, ' '); // Multiple spaces
  processedText = processedText.replace(/[^\x20-\x7E\n]/g, ''); // Non-printable chars

  // 2. Format for AI processing
  processedText = processedText.trim();

  // 3. Add agent-specific context
  const prompt = `${agent.system_prompt}\n\nDocument Content:\n${processedText}`;

  // 4. Validate length (stay within token limits)
  if (prompt.length > MAX_PROMPT_LENGTH) {
    return truncateIntelligently(prompt, MAX_PROMPT_LENGTH);
  }

  return prompt;
};
```

**Default Agent Schema Examples:**
```typescript
const DEFAULT_AGENTS = {
  invoice: {
    name: 'Invoice Processor',
    system_prompt: 'Extract key invoice information from the document...',
    output_schema: {
      vendor_name: 'string',
      invoice_number: 'string',
      total_amount: 'number',
      invoice_date: 'string (ISO date)',
      line_items: 'array of objects',
      tax_amount: 'number'
    }
  },
  receipt: {
    name: 'Receipt Processor',
    system_prompt: 'Extract receipt information from the document...',
    output_schema: {
      merchant_name: 'string',
      amount: 'number',
      date: 'string (ISO date)',
      category: 'string',
      payment_method: 'string'
    }
  },
  contract: {
    name: 'Contract Analyzer',
    system_prompt: 'Extract key contract terms and information...',
    output_schema: {
      parties: 'array of strings',
      effective_date: 'string (ISO date)',
      expiration_date: 'string (ISO date)',
      key_terms: 'array of objects'
    }
  }
};
```

**Processing Status Tracking:**
```typescript
interface ProcessingJob {
  id: string;
  customer_id: string;
  document_id: string;
  agent_id: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  created_at: Date;
  started_at?: Date;
  completed_at?: Date;
  result?: ProcessingResult;
  error_message?: string;
}

const updateProcessingStatus = async (jobId: string, status: ProcessingStatus, result?: any) => {
  const update: Partial<ProcessingJob> = { status };

  switch (status) {
    case 'processing':
      update.started_at = new Date();
      break;
    case 'completed':
      update.completed_at = new Date();
      update.result = result;
      break;
    case 'failed':
      update.completed_at = new Date();
      update.error_message = result?.error;
      break;
  }

  await supabase.from('processing_jobs').update(update).eq('id', jobId);

  // Notify via realtime for status updates
  await supabase.channel('job-updates').send({
    type: 'broadcast',
    event: 'status-update',
    payload: { job_id: jobId, status, ...update }
  });
};
```

**Document Caching Strategy:**
```typescript
const checkDocumentCache = async (documentHash: string): Promise<CacheResult | null> => {
  // 1. Check exact hash match
  const exactMatch = await supabase
    .from('document_cache')
    .select('result')
    .eq('document_hash', documentHash)
    .single();

  if (exactMatch.data) {
    return { type: 'exact', result: exactMatch.data.result };
  }

  // 2. Check vector similarity for near-duplicates
  const { data: embeddings } = await supabase
    .from('document_embeddings')
    .select('result')
    .rpc('find_similar_documents', {
      query_embedding: await generateEmbedding(documentHash),
      similarity_threshold: 0.95
    });

  if (embeddings?.length > 0) {
    return { type: 'similar', result: embeddings[0].result };
  }

  return null;
};
```

**Performance Requirements:**
- Standard documents (<1MB): <5 seconds processing time
- Large documents (1-10MB): <30 seconds processing time
- Edge Function timeout: 55 seconds maximum
- Accuracy target: >95% for structured documents

## 🧪 Testing Requirements
- [ ] Text extraction preserves document structure and formatting
- [ ] AI processing returns consistent, well-formatted JSON
- [ ] Processing timeouts handled gracefully with appropriate error messages
- [ ] Document caching prevents redundant processing
- [ ] Error handling covers all failure scenarios with meaningful messages
- [ ] Accuracy metrics meet >95% target on test document set

## 🔗 Dependencies
**Depends on**: #[2.2 AI Model Integration], #[2.3 Circuit Breaker & Fallback System]
**Blocks**: #[2.5 Usage Tracking & Credit System], #[3.1 Default Agent Creation]

## 📊 Metadata
- **Epic**: Epic 2
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This processing pipeline provides the core extraction foundation that enables senior developers to implement any document type or extraction requirement by simply adding new agents or preprocessing steps.