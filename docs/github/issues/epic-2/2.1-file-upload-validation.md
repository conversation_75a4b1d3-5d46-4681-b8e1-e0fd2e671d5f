**Title:** [Epic 2] File Upload & Validation

**Milestone:** Epic 2: Document Processing & Usage Tracking
**Labels:** story, epic-2, priority-high, effort-M

## 🎯 User Story
As a **Developer**,
I want **to upload documents via API with proper validation**,
So that **I can process supported file types reliably**.

## 📋 Requirements
Implement secure file upload with comprehensive validation supporting PDF, DOCX, XLSX, and image formats up to configurable size limits, with security scanning and input sanitization for the document processing pipeline.

## ✅ Acceptance Criteria
- [ ] POST /api/v1/extract endpoint accepts PDF, DOCX, XLSX, images
- [ ] File size validation against configurable limits per API key (default 50MB)
- [ ] File type validation using MIME type detection
- [ ] Multipart form upload handling with proper error responses
- [ ] File metadata extraction (size, type, creation date)
- [ ] Temporary file storage with automatic cleanup
- [ ] Input sanitization for all file-related parameters
- [ ] Malicious file detection (header validation)
- [ ] Rate limiting per API key for upload requests
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Supported File Types:**
- **PDF**: `application/pdf` (primary document type)
- **DOCX**: `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- **XLSX**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
- **Images**: `image/jpeg`, `image/png` (for document scans)

**Key Files to Modify/Create:**
- Function: `supabase/functions/extract/index.ts` - Main processing endpoint
- Utils: `file-validation.ts` - File validation and security scanning
- Utils: `file-storage.ts` - Temporary storage management
- Types: File upload interfaces and validation types
- Tests: `tests/integration/file-upload.test.ts`

**Security Validation Implementation:**
```typescript
// File header validation (magic numbers)
const validateFileHeader = (buffer: ArrayBuffer, mimeType: string): boolean => {
  const header = new Uint8Array(buffer, 0, 8);

  switch (mimeType) {
    case 'application/pdf':
      return String.fromCharCode(...header.slice(0, 4)) === '%PDF';
    case 'image/jpeg':
      return header[0] === 0xFF && header[1] === 0xD8;
    case 'image/png':
      return header[0] === 0x89 && header[1] === 0x50;
    // Add DOCX/XLSX validation (ZIP signature: PK)
    default:
      return false;
  }
};

const validateFileUpload = async (file: File, apiKey: ApiKey): Promise<ValidationResult> => {
  // 1. Size validation
  if (file.size > apiKey.file_size_limit) {
    throw new Error(`File too large. Max: ${apiKey.file_size_limit} bytes`);
  }

  // 2. Type validation
  if (!SUPPORTED_MIME_TYPES.includes(file.type)) {
    throw new Error('Unsupported file type');
  }

  // 3. Header validation
  const buffer = await file.slice(0, 8).arrayBuffer();
  if (!validateFileHeader(buffer, file.type)) {
    throw new Error('File header mismatch - possible malicious file');
  }

  return { valid: true, metadata: extractFileMetadata(file) };
};
```

**File Size Limits by Key Type:**
- Test keys (skt_): 10MB default
- Production keys (skp_): 50MB default
- Enterprise customers: Up to 100MB
- Queue processing for files >10MB

**Temporary Storage Management:**
```typescript
// Store files temporarily in Supabase Storage
const storeTemporaryFile = async (file: File, correlationId: string) => {
  const fileName = `temp/${correlationId}/${file.name}`;
  const { data, error } = await supabase.storage
    .from('documents')
    .upload(fileName, file, {
      cacheControl: '3600', // 1 hour
      upsert: false
    });

  // Schedule cleanup after processing
  setTimeout(() => {
    cleanupTemporaryFile(fileName);
  }, 3600000); // 1 hour cleanup

  return data;
};
```

## 🧪 Testing Requirements
- [ ] File upload accepts all supported formats
- [ ] File size limits enforced correctly per key type
- [ ] Malicious file detection prevents harmful uploads
- [ ] Temporary storage cleanup works automatically
- [ ] Error messages provide clear guidance
- [ ] Rate limiting prevents abuse

## 🔗 Dependencies
**Depends on**: #[1.3 Authentication System]
**Blocks**: #[2.2 AI Model Integration], #[2.4 Basic Document Processing]

## 📊 Metadata
- **Epic**: Epic 2
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This secure upload foundation enables senior developers to implement any document processing feature with confidence in file validation, security, and storage management patterns.