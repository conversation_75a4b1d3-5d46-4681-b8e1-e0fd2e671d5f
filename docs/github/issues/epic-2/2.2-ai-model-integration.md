**Title:** [Epic 2] AI Model Integration

**Milestone:** Epic 2: Document Processing & Usage Tracking
**Labels:** story, epic-2, priority-high, effort-L

## 🎯 User Story
As a **Developer**,
I want **reliable AI model integration with multiple providers**,
So that **document processing continues even if primary models are unavailable**.

## 📋 Requirements
Integrate OpenAI, Claude, and LlamaParse APIs with intelligent model selection, cost tracking, and comprehensive error handling to achieve 60%+ profit margins through optimal model routing.

## ✅ Acceptance Criteria
- [ ] OpenAI API integration with GPT-4 for document processing
- [ ] Claude API integration as secondary processing option
- [ ] LlamaParse API integration for complex PDF fallback
- [ ] API key management for all AI service providers
- [ ] Request/response logging for all AI service calls
- [ ] Error handling for AI service timeouts and rate limits
- [ ] Cost tracking per AI service call with model pricing
- [ ] Model selection based on document complexity
- [ ] Response format standardization across models
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Model Tiers & Pricing (Via OpenRouter):**
```typescript
const MODEL_TIERS = {
  fast: [
    'google/gemini-flash-1.5',     // $0.075/1M tokens - cost optimized
    'openai/gpt-4o-mini'           // $0.15/1M tokens - balanced
  ],
  balanced: [
    'anthropic/claude-3.5-sonnet', // $3/1M tokens - high quality
    'openai/gpt-4o'                // $2.50/1M tokens - reliable
  ],
  specialized: [
    'llamaparse'                   // $0.003/page - complex PDFs
  ]
};

const selectModel = (documentType: string, complexity: number): string => {
  if (documentType === 'pdf' && complexity > 8) {
    return 'llamaparse'; // Complex PDFs
  } else if (complexity > 5) {
    return MODEL_TIERS.balanced[0]; // Claude for complex docs
  } else {
    return MODEL_TIERS.fast[0]; // Gemini for simple docs
  }
};
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/ai-integration/index.ts`
- Utils: `model-selector.ts` - Intelligent model selection
- Utils: `cost-calculator.ts` - Real-time cost tracking
- Utils: `response-normalizer.ts` - Standardize AI responses
- Types: AI service interfaces and response types
- Config: Model pricing and selection configuration

**OpenRouter Integration:**
```typescript
const callOpenRouter = async (model: string, prompt: string, documentContent: string) => {
  const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
      'HTTP-Referer': 'https://oxviewglpxujnzrzmopx.supabase.co',
      'X-Title': 'IDP Platform',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model,
      messages: [
        { role: 'system', content: prompt },
        { role: 'user', content: documentContent }
      ],
      temperature: 0.1, // Low for consistency
      response_format: { type: "json_object" }
    })
  });

  const result = await response.json();

  // Track costs
  await trackAiCost({
    model,
    input_tokens: result.usage.prompt_tokens,
    output_tokens: result.usage.completion_tokens,
    cost_usd: calculateCost(model, result.usage)
  });

  return result;
};
```

**LlamaParse Direct Integration:**
```typescript
const callLlamaParse = async (pdfFile: File) => {
  const formData = new FormData();
  formData.append('file', pdfFile);
  formData.append('result_type', 'json');

  const response = await fetch('https://api.cloud.llamaindex.ai/api/parsing/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${LLAMAPARSE_API_KEY}`
    },
    body: formData
  });

  return await response.json();
};
```

**Cost Tracking & Profit Margin:**
```typescript
const trackAiCost = async (usage: AiUsage) => {
  const customerPrice = usage.cost_usd * getMarkupMultiplier(customer.tier);
  const profitMargin = ((customerPrice - usage.cost_usd) / customerPrice) * 100;

  await supabase.from('usage_logs').insert({
    customer_id: customer.id,
    api_key_id: apiKey.id,
    model_used: usage.model,
    model_cost: usage.cost_usd,
    customer_price: customerPrice,
    profit_margin: profitMargin,
    processing_time_ms: usage.duration
  });

  // Alert if margin below 60%
  if (profitMargin < 60) {
    await alertAdmins(`Low profit margin: ${profitMargin}% for ${usage.model}`);
  }
};
```

**Response Standardization:**
```typescript
interface StandardizedResponse {
  success: boolean;
  extracted_data: Record<string, any>;
  confidence: number;
  model_used: string;
  processing_time_ms: number;
  cost_breakdown: {
    model_cost_usd: number;
    customer_price_usd: number;
    profit_margin_percent: number;
  };
}
```

## 🧪 Testing Requirements
- [ ] All AI service integrations work correctly
- [ ] Cost tracking accurately calculates expenses and margins
- [ ] Model selection chooses appropriate models for document types
- [ ] Error handling gracefully manages service failures
- [ ] Response format standardization works across all models
- [ ] Token counting accurate for cost calculation

## 🔗 Dependencies
**Depends on**: #[2.1 File Upload & Validation]
**Blocks**: #[2.3 Circuit Breaker & Fallback System], #[2.4 Basic Document Processing]

## 📊 Metadata
- **Epic**: Epic 2
- **Effort**: L (Large: 3-5 days)
- **Priority**: High

---

### 💡 Development Notes
This AI integration provides the intelligent routing foundation that enables senior developers to implement profitable document processing with automated cost optimization and model selection.