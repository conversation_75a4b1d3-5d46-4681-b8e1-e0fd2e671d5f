**Title:** [Epic 2] Queue System for Large Documents

**Milestone:** Epic 2: Document Processing & Usage Tracking
**Labels:** story, epic-2, priority-medium, effort-L

## 🎯 User Story
As a **Developer**,
I want **asynchronous processing for large documents**,
So that **my API calls don't timeout on complex files**.

## 📋 Requirements
Implement PostgreSQL-based queue system with pg_cron for processing large documents (>10MB) and complex files that exceed the 55-second Edge Function timeout limit.

## ✅ Acceptance Criteria
- [ ] Queue table implementation using PostgreSQL + pg_cron
- [ ] Automatic queuing for documents >10MB or complex processing
- [ ] Job status tracking with unique job IDs
- [ ] Queue processing worker using Edge Functions
- [ ] Job retry logic with exponential backoff
- [ ] Queue monitoring with job count and processing time metrics
- [ ] Results retrieval endpoint for completed async jobs
- [ ] Dead letter queue for failed jobs after max retries
- [ ] Priority queue support for different customer tiers
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Queue Table Schema:**
```sql
-- Job queue table
CREATE TABLE job_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id),
  api_key_id UUID NOT NULL REFERENCES api_keys(id),
  document_id UUID NOT NULL,
  agent_id UUID NOT NULL REFERENCES agents(id),
  status job_status NOT NULL DEFAULT 'queued',
  priority INTEGER DEFAULT 5, -- 1 (highest) to 10 (lowest)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  error_message TEXT,
  result JSONB,
  correlation_id TEXT NOT NULL
);

CREATE TYPE job_status AS ENUM (
  'queued', 'processing', 'completed', 'failed', 'dead_letter'
);

-- Indexes for performance
CREATE INDEX idx_job_queue_status_priority ON job_queue(status, priority DESC, created_at);
CREATE INDEX idx_job_queue_customer ON job_queue(customer_id, created_at DESC);
CREATE INDEX idx_job_queue_correlation ON job_queue(correlation_id);
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/queue-processor/index.ts`
- Function: `supabase/functions/job-status/index.ts`
- Migration: `supabase/migrations/[timestamp]_queue_system.sql`
- Utils: `queue-manager.ts` - Queue operations and job management
- Utils: `job-processor.ts` - Document processing logic
- Types: Queue job interfaces and status types

**Queue Manager Implementation:**
```typescript
class QueueManager {
  // Add job to queue
  async enqueueJob(params: EnqueueParams): Promise<JobId> {
    const job: QueueJob = {
      customer_id: params.customerId,
      api_key_id: params.apiKeyId,
      document_id: params.documentId,
      agent_id: params.agentId,
      priority: this.calculatePriority(params.customerTier),
      correlation_id: generateCorrelationId()
    };

    const { data } = await supabase
      .from('job_queue')
      .insert(job)
      .select()
      .single();

    // Notify customer
    await this.notifyJobQueued(data.id, data.correlation_id);

    return data.id;
  }

  // Calculate priority based on customer tier
  private calculatePriority(tier: string): number {
    const priorities = {
      enterprise: 1,    // Highest priority
      professional: 3,
      starter: 5,       // Standard priority
      free: 8          // Lower priority
    };
    return priorities[tier] || 5;
  }

  // Process next job in queue
  async processNextJob(): Promise<ProcessingResult | null> {
    // Get highest priority job
    const { data: job } = await supabase
      .from('job_queue')
      .select('*')
      .eq('status', 'queued')
      .order('priority')
      .order('created_at')
      .limit(1)
      .single();

    if (!job) return null;

    try {
      // Mark as processing
      await this.updateJobStatus(job.id, 'processing');

      // Process the document
      const result = await this.processDocument(job);

      // Mark as completed
      await this.updateJobStatus(job.id, 'completed', result);

      return result;
    } catch (error) {
      await this.handleJobFailure(job, error);
      return null;
    }
  }

  // Handle job failures with retry logic
  private async handleJobFailure(job: QueueJob, error: Error) {
    const newRetryCount = job.retry_count + 1;

    if (newRetryCount >= job.max_retries) {
      // Move to dead letter queue
      await this.updateJobStatus(job.id, 'dead_letter', null, error.message);
      await this.notifyDeadLetter(job, error);
    } else {
      // Schedule retry with exponential backoff
      const delaySeconds = Math.pow(2, newRetryCount) * 60; // 2, 4, 8 minutes

      await supabase
        .from('job_queue')
        .update({
          status: 'queued',
          retry_count: newRetryCount,
          error_message: error.message,
          // Schedule for future processing
          created_at: new Date(Date.now() + delaySeconds * 1000)
        })
        .eq('id', job.id);
    }
  }
}
```

**pg_cron Setup for Queue Processing:**
```sql
-- Schedule queue processor to run every 10 seconds
SELECT cron.schedule(
  'process-document-queue',
  '*/10 * * * * *',  -- Every 10 seconds
  $$
    SELECT net.http_post(
      url := 'https://oxviewglpxujnzrzmopx.supabase.co/functions/v1/queue-processor',
      headers := jsonb_build_object(
        'Authorization', 'Bearer ' || current_setting('app.service_role_key'),
        'Content-Type', 'application/json'
      ),
      body := jsonb_build_object('action', 'process_next')
    );
  $$
);
```

**Queue Processor Edge Function:**
```typescript
// supabase/functions/queue-processor/index.ts
import "jsr:@supabase/functions-js/edge-runtime.d.ts";

Deno.serve(async (req: Request): Promise<Response> => {
  try {
    const queueManager = new QueueManager();

    // Process up to 3 jobs per invocation (batch processing)
    const results = [];
    for (let i = 0; i < 3; i++) {
      const result = await queueManager.processNextJob();
      if (!result) break; // No more jobs
      results.push(result);
    }

    return new Response(JSON.stringify({
      success: true,
      processed_jobs: results.length,
      results: results
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Queue processing error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
});
```

**Job Status API:**
```typescript
// GET /api/v1/jobs/{job_id}
const getJobStatus = async (jobId: string): Promise<JobStatusResponse> => {
  const { data: job } = await supabase
    .from('job_queue')
    .select('*')
    .eq('id', jobId)
    .single();

  if (!job) {
    throw new Error('Job not found');
  }

  return {
    job_id: job.id,
    correlation_id: job.correlation_id,
    status: job.status,
    created_at: job.created_at,
    started_at: job.started_at,
    completed_at: job.completed_at,
    result: job.result,
    error_message: job.error_message,
    retry_count: job.retry_count,
    estimated_completion: calculateEstimatedCompletion(job)
  };
};
```

**Real-time Status Updates:**
```typescript
// WebSocket/Realtime updates for job status
const subscribeToJobUpdates = (jobId: string) => {
  return supabase
    .channel(`job-${jobId}`)
    .on('postgres_changes', {
      event: 'UPDATE',
      schema: 'public',
      table: 'job_queue',
      filter: `id=eq.${jobId}`
    }, (payload) => {
      console.log('Job status update:', payload.new.status);
      // Notify client of status change
    })
    .subscribe();
};
```

**Queue Monitoring & Metrics:**
```typescript
const getQueueMetrics = async (): Promise<QueueMetrics> => {
  const { data: stats } = await supabase
    .from('job_queue')
    .select('status')
    .not('status', 'eq', 'completed');

  const statusCounts = stats.reduce((acc, job) => {
    acc[job.status] = (acc[job.status] || 0) + 1;
    return acc;
  }, {});

  // Average processing time for completed jobs (last 24h)
  const { data: completedJobs } = await supabase
    .from('job_queue')
    .select('completed_at, started_at')
    .eq('status', 'completed')
    .gte('completed_at', new Date(Date.now() - 24 * 60 * 60 * 1000))
    .not('started_at', 'is', null);

  const avgProcessingTime = completedJobs.reduce((sum, job) => {
    const duration = new Date(job.completed_at) - new Date(job.started_at);
    return sum + duration;
  }, 0) / completedJobs.length;

  return {
    queue_depth: statusCounts.queued || 0,
    processing_count: statusCounts.processing || 0,
    failed_count: statusCounts.failed || 0,
    dead_letter_count: statusCounts.dead_letter || 0,
    avg_processing_time_ms: avgProcessingTime || 0
  };
};
```

## 🧪 Testing Requirements
- [ ] Queue automatically handles large documents (>10MB)
- [ ] Job status tracking works accurately through all states
- [ ] Retry logic with exponential backoff functions correctly
- [ ] Priority queue processes high-priority jobs first
- [ ] Results retrieval returns correct data for completed jobs
- [ ] Dead letter queue captures permanently failed jobs

## 🔗 Dependencies
**Depends on**: #[2.4 Basic Document Processing]
**Blocks**: None (Enhancement to existing processing)

## 📊 Metadata
- **Epic**: Epic 2
- **Effort**: L (Large: 3-5 days)
- **Priority**: Medium

---

### 💡 Development Notes
This queue system provides the scalability foundation that enables senior developers to implement any document processing feature knowing that large files and long-running operations are handled asynchronously with proper retry logic and monitoring.