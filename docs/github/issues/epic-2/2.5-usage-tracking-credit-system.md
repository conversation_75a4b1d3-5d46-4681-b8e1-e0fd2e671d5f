**Title:** [Epic 2] Usage Tracking & Credit System

**Milestone:** Epic 2: Document Processing & Usage Tracking
**Labels:** story, epic-2, priority-high, effort-M

## 🎯 User Story
As a **Platform Administrator**,
I want **real-time tracking of processing costs and credit consumption**,
So that **I can monitor operational expenses and prevent abuse**.

## 📋 Requirements
Implement comprehensive usage tracking and credit management system with dual metrics (cost vs price) tracking to maintain 60%+ profit margins and enable transparent customer billing.

## ✅ Acceptance Criteria
- [ ] Credit deduction for each document processing request
- [ ] Cost tracking per AI service call with provider pricing
- [ ] Usage logging with customer, API key, and processing details
- [ ] Real-time credit balance updates per API key
- [ ] Credit limit enforcement with graceful error handling
- [ ] Usage analytics aggregation for admin dashboard visibility
- [ ] Dual metrics tracking (internal cost vs customer price)
- [ ] Automated low balance alerts and notifications
- [ ] Usage export functionality for billing integration
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Dual Metrics Architecture:**
```typescript
interface UsageRecord {
  id: string;
  customer_id: string;
  api_key_id: string;
  document_id: string;
  agent_id: string;
  model_used: string;
  input_tokens: number;
  output_tokens: number;
  model_cost_usd: number;      // What we pay AI provider
  customer_price_usd: number;  // What customer pays us
  credits_used: number;
  processing_time_ms: number;
  profit_margin_percent: number; // Calculated field
  created_at: Date;
}

const recordUsage = async (processing: ProcessingResult, customer: Customer, apiKey: ApiKey) => {
  const markup = getMarkupMultiplier(customer.tier); // 1.4-2.0 based on tier
  const customerPrice = processing.cost_usd * markup;
  const profitMargin = ((customerPrice - processing.cost_usd) / customerPrice) * 100;

  const usageRecord: UsageRecord = {
    customer_id: customer.id,
    api_key_id: apiKey.id,
    document_id: processing.document_id,
    agent_id: processing.agent_id,
    model_used: processing.model,
    input_tokens: processing.input_tokens,
    output_tokens: processing.output_tokens,
    model_cost_usd: processing.cost_usd,
    customer_price_usd: customerPrice,
    credits_used: Math.ceil(customerPrice * 100), // 1 credit = $0.01
    processing_time_ms: processing.duration_ms,
    profit_margin_percent: profitMargin
  };

  await supabase.from('usage_logs').insert(usageRecord);

  // Alert if margin falls below 60%
  if (profitMargin < 60) {
    await alertAdmins(`Low profit margin: ${profitMargin.toFixed(1)}% for ${processing.model}`);
  }
};
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/usage-tracking/index.ts`
- Utils: `credit-manager.ts` - Credit operations and enforcement
- Utils: `cost-calculator.ts` - Pricing and margin calculations
- Utils: `usage-aggregator.ts` - Analytics and reporting
- Database: Usage logs and credit balance tables
- Types: Usage tracking interfaces

**Credit System Implementation:**
```typescript
class CreditManager {
  // Deduct credits for processing
  async deductCredits(apiKeyId: string, amount: number): Promise<CreditTransaction> {
    const apiKey = await this.getApiKey(apiKeyId);

    if (apiKey.credits < amount) {
      throw new InsufficientCreditsError(`Insufficient credits. Required: ${amount}, Available: ${apiKey.credits}`);
    }

    // Atomic credit deduction
    const { data, error } = await supabase
      .from('api_keys')
      .update({ credits: apiKey.credits - amount })
      .eq('id', apiKeyId)
      .eq('credits', apiKey.credits) // Optimistic locking
      .select()
      .single();

    if (error || !data) {
      throw new Error('Credit deduction failed - possible race condition');
    }

    // Log the transaction
    await this.logCreditTransaction({
      api_key_id: apiKeyId,
      type: 'deduction',
      amount: -amount,
      balance_after: data.credits,
      description: 'Document processing'
    });

    return { success: true, new_balance: data.credits };
  }

  // Add credits (for top-ups)
  async addCredits(apiKeyId: string, amount: number, reason: string): Promise<CreditTransaction> {
    const { data } = await supabase
      .from('api_keys')
      .update({ credits: supabase.raw('credits + ?', [amount]) })
      .eq('id', apiKeyId)
      .select()
      .single();

    await this.logCreditTransaction({
      api_key_id: apiKeyId,
      type: 'addition',
      amount: amount,
      balance_after: data.credits,
      description: reason
    });

    return { success: true, new_balance: data.credits };
  }

  // Check if processing is allowed
  async checkCreditLimit(apiKeyId: string, estimatedCost: number): Promise<boolean> {
    const apiKey = await this.getApiKey(apiKeyId);
    return apiKey.credits >= estimatedCost;
  }
}
```

**Dynamic Pricing Based on Customer Tier:**
```typescript
const PRICING_TIERS = {
  starter: {
    markup_multiplier: 2.0,    // 100% markup for basic customers
    free_credits: 100,         // $1 worth of free credits
    rate_limit: 50             // 50 requests/hour
  },
  professional: {
    markup_multiplier: 1.6,    // 60% markup
    free_credits: 1000,        // $10 worth of free credits
    rate_limit: 500            // 500 requests/hour
  },
  enterprise: {
    markup_multiplier: 1.4,    // 40% markup
    free_credits: 5000,        // $50 worth of free credits
    rate_limit: 5000           // 5000 requests/hour
  }
};

const calculateCustomerPrice = (modelCost: number, customerTier: string): number => {
  const tier = PRICING_TIERS[customerTier] || PRICING_TIERS.starter;
  return modelCost * tier.markup_multiplier;
};
```

**Real-time Balance Monitoring:**
```typescript
const monitorCreditBalance = async (apiKeyId: string, threshold = 0.1): Promise<void> => {
  const apiKey = await getApiKey(apiKeyId);
  const lowBalanceThreshold = apiKey.initial_credits * threshold; // 10% of original

  if (apiKey.credits <= lowBalanceThreshold) {
    // Notify customer
    await sendLowBalanceAlert(apiKey.customer_id, {
      current_balance: apiKey.credits,
      threshold: lowBalanceThreshold,
      suggested_topup: Math.max(apiKey.initial_credits, 1000)
    });

    // Log the alert
    await supabase.from('audit_logs').insert({
      customer_id: apiKey.customer_id,
      action: 'low_balance_alert',
      details: {
        api_key_id: apiKeyId,
        balance: apiKey.credits,
        threshold: lowBalanceThreshold
      }
    });
  }
};
```

**Usage Analytics Aggregation:**
```typescript
const generateUsageAnalytics = async (customerId: string, period: 'day' | 'week' | 'month') => {
  const { data } = await supabase
    .from('usage_logs')
    .select(`
      model_used,
      COUNT(*) as request_count,
      SUM(model_cost_usd) as total_cost,
      SUM(customer_price_usd) as total_revenue,
      AVG(profit_margin_percent) as avg_profit_margin,
      SUM(processing_time_ms) as total_processing_time
    `)
    .eq('customer_id', customerId)
    .gte('created_at', getPeriodStart(period))
    .group('model_used');

  return {
    period,
    total_requests: data.reduce((sum, row) => sum + row.request_count, 0),
    total_cost_usd: data.reduce((sum, row) => sum + row.total_cost, 0),
    total_revenue_usd: data.reduce((sum, row) => sum + row.total_revenue, 0),
    avg_profit_margin: data.reduce((sum, row) => sum + row.avg_profit_margin, 0) / data.length,
    model_breakdown: data
  };
};
```

**Export for Billing Integration:**
```typescript
const exportUsageForBilling = async (customerId: string, startDate: Date, endDate: Date) => {
  const { data } = await supabase
    .from('usage_logs')
    .select('*')
    .eq('customer_id', customerId)
    .gte('created_at', startDate.toISOString())
    .lte('created_at', endDate.toISOString())
    .order('created_at');

  // Format for billing system
  return {
    customer_id: customerId,
    billing_period: { start: startDate, end: endDate },
    total_amount_usd: data.reduce((sum, row) => sum + row.customer_price_usd, 0),
    total_credits_used: data.reduce((sum, row) => sum + row.credits_used, 0),
    request_count: data.length,
    line_items: data.map(row => ({
      date: row.created_at,
      description: `${row.model_used} document processing`,
      amount_usd: row.customer_price_usd,
      credits_used: row.credits_used
    }))
  };
};
```

## 🧪 Testing Requirements
- [ ] Credit deduction accurate for all processing types and models
- [ ] Cost calculations match actual AI service charges
- [ ] Real-time balance updates work correctly under concurrent load
- [ ] Credit enforcement prevents processing when insufficient balance
- [ ] Usage aggregations calculate correctly for all time periods
- [ ] Profit margin tracking maintains accuracy across all models

## 🔗 Dependencies
**Depends on**: #[2.4 Basic Document Processing], #[1.4 Basic API Key Management]
**Blocks**: #[4.3 Credit Management System], #[4.5 Usage Analytics Dashboard]

## 📊 Metadata
- **Epic**: Epic 2
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This usage tracking foundation provides the billing and profitability insights that enable senior developers to implement any customer-facing feature with confidence in cost control and revenue optimization.