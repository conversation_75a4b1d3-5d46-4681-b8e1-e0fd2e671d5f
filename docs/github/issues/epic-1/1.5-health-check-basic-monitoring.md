**Title:** [Epic 1] Health Check & Basic Monitoring

**Milestone:** Epic 1: Foundation & Basic Admin
**Labels:** story, epic-1, priority-medium, effort-S

## 🎯 User Story
As a **Platform Administrator**,
I want **comprehensive health checks and basic monitoring**,
So that **I can validate system functionality and detect issues**.

## 📋 Requirements
Implement comprehensive health check endpoint and basic monitoring infrastructure to validate system status, AI service connectivity, and performance metrics required for 99.5% uptime SLA.

## ✅ Acceptance Criteria
- [ ] Health check endpoint returns database connection status
- [ ] Health check validates AI service connectivity (OpenAI, Claude, LlamaParse)
- [ ] System status includes Edge Function runtime information
- [ ] Basic logging framework implemented with structured logs
- [ ] Error tracking configured for all Edge Functions
- [ ] Performance metrics collection for response times
- [ ] Correlation ID system for request tracing
- [ ] Health endpoint responds in <500ms
- [ ] Monitoring data stored for trend analysis
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Health Check Response Format:**
```json
{
  "status": "healthy" | "degraded" | "unhealthy",
  "timestamp": "2025-09-21T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": { "status": "healthy", "latency_ms": 15 },
    "openai": { "status": "healthy", "latency_ms": 245 },
    "claude": { "status": "healthy", "latency_ms": 189 },
    "llamaparse": { "status": "healthy", "latency_ms": 432 }
  },
  "performance": {
    "response_time_ms": 45,
    "active_connections": 12,
    "edge_function_cold_start": false
  }
}
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/health/index.ts` - Main health endpoint
- Utils: `health-checks.ts` - Health check utility functions for each service
- Utils: `correlation-id.ts` - Request tracing utilities
- Types: Health status interfaces and response types
- Tests: `tests/integration/health-check.test.ts`

**Service Health Checks:**
```typescript
const checkDatabaseHealth = async (): Promise<ServiceHealth> => {
  const start = performance.now();
  try {
    await supabase.from('customers').select('count').single();
    return {
      status: 'healthy',
      latency_ms: Math.round(performance.now() - start)
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      latency_ms: Math.round(performance.now() - start),
      error: error.message
    };
  }
};

const checkAiServiceHealth = async (service: 'openai' | 'claude' | 'llamaparse') => {
  // Lightweight connectivity check to each AI service
  // Timeout after 2 seconds to prevent blocking
};
```

**Monitoring Requirements:**
- Database connectivity: <50ms response time
- AI service checks: <2s timeout per service
- Overall health endpoint: <500ms total response time
- Correlation ID format: `req_${timestamp}_${random}`

**Logging Framework:**
```typescript
interface LogEntry {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  correlation_id: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

const logger = {
  info: (message: string, correlationId: string, metadata = {}) => {
    console.log(JSON.stringify({
      level: 'info',
      message,
      correlation_id: correlationId,
      timestamp: new Date().toISOString(),
      metadata
    }));
  }
};
```

## 🧪 Testing Requirements
- [ ] Health endpoint returns proper JSON structure
- [ ] Database connectivity check works under load
- [ ] AI service connectivity validates correctly
- [ ] Performance metrics are accurate
- [ ] Correlation IDs generated uniquely
- [ ] Error scenarios handled gracefully

## 🔗 Dependencies
**Depends on**: #[1.1 Project Infrastructure Setup]
**Blocks**: None (Standalone monitoring)

## 📊 Metadata
- **Epic**: Epic 1
- **Effort**: S (Small: <1 day)
- **Priority**: Medium

---

### 💡 Development Notes
This monitoring foundation enables senior developers to implement robust error handling and performance tracking in all subsequent features, with established patterns for health checks and logging.