**Title:** [Epic 1] Authentication System

**Milestone:** Epic 1: Foundation & Basic Admin
**Labels:** story, epic-1, priority-high, effort-M

## 🎯 User Story
As a **Platform Administrator**,
I want **secure authentication for admin access and API key validation**,
So that **the platform is protected from unauthorized access**.

## 📋 Requirements
Implement comprehensive authentication system supporting both admin portal access and customer API key validation with the dual key architecture (skt_/skp_) and rate limiting required for the document processing platform.

## ✅ Acceptance Criteria
- [ ] Supabase Auth configured for admin portal authentication
- [ ] API key authentication middleware for customer endpoints
- [ ] JWT token validation for admin endpoints
- [ ] API key format validation (skt_ and skp_ prefixes)
- [ ] Rate limiting middleware implemented per API key
- [ ] Authentication error handling with proper HTTP status codes
- [ ] Session management for admin portal access
- [ ] SHA-256 API key hashing and secure storage
- [ ] Audit logging for all authentication events
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Authentication Architecture:**
- **Admin Access:** JWT-based authentication via Supabase Auth
- **Customer Access:** API key validation with SHA-256 hashing
- **Rate Limiting:** Per-key limits with configurable thresholds
- **Audit Trail:** All authentication attempts logged with correlation IDs

**Key Files to Modify/Create:**
- Function: `supabase/functions/validate-api-key/index.ts`
- Middleware: API key validation and rate limiting logic
- Types: Authentication interfaces and response types
- Tests: `tests/unit/auth-validation.test.ts`

**API Key Format Validation:**
```typescript
// Test keys: skt_live_[32-char-string]
// Production keys: skp_live_[32-char-string]
const keyPattern = /^sk[tp]_live_[a-zA-Z0-9]{32,}$/

const validateApiKeyFormat = (key: string): boolean => {
  return keyPattern.test(key);
};
```

**Rate Limiting Strategy:**
- Default: 100 requests per minute per API key
- Configurable limits based on customer tier
- Burst allowance for occasional spikes
- Circuit breaker for repeated violations

**Authentication Middleware:**
```typescript
export async function validateApiKey(key: string): Promise<AuthResult> {
  // 1. Format validation
  if (!validateApiKeyFormat(key)) {
    throw new Error('Invalid API key format');
  }

  // 2. Hash and lookup
  const keyHash = await hashApiKey(key);
  const apiKey = await getApiKeyByHash(keyHash);

  // 3. Rate limiting check
  await checkRateLimit(apiKey.id);

  // 4. Return customer context
  return {
    customerId: apiKey.customer_id,
    keyType: apiKey.key_type,
    credits: apiKey.credits
  };
}
```

## 🧪 Testing Requirements
- [ ] API key validation accepts valid formats and rejects invalid
- [ ] Rate limiting enforces configured limits correctly
- [ ] Authentication errors return proper HTTP status codes
- [ ] Audit logging captures all authentication events
- [ ] JWT validation works for admin endpoints
- [ ] SHA-256 hashing secure and performant

## 🔗 Dependencies
**Depends on**: #[1.2 Database Schema Foundation]
**Blocks**: #[1.4 Basic API Key Management], #[2.1 File Upload & Validation]

## 📊 Metadata
- **Epic**: Epic 1
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This authentication system provides the security foundation that allows senior developers to implement all customer-facing features with confidence in access control and rate limiting.