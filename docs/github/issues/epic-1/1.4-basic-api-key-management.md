**Title:** [Epic 1] Basic API Key Management

**Milestone:** Epic 1: Foundation & Basic Admin
**Labels:** story, epic-1, priority-high, effort-M

## 🎯 User Story
As a **Platform Administrator**,
I want **to generate and manage API key pairs for testing**,
So that **I can validate the complete customer workflow**.

## 📋 Requirements
Implement basic API key generation and management supporting the dual key architecture (skt_ test keys vs skp_ production keys) with separate credit allocations and retention policies for customer testing and validation.

## ✅ Acceptance Criteria
- [ ] Generate test key (skt_) with configurable credit allocation
- [ ] Generate production key (skp_) with separate credit allocation
- [ ] API key hashing and secure storage in database
- [ ] Basic key validation endpoint for customer testing
- [ ] Key status tracking (active, suspended, expired)
- [ ] 7-day retention policy implementation for test keys
- [ ] Admin endpoint to view key status and basic usage
- [ ] Credit balance tracking and enforcement
- [ ] Key revocation capability
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## ✅ Acceptance Criteria
**Dual Key Architecture:**
- **Test Keys (skt_):** 7-day retention, credit-based billing, limited rate limits
- **Production Keys (skp_):** Configurable retention, flexible billing, enterprise limits

**Key Files to Modify/Create:**
- Function: `supabase/functions/admin/generate-key/index.ts`
- Function: `supabase/functions/admin/manage-keys/index.ts`
- Database: Update API keys table with status and retention fields
- Types: API key management interfaces

**Key Generation Process:**
```typescript
// Format: skt_live_[32-random-chars] or skp_live_[32-random-chars]
const generateKey = (type: 'test' | 'prod') => {
  const prefix = type === 'test' ? 'skt' : 'skp';
  const randomString = generateSecureRandom(32);
  return `${prefix}_live_${randomString}`;
};

const createApiKey = async (customerId: string, keyType: 'test' | 'prod') => {
  const rawKey = generateKey(keyType);
  const keyHash = await hashApiKey(rawKey);

  const apiKey = await supabase.from('api_keys').insert({
    customer_id: customerId,
    key_type: keyType,
    key_hash: keyHash,
    credits: keyType === 'test' ? 100 : 1000,
    rate_limits: getDefaultRateLimits(keyType),
    expires_at: keyType === 'test' ? sevenDaysFromNow() : null
  });

  return { rawKey, apiKey }; // Return raw key only once
};
```

**Credit Management:**
- Test keys: Default 100 credits ($10 equivalent)
- Production keys: Configurable based on customer tier
- Credit deduction per API call
- Balance enforcement with graceful degradation

**Key Management Operations:**
```typescript
// Admin endpoints for key management
POST /admin/customers/{id}/keys/generate
PUT /admin/keys/{id}/status
GET /admin/keys/{id}/usage
DELETE /admin/keys/{id}/revoke
```

## 🧪 Testing Requirements
- [ ] Key generation creates valid, unique keys
- [ ] SHA-256 hashing works correctly and securely
- [ ] Test key retention policy auto-cleanup works
- [ ] Credit allocation and tracking accurate
- [ ] Key status updates reflect correctly
- [ ] Admin endpoints return proper data structures

## 🔗 Dependencies
**Depends on**: #[1.3 Authentication System]
**Blocks**: #[2.5 Usage Tracking & Credit System]

## 📊 Metadata
- **Epic**: Epic 1
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This key management system provides the foundation for all customer interactions. Senior developers can implement any customer-facing feature knowing the exact key validation, credit system, and administrative controls available.