**Title:** [Epic 1] Database Schema Foundation

**Milestone:** Epic 1: Foundation & Basic Admin
**Labels:** story, epic-1, priority-high, effort-L

## 🎯 User Story
As a **Platform Administrator**,
I want **a comprehensive database schema with proper security**,
So that **all platform data is structured and protected**.

## 📋 Requirements
Create the complete database foundation with 6 core tables supporting the dual API key architecture (skt_/skp_), customer isolation, and comprehensive audit trails required for the API-first document processing platform.

## ✅ Acceptance Criteria
- [ ] All tables created with comprehensive comments for auto-generated OpenAPI specs
- [ ] Row-Level Security (RLS) policies implemented for all customer-facing tables
- [ ] Database indexes created for performance optimization (<100ms query times)
- [ ] Audit trail tables configured for logging all operations
- [ ] API key storage table with SHA-256 hashing implementation
- [ ] Customer table with basic company information and tier management
- [ ] Usage tracking tables ready for dual metrics (cost/price) logging
- [ ] TypeScript types auto-generated successfully with `npm run gen:types`
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Core Tables Required:**
1. `customers` - Company accounts with tier-based configurations
2. `api_keys` - SHA-256 hashed keys with dual credit systems (skt_/skp_)
3. `agents` - Versioned default agents + customer customizations
4. `documents` - Processed documents with configurable retention
5. `usage_logs` - Dual metrics tracking (cost vs price)
6. `audit_logs` - Security and compliance event logging

**Key Files to Modify/Create:**
- Migration: `supabase/migrations/[timestamp]_initial_schema.sql`
- RLS Policies: Include in migration file with customer isolation
- Indexes: Strategic indexing for API performance requirements
- Types: Auto-generated `types/database.types.ts`

**Performance Requirements:**
- API key lookup: <50ms (hash-based index)
- Customer data queries: <100ms (proper indexing)
- Usage aggregation: <200ms (materialized views if needed)

**Critical Schema Design:**
```sql
-- API keys with dual credit systems
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL,
  key_type TEXT NOT NULL CHECK (key_type IN ('test', 'production')),
  key_hash TEXT NOT NULL UNIQUE,
  credits INTEGER DEFAULT 0,
  rate_limits JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer isolation with RLS
CREATE POLICY "customer_isolation" ON api_keys
  FOR ALL USING (customer_id = current_setting('app.customer_id')::uuid);
```

## 🧪 Testing Requirements
- [ ] All tables created with proper constraints
- [ ] RLS policies prevent cross-customer data access
- [ ] Indexes provide required query performance
- [ ] Foreign key relationships maintain data integrity
- [ ] TypeScript types compile without errors
- [ ] Database reset and migration testing successful

## 🔗 Dependencies
**Depends on**: #[1.1 Project Infrastructure Setup]
**Blocks**: #[1.3 Authentication System], #[1.4 Basic API Key Management]

## 📊 Metadata
- **Epic**: Epic 1
- **Effort**: L (Large: 3-5 days)
- **Priority**: High

---

### 💡 Development Notes
This schema provides the complete data foundation that enables all subsequent features. Senior developers can implement any story knowing the exact table structure, relationships, and security policies.