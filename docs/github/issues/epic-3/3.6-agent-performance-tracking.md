**Title:** [Epic 3] Agent Performance Tracking

**Milestone:** Epic 3: Agent Management System
**Labels:** story, epic-3, priority-low, effort-S

## 🎯 User Story
As a **Platform Administrator**,
I want **to track agent performance and usage patterns**,
So that **I can optimize default agents and identify popular customizations**.

## 📋 Requirements
Implement comprehensive agent performance tracking system that monitors accuracy, processing times, usage patterns, and customer satisfaction to enable data-driven agent optimization and platform improvements.

## ✅ Acceptance Criteria
- [ ] Agent usage tracking per customer and document type
- [ ] Processing accuracy metrics for each agent
- [ ] Performance benchmarking against default agents
- [ ] Popular customization pattern identification
- [ ] Agent error rate tracking and analysis
- [ ] Processing time metrics per agent configuration
- [ ] Customer satisfaction scoring for agent effectiveness
- [ ] Automated performance alerts for degraded agents
- [ ] Performance comparison reports between agent versions
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Performance Tracking Architecture:**
1. **Real-time Metrics**: Collect performance data during document processing
2. **Aggregation**: Process raw metrics into meaningful insights
3. **Benchmarking**: Compare custom agents against default baselines
4. **Pattern Analysis**: Identify common customization patterns
5. **Alerting**: Notify administrators of performance issues

**Key Files to Modify/Create:**
- Utils: `agent-metrics.ts` - Core performance tracking logic
- Utils: `performance-analyzer.ts` - Data analysis and insights
- Utils: `benchmark-comparison.ts` - Agent comparison utilities
- Function: `supabase/functions/agent-metrics/index.ts` - Metrics API
- Types: Performance tracking interfaces and metric types
- Tests: `tests/unit/agent-performance.test.ts`

**Core Performance Tracking Implementation:**
```typescript
interface AgentPerformanceMetrics {
  agent_id: string;
  customer_id: string;
  document_type: string;
  processing_time_ms: number;
  accuracy_score: number;
  confidence_score: number;
  success: boolean;
  error_type?: string;
  model_used: string;
  cost_usd: number;
  timestamp: Date;
  correlation_id: string;
}

class AgentPerformanceTracker {
  // Record performance metrics for each processing job
  async recordPerformance(metrics: AgentPerformanceMetrics): Promise<void> => {
    // Store raw metrics
    await supabase.from('agent_performance_logs').insert(metrics);

    // Update real-time aggregations
    await this.updateAggregatedMetrics(metrics);

    // Check for performance alerts
    await this.checkPerformanceAlerts(metrics);
  }

  // Update aggregated performance metrics
  private async updateAggregatedMetrics(metrics: AgentPerformanceMetrics): Promise<void> => {
    const aggregationKey = `${metrics.agent_id}:${metrics.document_type}`;

    // Update daily aggregations
    await supabase
      .from('agent_performance_daily')
      .upsert({
        agent_id: metrics.agent_id,
        document_type: metrics.document_type,
        date: new Date().toISOString().split('T')[0],
        total_requests: 1,
        successful_requests: metrics.success ? 1 : 0,
        avg_processing_time_ms: metrics.processing_time_ms,
        avg_accuracy_score: metrics.accuracy_score,
        avg_confidence_score: metrics.confidence_score,
        total_cost_usd: metrics.cost_usd
      }, {
        onConflict: 'agent_id,document_type,date',
        // Increment counters and recalculate averages
        ignoreDuplicates: false
      });

    // Update agent summary metrics
    await this.updateAgentSummary(metrics.agent_id);
  }

  // Update overall agent performance summary
  private async updateAgentSummary(agentId: string): Promise<void> => {
    // Calculate metrics from last 30 days
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const { data: recentMetrics } = await supabase
      .from('agent_performance_logs')
      .select('processing_time_ms, accuracy_score, confidence_score, success, cost_usd')
      .eq('agent_id', agentId)
      .gte('timestamp', thirtyDaysAgo.toISOString());

    if (!recentMetrics || recentMetrics.length === 0) return;

    const summary = this.calculateSummaryMetrics(recentMetrics);

    await supabase
      .from('agents')
      .update({
        avg_processing_time_ms: summary.avgProcessingTime,
        accuracy_rating: summary.avgAccuracy,
        success_rate: summary.successRate,
        last_performance_update: new Date()
      })
      .eq('id', agentId);
  }

  private calculateSummaryMetrics(metrics: any[]): AgentSummary {
    const total = metrics.length;
    const successful = metrics.filter(m => m.success).length;

    return {
      avgProcessingTime: Math.round(
        metrics.reduce((sum, m) => sum + m.processing_time_ms, 0) / total
      ),
      avgAccuracy: parseFloat(
        (metrics.reduce((sum, m) => sum + (m.accuracy_score || 0), 0) / total).toFixed(3)
      ),
      avgConfidence: parseFloat(
        (metrics.reduce((sum, m) => sum + (m.confidence_score || 0), 0) / total).toFixed(3)
      ),
      successRate: parseFloat((successful / total).toFixed(3)),
      totalCost: metrics.reduce((sum, m) => sum + m.cost_usd, 0),
      requestCount: total
    };
  }
}
```

**Performance Benchmarking:**
```typescript
class AgentBenchmarker {
  // Compare custom agent performance against default baseline
  async benchmarkAgentPerformance(
    customAgentId: string,
    timeframe: 'day' | 'week' | 'month' = 'week'
  ): Promise<BenchmarkReport> => {
    // Get custom agent and its parent
    const { data: customAgent } = await supabase
      .from('agents')
      .select('*, parent_agent:parent_agent_id(*)')
      .eq('id', customAgentId)
      .single();

    if (!customAgent?.parent_agent) {
      throw new Error('Cannot benchmark agent without default parent');
    }

    const timeframeDays = { day: 1, week: 7, month: 30 }[timeframe];
    const startDate = new Date(Date.now() - timeframeDays * 24 * 60 * 60 * 1000);

    // Get performance metrics for both agents
    const [customMetrics, defaultMetrics] = await Promise.all([
      this.getAgentMetrics(customAgentId, startDate),
      this.getAgentMetrics(customAgent.parent_agent.id, startDate)
    ]);

    return this.generateBenchmarkReport(customMetrics, defaultMetrics, customAgent);
  }

  private async getAgentMetrics(agentId: string, startDate: Date): Promise<AgentMetrics> {
    const { data: logs } = await supabase
      .from('agent_performance_logs')
      .select('*')
      .eq('agent_id', agentId)
      .gte('timestamp', startDate.toISOString());

    if (!logs || logs.length === 0) {
      return this.getEmptyMetrics();
    }

    return {
      requestCount: logs.length,
      successRate: logs.filter(l => l.success).length / logs.length,
      avgProcessingTime: logs.reduce((sum, l) => sum + l.processing_time_ms, 0) / logs.length,
      avgAccuracy: logs.reduce((sum, l) => sum + (l.accuracy_score || 0), 0) / logs.length,
      avgConfidence: logs.reduce((sum, l) => sum + (l.confidence_score || 0), 0) / logs.length,
      avgCost: logs.reduce((sum, l) => sum + l.cost_usd, 0) / logs.length,
      errorTypes: this.analyzeErrorTypes(logs.filter(l => !l.success))
    };
  }

  private generateBenchmarkReport(
    customMetrics: AgentMetrics,
    defaultMetrics: AgentMetrics,
    agent: Agent
  ): BenchmarkReport {
    return {
      agent_id: agent.id,
      agent_name: agent.name,
      parent_agent_id: agent.parent_agent.id,
      parent_agent_name: agent.parent_agent.name,
      comparison: {
        processing_time: {
          custom: customMetrics.avgProcessingTime,
          default: defaultMetrics.avgProcessingTime,
          improvement_percent: this.calculateImprovement(
            defaultMetrics.avgProcessingTime,
            customMetrics.avgProcessingTime,
            'lower_better'
          )
        },
        accuracy: {
          custom: customMetrics.avgAccuracy,
          default: defaultMetrics.avgAccuracy,
          improvement_percent: this.calculateImprovement(
            defaultMetrics.avgAccuracy,
            customMetrics.avgAccuracy,
            'higher_better'
          )
        },
        success_rate: {
          custom: customMetrics.successRate,
          default: defaultMetrics.successRate,
          improvement_percent: this.calculateImprovement(
            defaultMetrics.successRate,
            customMetrics.successRate,
            'higher_better'
          )
        },
        cost_efficiency: {
          custom: customMetrics.avgCost,
          default: defaultMetrics.avgCost,
          improvement_percent: this.calculateImprovement(
            defaultMetrics.avgCost,
            customMetrics.avgCost,
            'lower_better'
          )
        }
      },
      recommendation: this.generateRecommendation(customMetrics, defaultMetrics)
    };
  }
}
```

**Customization Pattern Analysis:**
```typescript
class CustomizationAnalyzer {
  // Identify popular customization patterns across all customers
  async analyzeCustomizationPatterns(): Promise<CustomizationInsights> => {
    const { data: customAgents } = await supabase
      .from('agents')
      .select('*, parent_agent:parent_agent_id(*)')
      .not('parent_agent_id', 'is', null)
      .not('customer_id', 'is', null);

    if (!customAgents) return this.getEmptyInsights();

    const patterns = {
      prompt_modifications: this.analyzePromptPatterns(customAgents),
      schema_modifications: this.analyzeSchemaPatterns(customAgents),
      popular_additions: this.analyzePopularAdditions(customAgents),
      performance_improvements: await this.analyzePerformanceImprovements(customAgents)
    };

    return {
      total_custom_agents: customAgents.length,
      analyzed_date: new Date(),
      patterns: patterns,
      recommendations: this.generatePlatformRecommendations(patterns)
    };
  }

  private analyzePromptPatterns(agents: Agent[]): PromptPatternAnalysis {
    const modifications = [];

    for (const agent of agents) {
      if (agent.parent_agent && agent.system_prompt !== agent.parent_agent.system_prompt) {
        const diff = this.calculatePromptDifference(
          agent.parent_agent.system_prompt,
          agent.system_prompt
        );
        modifications.push({
          category: agent.category,
          modification_type: diff.type,
          added_keywords: diff.addedKeywords,
          removed_keywords: diff.removedKeywords,
          usage_count: 1
        });
      }
    }

    // Group and count similar modifications
    return this.groupPromptModifications(modifications);
  }

  private analyzeSchemaPatterns(agents: Agent[]): SchemaPatternAnalysis {
    const schemaChanges = [];

    for (const agent of agents) {
      if (agent.parent_agent) {
        const changes = this.compareSchemas(
          agent.parent_agent.output_schema,
          agent.output_schema
        );

        if (changes.length > 0) {
          schemaChanges.push({
            category: agent.category,
            changes: changes,
            usage_count: 1
          });
        }
      }
    }

    return this.groupSchemaChanges(schemaChanges);
  }

  // Generate recommendations for platform improvements
  private generatePlatformRecommendations(patterns: any): PlatformRecommendation[] {
    const recommendations = [];

    // Suggest new default agents based on common customizations
    if (patterns.prompt_modifications.common_additions.length > 0) {
      recommendations.push({
        type: 'new_default_agent',
        priority: 'high',
        description: 'Create new default agents based on popular customizations',
        evidence: patterns.prompt_modifications.common_additions,
        estimated_adoption: this.estimateAdoption(patterns.prompt_modifications)
      });
    }

    // Suggest schema enhancements
    if (patterns.schema_modifications.common_fields.length > 0) {
      recommendations.push({
        type: 'schema_enhancement',
        priority: 'medium',
        description: 'Add popular custom fields to default schemas',
        evidence: patterns.schema_modifications.common_fields,
        estimated_impact: 'Reduce customization need by 40%'
      });
    }

    return recommendations;
  }
}
```

**Performance Alerts:**
```typescript
class PerformanceAlerter {
  async checkPerformanceAlerts(metrics: AgentPerformanceMetrics): Promise<void> => {
    const alerts = [];

    // Check processing time degradation
    if (metrics.processing_time_ms > 10000) { // > 10 seconds
      alerts.push({
        type: 'slow_processing',
        severity: 'warning',
        agent_id: metrics.agent_id,
        message: `Agent processing time exceeded 10 seconds: ${metrics.processing_time_ms}ms`
      });
    }

    // Check accuracy degradation
    if (metrics.accuracy_score < 0.8) { // < 80% accuracy
      alerts.push({
        type: 'low_accuracy',
        severity: 'error',
        agent_id: metrics.agent_id,
        message: `Agent accuracy dropped below 80%: ${(metrics.accuracy_score * 100).toFixed(1)}%`
      });
    }

    // Check error rate spike
    if (!metrics.success) {
      const recentErrorRate = await this.calculateRecentErrorRate(metrics.agent_id);
      if (recentErrorRate > 0.1) { // > 10% error rate
        alerts.push({
          type: 'high_error_rate',
          severity: 'critical',
          agent_id: metrics.agent_id,
          message: `Agent error rate exceeded 10%: ${(recentErrorRate * 100).toFixed(1)}%`
        });
      }
    }

    // Send alerts if any found
    if (alerts.length > 0) {
      await this.sendAlerts(alerts);
    }
  }

  private async sendAlerts(alerts: PerformanceAlert[]): Promise<void> => {
    for (const alert of alerts) {
      // Store alert in database
      await supabase.from('performance_alerts').insert(alert);

      // Send notification based on severity
      if (alert.severity === 'critical') {
        await this.sendImmediateNotification(alert);
      }
    }
  }
}
```

## 🧪 Testing Requirements
- [ ] Performance metrics accurately track processing times and accuracy
- [ ] Benchmarking correctly compares custom vs default agents
- [ ] Pattern analysis identifies meaningful customization trends
- [ ] Alerts trigger correctly for performance degradation
- [ ] Aggregated metrics calculate accurately over time periods
- [ ] Performance data enables actionable insights

## 🔗 Dependencies
**Depends on**: #[3.5 JSON Schema Validation]
**Blocks**: None (Reporting and optimization feature)

## 📊 Metadata
- **Epic**: Epic 3
- **Effort**: S (Small: <1 day)
- **Priority**: Low

---

### 💡 Development Notes
This performance tracking foundation provides the analytics insights that enable senior developers to implement data-driven optimization features and identify opportunities for platform improvements.