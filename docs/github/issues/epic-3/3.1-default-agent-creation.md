**Title:** [Epic 3] Default Agent Creation

**Milestone:** Epic 3: Agent Management System
**Labels:** story, epic-3, priority-high, effort-M

## 🎯 User Story
As a **Platform Administrator**,
I want **versioned default agents for common document types**,
So that **customers have immediate value without customization**.

## 📋 Requirements
Create comprehensive default agent catalog with versioned agents for invoice, contract, receipt, and general document processing, providing immediate value and serving as customization templates for customers.

## ✅ Acceptance Criteria
- [ ] Invoice processing agent with standard field extraction
- [ ] Contract analysis agent with key terms identification
- [ ] Receipt processing agent with expense categorization
- [ ] General document agent with flexible field detection
- [ ] Agent versioning system with immutable default agents
- [ ] JSON schema definition for each agent output format
- [ ] Agent metadata including description, use case, and example output
- [ ] Performance validation on test document sets (>90% accuracy)
- [ ] Agent documentation with usage examples
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Default Agent Catalog:**
```typescript
const DEFAULT_AGENTS = {
  invoice: {
    id: 'default-invoice-v1',
    name: 'Invoice Processor',
    version: '1.0.0',
    category: 'invoice',
    description: 'Extracts structured data from invoices including vendor, amounts, dates, and line items',
    is_default: true,
    system_prompt: `You are an expert invoice data extraction system. Extract the following information from the provided invoice document and return it as JSON:

REQUIRED FIELDS:
- vendor_name: Company/person issuing the invoice
- invoice_number: Invoice identifier
- total_amount: Total amount due (number)
- invoice_date: Date invoice was issued (ISO format)
- due_date: Payment due date (ISO format)
- currency: Currency code (e.g., "USD")

OPTIONAL FIELDS:
- subtotal: Amount before tax
- tax_amount: Total tax amount
- line_items: Array of {description, quantity, unit_price, total}
- billing_address: Customer billing address
- vendor_address: Vendor address

Return valid JSON only. If a field cannot be determined, use null.`,
    output_schema: {
      type: 'object',
      required: ['vendor_name', 'total_amount', 'invoice_date'],
      properties: {
        vendor_name: { type: 'string' },
        invoice_number: { type: 'string' },
        total_amount: { type: 'number' },
        invoice_date: { type: 'string', format: 'date' },
        due_date: { type: 'string', format: 'date' },
        currency: { type: 'string', default: 'USD' },
        subtotal: { type: 'number' },
        tax_amount: { type: 'number' },
        line_items: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              quantity: { type: 'number' },
              unit_price: { type: 'number' },
              total: { type: 'number' }
            }
          }
        }
      }
    }
  },

  contract: {
    id: 'default-contract-v1',
    name: 'Contract Analyzer',
    version: '1.0.0',
    category: 'contract',
    description: 'Analyzes contracts and extracts key terms, parties, dates, and obligations',
    is_default: true,
    system_prompt: `You are an expert contract analysis system. Extract key information from the provided contract document and return it as JSON:

REQUIRED FIELDS:
- parties: Array of contracting parties (names/entities)
- contract_type: Type of contract (e.g., "Service Agreement", "NDA", "Employment")
- effective_date: When contract becomes effective (ISO format)

OPTIONAL FIELDS:
- expiration_date: Contract end date (ISO format)
- governing_law: Jurisdiction governing the contract
- key_terms: Array of important terms/clauses
- renewal_terms: Auto-renewal or renewal process description
- termination_clause: How contract can be terminated
- payment_terms: Payment obligations and schedule

Return valid JSON only. If a field cannot be determined, use null.`,
    output_schema: {
      type: 'object',
      required: ['parties', 'contract_type', 'effective_date'],
      properties: {
        parties: { type: 'array', items: { type: 'string' } },
        contract_type: { type: 'string' },
        effective_date: { type: 'string', format: 'date' },
        expiration_date: { type: 'string', format: 'date' },
        governing_law: { type: 'string' },
        key_terms: { type: 'array', items: { type: 'string' } },
        renewal_terms: { type: 'string' },
        termination_clause: { type: 'string' },
        payment_terms: { type: 'string' }
      }
    }
  },

  receipt: {
    id: 'default-receipt-v1',
    name: 'Receipt Processor',
    version: '1.0.0',
    category: 'receipt',
    description: 'Processes receipts for expense tracking with merchant, amount, date, and category',
    is_default: true,
    system_prompt: `You are an expert receipt processing system. Extract expense information from the provided receipt and return it as JSON:

REQUIRED FIELDS:
- merchant_name: Business name where purchase was made
- total_amount: Total amount paid (number)
- transaction_date: Date of purchase (ISO format)
- category: Expense category (e.g., "meals", "travel", "office supplies", "fuel")

OPTIONAL FIELDS:
- payment_method: How payment was made (e.g., "cash", "credit card", "debit card")
- tax_amount: Tax amount if separately listed
- tip_amount: Tip amount if applicable
- items: Array of purchased items with descriptions and prices
- merchant_address: Store location

Return valid JSON only. If a field cannot be determined, use null.`,
    output_schema: {
      type: 'object',
      required: ['merchant_name', 'total_amount', 'transaction_date', 'category'],
      properties: {
        merchant_name: { type: 'string' },
        total_amount: { type: 'number' },
        transaction_date: { type: 'string', format: 'date' },
        category: {
          type: 'string',
          enum: ['meals', 'travel', 'office supplies', 'fuel', 'equipment', 'services', 'other']
        },
        payment_method: { type: 'string' },
        tax_amount: { type: 'number' },
        tip_amount: { type: 'number' },
        items: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              description: { type: 'string' },
              price: { type: 'number' }
            }
          }
        }
      }
    }
  },

  general: {
    id: 'default-general-v1',
    name: 'General Document Processor',
    version: '1.0.0',
    category: 'general',
    description: 'Flexible document processor that identifies document type and extracts basic structured data',
    is_default: true,
    system_prompt: `You are a general document analysis system. Analyze the provided document and extract structured information:

First, identify the document type, then extract relevant information based on the type:

ALWAYS INCLUDE:
- document_type: Type of document (e.g., "invoice", "receipt", "contract", "letter", "form", "report")
- title: Document title or subject
- date: Any date found in the document (ISO format)
- entities: Important names, companies, or organizations mentioned

CONDITIONALLY INCLUDE (based on document type):
- amounts: Any monetary amounts found
- addresses: Physical addresses mentioned
- contact_info: Phone numbers, emails
- key_points: Important information or conclusions

Return valid JSON only. Adapt the fields based on what's most relevant for the detected document type.`,
    output_schema: {
      type: 'object',
      required: ['document_type'],
      properties: {
        document_type: { type: 'string' },
        title: { type: 'string' },
        date: { type: 'string', format: 'date' },
        entities: { type: 'array', items: { type: 'string' } },
        amounts: { type: 'array', items: { type: 'number' } },
        addresses: { type: 'array', items: { type: 'string' } },
        contact_info: { type: 'array', items: { type: 'string' } },
        key_points: { type: 'array', items: { type: 'string' } }
      }
    }
  }
};
```

**Key Files to Modify/Create:**
- Migration: `supabase/migrations/[timestamp]_default_agents.sql`
- Data: `default-agents.ts` - Agent definitions and seeding
- Utils: `agent-validator.ts` - Schema validation utilities
- Tests: `tests/unit/default-agents.test.ts`
- Documentation: Agent usage examples and performance metrics

**Agent Seeding Implementation:**
```typescript
const seedDefaultAgents = async () => {
  for (const [key, agent] of Object.entries(DEFAULT_AGENTS)) {
    const { error } = await supabase.from('agents').insert({
      id: agent.id,
      name: agent.name,
      category: agent.category,
      description: agent.description,
      system_prompt: agent.system_prompt,
      output_schema: agent.output_schema,
      version: agent.version,
      is_default: true,
      customer_id: null, // Default agents have no customer
      parent_agent_id: null,
      created_at: new Date(),
      updated_at: new Date()
    });

    if (error) {
      console.error(`Failed to seed agent ${agent.name}:`, error);
    } else {
      console.log(`✅ Seeded default agent: ${agent.name}`);
    }
  }
};
```

**Agent Versioning System:**
```typescript
interface AgentVersion {
  id: string;
  version: string;
  is_current: boolean;
  changelog: string;
  created_at: Date;
  deprecation_date?: Date;
}

const createAgentVersion = async (agentId: string, changes: string) => {
  // Mark current version as not current
  await supabase
    .from('agent_versions')
    .update({ is_current: false })
    .eq('agent_id', agentId)
    .eq('is_current', true);

  // Create new version
  const version = generateVersionNumber(); // e.g., "1.1.0"
  await supabase.from('agent_versions').insert({
    agent_id: agentId,
    version,
    is_current: true,
    changelog: changes
  });

  return version;
};
```

**Performance Validation:**
```typescript
const validateAgentPerformance = async (agentId: string) => {
  const testDocuments = await loadTestDocuments(agentId);
  const results = [];

  for (const testDoc of testDocuments) {
    const startTime = performance.now();
    const result = await processDocumentWithAgent(testDoc.content, agentId);
    const processingTime = performance.now() - startTime;

    const accuracy = calculateAccuracy(result, testDoc.expectedOutput);
    results.push({
      document_id: testDoc.id,
      accuracy,
      processing_time_ms: processingTime,
      passed: accuracy >= 0.9 // 90% accuracy threshold
    });
  }

  const overallAccuracy = results.reduce((sum, r) => sum + r.accuracy, 0) / results.length;
  const avgProcessingTime = results.reduce((sum, r) => sum + r.processing_time_ms, 0) / results.length;

  return {
    agent_id: agentId,
    overall_accuracy: overallAccuracy,
    avg_processing_time_ms: avgProcessingTime,
    test_count: results.length,
    passed_threshold: overallAccuracy >= 0.9,
    individual_results: results
  };
};
```

## 🧪 Testing Requirements
- [ ] All default agents process test documents with >90% accuracy
- [ ] Agent output strictly follows defined JSON schemas
- [ ] Agent versioning correctly tracks changes and current versions
- [ ] Performance validation runs successfully for all agents
- [ ] Agent documentation includes working examples
- [ ] Schema validation prevents malformed outputs

## 🔗 Dependencies
**Depends on**: #[2.4 Basic Document Processing]
**Blocks**: #[3.2 Agent Storage & Retrieval], #[3.3 Agent Cloning System]

## 📊 Metadata
- **Epic**: Epic 3
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This default agent foundation provides the immediate value and customization templates that enable senior developers to implement any customer-specific agent requirements by building on proven, tested agent patterns.