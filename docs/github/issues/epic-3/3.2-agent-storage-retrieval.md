**Title:** [Epic 3] Agent Storage & Retrieval

**Milestone:** Epic 3: Agent Management System
**Labels:** story, epic-3, priority-medium, effort-S

## 🎯 User Story
As a **Developer**,
I want **to list and retrieve available agents via API**,
So that **I can understand platform capabilities and select appropriate agents**.

## 📋 Requirements
Implement comprehensive agent storage and retrieval system with proper access controls, filtering capabilities, and detailed agent information for customer integration and selection.

## ✅ Acceptance Criteria
- [ ] GET /api/v1/agents endpoint lists all available agents
- [ ] Agent response includes metadata, schema, and example output
- [ ] Filtering by agent type, version, and availability status
- [ ] Pagination support for large agent catalogs
- [ ] Agent details endpoint with comprehensive information
- [ ] Permission-based agent visibility (public vs custom)
- [ ] Agent search functionality by keywords and use case
- [ ] Response caching for improved performance
- [ ] Rate limiting per API key for agent queries
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## ✅ Acceptance Criteria
**Agent Listing Response Format:**
```json
{
  "agents": [
    {
      "id": "default-invoice-v1",
      "name": "Invoice Processor",
      "category": "invoice",
      "version": "1.0.0",
      "description": "Extracts structured data from invoices...",
      "is_default": true,
      "customer_id": null,
      "parent_agent_id": null,
      "use_cases": ["accounts payable", "expense tracking"],
      "supported_formats": ["pdf", "image"],
      "accuracy_rating": 0.95,
      "avg_processing_time_ms": 2400,
      "created_at": "2025-09-21T10:00:00Z",
      "updated_at": "2025-09-21T10:00:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 3,
    "total_count": 15,
    "page_size": 10
  },
  "filters_applied": {
    "category": "invoice",
    "is_default": true
  }
}
```

**Key Files to Modify/Create:**
- Function: `supabase/functions/agents/index.ts` - Main agent API
- Utils: `agent-filter.ts` - Filtering and search logic
- Utils: `agent-cache.ts` - Response caching implementation
- Types: Agent API interfaces and response types
- Tests: `tests/integration/agent-api.test.ts`

**Agent Listing Implementation:**
```typescript
// GET /api/v1/agents
const getAgents = async (req: Request): Promise<AgentListResponse> => {
  const url = new URL(req.url);
  const params = {
    category: url.searchParams.get('category'),
    is_default: url.searchParams.get('is_default'),
    search: url.searchParams.get('search'),
    page: parseInt(url.searchParams.get('page') || '1'),
    limit: Math.min(parseInt(url.searchParams.get('limit') || '10'), 50)
  };

  // Validate API key and get customer context
  const { customerId } = await validateApiKey(req.headers.get('Authorization'));

  // Build query with proper access controls
  let query = supabase
    .from('agents')
    .select(`
      id, name, category, version, description, is_default,
      customer_id, parent_agent_id, use_cases, supported_formats,
      accuracy_rating, avg_processing_time_ms, created_at, updated_at
    `)
    .or(`is_default.eq.true,customer_id.eq.${customerId}`); // Default agents OR customer's agents

  // Apply filters
  if (params.category) {
    query = query.eq('category', params.category);
  }

  if (params.is_default !== null) {
    query = query.eq('is_default', params.is_default === 'true');
  }

  if (params.search) {
    query = query.or(
      `name.ilike.%${params.search}%,description.ilike.%${params.search}%`
    );
  }

  // Apply pagination
  const offset = (params.page - 1) * params.limit;
  query = query.range(offset, offset + params.limit - 1);

  const { data: agents, error, count } = await query;

  if (error) throw error;

  return {
    agents: agents || [],
    pagination: {
      current_page: params.page,
      total_pages: Math.ceil(count / params.limit),
      total_count: count,
      page_size: params.limit
    },
    filters_applied: {
      category: params.category,
      is_default: params.is_default,
      search: params.search
    }
  };
};
```

**Agent Details Endpoint:**
```typescript
// GET /api/v1/agents/{agent_id}
const getAgentDetails = async (agentId: string, customerId: string): Promise<AgentDetails> => {
  const { data: agent } = await supabase
    .from('agents')
    .select(`
      *,
      agent_versions!inner(version, changelog, created_at),
      agent_performance(accuracy_rating, avg_processing_time_ms, last_updated)
    `)
    .eq('id', agentId)
    .or(`is_default.eq.true,customer_id.eq.${customerId}`)
    .single();

  if (!agent) {
    throw new Error('Agent not found or access denied');
  }

  // Include example output
  const exampleOutput = await generateExampleOutput(agent);

  return {
    ...agent,
    output_schema: agent.output_schema,
    example_input: getExampleInput(agent.category),
    example_output: exampleOutput,
    performance_metrics: agent.agent_performance?.[0] || null,
    version_history: agent.agent_versions || []
  };
};
```

**Agent Search Implementation:**
```typescript
const searchAgents = async (searchTerm: string, customerId: string): Promise<AgentSearchResult[]> => {
  // Full-text search across name, description, and use cases
  const { data: results } = await supabase
    .from('agents')
    .select('id, name, category, description, use_cases')
    .or(`is_default.eq.true,customer_id.eq.${customerId}`)
    .textSearch('search_vector', searchTerm, {
      type: 'websearch',
      config: 'english'
    });

  return results.map(agent => ({
    ...agent,
    relevance_score: calculateRelevanceScore(agent, searchTerm)
  })).sort((a, b) => b.relevance_score - a.relevance_score);
};
```

**Response Caching:**
```typescript
const CACHE_TTL = 300; // 5 minutes

const getCachedAgentList = async (cacheKey: string): Promise<AgentListResponse | null> => {
  const cached = await redis.get(cacheKey);
  return cached ? JSON.parse(cached) : null;
};

const setCachedAgentList = async (cacheKey: string, data: AgentListResponse): Promise<void> => {
  await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(data));
};

const generateCacheKey = (params: any, customerId: string): string => {
  const key = Object.keys(params)
    .sort()
    .map(k => `${k}:${params[k]}`)
    .join('|');
  return `agents:${customerId}:${hashString(key)}`;
};
```

**Agent Categories and Filtering:**
```typescript
const AGENT_CATEGORIES = {
  invoice: {
    name: 'Invoice Processing',
    description: 'Extract data from invoices and bills',
    use_cases: ['accounts payable', 'expense tracking', 'vendor management']
  },
  contract: {
    name: 'Contract Analysis',
    description: 'Analyze contracts and legal documents',
    use_cases: ['contract review', 'compliance', 'legal analysis']
  },
  receipt: {
    name: 'Receipt Processing',
    description: 'Process receipts for expense tracking',
    use_cases: ['expense management', 'tax preparation', 'reimbursement']
  },
  general: {
    name: 'General Documents',
    description: 'Process various document types',
    use_cases: ['document classification', 'data extraction', 'content analysis']
  }
};

const getAgentCategories = (): CategoryInfo[] => {
  return Object.entries(AGENT_CATEGORIES).map(([key, info]) => ({
    category: key,
    ...info
  }));
};
```

**Performance Metrics Integration:**
```typescript
const updateAgentPerformanceMetrics = async (agentId: string): Promise<void> => {
  // Calculate metrics from recent usage
  const { data: recentUsage } = await supabase
    .from('usage_logs')
    .select('processing_time_ms, accuracy_score')
    .eq('agent_id', agentId)
    .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)) // Last 30 days
    .not('accuracy_score', 'is', null);

  if (recentUsage.length === 0) return;

  const avgProcessingTime = recentUsage.reduce((sum, u) => sum + u.processing_time_ms, 0) / recentUsage.length;
  const avgAccuracy = recentUsage.reduce((sum, u) => sum + u.accuracy_score, 0) / recentUsage.length;

  await supabase
    .from('agents')
    .update({
      avg_processing_time_ms: Math.round(avgProcessingTime),
      accuracy_rating: parseFloat(avgAccuracy.toFixed(3))
    })
    .eq('id', agentId);
};
```

## 🧪 Testing Requirements
- [ ] Agent listing returns correct agents based on customer access
- [ ] Filtering and search functionality works accurately
- [ ] Pagination handles large agent catalogs correctly
- [ ] Agent details endpoint provides comprehensive information
- [ ] Caching improves response times for repeated requests
- [ ] Rate limiting prevents API abuse

## 🔗 Dependencies
**Depends on**: #[3.1 Default Agent Creation]
**Blocks**: #[3.3 Agent Cloning System], #[3.4 Agent Customization]

## 📊 Metadata
- **Epic**: Epic 3
- **Effort**: S (Small: <1 day)
- **Priority**: Medium

---

### 💡 Development Notes
This agent discovery system provides the API foundation that enables senior developers to implement any agent selection or browsing interface with comprehensive filtering, search, and performance insights.