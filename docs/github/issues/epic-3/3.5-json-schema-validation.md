**Title:** [Epic 3] JSON Schema Validation

**Milestone:** Epic 3: Agent Management System
**Labels:** story, epic-3, priority-medium, effort-M

## 🎯 User Story
As a **Platform Administrator**,
I want **automatic validation of agent outputs against defined schemas**,
So that **customers receive consistent, reliable data structures**.

## 📋 Requirements
Implement comprehensive JSON Schema validation system that ensures all agent outputs conform to defined schemas, provides clear error messages, and maintains backward compatibility for evolving schemas.

## ✅ Acceptance Criteria
- [ ] JSON Schema validation for all agent outputs
- [ ] Schema enforcement prevents malformed responses
- [ ] Validation error reporting with specific field issues
- [ ] Schema compatibility checking for agent updates
- [ ] Default schema generation from agent field definitions
- [ ] Custom schema upload capability for advanced users
- [ ] Schema versioning with backward compatibility support
- [ ] Performance optimization for high-volume validation
- [ ] Validation bypass for testing and debugging
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Schema Validation Architecture:**
1. **Pre-processing**: Validate schema definitions when agents are created/updated
2. **Runtime Validation**: Validate AI outputs against agent schemas
3. **Error Handling**: Provide detailed validation errors with field-level feedback
4. **Performance**: Cache compiled schemas for high-volume processing
5. **Compatibility**: Check schema changes for breaking compatibility issues

**Key Files to Modify/Create:**
- Utils: `schema-validator.ts` - Core JSON Schema validation logic
- Utils: `schema-compiler.ts` - Schema compilation and caching
- Utils: `schema-compatibility.ts` - Backward compatibility checking
- Utils: `schema-generator.ts` - Auto-generation from field definitions
- Types: Schema validation interfaces and error types
- Tests: `tests/unit/schema-validation.test.ts`

**Core Schema Validation Implementation:**
```typescript
import Ajv from 'ajv';
import addFormats from 'ajv-formats';

interface SchemaValidationResult {
  valid: boolean;
  errors?: ValidationError[];
  data?: any; // Validated and potentially cleaned data
}

interface ValidationError {
  field: string;
  message: string;
  value: any;
  expected: string;
  severity: 'error' | 'warning';
}

class SchemaValidator {
  private ajv: Ajv;
  private compiledSchemas = new Map<string, ValidateFunction>();

  constructor() {
    this.ajv = new Ajv({
      allErrors: true,      // Collect all validation errors
      removeAdditional: true, // Remove properties not in schema
      useDefaults: true,    // Apply default values
      coerceTypes: true,    // Type coercion (string "123" -> number 123)
      verbose: true         // Include schema and data in errors
    });

    // Add format validation (date, email, etc.)
    addFormats(this.ajv);

    // Add custom formats for business data
    this.addCustomFormats();
  }

  // Validate data against agent's output schema
  async validateAgentOutput(agentId: string, data: any): Promise<SchemaValidationResult> {
    const schema = await this.getAgentSchema(agentId);
    const validate = await this.getCompiledValidator(schema);

    const valid = validate(data);

    if (valid) {
      return {
        valid: true,
        data: data // May be modified by removeAdditional/useDefaults
      };
    }

    const errors = this.formatValidationErrors(validate.errors || []);
    return {
      valid: false,
      errors: errors,
      data: data
    };
  }

  // Get compiled validator with caching
  private async getCompiledValidator(schema: JSONSchema): Promise<ValidateFunction> {
    const schemaKey = this.getSchemaKey(schema);

    if (!this.compiledSchemas.has(schemaKey)) {
      try {
        const validate = this.ajv.compile(schema);
        this.compiledSchemas.set(schemaKey, validate);
      } catch (error) {
        throw new Error(`Schema compilation failed: ${error.message}`);
      }
    }

    return this.compiledSchemas.get(schemaKey)!;
  }

  // Format AJV errors into user-friendly format
  private formatValidationErrors(ajvErrors: ErrorObject[]): ValidationError[] {
    return ajvErrors.map(error => ({
      field: error.instancePath.replace(/^\//, '') || error.schemaPath.split('/').pop() || 'root',
      message: this.getHumanReadableError(error),
      value: error.data,
      expected: this.getExpectedValue(error),
      severity: this.getErrorSeverity(error)
    }));
  }

  private getHumanReadableError(error: ErrorObject): string {
    const field = error.instancePath.replace(/^\//, '') || 'field';

    switch (error.keyword) {
      case 'required':
        return `Required field '${error.params.missingProperty}' is missing`;
      case 'type':
        return `Field '${field}' must be of type ${error.params.type}`;
      case 'format':
        return `Field '${field}' must be in ${error.params.format} format`;
      case 'minimum':
        return `Field '${field}' must be at least ${error.params.limit}`;
      case 'maximum':
        return `Field '${field}' must be at most ${error.params.limit}`;
      case 'enum':
        return `Field '${field}' must be one of: ${error.params.allowedValues.join(', ')}`;
      default:
        return error.message || 'Validation failed';
    }
  }

  private addCustomFormats(): void {
    // Currency format (e.g., "USD", "EUR")
    this.ajv.addFormat('currency', /^[A-Z]{3}$/);

    // Phone number format
    this.ajv.addFormat('phone', /^\+?[\d\s\-\(\)]{10,}$/);

    // Business identifier format
    this.ajv.addFormat('business-id', /^[A-Z0-9\-]{5,20}$/);

    // Confidence score (0.0 to 1.0)
    this.ajv.addFormat('confidence', (data: number) => data >= 0 && data <= 1);
  }
}
```

**Schema Compatibility Checking:**
```typescript
class SchemaCompatibilityChecker {
  // Check if new schema is backward compatible with old schema
  checkCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityResult {
    const issues: CompatibilityIssue[] = [];

    // Check required fields
    const oldRequired = oldSchema.required || [];
    const newRequired = newSchema.required || [];

    // Breaking change: new required fields
    for (const field of newRequired) {
      if (!oldRequired.includes(field)) {
        issues.push({
          type: 'breaking',
          field: field,
          message: `New required field '${field}' added`,
          impact: 'Existing data may fail validation'
        });
      }
    }

    // Non-breaking change: removed required fields
    for (const field of oldRequired) {
      if (!newRequired.includes(field)) {
        issues.push({
          type: 'warning',
          field: field,
          message: `Required field '${field}' is now optional`,
          impact: 'May affect data consistency'
        });
      }
    }

    // Check field type changes
    const typeChanges = this.checkTypeCompatibility(oldSchema, newSchema);
    issues.push(...typeChanges);

    // Check enum value changes
    const enumChanges = this.checkEnumCompatibility(oldSchema, newSchema);
    issues.push(...enumChanges);

    return {
      compatible: !issues.some(i => i.type === 'breaking'),
      issues: issues
    };
  }

  private checkTypeCompatibility(oldSchema: JSONSchema, newSchema: JSONSchema): CompatibilityIssue[] {
    const issues: CompatibilityIssue[] = [];
    const oldProps = oldSchema.properties || {};
    const newProps = newSchema.properties || {};

    for (const [fieldName, oldProp] of Object.entries(oldProps)) {
      const newProp = newProps[fieldName];
      if (newProp && oldProp.type !== newProp.type) {
        // Some type changes are compatible
        const compatible = this.areTypesCompatible(oldProp.type, newProp.type);

        issues.push({
          type: compatible ? 'warning' : 'breaking',
          field: fieldName,
          message: `Field type changed from ${oldProp.type} to ${newProp.type}`,
          impact: compatible
            ? 'May require data conversion'
            : 'Will break existing data structures'
        });
      }
    }

    return issues;
  }

  private areTypesCompatible(oldType: string, newType: string): boolean {
    // Define compatible type transitions
    const compatibleTransitions = {
      'integer': ['number'],     // integer -> number is safe
      'string': ['string'],      // string -> string (format changes)
      'number': []               // number -> other types generally not safe
    };

    return compatibleTransitions[oldType]?.includes(newType) || false;
  }
}
```

**Schema Generation from Field Definitions:**
```typescript
class SchemaGenerator {
  // Generate JSON Schema from simplified field definitions
  generateFromFields(fields: FieldDefinition[]): JSONSchema {
    const properties: Record<string, any> = {};
    const required: string[] = [];

    for (const field of fields) {
      properties[field.name] = this.fieldToSchemaProperty(field);

      if (field.required) {
        required.push(field.name);
      }
    }

    return {
      type: 'object',
      properties: properties,
      required: required,
      additionalProperties: false,
      $schema: 'http://json-schema.org/draft-07/schema#'
    };
  }

  private fieldToSchemaProperty(field: FieldDefinition): any {
    const property: any = {
      type: field.type,
      description: field.description
    };

    // Add type-specific properties
    switch (field.type) {
      case 'string':
        if (field.format) property.format = field.format;
        if (field.maxLength) property.maxLength = field.maxLength;
        if (field.enum) property.enum = field.enum;
        break;

      case 'number':
      case 'integer':
        if (field.minimum !== undefined) property.minimum = field.minimum;
        if (field.maximum !== undefined) property.maximum = field.maximum;
        break;

      case 'array':
        if (field.items) {
          property.items = this.fieldToSchemaProperty(field.items);
        }
        if (field.minItems) property.minItems = field.minItems;
        if (field.maxItems) property.maxItems = field.maxItems;
        break;

      case 'object':
        if (field.properties) {
          property.properties = {};
          for (const [propName, propDef] of Object.entries(field.properties)) {
            property.properties[propName] = this.fieldToSchemaProperty(propDef);
          }
        }
        break;
    }

    // Add default value
    if (field.default !== undefined) {
      property.default = field.default;
    }

    return property;
  }
}
```

**Performance Optimization:**
```typescript
class SchemaCache {
  private cache = new Map<string, CompiledSchema>();
  private metrics = {
    hits: 0,
    misses: 0,
    compilations: 0
  };

  async getSchema(agentId: string): Promise<CompiledSchema> {
    if (this.cache.has(agentId)) {
      this.metrics.hits++;
      return this.cache.get(agentId)!;
    }

    this.metrics.misses++;
    const schema = await this.compileSchema(agentId);
    this.cache.set(agentId, schema);

    // Cache cleanup - remove old entries
    if (this.cache.size > 1000) {
      this.cleanupCache();
    }

    return schema;
  }

  private async compileSchema(agentId: string): Promise<CompiledSchema> {
    this.metrics.compilations++;

    const { data: agent } = await supabase
      .from('agents')
      .select('output_schema, updated_at')
      .eq('id', agentId)
      .single();

    if (!agent) {
      throw new Error(`Agent ${agentId} not found`);
    }

    const validator = new SchemaValidator();
    const compiled = await validator.getCompiledValidator(agent.output_schema);

    return {
      validator: compiled,
      compiledAt: new Date(),
      schemaVersion: agent.updated_at
    };
  }

  getCacheMetrics() {
    const total = this.metrics.hits + this.metrics.misses;
    return {
      ...this.metrics,
      hit_rate: total > 0 ? this.metrics.hits / total : 0,
      cache_size: this.cache.size
    };
  }
}
```

**Integration with Document Processing:**
```typescript
const validateAndFormatOutput = async (
  agentId: string,
  rawOutput: any
): Promise<ProcessedOutput> => {
  const validator = new SchemaValidator();

  try {
    // Validate against agent schema
    const validation = await validator.validateAgentOutput(agentId, rawOutput);

    if (validation.valid) {
      return {
        success: true,
        data: validation.data,
        validation_passed: true
      };
    } else {
      // Return validation errors but don't fail completely
      return {
        success: true,
        data: rawOutput, // Return original data
        validation_passed: false,
        validation_errors: validation.errors,
        warning: 'Output does not match expected schema'
      };
    }

  } catch (error) {
    // Schema validation system error
    console.error('Schema validation error:', error);
    return {
      success: true,
      data: rawOutput,
      validation_passed: false,
      validation_errors: [{
        field: 'system',
        message: 'Schema validation system error',
        value: null,
        expected: 'valid schema',
        severity: 'error'
      }]
    };
  }
};
```

## 🧪 Testing Requirements
- [ ] Schema validation correctly identifies valid/invalid outputs
- [ ] Compatibility checking detects breaking changes accurately
- [ ] Schema generation creates valid JSON Schema from field definitions
- [ ] Performance optimization maintains speed under high load
- [ ] Error messages provide clear, actionable feedback
- [ ] Caching system improves validation performance

## 🔗 Dependencies
**Depends on**: #[3.4 Agent Customization]
**Blocks**: #[3.6 Agent Performance Tracking]

## 📊 Metadata
- **Epic**: Epic 3
- **Effort**: M (Medium: 1-3 days)
- **Priority**: Medium

---

### 💡 Development Notes
This schema validation foundation ensures data quality and consistency, enabling senior developers to implement any output format requirement with confidence in structural validation and compatibility.