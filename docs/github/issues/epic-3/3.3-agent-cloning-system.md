**Title:** [Epic 3] Agent Cloning System

**Milestone:** Epic 3: Agent Management System
**Labels:** story, epic-3, priority-high, effort-M

## 🎯 User Story
As a **Developer**,
I want **to clone default agents for customization**,
So that **I can tailor extraction for my specific document formats**.

## 📋 Requirements
Implement comprehensive agent cloning system that allows customers to create customizable copies of default agents while preserving parent-child relationships, version tracking, and inheritance capabilities.

## ✅ Acceptance Criteria
- [ ] POST /api/v1/agents/clone endpoint creates customer-specific copy
- [ ] Cloned agent inherits base structure with customization capability
- [ ] Customer ownership tracking for cloned agents
- [ ] Cloning preserves original agent version and metadata
- [ ] Custom agent naming and description functionality
- [ ] Cloning permission validation based on API key
- [ ] Clone operation logging for audit and usage tracking
- [ ] Bulk cloning support for multiple agents
- [ ] Clone conflict resolution (duplicate names)
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Clone Operation Flow:**
1. **Validate Source Agent**: Ensure agent exists and is accessible
2. **Check Permissions**: Verify customer can clone this agent
3. **Create Clone**: Copy agent with new ownership and customization flags
4. **Establish Relationship**: Link clone to parent agent
5. **Initialize Customization**: Set up for customer modifications
6. **Audit Logging**: Record clone operation for tracking

**Key Files to Modify/Create:**
- Function: `supabase/functions/agents/clone/index.ts`
- Utils: `agent-cloner.ts` - Core cloning logic
- Utils: `clone-validator.ts` - Permission and validation checks
- Types: Clone operation interfaces and responses
- Tests: `tests/integration/agent-cloning.test.ts`

**Agent Cloning Implementation:**
```typescript
// POST /api/v1/agents/clone
interface CloneAgentRequest {
  source_agent_id: string;
  name?: string;           // Custom name for cloned agent
  description?: string;    // Custom description
  category?: string;       // Override category if needed
  customize_immediately?: boolean; // Whether to open for customization
}

interface CloneAgentResponse {
  cloned_agent: {
    id: string;
    name: string;
    parent_agent_id: string;
    customer_id: string;
    is_customizable: boolean;
    cloned_at: string;
  };
  customization_url?: string; // If customize_immediately = true
}

const cloneAgent = async (req: Request): Promise<CloneAgentResponse> => {
  const { customerId, apiKeyId } = await validateApiKey(req.headers.get('Authorization'));
  const cloneRequest: CloneAgentRequest = await req.json();

  // 1. Validate source agent
  const sourceAgent = await getSourceAgent(cloneRequest.source_agent_id, customerId);
  if (!sourceAgent) {
    throw new Error('Source agent not found or access denied');
  }

  // 2. Check cloning permissions
  await validateClonePermissions(sourceAgent, customerId);

  // 3. Check for naming conflicts
  const cloneName = cloneRequest.name || `${sourceAgent.name} (Custom)`;
  await checkNamingConflict(cloneName, customerId);

  // 4. Create cloned agent
  const clonedAgent = await createClonedAgent({
    ...sourceAgent,
    name: cloneName,
    description: cloneRequest.description || `Customized version of ${sourceAgent.name}`,
    category: cloneRequest.category || sourceAgent.category,
    customer_id: customerId,
    parent_agent_id: sourceAgent.id,
    is_default: false,
    is_customizable: true,
    clone_generation: (sourceAgent.clone_generation || 0) + 1
  });

  // 5. Log clone operation
  await logCloneOperation(customerId, apiKeyId, sourceAgent.id, clonedAgent.id);

  const response: CloneAgentResponse = {
    cloned_agent: {
      id: clonedAgent.id,
      name: clonedAgent.name,
      parent_agent_id: clonedAgent.parent_agent_id,
      customer_id: clonedAgent.customer_id,
      is_customizable: clonedAgent.is_customizable,
      cloned_at: clonedAgent.created_at
    }
  };

  // 6. Optionally provide customization URL
  if (cloneRequest.customize_immediately) {
    response.customization_url = `/api/v1/agents/${clonedAgent.id}/customize`;
  }

  return response;
};
```

**Source Agent Validation:**
```typescript
const getSourceAgent = async (agentId: string, customerId: string): Promise<Agent | null> => {
  const { data: agent } = await supabase
    .from('agents')
    .select('*')
    .eq('id', agentId)
    .or(`is_default.eq.true,customer_id.eq.${customerId}`) // Can clone defaults or own agents
    .single();

  return agent;
};

const validateClonePermissions = async (sourceAgent: Agent, customerId: string): Promise<void> => {
  // Check if customer has cloning privileges
  const customer = await getCustomer(customerId);

  if (customer.tier === 'free' && !sourceAgent.is_default) {
    throw new Error('Free tier customers can only clone default agents');
  }

  // Check clone limits per tier
  const cloneCount = await getCustomerCloneCount(customerId);
  const maxClones = getMaxClonesForTier(customer.tier);

  if (cloneCount >= maxClones) {
    throw new Error(`Clone limit reached. Maximum ${maxClones} clones allowed for ${customer.tier} tier`);
  }

  // Check if agent is cloneable
  if (sourceAgent.is_deprecated) {
    throw new Error('Cannot clone deprecated agents');
  }
};
```

**Clone Creation with Inheritance:**
```typescript
const createClonedAgent = async (cloneParams: CreateCloneParams): Promise<Agent> => {
  const cloneId = generateAgentId();

  // Create the cloned agent with inherited properties
  const { data: clonedAgent, error } = await supabase
    .from('agents')
    .insert({
      id: cloneId,
      name: cloneParams.name,
      category: cloneParams.category,
      description: cloneParams.description,
      system_prompt: cloneParams.system_prompt, // Inherited, but customizable
      output_schema: cloneParams.output_schema, // Inherited, but customizable
      version: '1.0.0', // Clones start at v1.0.0
      is_default: false,
      is_customizable: true,
      customer_id: cloneParams.customer_id,
      parent_agent_id: cloneParams.parent_agent_id,
      clone_generation: cloneParams.clone_generation,
      inheritance_locked: false, // Allow customization
      created_at: new Date(),
      updated_at: new Date()
    })
    .select()
    .single();

  if (error) throw error;

  // Create inheritance tracking record
  await supabase.from('agent_inheritance').insert({
    child_agent_id: cloneId,
    parent_agent_id: cloneParams.parent_agent_id,
    inheritance_type: 'clone',
    created_at: new Date()
  });

  return clonedAgent;
};
```

**Naming Conflict Resolution:**
```typescript
const checkNamingConflict = async (name: string, customerId: string): Promise<void> => {
  const { data: existingAgent } = await supabase
    .from('agents')
    .select('id')
    .eq('name', name)
    .eq('customer_id', customerId)
    .single();

  if (existingAgent) {
    throw new Error(`Agent with name "${name}" already exists. Please choose a different name.`);
  }
};

const generateUniqueCloneName = async (baseName: string, customerId: string): Promise<string> => {
  let attempt = 1;
  let candidateName = `${baseName} (Custom)`;

  while (await nameExists(candidateName, customerId)) {
    attempt++;
    candidateName = `${baseName} (Custom ${attempt})`;
  }

  return candidateName;
};
```

**Bulk Cloning Support:**
```typescript
// POST /api/v1/agents/clone/bulk
interface BulkCloneRequest {
  source_agent_ids: string[];
  name_prefix?: string;
  category_override?: string;
}

const bulkCloneAgents = async (req: Request): Promise<BulkCloneResponse> => {
  const { customerId } = await validateApiKey(req.headers.get('Authorization'));
  const bulkRequest: BulkCloneRequest = await req.json();

  const results = [];
  const errors = [];

  for (const sourceAgentId of bulkRequest.source_agent_ids) {
    try {
      const sourceAgent = await getSourceAgent(sourceAgentId, customerId);
      if (!sourceAgent) {
        errors.push({ agent_id: sourceAgentId, error: 'Agent not found' });
        continue;
      }

      const cloneName = bulkRequest.name_prefix
        ? `${bulkRequest.name_prefix} ${sourceAgent.name}`
        : await generateUniqueCloneName(sourceAgent.name, customerId);

      const clonedAgent = await createClonedAgent({
        ...sourceAgent,
        name: cloneName,
        category: bulkRequest.category_override || sourceAgent.category,
        customer_id: customerId,
        parent_agent_id: sourceAgent.id
      });

      results.push({
        source_agent_id: sourceAgentId,
        cloned_agent_id: clonedAgent.id,
        cloned_agent_name: clonedAgent.name
      });

    } catch (error) {
      errors.push({ agent_id: sourceAgentId, error: error.message });
    }
  }

  return {
    successful_clones: results,
    failed_clones: errors,
    total_requested: bulkRequest.source_agent_ids.length,
    successful_count: results.length,
    failed_count: errors.length
  };
};
```

**Clone Audit Logging:**
```typescript
const logCloneOperation = async (
  customerId: string,
  apiKeyId: string,
  sourceAgentId: string,
  clonedAgentId: string
): Promise<void> => {
  await supabase.from('audit_logs').insert({
    customer_id: customerId,
    api_key_id: apiKeyId,
    action: 'agent_clone',
    resource_type: 'agent',
    resource_id: clonedAgentId,
    details: {
      source_agent_id: sourceAgentId,
      cloned_agent_id: clonedAgentId,
      clone_timestamp: new Date().toISOString()
    },
    ip_address: req.headers.get('x-forwarded-for'),
    user_agent: req.headers.get('user-agent')
  });
};
```

**Clone Tier Limits:**
```typescript
const CLONE_LIMITS = {
  free: 2,        // 2 agent clones
  starter: 10,    // 10 agent clones
  professional: 50, // 50 agent clones
  enterprise: -1  // Unlimited clones
};

const getMaxClonesForTier = (tier: string): number => {
  return CLONE_LIMITS[tier] || CLONE_LIMITS.free;
};

const getCustomerCloneCount = async (customerId: string): Promise<number> => {
  const { count } = await supabase
    .from('agents')
    .select('id', { count: 'exact' })
    .eq('customer_id', customerId)
    .not('parent_agent_id', 'is', null); // Only count cloned agents

  return count || 0;
};
```

## 🧪 Testing Requirements
- [ ] Agent cloning creates correct parent-child relationships
- [ ] Cloned agents inherit proper base structure and schema
- [ ] Customer ownership and access controls work correctly
- [ ] Naming conflict resolution prevents duplicate names
- [ ] Tier-based clone limits are enforced properly
- [ ] Bulk cloning handles errors gracefully

## 🔗 Dependencies
**Depends on**: #[3.2 Agent Storage & Retrieval]
**Blocks**: #[3.4 Agent Customization], #[3.6 Agent Performance Tracking]

## 📊 Metadata
- **Epic**: Epic 3
- **Effort**: M (Medium: 1-3 days)
- **Priority**: High

---

### 💡 Development Notes
This cloning system provides the customization foundation that enables senior developers to implement any agent personalization feature while maintaining clear inheritance relationships and access controls.