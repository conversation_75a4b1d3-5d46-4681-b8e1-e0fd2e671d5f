**Title:** [Epic 3] Agent Customization

**Milestone:** Epic 3: Agent Management System
**Labels:** story, epic-3, priority-high, effort-L

## 🎯 User Story
As a **Developer**,
I want **to modify cloned agents with custom prompts and field definitions**,
So that **extraction matches my specific business requirements**.

## 📋 Requirements
Implement comprehensive agent customization system allowing customers to modify prompts, output schemas, and processing parameters while maintaining validation, version control, and rollback capabilities.

## ✅ Acceptance Criteria
- [ ] PUT /api/v1/agents/{id} endpoint for agent updates
- [ ] Custom prompt modification with validation
- [ ] Custom field definition with JSON schema validation
- [ ] Agent testing capability with sample documents
- [ ] Version control for custom agent modifications
- [ ] Rollback capability to previous agent versions
- [ ] Validation prevents breaking changes to output schema
- [ ] Preview mode for testing changes before saving
- [ ] Collaborative editing with change tracking
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

## 🏗️ Implementation Notes
**Customization Capabilities:**
1. **Prompt Engineering**: Modify system prompts for specific extraction needs
2. **Schema Customization**: Add/remove fields, change data types, set validation rules
3. **Processing Parameters**: Adjust confidence thresholds, retry logic, model selection
4. **Output Formatting**: Customize response structure and field naming
5. **Validation Rules**: Add business-specific validation and data cleaning

**Key Files to Modify/Create:**
- Function: `supabase/functions/agents/{id}/customize/index.ts`
- Utils: `agent-customizer.ts` - Core customization logic
- Utils: `schema-validator.ts` - JSON schema validation and compatibility
- Utils: `prompt-validator.ts` - Prompt safety and injection prevention
- Utils: `agent-tester.ts` - Preview and testing capabilities
- Types: Customization interfaces and validation types

**Agent Customization Implementation:**
```typescript
// PUT /api/v1/agents/{id}
interface CustomizeAgentRequest {
  name?: string;
  description?: string;
  system_prompt?: string;
  output_schema?: JSONSchema;
  processing_config?: ProcessingConfig;
  preview_mode?: boolean; // Test changes without saving
  save_as_version?: string; // Create new version
}

interface CustomizeAgentResponse {
  agent: CustomAgent;
  validation_results: ValidationResult[];
  preview_results?: PreviewResult[]; // If preview_mode = true
  version_created?: string;
}

const customizeAgent = async (req: Request, agentId: string): Promise<CustomizeAgentResponse> => {
  const { customerId } = await validateApiKey(req.headers.get('Authorization'));
  const customization: CustomizeAgentRequest = await req.json();

  // 1. Validate agent ownership and customization permissions
  const agent = await validateAgentOwnership(agentId, customerId);
  if (!agent.is_customizable) {
    throw new Error('This agent cannot be customized');
  }

  // 2. Validate customization changes
  const validationResults = await validateCustomization(agent, customization);
  const hasErrors = validationResults.some(r => r.level === 'error');

  if (hasErrors && !customization.preview_mode) {
    throw new Error('Validation errors must be resolved before saving');
  }

  // 3. Preview mode - test without saving
  if (customization.preview_mode) {
    const previewResults = await previewCustomization(agent, customization);
    return {
      agent,
      validation_results: validationResults,
      preview_results: previewResults
    };
  }

  // 4. Apply customizations
  const customizedAgent = await applyCustomization(agent, customization, customerId);

  // 5. Create version if requested
  let versionCreated;
  if (customization.save_as_version) {
    versionCreated = await createAgentVersion(agentId, customization.save_as_version, customization);
  }

  return {
    agent: customizedAgent,
    validation_results: validationResults,
    version_created: versionCreated
  };
};
```

**Prompt Customization with Safety:**
```typescript
const validateCustomPrompt = async (prompt: string, agentCategory: string): Promise<ValidationResult[]> => {
  const results: ValidationResult[] = [];

  // 1. Check for prompt injection attempts
  const injectionPatterns = [
    /ignore\s+previous\s+instructions/i,
    /system\s*[:.]?\s*prompt/i,
    /\/\*.*\*\//gs, // Block comments
    /<script.*?>.*?<\/script>/gis
  ];

  for (const pattern of injectionPatterns) {
    if (pattern.test(prompt)) {
      results.push({
        level: 'error',
        field: 'system_prompt',
        message: 'Potential prompt injection detected',
        suggestion: 'Remove suspicious instructions and system commands'
      });
    }
  }

  // 2. Check prompt length
  if (prompt.length > 4000) {
    results.push({
      level: 'warning',
      field: 'system_prompt',
      message: 'Prompt is very long and may affect performance',
      suggestion: 'Consider shortening to under 4000 characters'
    });
  }

  // 3. Validate required elements for agent category
  const requiredElements = getRequiredPromptElements(agentCategory);
  for (const element of requiredElements) {
    if (!prompt.toLowerCase().includes(element.toLowerCase())) {
      results.push({
        level: 'warning',
        field: 'system_prompt',
        message: `Missing recommended element: "${element}"`,
        suggestion: `Consider including "${element}" for better extraction accuracy`
      });
    }
  }

  return results;
};

const getRequiredPromptElements = (category: string): string[] => {
  const elements = {
    invoice: ['total amount', 'vendor', 'date', 'JSON'],
    contract: ['parties', 'terms', 'dates', 'JSON'],
    receipt: ['merchant', 'amount', 'date', 'JSON'],
    general: ['structured data', 'JSON']
  };
  return elements[category] || elements.general;
};
```

**Schema Customization with Validation:**
```typescript
const validateSchemaCustomization = async (
  originalSchema: JSONSchema,
  newSchema: JSONSchema,
  agentCategory: string
): Promise<ValidationResult[]> => {
  const results: ValidationResult[] = [];

  // 1. Check schema validity
  try {
    await validateJSONSchema(newSchema);
  } catch (error) {
    results.push({
      level: 'error',
      field: 'output_schema',
      message: 'Invalid JSON schema format',
      suggestion: error.message
    });
    return results; // Can't continue with invalid schema
  }

  // 2. Check backward compatibility
  const compatibilityIssues = checkSchemaCompatibility(originalSchema, newSchema);
  results.push(...compatibilityIssues);

  // 3. Validate required fields for category
  const requiredFields = getRequiredFieldsForCategory(agentCategory);
  for (const field of requiredFields) {
    if (!newSchema.properties?.[field]) {
      results.push({
        level: 'warning',
        field: 'output_schema',
        message: `Missing recommended field: "${field}"`,
        suggestion: `Consider adding "${field}" for consistency with ${agentCategory} documents`
      });
    }
  }

  // 4. Check field data types
  const typeIssues = validateFieldTypes(newSchema);
  results.push(...typeIssues);

  return results;
};

const checkSchemaCompatibility = (original: JSONSchema, updated: JSONSchema): ValidationResult[] => {
  const issues: ValidationResult[] = [];

  // Check if required fields were removed
  const originalRequired = original.required || [];
  const updatedRequired = updated.required || [];

  for (const field of originalRequired) {
    if (!updatedRequired.includes(field)) {
      issues.push({
        level: 'warning',
        field: 'output_schema',
        message: `Required field "${field}" was removed`,
        suggestion: 'This may break existing integrations'
      });
    }
  }

  // Check if field types changed incompatibly
  for (const [fieldName, originalProp] of Object.entries(original.properties || {})) {
    const updatedProp = updated.properties?.[fieldName];
    if (updatedProp && originalProp.type !== updatedProp.type) {
      issues.push({
        level: 'error',
        field: 'output_schema',
        message: `Field "${fieldName}" type changed from ${originalProp.type} to ${updatedProp.type}`,
        suggestion: 'Type changes may break existing code'
      });
    }
  }

  return issues;
};
```

**Preview and Testing System:**
```typescript
const previewCustomization = async (
  agent: Agent,
  customization: CustomizeAgentRequest
): Promise<PreviewResult[]> => {
  // Create temporary agent with customizations
  const previewAgent = {
    ...agent,
    system_prompt: customization.system_prompt || agent.system_prompt,
    output_schema: customization.output_schema || agent.output_schema,
    processing_config: { ...agent.processing_config, ...customization.processing_config }
  };

  // Get test documents for agent category
  const testDocuments = await getTestDocuments(agent.category, 3); // 3 test docs

  const results: PreviewResult[] = [];

  for (const testDoc of testDocuments) {
    try {
      const startTime = performance.now();
      const result = await processDocumentWithAgent(testDoc.content, previewAgent);
      const processingTime = performance.now() - startTime;

      // Validate result against schema
      const schemaValid = await validateAgainstSchema(result, previewAgent.output_schema);

      results.push({
        document_id: testDoc.id,
        document_type: testDoc.type,
        success: true,
        processing_time_ms: processingTime,
        schema_valid: schemaValid,
        extracted_data: result,
        confidence_score: calculateConfidenceScore(result),
        comparison_with_original: testDoc.expected_output
          ? compareResults(result, testDoc.expected_output)
          : null
      });

    } catch (error) {
      results.push({
        document_id: testDoc.id,
        document_type: testDoc.type,
        success: false,
        error: error.message,
        processing_time_ms: 0,
        schema_valid: false
      });
    }
  }

  return results;
};
```

**Version Control and Rollback:**
```typescript
const createAgentVersion = async (
  agentId: string,
  versionName: string,
  changes: CustomizeAgentRequest
): Promise<string> => {
  const versionId = generateVersionId();

  // Save current state as a version
  const currentAgent = await getAgent(agentId);

  await supabase.from('agent_versions').insert({
    id: versionId,
    agent_id: agentId,
    version_name: versionName,
    system_prompt: currentAgent.system_prompt,
    output_schema: currentAgent.output_schema,
    processing_config: currentAgent.processing_config,
    changes_summary: summarizeChanges(changes),
    created_at: new Date()
  });

  return versionId;
};

const rollbackToVersion = async (agentId: string, versionId: string, customerId: string): Promise<Agent> => {
  // Validate ownership
  await validateAgentOwnership(agentId, customerId);

  // Get version data
  const { data: version } = await supabase
    .from('agent_versions')
    .select('*')
    .eq('id', versionId)
    .eq('agent_id', agentId)
    .single();

  if (!version) {
    throw new Error('Version not found');
  }

  // Apply version data to current agent
  const { data: updatedAgent } = await supabase
    .from('agents')
    .update({
      system_prompt: version.system_prompt,
      output_schema: version.output_schema,
      processing_config: version.processing_config,
      updated_at: new Date()
    })
    .eq('id', agentId)
    .select()
    .single();

  // Log rollback operation
  await logAgentRollback(agentId, versionId, customerId);

  return updatedAgent;
};
```

**Collaborative Editing:**
```typescript
const trackAgentChanges = async (
  agentId: string,
  customerId: string,
  changes: Partial<Agent>
): Promise<void> => {
  await supabase.from('agent_change_log').insert({
    agent_id: agentId,
    customer_id: customerId,
    changes: changes,
    change_type: 'customization',
    timestamp: new Date()
  });
};

const getAgentChangeHistory = async (agentId: string): Promise<ChangeHistoryEntry[]> => {
  const { data } = await supabase
    .from('agent_change_log')
    .select('*')
    .eq('agent_id', agentId)
    .order('timestamp', { ascending: false });

  return data || [];
};
```

## 🧪 Testing Requirements
- [ ] Prompt customization prevents injection attacks
- [ ] Schema validation catches breaking changes
- [ ] Preview mode accurately tests customizations
- [ ] Version control properly tracks changes
- [ ] Rollback functionality restores previous states
- [ ] Collaborative editing tracks all modifications

## 🔗 Dependencies
**Depends on**: #[3.3 Agent Cloning System]
**Blocks**: #[3.5 JSON Schema Validation], #[3.6 Agent Performance Tracking]

## 📊 Metadata
- **Epic**: Epic 3
- **Effort**: L (Large: 3-5 days)
- **Priority**: High

---

### 💡 Development Notes
This customization system provides the flexibility foundation that enables senior developers to implement any customer-specific extraction requirement while maintaining safety, validation, and version control.