# GitHub Projects Setup Guide

*Last Updated: September 21, 2025*  
*Version: 1.0*  
*Author: Winston - System Architect*

## 🎯 Overview

This guide walks through setting up the complete GitHub Projects workflow for the IDP Platform, including Milestones, Issues, Project Board, and automation. This setup enables seamless integration between Claude AI development and GitHub project management.

**Project URL**: https://github.com/orgs/GPT-Integrators/projects/3/views/1

---

## 📋 Quick Setup Checklist

- [ ] Create 5 Epic Milestones
- [ ] Set up Project Board columns
- [ ] Create Issue templates
- [ ] Configure GitHub Actions
- [ ] Create initial Stories/Issues
- [ ] Test automation workflow
- [ ] Train team on developer workflow

---

## 🏗️ Step 1: Epic Milestones Setup

### Create Milestones (Repository → Issues → Milestones → New)

Create these 5 milestones exactly as shown:

```
📊 Epic 1: Foundation & Basic Admin
   Description: Establish project setup, Supabase configuration, authentication system, basic API key management, and health check endpoint with minimal admin functions for testing.
   Due Date: 2 weeks from project start
   
📊 Epic 2: Document Processing & Usage Tracking  
   Description: Implement core document processing with AI integration, fallback systems, and essential usage tracking with credit deduction to enable operational visibility.
   Due Date: 4 weeks from project start
   
📊 Epic 3: Agent Management System
   Description: Create versioned default agents, enable agent cloning and customization, with JSON schema validation and storage for customer differentiation.
   Due Date: 6 weeks from project start
   
📊 Epic 4: Advanced Admin & Customer Management
   Description: Complete enterprise admin capabilities including comprehensive customer management, advanced rate limiting, and credit management workflows.
   Due Date: 8 weeks from project start
   
📊 Epic 5: Security & Monitoring
   Description: Deploy prompt injection protection, comprehensive audit logging, error handling, and monitoring systems for production readiness.
   Due Date: 10 weeks from project start
```

### Milestone Configuration
- **State**: Open
- **Due Dates**: Set realistic dates based on team capacity
- **Description**: Include Epic goals from PRD

---

## 📋 Step 2: Project Board Configuration

### Board Setup

Navigate to: https://github.com/orgs/GPT-Integrators/projects/3/settings

**Column Configuration:**
```
1. 📋 Backlog
   - Description: Stories ready for development
   - Color: Gray
   
2. 🔄 In Progress  
   - Description: Currently being worked on
   - Color: Blue
   - Limit: 3 items (prevents overload)
   
3. 🔍 PR Review
   - Description: Code review and testing
   - Color: Orange
   
4. ✅ Done
   - Description: Completed and merged
   - Color: Green
```

### Board Views

**Create these views for different perspectives:**

1. **Epic Overview** (Group by Milestone)
   - Shows progress by Epic
   - Useful for stakeholders

2. **Developer View** (Filter by Assignee)
   - Shows individual developer workload
   - Useful for daily standup

3. **Sprint View** (Filter by Labels)
   - Shows current iteration work
   - Useful for sprint planning

---

## 📝 Step 3: Issue Templates

### Create Issue Template

Navigate to: Repository → Settings → Features → Issues → Set up templates

**Template 1: User Story**

```markdown
---
name: User Story
about: Standard user story template for development
title: '[Epic X] Story Title'
labels: story, epic-X
assignees: ''
---

## 📋 Story: [Epic X] - [Story Title]

### 🎯 User Story
As a **[user type]**,  
I want **[functionality]**,  
So that **[business value]**.

### 📋 Requirements
[Detailed requirements from PRD]

### ✅ Acceptance Criteria
- [ ] Criterion 1: Specific, testable requirement
- [ ] Criterion 2: Specific, testable requirement  
- [ ] Criterion 3: Specific, testable requirement
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] Code review completed

### 🏗️ Implementation Notes
[Technical guidance from architecture docs]

**Key Files to Modify/Create:**
- Database: `supabase/migrations/[timestamp]_[description].sql`
- Functions: `supabase/functions/[function-name]/index.ts`
- Types: `types/database.types.ts`
- Tests: `tests/[unit|integration]/[test-name].test.ts`

### 🧪 Testing Requirements
- [ ] Unit tests written and passing
- [ ] Integration tests pass
- [ ] Manual testing completed
- [ ] Performance requirements met
- [ ] Security requirements validated

### 🔗 Dependencies
**Depends on**: #[issue-number]
**Blocks**: #[issue-number]

### 📊 Metadata
- **Epic**: Epic X
- **Effort**: [S/M/L] (Small: <1 day, Medium: 1-3 days, Large: 3-5 days)
- **Priority**: [High/Medium/Low]

---

### 💡 Development Notes
[Space for developer notes, progress updates, and discoveries]
```

### Issue Labels

Create these labels for organization:

**Epic Labels:**
- `epic-1` (Blue) - Foundation & Basic Admin
- `epic-2` (Green) - Document Processing & Usage Tracking
- `epic-3` (Purple) - Agent Management System
- `epic-4` (Orange) - Advanced Admin & Customer Management
- `epic-5` (Red) - Security & Monitoring

**Type Labels:**
- `story` (Light Blue) - User story
- `bug` (Red) - Bug report
- `enhancement` (Green) - Feature enhancement
- `documentation` (Yellow) - Documentation update
- `question` (Pink) - Question or discussion

**Priority Labels:**
- `priority-high` (Dark Red) - Must complete this sprint
- `priority-medium` (Orange) - Should complete this sprint
- `priority-low` (Yellow) - Nice to have

**Effort Labels:**
- `effort-S` (Light Green) - Small effort (<1 day)
- `effort-M` (Yellow) - Medium effort (1-3 days)
- `effort-L` (Red) - Large effort (3-5 days)

---

## 🤖 Step 4: GitHub Actions Automation

### Create Workflow File

Create `.github/workflows/project-automation.yml`:

```yaml
name: Project Management Automation

on:
  issues:
    types: [assigned, unassigned, closed, reopened]
  pull_request:
    types: [opened, closed, ready_for_review, converted_to_draft]

jobs:
  move_assigned_issue_to_in_progress:
    if: github.event_name == 'issues' && github.event.action == 'assigned'
    runs-on: ubuntu-latest
    steps:
      - name: Move assigned issue to In Progress
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: In Progress
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  move_pr_to_review:
    if: github.event_name == 'pull_request' && (github.event.action == 'opened' || github.event.action == 'ready_for_review')
    runs-on: ubuntu-latest
    steps:
      - name: Get linked issue
        id: get_issue
        uses: actions/github-script@v6
        with:
          script: |
            const pr = context.payload.pull_request;
            const body = pr.body || '';
            
            // Look for "Closes #123" or "Fixes #123" patterns
            const issueMatch = body.match(/(?:closes|fixes|resolves)\s+#(\d+)/i);
            if (issueMatch) {
              const issueNumber = issueMatch[1];
              return { issue_number: issueNumber };
            }
            return null;

      - name: Move linked issue to PR Review
        if: steps.get_issue.outputs.result != 'null'
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: PR Review
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  move_merged_pr_to_done:
    if: github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    steps:
      - name: Move merged PR issue to Done
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: Done
          repo-token: ${{ secrets.GITHUB_TOKEN }}

  move_closed_pr_back_to_backlog:
    if: github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == false
    runs-on: ubuntu-latest
    steps:
      - name: Move closed PR issue back to Backlog
        uses: alex-page/github-project-automation-plus@v0.8.3
        with:
          project: IDP Platform
          column: Backlog
          repo-token: ${{ secrets.GITHUB_TOKEN }}
```

### Required Repository Secrets

No additional secrets needed - uses default `GITHUB_TOKEN`.

### Test Automation

1. Create a test issue
2. Assign to yourself → Should move to "In Progress"
3. Create PR that references the issue → Should move to "PR Review"
4. Merge PR → Should move to "Done"

---

## 📚 Step 5: Initial Stories Creation

### Epic 1 Stories (Foundation & Basic Admin)

Use the Issue template to create these stories:

**Story 1.1: Database Schema Foundation**
```
Title: [Epic 1] Database Schema Foundation
Milestone: Epic 1: Foundation & Basic Admin
Labels: story, epic-1, priority-high, effort-L

As a Platform Administrator,
I want a comprehensive database schema with proper security,
So that all platform data is structured and protected.

Acceptance Criteria:
- [ ] All core tables created (customers, api_keys, audit_logs)
- [ ] Row-Level Security policies implemented
- [ ] Database indexes created for performance
- [ ] Comprehensive table/field comments added
- [ ] TypeScript types generated successfully
```

**Story 1.2: API Key Authentication System**
```
Title: [Epic 1] API Key Authentication System
Milestone: Epic 1: Foundation & Basic Admin
Labels: story, epic-1, priority-high, effort-M

As a Platform Administrator,
I want secure authentication for admin access and API key validation,
So that the platform is protected from unauthorized access.

Acceptance Criteria:
- [ ] SHA-256 API key hashing implemented
- [ ] API key validation Edge Function created
- [ ] Rate limiting middleware operational
- [ ] Authentication error handling with proper HTTP codes
- [ ] Audit logging for all authentication events
```

**Story 1.3: Health Check & Monitoring**
```
Title: [Epic 1] Health Check & Monitoring
Milestone: Epic 1: Foundation & Basic Admin
Labels: story, epic-1, priority-medium, effort-S

As a Platform Administrator,
I want comprehensive health checks and basic monitoring,
So that I can validate system functionality and detect issues.

Acceptance Criteria:
- [ ] Health endpoint returns database status
- [ ] AI service connectivity validation
- [ ] Performance metrics collection implemented
- [ ] Correlation ID system for request tracing
- [ ] Basic logging framework operational
```

**Story 1.4: Basic API Key Management**
```
Title: [Epic 1] Basic API Key Management
Milestone: Epic 1: Foundation & Basic Admin
Labels: story, epic-1, priority-high, effort-M

As a Platform Administrator,
I want to generate and manage API key pairs for testing,
So that I can validate the complete customer workflow.

Acceptance Criteria:
- [ ] Generate test keys (skt_) with credit allocation
- [ ] Generate production keys (skp_) with separate credits
- [ ] Key validation endpoint for customer testing
- [ ] 7-day retention policy for test keys
- [ ] Admin endpoint to view key status and usage
```

**Story 1.5: Project Infrastructure Setup**
```
Title: [Epic 1] Project Infrastructure Setup
Milestone: Epic 1: Foundation & Basic Admin
Labels: story, epic-1, priority-high, effort-M

As a Platform Administrator,
I want a properly configured Supabase project with Edge Functions,
So that I have a scalable foundation for the platform.

Acceptance Criteria:
- [ ] Supabase project configured with PostgreSQL
- [ ] Edge Functions runtime environment setup
- [ ] Environment variables configured
- [ ] Basic CI/CD pipeline operational
- [ ] Database connection pooling configured
```

### Repeat for Epic 2-5

Create similar detailed stories for each epic using the PRD as reference.

---

## 🔧 Step 6: Repository Configuration

### Branch Protection Rules

Navigate to: Repository → Settings → Branches → Add rule

**Branch name pattern**: `main`

**Protection rules:**
- [x] Require a pull request before merging
- [x] Require approvals: 1
- [x] Dismiss stale PR reviews when new commits are pushed
- [x] Require status checks to pass before merging
  - [x] Require branches to be up to date before merging
- [x] Require linear history
- [x] Include administrators

### Repository Settings

**General Settings:**
- [x] Allow merge commits
- [x] Allow squash merging  
- [x] Allow rebase merging
- [x] Automatically delete head branches

**Issue Settings:**
- [x] Issues enabled
- [x] Allow projects
- [x] Use issue templates

---

## 🧪 Step 7: Testing the Workflow

### End-to-End Test

1. **Create Test Issue**
   ```
   Title: [Epic 1] Test Story - Health Check
   Use the story template
   Assign to Epic 1 milestone
   Add appropriate labels
   ```

2. **Assign Issue**
   - Assign to yourself
   - Verify it moves to "In Progress" column

3. **Create Branch**
   ```bash
   git checkout -b story/epic-1-test-health-check
   ```

4. **Make Changes**
   ```bash
   # Create a simple test file
   echo "console.log('Health check test');" > test-file.js
   git add test-file.js
   git commit -m "Add test health check functionality"
   git push -u origin story/epic-1-test-health-check
   ```

5. **Create PR**
   ```markdown
   Title: [Epic 1] Implement test health check
   
   Body:
   Closes #[issue-number]
   
   This PR implements basic health check functionality for testing the workflow.
   
   - Added test health check endpoint
   - Verified automation works correctly
   ```

6. **Verify Automation**
   - Issue should move to "PR Review"
   - Merge PR
   - Issue should move to "Done"

### Troubleshooting

**If automation doesn't work:**

1. **Check GitHub Actions**
   - Go to Actions tab in repository
   - Look for failed workflows
   - Check logs for errors

2. **Verify Project Integration**
   - Ensure project is linked to repository
   - Check column names match exactly
   - Verify issue is added to project board

3. **Check Permissions**
   - Ensure `GITHUB_TOKEN` has proper permissions
   - Verify organization settings allow automation

---

## 📊 Step 8: Initial Population

### Bulk Issue Creation

**Option A: Manual Creation (Recommended for first setup)**
1. Use issue templates for consistency
2. Create 4-6 stories per epic
3. Link to appropriate milestones
4. Add proper labels and effort estimates

**Option B: Scripted Creation (For future iterations)**

Create a script to generate issues from story definitions:

```javascript
// scripts/create-issues.js
const { Octokit } = require("@octokit/rest");

const octokit = new Octokit({
  auth: process.env.GITHUB_TOKEN,
});

const stories = [
  {
    title: "[Epic 1] Database Schema Foundation",
    milestone: 1, // Epic 1 milestone number
    labels: ["story", "epic-1", "priority-high", "effort-L"],
    body: "Story template content here..."
  },
  // Add more stories...
];

async function createIssues() {
  for (const story of stories) {
    await octokit.rest.issues.create({
      owner: "GPT-Integrators",
      repo: "IDP-Platform",
      title: story.title,
      body: story.body,
      milestone: story.milestone,
      labels: story.labels,
    });
  }
}

createIssues();
```

---

## 🎯 Success Criteria

### Setup Completion Checklist

- [ ] **Milestones Created**: 5 Epic milestones with proper dates
- [ ] **Project Board**: 4 columns with proper configuration
- [ ] **Issue Templates**: Story template with all required fields
- [ ] **Labels**: All Epic, priority, and effort labels created
- [ ] **Automation**: GitHub Actions workflow tested and working
- [ ] **Initial Stories**: At least Epic 1 stories created and ready
- [ ] **Branch Protection**: Main branch protected with PR requirements
- [ ] **Team Training**: Developer workflow document reviewed

### Validation Tests

- [ ] **Issue Assignment**: Assigning moves to "In Progress"
- [ ] **PR Creation**: Creating PR moves to "PR Review"  
- [ ] **PR Merge**: Merging moves to "Done"
- [ ] **PR Rejection**: Closing without merge moves back to "Backlog"
- [ ] **Branch Creation**: Manual branch creation follows naming convention
- [ ] **Claude Integration**: Issue URL parsing works correctly

---

## 🚀 Next Steps

1. **Train Development Team**: Review [Developer Workflow Guide](./developer-workflow.md)
2. **Epic 1 Planning**: Prioritize and assign Epic 1 stories
3. **Sprint Kickoff**: Begin development with first story
4. **Monitor Metrics**: Track velocity and identify bottlenecks
5. **Iterate Process**: Adjust workflow based on team feedback

---

## 📞 Support

**Issues with setup?**
- Check GitHub Actions logs for automation issues
- Verify project permissions and organization settings
- Review branch protection rules if PRs are blocked
- Contact project administrator for access issues

**Process Improvements?**
- Suggest changes via GitHub issue with `enhancement` label
- Document pain points in retrospective meetings
- Test changes in separate project first

---

*This setup guide provides the foundation for efficient GitHub project management integrated with Claude AI development workflows. Follow the developer workflow guide for daily usage patterns.*

---

*Last Updated: September 21, 2025*  
*Setup Version: 1.0*  
*Compatible with: GitHub Projects (Beta), GitHub Actions*