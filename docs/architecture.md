# IDP Platform System Architecture

*Last Updated: September 21, 2025*  
*Version: 4.0*  
*Author: Winston - System Architect*

## 🎯 Executive Summary

This document defines the complete technical architecture for the IDP Platform - an API-first document processing service that transforms unstructured documents into structured JSON data. The architecture is designed to achieve 60%+ profit margins while reducing customer costs from $0.10-$1.00 per document, maintaining 99.5% uptime with <5 second processing times.

**Architecture Philosophy**: Build a pure API backend service using Supabase Edge Functions with intelligent multi-model AI fallbacks, achieving enterprise-grade reliability while maintaining startup-level cost efficiency.

---

## 🏗️ High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        DEV[Developer Applications]
        API[Direct API Calls]
        SDK[Custom SDKs]
        WEBHOOK[Webhook Consumers]
    end

    subgraph "API Gateway Layer"
        KONG[Kong Gateway<br/>Rate Limiting<br/>Auth Validation]
    end

    subgraph "Supabase Edge Functions Layer"
        AUTH_EF[Auth Validation<br/>API Key Management]
        EXTRACT_EF[Document Processing<br/>Queue Management]
        AGENT_EF[Agent Management<br/>Cloning & Custom]
        ADMIN_EF[Admin Operations<br/>Customer Management]
        HEALTH_EF[Health Check<br/>System Status]
    end

    subgraph "AI Processing Layer"
        CIRCUIT[Circuit Breaker<br/>Fallback Router]
        OPENAI[OpenAI GPT-4]
        CLAUDE[Claude 3.5 Sonnet]
        LLAMA[LlamaParse]
    end

    subgraph "Data Layer"
        DB[(PostgreSQL 17<br/>Row-Level Security)]
        VECTOR[(pgvector<br/>Document Similarity)]
        QUEUE[(pg_cron<br/>Job Processing)]
        AUDIT[(Audit Logs<br/>Compliance)]
    end

    subgraph "Storage Layer"
        TEMP[Temporary Files<br/>7-day Retention]
        RESULTS[Processing Results<br/>Configurable TTL]
    end

    DEV --> KONG
    API --> KONG
    SDK --> KONG
    WEBHOOK <--> KONG

    KONG --> AUTH_EF
    KONG --> EXTRACT_EF
    KONG --> AGENT_EF
    KONG --> ADMIN_EF
    KONG --> HEALTH_EF

    EXTRACT_EF --> CIRCUIT
    CIRCUIT --> OPENAI
    CIRCUIT --> CLAUDE
    CIRCUIT --> LLAMA

    AUTH_EF --> DB
    EXTRACT_EF --> DB
    EXTRACT_EF --> VECTOR
    EXTRACT_EF --> QUEUE
    AGENT_EF --> DB
    ADMIN_EF --> DB
    ADMIN_EF --> AUDIT

    EXTRACT_EF --> TEMP
    EXTRACT_EF --> RESULTS
```

---

## 🔧 Core Components Architecture

### 1. API Gateway & Authentication Layer

#### Kong Gateway Configuration
```yaml
# Kong configuration for API management
services:
  - name: idp-platform-api
    url: https://oxviewglpxujnzrzmopx.supabase.co/functions/v1
    
plugins:
  - name: rate-limiting
    config:
      minute: 100
      hour: 1000
      day: 10000
      policy: redis
      
  - name: key-auth
    config:
      key_names: ["Authorization"]
      key_in_header: true
      
  - name: request-size-limiting
    config:
      allowed_payload_size: 52428800  # 50MB max
```

#### API Key Authentication Architecture
```typescript
interface ApiKeyValidation {
  customerId: string;
  keyType: 'skt' | 'skp';  // test vs production
  credits: number;
  rateLimit: RateLimit;
  expires: Date | null;
  scopes: string[];
  isRevoked: boolean;
}

interface RateLimit {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
  concurrentRequests: number;
}
```

### 2. Edge Functions Microservices Architecture

#### Function Structure & Responsibilities

**Authentication Function** (`auth-validation`)
- API key validation and hashing
- Customer context resolution
- Rate limiting enforcement
- Security audit logging

**Document Processing Function** (`document-extract`)
- File validation and preprocessing
- AI model orchestration
- Result caching and storage
- Usage tracking and billing

**Agent Management Function** (`agent-management`)
- Default agent catalog
- Customer agent cloning
- Schema validation
- Version control

**Admin Operations Function** (`admin-ops`)
- Customer lifecycle management
- Credit allocation and billing
- System monitoring and alerts
- Compliance reporting

**Health Check Function** (`health-check`)
- System status aggregation
- AI service connectivity
- Database health monitoring
- Performance metrics

#### Inter-Service Communication Pattern
```typescript
// Service-to-service communication via Supabase channels
interface ServiceMessage {
  type: 'document_processed' | 'agent_updated' | 'credit_depleted';
  source: string;
  target: string;
  payload: any;
  correlationId: string;
  timestamp: Date;
}

// Publish-subscribe pattern for loose coupling
const publishEvent = async (message: ServiceMessage) => {
  await supabase.channel('service-events').send({
    type: 'broadcast',
    event: message.type,
    payload: message
  });
};
```

---

## 🤖 Multi-Model AI Fallback System

### Circuit Breaker Implementation

```typescript
interface CircuitState {
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
  failureCount: number;
  lastFailureTime: Date | null;
  nextRetryTime: Date | null;
  successCount: number;
}

class AIServiceCircuitBreaker {
  private circuits: Map<string, CircuitState> = new Map();
  private readonly failureThreshold = 5;
  private readonly recoveryTimeout = 30000; // 30 seconds
  private readonly halfOpenMaxRequests = 3;

  async processWithFallback(document: Document, agent: Agent): Promise<ExtractionResult> {
    const modelTiers = ['openai', 'claude', 'llamaparse'];
    
    for (const model of modelTiers) {
      if (this.canExecute(model)) {
        try {
          const result = await this.executeModel(model, document, agent);
          this.recordSuccess(model);
          return result;
        } catch (error) {
          this.recordFailure(model);
          console.warn(`${model} failed, trying next: ${error.message}`);
        }
      }
    }
    
    throw new Error('All AI services unavailable');
  }

  private canExecute(service: string): boolean {
    const circuit = this.getCircuit(service);
    
    switch (circuit.state) {
      case 'CLOSED':
        return true;
      case 'OPEN':
        return Date.now() > circuit.nextRetryTime!.getTime();
      case 'HALF_OPEN':
        return circuit.successCount < this.halfOpenMaxRequests;
    }
  }

  private recordSuccess(service: string): void {
    const circuit = this.getCircuit(service);
    circuit.failureCount = 0;
    circuit.lastFailureTime = null;
    
    if (circuit.state === 'HALF_OPEN') {
      circuit.successCount++;
      if (circuit.successCount >= this.halfOpenMaxRequests) {
        circuit.state = 'CLOSED';
        circuit.successCount = 0;
      }
    }
  }

  private recordFailure(service: string): void {
    const circuit = this.getCircuit(service);
    circuit.failureCount++;
    circuit.lastFailureTime = new Date();
    
    if (circuit.failureCount >= this.failureThreshold) {
      circuit.state = 'OPEN';
      circuit.nextRetryTime = new Date(Date.now() + this.recoveryTimeout);
    }
  }
}
```

### AI Model Selection & Cost Optimization

```typescript
interface ModelConfig {
  name: string;
  costPerToken: number;
  maxTokens: number;
  capabilities: string[];
  avgProcessingTime: number;
  reliability: number; // 0-1 score
}

const MODEL_CONFIGS: ModelConfig[] = [
  {
    name: 'gpt-4o-mini',
    costPerToken: 0.00000015, // $0.15/1M tokens
    maxTokens: 128000,
    capabilities: ['text', 'vision', 'structured_output'],
    avgProcessingTime: 2000,
    reliability: 0.98
  },
  {
    name: 'claude-3.5-sonnet',
    costPerToken: 0.000003, // $3/1M tokens
    maxTokens: 200000,
    capabilities: ['text', 'vision', 'complex_reasoning'],
    avgProcessingTime: 3000,
    reliability: 0.99
  },
  {
    name: 'llamaparse',
    costPerToken: 0.000001, // $1/1M tokens
    maxTokens: 50000,
    capabilities: ['pdf_parsing', 'table_extraction'],
    avgProcessingTime: 5000,
    reliability: 0.95
  }
];

class ModelSelector {
  selectOptimalModel(document: DocumentMetadata, requirements: ProcessingRequirements): string {
    // Analyze document complexity
    const complexity = this.analyzeComplexity(document);
    
    // Filter models by capability requirements
    const capable = MODEL_CONFIGS.filter(model =>
      requirements.capabilities.every(cap => model.capabilities.includes(cap))
    );
    
    // Select based on cost-performance optimization
    const scored = capable.map(model => ({
      model,
      score: this.calculateScore(model, complexity, requirements)
    }));
    
    return scored.sort((a, b) => b.score - a.score)[0].model.name;
  }

  private calculateScore(model: ModelConfig, complexity: number, requirements: ProcessingRequirements): number {
    // Weighted scoring: cost efficiency (40%) + reliability (30%) + speed (20%) + capability match (10%)
    const costScore = 1 - (model.costPerToken / 0.000003); // Normalize to Claude cost
    const reliabilityScore = model.reliability;
    const speedScore = 1 - (model.avgProcessingTime / 5000); // Normalize to max time
    const capabilityScore = requirements.capabilities.length / model.capabilities.length;
    
    return (costScore * 0.4) + (reliabilityScore * 0.3) + (speedScore * 0.2) + (capabilityScore * 0.1);
  }
}
```

---

## 💾 Database Architecture & Schema Design

### Core Tables with Row-Level Security

```sql
-- Core customer and authentication tables
CREATE TABLE customers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_name TEXT NOT NULL,
  contact_email TEXT UNIQUE NOT NULL,
  status customer_status DEFAULT 'active',
  tier_settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE customers IS 'Customer accounts with company information and service tier configuration';

-- API key management with SHA-256 hashing
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  key_type TEXT CHECK (key_type IN ('skt', 'skp')) NOT NULL,
  key_hash TEXT UNIQUE NOT NULL, -- SHA-256 hash of actual key
  key_prefix TEXT NOT NULL, -- First 8 chars for identification
  credits INTEGER DEFAULT 0,
  rate_limits JSONB DEFAULT '{}',
  scopes TEXT[] DEFAULT ARRAY['extract', 'agents:read'],
  revoked BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMPTZ,
  last_used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE api_keys IS 'Customer API keys with SHA-256 hashing and granular access control';

-- Agent catalog and customization
CREATE TABLE agents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id) ON DELETE CASCADE, -- NULL for default agents
  agent_id TEXT UNIQUE NOT NULL, -- Human-readable ID
  name TEXT NOT NULL,
  category TEXT CHECK (category IN ('invoice', 'contract', 'receipt', 'insurance', 'general')),
  system_prompt TEXT NOT NULL,
  output_schema JSONB NOT NULL,
  is_default BOOLEAN DEFAULT FALSE,
  parent_agent_id UUID REFERENCES agents(id), -- For cloned agents
  version INTEGER DEFAULT 1,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE agents IS 'AI extraction agents with customizable prompts and output schemas';

-- Document processing and results
CREATE TABLE documents (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id UUID NOT NULL REFERENCES api_keys(id),
  agent_id UUID NOT NULL REFERENCES agents(id),
  job_id TEXT UNIQUE, -- For async processing tracking
  file_name TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_hash TEXT NOT NULL, -- SHA-256 for deduplication
  extraction_result JSONB,
  processing_status TEXT DEFAULT 'pending' CHECK (
    processing_status IN ('pending', 'processing', 'completed', 'failed', 'expired')
  ),
  model_used TEXT,
  processing_time_ms INTEGER,
  confidence_score DECIMAL(3,2), -- 0.00 to 1.00
  error_message TEXT,
  expires_at TIMESTAMPTZ, -- Based on key type (7 days for test, configurable for prod)
  processed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE documents IS 'Document processing records with results and metadata';

-- Usage tracking for billing and analytics
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  api_key_id UUID NOT NULL REFERENCES api_keys(id),
  document_id UUID REFERENCES documents(id),
  operation_type TEXT NOT NULL, -- 'extract', 'agent_clone', 'agent_update'
  model_used TEXT,
  input_tokens INTEGER DEFAULT 0,
  output_tokens INTEGER DEFAULT 0,
  model_cost DECIMAL(10,6) NOT NULL, -- What we pay AI provider
  customer_price DECIMAL(10,6) NOT NULL, -- What customer pays us
  credits_used INTEGER NOT NULL,
  processing_time_ms INTEGER,
  success BOOLEAN NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE usage_logs IS 'Detailed usage tracking for billing and cost analysis';

-- Comprehensive audit logging
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID REFERENCES customers(id),
  api_key_id UUID REFERENCES api_keys(id),
  action TEXT NOT NULL,
  resource_type TEXT NOT NULL,
  resource_id TEXT,
  status TEXT CHECK (status IN ('success', 'failure', 'warning')) NOT NULL,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  correlation_id TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

COMMENT ON TABLE audit_logs IS 'Security and compliance audit trail for all platform operations';
```

### High-Performance Indexes

```sql
-- Critical indexes for API performance
CREATE INDEX idx_api_keys_hash ON api_keys USING hash(key_hash);
CREATE INDEX idx_api_keys_customer_active ON api_keys(customer_id) WHERE NOT revoked;
CREATE INDEX idx_api_keys_prefix ON api_keys(key_prefix);

-- Document processing indexes
CREATE INDEX idx_documents_customer_status ON documents(customer_id, processing_status);
CREATE INDEX idx_documents_hash ON documents(file_hash);
CREATE INDEX idx_documents_expires ON documents(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX idx_documents_job_id ON documents(job_id) WHERE job_id IS NOT NULL;

-- Agent management indexes
CREATE INDEX idx_agents_customer_category ON agents(customer_id, category) WHERE active;
CREATE INDEX idx_agents_default ON agents(category) WHERE is_default AND active;
CREATE INDEX idx_agents_agent_id ON agents(agent_id) WHERE active;

-- Usage analytics indexes
CREATE INDEX idx_usage_logs_customer_date ON usage_logs(customer_id, created_at DESC);
CREATE INDEX idx_usage_logs_key_date ON usage_logs(api_key_id, created_at DESC);
CREATE INDEX idx_usage_logs_model_date ON usage_logs(model_used, created_at DESC);

-- Audit and compliance indexes
CREATE INDEX idx_audit_logs_customer_date ON audit_logs(customer_id, created_at DESC);
CREATE INDEX idx_audit_logs_correlation ON audit_logs(correlation_id);
CREATE INDEX idx_audit_logs_action_date ON audit_logs(action, created_at DESC);
```

### Row-Level Security Policies

```sql
-- Enable RLS on all customer-facing tables
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- API keys: customers see only their own keys
CREATE POLICY "api_keys_customer_isolation" ON api_keys
  FOR ALL USING (
    customer_id = current_setting('app.customer_id', true)::uuid
  );

-- Agents: customers see default agents + their own custom agents
CREATE POLICY "agents_customer_access" ON agents
  FOR SELECT USING (
    is_default = true OR 
    customer_id = current_setting('app.customer_id', true)::uuid
  );

-- Documents: strict customer isolation
CREATE POLICY "documents_customer_isolation" ON documents
  FOR ALL USING (
    customer_id = current_setting('app.customer_id', true)::uuid
  );

-- Usage logs: customers see only their own usage
CREATE POLICY "usage_logs_customer_isolation" ON usage_logs
  FOR SELECT USING (
    customer_id = current_setting('app.customer_id', true)::uuid
  );

-- Admin full access policy (for service role)
CREATE POLICY "admin_full_access" ON api_keys
  FOR ALL USING (current_setting('role') = 'service_role');

CREATE POLICY "admin_full_access" ON agents
  FOR ALL USING (current_setting('role') = 'service_role');

CREATE POLICY "admin_full_access" ON documents
  FOR ALL USING (current_setting('role') = 'service_role');

CREATE POLICY "admin_full_access" ON usage_logs
  FOR ALL USING (current_setting('role') = 'service_role');

CREATE POLICY "admin_full_access" ON audit_logs
  FOR ALL USING (current_setting('role') = 'service_role');
```

### Vector Similarity for Document Deduplication

```sql
-- Document embeddings for similarity detection
CREATE TABLE document_embeddings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  embedding vector(1536), -- OpenAI embedding dimension
  document_hash TEXT NOT NULL,
  file_type TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- HNSW index for fast similarity search
CREATE INDEX idx_document_embeddings_similarity 
ON document_embeddings USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Function to find similar documents
CREATE OR REPLACE FUNCTION find_similar_documents(
  query_embedding vector(1536),
  customer_id_param uuid,
  similarity_threshold decimal DEFAULT 0.9,
  max_results integer DEFAULT 5
) RETURNS TABLE (
  document_id uuid,
  similarity decimal,
  file_name text,
  extraction_result jsonb
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    (1 - (de.embedding <=> query_embedding))::decimal as similarity,
    d.file_name,
    d.extraction_result
  FROM document_embeddings de
  JOIN documents d ON de.document_id = d.id
  WHERE de.customer_id = customer_id_param
    AND d.processing_status = 'completed'
    AND (1 - (de.embedding <=> query_embedding)) >= similarity_threshold
  ORDER BY de.embedding <=> query_embedding
  LIMIT max_results;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

---

## 🔄 Asynchronous Processing Architecture

### Queue System Implementation

```sql
-- Job queue table for async processing
CREATE TABLE job_queue (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  job_type TEXT NOT NULL CHECK (job_type IN ('document_extract', 'agent_update', 'cleanup', 'billing')),
  customer_id UUID NOT NULL REFERENCES customers(id),
  priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10),
  payload JSONB NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (
    status IN ('pending', 'processing', 'completed', 'failed', 'retrying')
  ),
  attempts INTEGER DEFAULT 0,
  max_attempts INTEGER DEFAULT 3,
  scheduled_at TIMESTAMPTZ DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for efficient queue processing
CREATE INDEX idx_job_queue_processing ON job_queue(status, scheduled_at, priority DESC) 
WHERE status IN ('pending', 'retrying');

CREATE INDEX idx_job_queue_customer ON job_queue(customer_id, created_at DESC);

-- Queue processing function
CREATE OR REPLACE FUNCTION process_job_queue()
RETURNS void AS $$
DECLARE
  job_record RECORD;
  processing_result JSONB;
BEGIN
  -- Get next job with FOR UPDATE SKIP LOCKED for concurrency
  SELECT * INTO job_record
  FROM job_queue
  WHERE status IN ('pending', 'retrying')
    AND scheduled_at <= NOW()
  ORDER BY priority DESC, created_at ASC
  LIMIT 1
  FOR UPDATE SKIP LOCKED;

  -- Exit if no jobs available
  IF NOT FOUND THEN
    RETURN;
  END IF;

  -- Mark job as processing
  UPDATE job_queue
  SET status = 'processing', started_at = NOW(), attempts = attempts + 1
  WHERE id = job_record.id;

  -- Process job based on type
  CASE job_record.job_type
    WHEN 'document_extract' THEN
      SELECT process_document_extraction(job_record.payload) INTO processing_result;
    WHEN 'agent_update' THEN
      SELECT process_agent_update(job_record.payload) INTO processing_result;
    WHEN 'cleanup' THEN
      SELECT process_cleanup_task(job_record.payload) INTO processing_result;
    WHEN 'billing' THEN
      SELECT process_billing_task(job_record.payload) INTO processing_result;
  END CASE;

  -- Update job status based on result
  IF processing_result->>'success' = 'true' THEN
    UPDATE job_queue
    SET status = 'completed', completed_at = NOW()
    WHERE id = job_record.id;
  ELSE
    -- Handle retry logic
    IF job_record.attempts >= job_record.max_attempts THEN
      UPDATE job_queue
      SET status = 'failed', error_message = processing_result->>'error'
      WHERE id = job_record.id;
    ELSE
      -- Exponential backoff: 2^attempts minutes
      UPDATE job_queue
      SET status = 'retrying', 
          scheduled_at = NOW() + INTERVAL '1 minute' * POWER(2, job_record.attempts),
          error_message = processing_result->>'error'
      WHERE id = job_record.id;
    END IF;
  END IF;

EXCEPTION
  WHEN OTHERS THEN
    -- Handle unexpected errors
    UPDATE job_queue
    SET status = 'failed', 
        error_message = SQLERRM,
        completed_at = NOW()
    WHERE id = job_record.id;
END;
$$ LANGUAGE plpgsql;

-- Schedule queue processing every 10 seconds
SELECT cron.schedule('process-job-queue', '*/10 * * * * *', 'SELECT process_job_queue();');
```

### Real-time Status Updates

```typescript
// Real-time job status updates via Supabase Realtime
interface JobStatusUpdate {
  jobId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  result?: any;
  error?: string;
  estimatedCompletion?: Date;
}

class JobStatusManager {
  private subscriptions = new Map<string, RealtimeChannel>();

  subscribeToJob(jobId: string, callback: (update: JobStatusUpdate) => void): void {
    const channel = supabase
      .channel(`job-${jobId}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'job_queue',
        filter: `id=eq.${jobId}`
      }, (payload) => {
        const update: JobStatusUpdate = {
          jobId,
          status: payload.new.status,
          progress: payload.new.progress,
          result: payload.new.result,
          error: payload.new.error_message
        };
        callback(update);
      })
      .subscribe();

    this.subscriptions.set(jobId, channel);
  }

  unsubscribeFromJob(jobId: string): void {
    const channel = this.subscriptions.get(jobId);
    if (channel) {
      channel.unsubscribe();
      this.subscriptions.delete(jobId);
    }
  }

  async updateJobProgress(jobId: string, progress: number, status?: string): Promise<void> {
    await supabase
      .from('job_queue')
      .update({ 
        progress,
        ...(status && { status }),
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);
  }
}
```

---

## 🔐 Security Architecture

### API Key Security Implementation

```typescript
// SHA-256 API key hashing with salt
import { createHash, randomBytes } from 'crypto';

class ApiKeyManager {
  private readonly salt = Deno.env.get('API_KEY_SALT') || 'default-salt-change-in-prod';

  generateApiKey(keyType: 'skt' | 'skp'): { key: string; hash: string; prefix: string } {
    const randomPart = randomBytes(32).toString('hex');
    const key = `${keyType}_live_${randomPart}`;
    const hash = this.hashApiKey(key);
    const prefix = key.substring(0, 12); // Store first 12 chars for identification
    
    return { key, hash, prefix };
  }

  hashApiKey(key: string): string {
    return createHash('sha256')
      .update(key + this.salt)
      .digest('hex');
  }

  async validateApiKey(providedKey: string): Promise<ApiKeyValidation | null> {
    // Validate format first
    if (!providedKey.match(/^sk[tp]_live_[a-f0-9]{64}$/)) {
      throw new Error('Invalid API key format');
    }

    const keyHash = this.hashApiKey(providedKey);
    
    const { data, error } = await supabase
      .from('api_keys')
      .select(`
        id,
        customer_id,
        key_type,
        credits,
        rate_limits,
        scopes,
        revoked,
        expires_at,
        customers (
          id,
          company_name,
          status,
          tier_settings
        )
      `)
      .eq('key_hash', keyHash)
      .single();

    if (error || !data || data.revoked) {
      return null;
    }

    // Check expiration
    if (data.expires_at && new Date(data.expires_at) < new Date()) {
      return null;
    }

    // Check customer status
    if (data.customers.status !== 'active') {
      return null;
    }

    // Update last used timestamp
    await supabase
      .from('api_keys')
      .update({ last_used_at: new Date().toISOString() })
      .eq('id', data.id);

    return {
      keyId: data.id,
      customerId: data.customer_id,
      keyType: data.key_type,
      credits: data.credits,
      rateLimit: data.rate_limits,
      scopes: data.scopes,
      customer: data.customers
    };
  }
}
```

### Input Sanitization & Prompt Injection Protection

```typescript
// Multi-layer input sanitization
import DOMPurify from 'dompurify';

class InputSanitizer {
  private readonly maxPromptLength = 4000;
  private readonly maxFileSize = 52428800; // 50MB
  
  private readonly injectionPatterns = [
    /ignore\s+previous\s+instructions/i,
    /system\s*:\s*you\s+are/i,
    /assistant\s+mode/i,
    /<script.*?>/i,
    /javascript:/i,
    /data:text\/html/i,
    /\/\*.*?\*\//gs,
    /<!--.*?-->/gs
  ];

  sanitizePrompt(userPrompt: string): string {
    // 1. Basic length validation
    if (userPrompt.length > this.maxPromptLength) {
      throw new Error(`Prompt too long. Max ${this.maxPromptLength} characters.`);
    }

    // 2. HTML sanitization
    const cleaned = DOMPurify.sanitize(userPrompt, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    });

    // 3. Prompt injection detection
    for (const pattern of this.injectionPatterns) {
      if (pattern.test(cleaned)) {
        throw new Error('Potential prompt injection detected');
      }
    }

    // 4. Character encoding normalization
    return cleaned.normalize('NFKC');
  }

  async validateFile(file: File): Promise<void> {
    // Size validation
    if (file.size > this.maxFileSize) {
      throw new Error(`File too large. Max ${this.maxFileSize / 1024 / 1024}MB`);
    }

    // MIME type validation
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png', 
      'image/webp',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error(`Unsupported file type: ${file.type}`);
    }

    // Magic number validation (check file header)
    const header = new Uint8Array(await file.slice(0, 8).arrayBuffer());
    if (!this.validateFileHeader(header, file.type)) {
      throw new Error('File header mismatch - possible malicious file');
    }
  }

  private validateFileHeader(header: Uint8Array, expectedType: string): boolean {
    const signatures: Record<string, number[][]> = {
      'application/pdf': [[0x25, 0x50, 0x44, 0x46]], // %PDF
      'image/jpeg': [[0xFF, 0xD8, 0xFF]], // JPEG
      'image/png': [[0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A]], // PNG
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 
        [[0x50, 0x4B, 0x03, 0x04]], // ZIP (DOCX)
    };

    const expectedSignatures = signatures[expectedType];
    if (!expectedSignatures) return true; // Unknown type, allow

    return expectedSignatures.some(signature =>
      signature.every((byte, index) => header[index] === byte)
    );
  }
}
```

### Rate Limiting Implementation

```typescript
// Redis-backed rate limiting with sliding window
class RateLimiter {
  private redis: Redis;

  constructor() {
    this.redis = new Redis(Deno.env.get('REDIS_URL')!);
  }

  async checkRateLimit(apiKeyId: string, endpoint: string): Promise<RateLimitResult> {
    const now = Date.now();
    const windowMs = 60000; // 1 minute window
    const key = `rate_limit:${apiKeyId}:${endpoint}`;
    
    // Get current limit for this API key
    const { data: keyData } = await supabase
      .from('api_keys')
      .select('rate_limits')
      .eq('id', apiKeyId)
      .single();

    const limits = keyData?.rate_limits || { requestsPerMinute: 100 };
    const maxRequests = limits.requestsPerMinute;

    // Sliding window implementation
    const pipeline = this.redis.pipeline();
    
    // Remove expired entries
    pipeline.zremrangebyscore(key, 0, now - windowMs);
    
    // Count current requests in window
    pipeline.zcard(key);
    
    // Add current request
    pipeline.zadd(key, now, `${now}-${Math.random()}`);
    
    // Set expiration
    pipeline.expire(key, Math.ceil(windowMs / 1000));
    
    const results = await pipeline.exec();
    const currentCount = results[1][1] as number;

    if (currentCount >= maxRequests) {
      // Calculate retry after
      const oldestEntry = await this.redis.zrange(key, 0, 0, 'WITHSCORES');
      const retryAfter = oldestEntry.length > 0 
        ? Math.ceil((parseInt(oldestEntry[1]) + windowMs - now) / 1000)
        : 60;

      return {
        allowed: false,
        limit: maxRequests,
        remaining: 0,
        resetTime: now + (retryAfter * 1000),
        retryAfter
      };
    }

    return {
      allowed: true,
      limit: maxRequests,
      remaining: maxRequests - currentCount - 1,
      resetTime: now + windowMs,
      retryAfter: 0
    };
  }
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter: number;
}
```

---

## 📊 Monitoring & Observability

### Performance Metrics Collection

```typescript
// Comprehensive metrics collection
interface MetricsData {
  // Request metrics
  requestDuration: number;
  requestSize: number;
  responseSize: number;
  
  // AI processing metrics
  modelUsed: string;
  tokenCount: number;
  processingTime: number;
  confidenceScore: number;
  
  // Cost metrics
  modelCost: number;
  customerPrice: number;
  profitMargin: number;
  
  // System metrics
  memoryUsage: number;
  cpuUsage: number;
  queueLength: number;
  
  // Business metrics
  documentType: string;
  extractionAccuracy: number;
  customerTier: string;
}

class MetricsCollector {
  async recordMetrics(metrics: MetricsData, correlationId: string): Promise<void> {
    // Store in database for persistence
    await supabase.from('performance_metrics').insert({
      correlation_id: correlationId,
      timestamp: new Date(),
      ...metrics
    });

    // Send to real-time monitoring (if configured)
    if (Deno.env.get('METRICS_WEBHOOK_URL')) {
      await fetch(Deno.env.get('METRICS_WEBHOOK_URL')!, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timestamp: new Date().toISOString(),
          service: 'idp-platform',
          metrics
        })
      });
    }
  }

  async getHealthMetrics(): Promise<HealthStatus> {
    // Database health
    const dbStart = performance.now();
    const { error: dbError } = await supabase.from('customers').select('id').limit(1);
    const dbLatency = performance.now() - dbStart;

    // AI service health
    const aiHealth = await this.checkAIServices();

    // Queue health
    const { count: queueLength } = await supabase
      .from('job_queue')
      .select('*', { count: 'exact' })
      .in('status', ['pending', 'processing']);

    // Error rate (last hour)
    const hourAgo = new Date(Date.now() - 3600000);
    const { data: errorData } = await supabase
      .from('audit_logs')
      .select('status')
      .gte('created_at', hourAgo.toISOString());

    const totalRequests = errorData?.length || 0;
    const errorRequests = errorData?.filter(log => log.status === 'failure').length || 0;
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;

    return {
      status: dbError ? 'unhealthy' : 'healthy',
      database: {
        status: dbError ? 'down' : 'up',
        latency: dbLatency
      },
      aiServices: aiHealth,
      queue: {
        length: queueLength,
        status: queueLength > 100 ? 'degraded' : 'healthy'
      },
      errorRate,
      uptime: process.uptime?.() || 0
    };
  }

  private async checkAIServices(): Promise<Record<string, ServiceHealth>> {
    const services = ['openai', 'claude', 'llamaparse'];
    const results: Record<string, ServiceHealth> = {};

    await Promise.all(services.map(async (service) => {
      try {
        const start = performance.now();
        await this.pingService(service);
        const latency = performance.now() - start;
        
        results[service] = {
          status: 'up',
          latency,
          lastChecked: new Date()
        };
      } catch (error) {
        results[service] = {
          status: 'down',
          latency: -1,
          lastChecked: new Date(),
          error: error.message
        };
      }
    }));

    return results;
  }
}

interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  database: {
    status: 'up' | 'down';
    latency: number;
  };
  aiServices: Record<string, ServiceHealth>;
  queue: {
    length: number;
    status: 'healthy' | 'degraded';
  };
  errorRate: number;
  uptime: number;
}

interface ServiceHealth {
  status: 'up' | 'down';
  latency: number;
  lastChecked: Date;
  error?: string;
}
```

### Alert System Configuration

```typescript
// Configurable alerting system
interface AlertRule {
  id: string;
  name: string;
  condition: AlertCondition;
  severity: 'low' | 'medium' | 'high' | 'critical';
  channels: ('email' | 'slack' | 'webhook')[];
  cooldown: number; // minutes
}

interface AlertCondition {
  metric: string;
  operator: '>' | '<' | '==' | '>=' | '<=';
  threshold: number;
  duration: number; // minutes
}

const ALERT_RULES: AlertRule[] = [
  {
    id: 'high-error-rate',
    name: 'High Error Rate',
    condition: {
      metric: 'error_rate_percent',
      operator: '>',
      threshold: 5, // 5% error rate
      duration: 5
    },
    severity: 'high',
    channels: ['email', 'slack'],
    cooldown: 30
  },
  {
    id: 'low-credits',
    name: 'Customer Low Credits',
    condition: {
      metric: 'customer_credits',
      operator: '<',
      threshold: 100,
      duration: 0
    },
    severity: 'medium',
    channels: ['email'],
    cooldown: 60
  },
  {
    id: 'queue-backlog',
    name: 'Queue Backlog',
    condition: {
      metric: 'queue_length',
      operator: '>',
      threshold: 50,
      duration: 10
    },
    severity: 'high',
    channels: ['slack', 'webhook'],
    cooldown: 15
  },
  {
    id: 'high-costs',
    name: 'High AI Costs',
    condition: {
      metric: 'hourly_ai_cost',
      operator: '>',
      threshold: 10, // $10/hour
      duration: 0
    },
    severity: 'critical',
    channels: ['email', 'slack'],
    cooldown: 60
  }
];

class AlertManager {
  private lastAlerts = new Map<string, Date>();

  async checkAlerts(): Promise<void> {
    for (const rule of ALERT_RULES) {
      // Check cooldown
      const lastAlert = this.lastAlerts.get(rule.id);
      if (lastAlert && Date.now() - lastAlert.getTime() < rule.cooldown * 60000) {
        continue;
      }

      // Evaluate condition
      const shouldAlert = await this.evaluateCondition(rule.condition);
      
      if (shouldAlert) {
        await this.triggerAlert(rule);
        this.lastAlerts.set(rule.id, new Date());
      }
    }
  }

  private async evaluateCondition(condition: AlertCondition): Promise<boolean> {
    const metricValue = await this.getMetricValue(condition.metric, condition.duration);
    
    switch (condition.operator) {
      case '>': return metricValue > condition.threshold;
      case '<': return metricValue < condition.threshold;
      case '>=': return metricValue >= condition.threshold;
      case '<=': return metricValue <= condition.threshold;
      case '==': return metricValue === condition.threshold;
      default: return false;
    }
  }

  private async triggerAlert(rule: AlertRule): Promise<void> {
    const alert = {
      rule: rule.name,
      severity: rule.severity,
      timestamp: new Date(),
      message: `Alert: ${rule.name} triggered`
    };

    // Log alert
    await supabase.from('alert_logs').insert(alert);

    // Send notifications
    for (const channel of rule.channels) {
      switch (channel) {
        case 'email':
          await this.sendEmailAlert(alert);
          break;
        case 'slack':
          await this.sendSlackAlert(alert);
          break;
        case 'webhook':
          await this.sendWebhookAlert(alert);
          break;
      }
    }
  }
}
```

---

## 🚀 Deployment Architecture

### Multi-Environment Configuration

```yaml
# deployment/environments/production.yml
apiVersion: v1
kind: ConfigMap
metadata:
  name: idp-platform-config
data:
  ENVIRONMENT: "production"
  LOG_LEVEL: "info"
  
  # Supabase Configuration
  SUPABASE_URL: "https://oxviewglpxujnzrzmopx.supabase.co"
  SUPABASE_PROJECT_REF: "oxviewglpxujnzrzmopx"
  
  # AI Service Configuration
  OPENROUTER_REFERER: "https://oxviewglpxujnzrzmopx.supabase.co"
  DEFAULT_MODEL_TIER: "balanced"
  AUTO_SELECT_MODEL: "true"
  MAX_COST_PER_DOCUMENT: "0.10"
  OPENROUTER_BUDGET_LIMIT: "1000.00"
  
  # Performance Configuration
  MAX_FILE_SIZE: "52428800"  # 50MB
  QUEUE_BATCH_SIZE: "10"
  PROCESSING_TIMEOUT: "300000"  # 5 minutes
  
  # Security Configuration
  RATE_LIMIT_WINDOW: "60000"  # 1 minute
  MAX_REQUESTS_PER_MINUTE: "100"
  API_KEY_EXPIRY_DAYS: "365"
  
  # Monitoring Configuration
  METRICS_ENABLED: "true"
  ALERT_WEBHOOK_URL: "https://hooks.slack.com/..."
  HEALTH_CHECK_INTERVAL: "30000"  # 30 seconds
---
apiVersion: v1
kind: Secret
metadata:
  name: idp-platform-secrets
type: Opaque
stringData:
  SUPABASE_SERVICE_ROLE_KEY: "eyJ..."
  OPENROUTER_API_KEY: "sk-or-v1-..."
  LLAMAPARSE_API_KEY: "llx-..."
  DATABASE_URL: "postgresql://..."
  REDIS_URL: "redis://..."
  API_KEY_SALT: "production-salt-key"
```

### CI/CD Pipeline Configuration

```yaml
# .github/workflows/deploy.yml
name: Deploy IDP Platform

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
  SUPABASE_DB_PASSWORD: ${{ secrets.SUPABASE_DB_PASSWORD }}
  SUPABASE_PROJECT_ID: oxviewglpxujnzrzmopx

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Deno
        uses: denoland/setup-deno@v1
        with:
          deno-version: v1.43.0  # Match Supabase Edge Runtime
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Start Supabase
        run: npx supabase start
        
      - name: Run database migrations
        run: npx supabase db push
        
      - name: Generate types
        run: npm run gen:types
        
      - name: Run tests
        run: npm test
        
      - name: Lint Edge Functions
        run: npm run lint
        
      - name: Type check
        run: npm run typecheck

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Supabase CLI
        uses: supabase/setup-cli@v1
        with:
          version: latest
          
      - name: Deploy to staging
        run: |
          npx supabase link --project-ref $SUPABASE_PROJECT_ID
          npx supabase db push
          npx supabase functions deploy --no-verify-jwt
          
      - name: Run integration tests
        run: npm run test:integration
        env:
          SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}

  deploy-production:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Deploy to production
        run: |
          npx supabase link --project-ref $SUPABASE_PROJECT_ID
          npx supabase db push
          npx supabase functions deploy --no-verify-jwt
          
      - name: Verify deployment
        run: npm run test:smoke
        env:
          SUPABASE_URL: ${{ secrets.PRODUCTION_SUPABASE_URL }}
          SUPABASE_ANON_KEY: ${{ secrets.PRODUCTION_SUPABASE_ANON_KEY }}
          
      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

### Database Migration Strategy

```sql
-- Migration versioning and rollback strategy
CREATE TABLE IF NOT EXISTS migration_history (
  id SERIAL PRIMARY KEY,
  version TEXT UNIQUE NOT NULL,
  description TEXT NOT NULL,
  applied_at TIMESTAMPTZ DEFAULT NOW(),
  applied_by TEXT DEFAULT current_user,
  rollback_sql TEXT,
  checksum TEXT NOT NULL
);

-- Example migration with rollback
-- migrations/20250921000001_add_priority_queue.sql
BEGIN;

-- Forward migration
ALTER TABLE job_queue ADD COLUMN priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10);

CREATE INDEX idx_job_queue_priority ON job_queue(priority DESC, created_at ASC) 
WHERE status IN ('pending', 'retrying');

-- Record migration
INSERT INTO migration_history (version, description, rollback_sql, checksum) VALUES (
  '20250921000001',
  'Add priority queue support',
  'DROP INDEX idx_job_queue_priority; ALTER TABLE job_queue DROP COLUMN priority;',
  'sha256-checksum-here'
);

COMMIT;
```

---

## 💰 Cost Optimization Architecture

### Intelligent Model Selection

```typescript
// Cost-aware model selection with profit margin enforcement
interface CostCalculation {
  modelCost: number;
  customerPrice: number;
  profitMargin: number;
  tokensUsed: number;
  processingTime: number;
}

class CostOptimizer {
  private readonly targetProfitMargin = 0.6; // 60% minimum
  private readonly maxCostPerDocument = 0.10; // $0.10 maximum
  
  async selectCostOptimalModel(
    document: DocumentMetadata, 
    agent: Agent,
    customerTier: string
  ): Promise<ModelSelection> {
    // Analyze document complexity
    const complexity = await this.analyzeDocumentComplexity(document);
    
    // Get model costs and capabilities
    const availableModels = await this.getAvailableModels();
    
    // Filter models that can handle the document
    const capableModels = availableModels.filter(model =>
      this.canModelHandleDocument(model, document, complexity)
    );
    
    // Calculate cost-benefit for each model
    const modelScores = await Promise.all(
      capableModels.map(async (model) => {
        const costEstimate = await this.estimateModelCost(model, document, complexity);
        const qualityScore = this.getModelQualityScore(model, document.type);
        const speedScore = this.getModelSpeedScore(model);
        
        // Weighted scoring: cost (50%), quality (30%), speed (20%)
        const totalScore = (
          (1 - costEstimate.normalizedCost) * 0.5 +
          qualityScore * 0.3 +
          speedScore * 0.2
        );
        
        return {
          model,
          costEstimate,
          qualityScore,
          speedScore,
          totalScore
        };
      })
    );
    
    // Select highest scoring model that meets profit requirements
    const sortedModels = modelScores
      .filter(score => score.costEstimate.profitMargin >= this.targetProfitMargin)
      .sort((a, b) => b.totalScore - a.totalScore);
    
    if (sortedModels.length === 0) {
      throw new Error('No profitable models available for this document');
    }
    
    return {
      selectedModel: sortedModels[0].model,
      estimatedCost: sortedModels[0].costEstimate,
      reasoning: this.generateSelectionReasoning(sortedModels[0])
    };
  }

  private async estimateModelCost(
    model: ModelConfig,
    document: DocumentMetadata,
    complexity: number
  ): Promise<CostEstimate> {
    // Estimate token usage based on document size and complexity
    const estimatedInputTokens = Math.ceil(document.size / 3) * (1 + complexity);
    const estimatedOutputTokens = Math.ceil(estimatedInputTokens * 0.1); // ~10% output
    
    const modelCost = (
      estimatedInputTokens * model.inputCostPerToken +
      estimatedOutputTokens * model.outputCostPerToken
    );
    
    // Calculate customer price with tier-based markup
    const markup = this.getTierMarkup(document.customerTier);
    const customerPrice = modelCost * markup;
    const profitMargin = (customerPrice - modelCost) / customerPrice;
    
    return {
      modelCost,
      customerPrice,
      profitMargin,
      estimatedInputTokens,
      estimatedOutputTokens,
      normalizedCost: modelCost / this.maxCostPerDocument
    };
  }

  private getTierMarkup(tier: string): number {
    const markups: Record<string, number> = {
      'free': 2.0,      // 100% markup
      'starter': 1.8,   // 80% markup  
      'professional': 1.6, // 60% markup
      'enterprise': 1.4    // 40% markup
    };
    return markups[tier] || markups['free'];
  }
}

interface ModelSelection {
  selectedModel: ModelConfig;
  estimatedCost: CostEstimate;
  reasoning: string;
}

interface CostEstimate {
  modelCost: number;
  customerPrice: number;
  profitMargin: number;
  estimatedInputTokens: number;
  estimatedOutputTokens: number;
  normalizedCost: number;
}
```

### Real-time Cost Tracking

```typescript
// Real-time cost monitoring and budget enforcement
class CostMonitor {
  private readonly dailyBudgetLimit = 500; // $500/day
  private readonly monthlyBudgetLimit = 10000; // $10k/month
  
  async trackUsage(usage: UsageRecord): Promise<void> {
    // Record detailed usage
    await supabase.from('usage_logs').insert({
      customer_id: usage.customerId,
      api_key_id: usage.apiKeyId,
      operation_type: usage.operationType,
      model_used: usage.modelUsed,
      input_tokens: usage.inputTokens,
      output_tokens: usage.outputTokens,
      model_cost: usage.modelCost,
      customer_price: usage.customerPrice,
      credits_used: usage.creditsUsed,
      processing_time_ms: usage.processingTimeMs,
      success: usage.success,
      created_at: new Date().toISOString()
    });

    // Update running totals
    await this.updateCostTotals(usage);
    
    // Check budget limits
    await this.checkBudgetLimits();
    
    // Alert on high costs
    await this.checkCostAlerts(usage);
  }

  private async updateCostTotals(usage: UsageRecord): Promise<void> {
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().substring(0, 7);
    
    // Update daily totals
    await supabase.rpc('update_daily_costs', {
      date_key: today,
      model_cost: usage.modelCost,
      customer_revenue: usage.customerPrice,
      token_count: usage.inputTokens + usage.outputTokens
    });
    
    // Update monthly totals
    await supabase.rpc('update_monthly_costs', {
      month_key: thisMonth,
      model_cost: usage.modelCost,
      customer_revenue: usage.customerPrice,
      token_count: usage.inputTokens + usage.outputTokens
    });
  }

  async getCostSummary(period: 'day' | 'week' | 'month'): Promise<CostSummary> {
    const { data } = await supabase.rpc('get_cost_summary', {
      period_type: period,
      start_date: this.getPeriodStart(period)
    });

    return {
      totalModelCosts: data.total_model_costs,
      totalCustomerRevenue: data.total_customer_revenue,
      totalProfitMargin: (data.total_customer_revenue - data.total_model_costs) / data.total_customer_revenue,
      totalTokensProcessed: data.total_tokens,
      averageCostPerDocument: data.total_model_costs / data.document_count,
      topCostlyModels: data.top_models,
      customerBreakdown: data.customer_costs
    };
  }

  private async checkBudgetLimits(): Promise<void> {
    const todayCosts = await this.getDailyCosts();
    const monthCosts = await this.getMonthlyCosts();
    
    if (todayCosts > this.dailyBudgetLimit * 0.9) {
      await this.sendBudgetAlert('daily', todayCosts, this.dailyBudgetLimit);
    }
    
    if (monthCosts > this.monthlyBudgetLimit * 0.9) {
      await this.sendBudgetAlert('monthly', monthCosts, this.monthlyBudgetLimit);
    }
    
    // Implement circuit breaker if budget exceeded
    if (todayCosts > this.dailyBudgetLimit || monthCosts > this.monthlyBudgetLimit) {
      await this.enableCostCircuitBreaker();
    }
  }
}

interface CostSummary {
  totalModelCosts: number;
  totalCustomerRevenue: number;
  totalProfitMargin: number;
  totalTokensProcessed: number;
  averageCostPerDocument: number;
  topCostlyModels: Array<{ model: string; cost: number; usage: number }>;
  customerBreakdown: Array<{ customerId: string; cost: number; revenue: number }>;
}
```

---

## 🔄 Data Retention & Cleanup Architecture

### Automated Data Lifecycle Management

```sql
-- Data retention policies by key type and customer tier
CREATE TABLE data_retention_policies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  key_type TEXT CHECK (key_type IN ('skt', 'skp')) NOT NULL,
  customer_tier TEXT DEFAULT 'free',
  document_retention_days INTEGER NOT NULL,
  result_retention_days INTEGER NOT NULL,
  log_retention_days INTEGER NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Default retention policies
INSERT INTO data_retention_policies (key_type, customer_tier, document_retention_days, result_retention_days, log_retention_days) VALUES
('skt', 'free', 7, 7, 7),           -- Test keys: 7 days everything
('skp', 'free', 30, 30, 90),        -- Production free: 30 days docs, 90 days logs
('skp', 'starter', 90, 90, 180),    -- Starter: 90 days docs, 180 days logs
('skp', 'professional', 365, 365, 730), -- Professional: 1 year docs, 2 years logs
('skp', 'enterprise', 1095, 1095, 2555); -- Enterprise: 3 years docs, 7 years logs

-- Automated cleanup function
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
DECLARE
  cleanup_stats JSONB := '{}';
  deleted_count INTEGER;
BEGIN
  -- Clean up expired documents
  WITH deleted_docs AS (
    DELETE FROM documents 
    WHERE expires_at IS NOT NULL AND expires_at < NOW()
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted_docs;
  cleanup_stats := jsonb_set(cleanup_stats, '{expired_documents}', to_jsonb(deleted_count));

  -- Clean up orphaned document embeddings
  WITH deleted_embeddings AS (
    DELETE FROM document_embeddings 
    WHERE document_id NOT IN (SELECT id FROM documents)
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted_embeddings;
  cleanup_stats := jsonb_set(cleanup_stats, '{orphaned_embeddings}', to_jsonb(deleted_count));

  -- Clean up old usage logs (keep based on customer tier)
  WITH deleted_logs AS (
    DELETE FROM usage_logs ul
    WHERE ul.created_at < (
      SELECT NOW() - INTERVAL '1 day' * drp.log_retention_days
      FROM api_keys ak
      JOIN customers c ON ak.customer_id = c.id
      JOIN data_retention_policies drp ON drp.key_type = ak.key_type 
        AND drp.customer_tier = COALESCE(c.tier_settings->>'tier', 'free')
      WHERE ak.id = ul.api_key_id
    )
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted_logs;
  cleanup_stats := jsonb_set(cleanup_stats, '{old_usage_logs}', to_jsonb(deleted_count));

  -- Clean up old audit logs (7 year retention for compliance)
  WITH deleted_audit AS (
    DELETE FROM audit_logs 
    WHERE created_at < NOW() - INTERVAL '7 years'
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted_audit;
  cleanup_stats := jsonb_set(cleanup_stats, '{old_audit_logs}', to_jsonb(deleted_count));

  -- Clean up failed jobs older than 30 days
  WITH deleted_jobs AS (
    DELETE FROM job_queue 
    WHERE status = 'failed' AND created_at < NOW() - INTERVAL '30 days'
    RETURNING id
  )
  SELECT COUNT(*) INTO deleted_count FROM deleted_jobs;
  cleanup_stats := jsonb_set(cleanup_stats, '{failed_jobs}', to_jsonb(deleted_count));

  -- Log cleanup statistics
  INSERT INTO audit_logs (action, resource_type, status, details, correlation_id) VALUES (
    'automated_cleanup',
    'system',
    'success',
    cleanup_stats,
    'cleanup-' || extract(epoch from now())::text
  );

  -- Vacuum and analyze to reclaim space
  PERFORM pg_advisory_lock(12345);
  EXECUTE 'VACUUM ANALYZE documents, document_embeddings, usage_logs, audit_logs, job_queue';
  PERFORM pg_advisory_unlock(12345);

END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup every 4 hours
SELECT cron.schedule('cleanup-expired-data', '0 */4 * * *', 'SELECT cleanup_expired_data();');
```

### GDPR Compliance Implementation

```typescript
// GDPR-compliant data handling
class GDPRManager {
  async handleDataDeletionRequest(customerId: string, requestType: 'soft' | 'hard'): Promise<DeletionReport> {
    const report: DeletionReport = {
      customerId,
      requestType,
      startTime: new Date(),
      deletedRecords: {},
      anonymizedRecords: {},
      errors: []
    };

    try {
      if (requestType === 'hard') {
        // Hard deletion - complete removal (GDPR "right to erasure")
        await this.performHardDeletion(customerId, report);
      } else {
        // Soft deletion - anonymization while preserving analytics
        await this.performSoftDeletion(customerId, report);
      }

      // Log deletion request compliance
      await supabase.from('audit_logs').insert({
        customer_id: customerId,
        action: 'gdpr_data_deletion',
        resource_type: 'customer_data',
        status: 'success',
        details: report,
        correlation_id: `gdpr-${Date.now()}`
      });

    } catch (error) {
      report.errors.push(error.message);
      report.status = 'failed';
    }

    report.endTime = new Date();
    report.duration = report.endTime.getTime() - report.startTime.getTime();
    return report;
  }

  private async performHardDeletion(customerId: string, report: DeletionReport): Promise<void> {
    // Delete in dependency order to maintain referential integrity
    
    // 1. Delete document embeddings
    const { count: embeddingCount } = await supabase
      .from('document_embeddings')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId);
    report.deletedRecords.document_embeddings = embeddingCount;

    // 2. Delete documents
    const { count: documentCount } = await supabase
      .from('documents')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId);
    report.deletedRecords.documents = documentCount;

    // 3. Delete custom agents (keep references to default agents)
    const { count: agentCount } = await supabase
      .from('agents')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId)
      .neq('parent_agent_id', null);
    report.deletedRecords.custom_agents = agentCount;

    // 4. Delete usage logs
    const { count: usageCount } = await supabase
      .from('usage_logs')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId);
    report.deletedRecords.usage_logs = usageCount;

    // 5. Delete API keys
    const { count: keyCount } = await supabase
      .from('api_keys')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId);
    report.deletedRecords.api_keys = keyCount;

    // 6. Delete customer record (this will cascade delete remaining references)
    const { count: customerCount } = await supabase
      .from('customers')
      .delete({ count: 'exact' })
      .eq('id', customerId);
    report.deletedRecords.customers = customerCount;

    report.status = 'completed';
  }

  private async performSoftDeletion(customerId: string, report: DeletionReport): Promise<void> {
    // Anonymize customer data while preserving analytics value
    
    const anonymizedId = `anon_${crypto.randomUUID()}`;
    const anonymizedEmail = `deleted_${Date.now()}@anonymized.local`;
    
    // 1. Anonymize customer record
    const { count: customerCount } = await supabase
      .from('customers')
      .update({
        company_name: 'Anonymized Customer',
        contact_email: anonymizedEmail,
        status: 'deleted',
        tier_settings: {},
        gdpr_anonymized_at: new Date().toISOString()
      })
      .eq('id', customerId);
    report.anonymizedRecords.customers = customerCount;

    // 2. Anonymize usage logs (keep for analytics but remove PII)
    const { count: usageCount } = await supabase
      .from('usage_logs')
      .update({
        anonymized_customer_id: anonymizedId,
        // Keep: model costs, processing times, success rates
        // Remove: specific document references
      })
      .eq('customer_id', customerId);
    report.anonymizedRecords.usage_logs = usageCount;

    // 3. Delete API keys (no analytics value)
    const { count: keyCount } = await supabase
      .from('api_keys')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId);
    report.deletedRecords.api_keys = keyCount;

    // 4. Delete documents and results (contain PII)
    const { count: documentCount } = await supabase
      .from('documents')
      .delete({ count: 'exact' })
      .eq('customer_id', customerId);
    report.deletedRecords.documents = documentCount;

    report.status = 'completed';
  }

  async generateDataExport(customerId: string): Promise<DataExport> {
    // GDPR "right to data portability"
    const customerData = await supabase
      .from('customers')
      .select('*')
      .eq('id', customerId)
      .single();

    const apiKeys = await supabase
      .from('api_keys')
      .select('key_prefix, key_type, credits, created_at, last_used_at')
      .eq('customer_id', customerId);

    const documents = await supabase
      .from('documents')
      .select('file_name, file_type, extraction_result, processed_at')
      .eq('customer_id', customerId)
      .eq('processing_status', 'completed');

    const usage = await supabase
      .from('usage_logs')
      .select('operation_type, model_used, customer_price, created_at')
      .eq('customer_id', customerId);

    return {
      exportDate: new Date(),
      customer: customerData.data,
      apiKeys: apiKeys.data,
      documents: documents.data?.map(doc => ({
        ...doc,
        extraction_result: undefined // Remove actual extraction for privacy
      })),
      usage: usage.data,
      format: 'json',
      version: '1.0'
    };
  }
}

interface DeletionReport {
  customerId: string;
  requestType: 'soft' | 'hard';
  status: 'completed' | 'failed' | 'in_progress';
  startTime: Date;
  endTime?: Date;
  duration?: number;
  deletedRecords: Record<string, number>;
  anonymizedRecords: Record<string, number>;
  errors: string[];
}

interface DataExport {
  exportDate: Date;
  customer: any;
  apiKeys: any[];
  documents: any[];
  usage: any[];
  format: string;
  version: string;
}
```

---

## 📈 Performance Optimization Strategies

### Query Optimization & Caching

```sql
-- Materialized views for common analytics queries
CREATE MATERIALIZED VIEW customer_usage_summary AS
SELECT 
  c.id as customer_id,
  c.company_name,
  c.status,
  COUNT(d.id) as total_documents,
  SUM(ul.customer_price) as total_revenue,
  SUM(ul.model_cost) as total_costs,
  (SUM(ul.customer_price) - SUM(ul.model_cost)) / SUM(ul.customer_price) as profit_margin,
  AVG(d.processing_time_ms) as avg_processing_time,
  MAX(d.created_at) as last_activity
FROM customers c
LEFT JOIN documents d ON c.id = d.customer_id
LEFT JOIN usage_logs ul ON d.id = ul.document_id
WHERE d.processing_status = 'completed'
GROUP BY c.id, c.company_name, c.status;

-- Refresh materialized view hourly
SELECT cron.schedule('refresh-usage-summary', '0 * * * *', 'REFRESH MATERIALIZED VIEW customer_usage_summary;');

-- Partial indexes for common query patterns
CREATE INDEX idx_documents_recent_completed 
ON documents(customer_id, created_at DESC) 
WHERE processing_status = 'completed' AND created_at > NOW() - INTERVAL '30 days';

CREATE INDEX idx_usage_logs_billing 
ON usage_logs(customer_id, created_at DESC) 
WHERE success = true AND created_at > NOW() - INTERVAL '1 year';

-- Composite indexes for complex queries
CREATE INDEX idx_api_keys_customer_status 
ON api_keys(customer_id, key_type, revoked) 
WHERE NOT revoked;
```

### Application-Level Caching Strategy

```typescript
// Multi-tier caching with Redis and in-memory
class CacheManager {
  private memoryCache = new Map<string, CacheEntry>();
  private redis: Redis;
  private readonly TTL = {
    short: 300,    // 5 minutes
    medium: 3600,  // 1 hour
    long: 86400    // 24 hours
  };

  constructor() {
    this.redis = new Redis(Deno.env.get('REDIS_URL')!);
  }

  async get<T>(key: string, fallback?: () => Promise<T>): Promise<T | null> {
    // L1: Memory cache
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.data as T;
    }

    // L2: Redis cache
    try {
      const redisValue = await this.redis.get(key);
      if (redisValue) {
        const data = JSON.parse(redisValue) as T;
        // Update memory cache
        this.memoryCache.set(key, {
          data,
          expires: Date.now() + this.TTL.short * 1000
        });
        return data;
      }
    } catch (error) {
      console.warn('Redis cache miss:', error.message);
    }

    // L3: Fallback function
    if (fallback) {
      const data = await fallback();
      await this.set(key, data, this.TTL.medium);
      return data;
    }

    return null;
  }

  async set<T>(key: string, data: T, ttlSeconds: number = this.TTL.medium): Promise<void> {
    // Store in memory cache
    this.memoryCache.set(key, {
      data,
      expires: Date.now() + Math.min(ttlSeconds, this.TTL.short) * 1000
    });

    // Store in Redis
    try {
      await this.redis.setex(key, ttlSeconds, JSON.stringify(data));
    } catch (error) {
      console.warn('Redis cache set failed:', error.message);
    }
  }

  async invalidate(pattern: string): Promise<void> {
    // Clear memory cache
    for (const key of this.memoryCache.keys()) {
      if (key.includes(pattern)) {
        this.memoryCache.delete(key);
      }
    }

    // Clear Redis cache
    try {
      const keys = await this.redis.keys(`*${pattern}*`);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.warn('Redis cache invalidation failed:', error.message);
    }
  }

  // Cache specific methods for common data
  async getCachedCustomer(customerId: string): Promise<Customer | null> {
    return this.get(
      `customer:${customerId}`,
      async () => {
        const { data } = await supabase
          .from('customers')
          .select('*')
          .eq('id', customerId)
          .single();
        return data;
      }
    );
  }

  async getCachedAgent(agentId: string): Promise<Agent | null> {
    return this.get(
      `agent:${agentId}`,
      async () => {
        const { data } = await supabase
          .from('agents')
          .select('*')
          .eq('agent_id', agentId)
          .eq('active', true)
          .single();
        return data;
      }
    );
  }

  async getCachedSimilarDocuments(embedding: number[], customerId: string): Promise<SimilarDocument[]> {
    const embeddingHash = this.hashEmbedding(embedding);
    return this.get(
      `similar:${customerId}:${embeddingHash}`,
      async () => {
        const { data } = await supabase.rpc('find_similar_documents', {
          query_embedding: embedding,
          customer_id_param: customerId,
          similarity_threshold: 0.9
        });
        return data || [];
      }
    );
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expires;
  }

  private hashEmbedding(embedding: number[]): string {
    // Simple hash for embedding cache keys
    return embedding.slice(0, 10).join(',');
  }
}

interface CacheEntry {
  data: any;
  expires: number;
}
```

### Connection Pooling & Database Optimization

```typescript
// Optimized database connection management
class DatabaseManager {
  private pool: Pool;
  private readonly maxConnections = 20;
  private readonly idleTimeout = 30000; // 30 seconds

  constructor() {
    this.pool = new Pool({
      connectionString: Deno.env.get('DATABASE_URL'),
      max: this.maxConnections,
      idleTimeoutMillis: this.idleTimeout,
      connectionTimeoutMillis: 10000,
      maxUses: 7500, // Rotate connections to prevent memory leaks
      allowExitOnIdle: true
    });

    // Monitor pool health
    this.setupPoolMonitoring();
  }

  async executeQuery<T>(
    query: string, 
    params: any[] = [],
    timeout: number = 30000
  ): Promise<QueryResult<T>> {
    const client = await this.pool.connect();
    
    try {
      // Set statement timeout
      await client.query('SET statement_timeout = $1', [timeout]);
      
      const result = await client.query(query, params);
      return result;
    } finally {
      client.release();
    }
  }

  async executeTransaction<T>(
    queries: Array<{ query: string; params: any[] }>,
    timeout: number = 60000
  ): Promise<T[]> {
    const client = await this.pool.connect();
    
    try {
      await client.query('BEGIN');
      await client.query('SET statement_timeout = $1', [timeout]);
      
      const results: T[] = [];
      for (const { query, params } of queries) {
        const result = await client.query(query, params);
        results.push(result.rows);
      }
      
      await client.query('COMMIT');
      return results;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  private setupPoolMonitoring(): void {
    setInterval(() => {
      const stats = {
        totalCount: this.pool.totalCount,
        idleCount: this.pool.idleCount,
        waitingCount: this.pool.waitingCount
      };
      
      // Log if connection usage is high
      if (stats.totalCount > this.maxConnections * 0.8) {
        console.warn('Database connection pool usage high:', stats);
      }
      
      // Log if too many waiting
      if (stats.waitingCount > 5) {
        console.warn('Many queries waiting for connections:', stats);
      }
    }, 30000); // Check every 30 seconds
  }

  async getPoolStats(): Promise<PoolStats> {
    return {
      totalConnections: this.pool.totalCount,
      idleConnections: this.pool.idleCount,
      waitingQueries: this.pool.waitingCount,
      maxConnections: this.maxConnections
    };
  }
}

interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  waitingQueries: number;
  maxConnections: number;
}
```

---

## 🎯 Success Metrics & KPIs

### Business Metrics Dashboard

```typescript
// Real-time business metrics calculation
interface BusinessMetrics {
  // Financial KPIs
  monthlyRecurringRevenue: number;
  customerAcquisitionCost: number;
  lifetimeValue: number;
  profitMargin: number;
  revenuePerCustomer: number;
  
  // Operational KPIs
  documentsProcessedPerDay: number;
  averageProcessingTime: number;
  systemUptime: number;
  errorRate: number;
  
  // Customer KPIs
  activeCustomers: number;
  customerRetentionRate: number;
  apiUsageGrowth: number;
  supportTicketRate: number;
  
  // Technical KPIs
  systemPerformance: number;
  aiModelAccuracy: number;
  costPerDocument: number;
  scalabilityMetric: number;
}

class MetricsDashboard {
  async calculateBusinessMetrics(period: 'day' | 'week' | 'month'): Promise<BusinessMetrics> {
    const startDate = this.getPeriodStart(period);
    const endDate = new Date();

    // Financial metrics
    const revenueData = await this.getRevenueMetrics(startDate, endDate);
    const costData = await this.getCostMetrics(startDate, endDate);
    
    // Operational metrics
    const operationalData = await this.getOperationalMetrics(startDate, endDate);
    
    // Customer metrics
    const customerData = await this.getCustomerMetrics(startDate, endDate);
    
    // Technical metrics
    const technicalData = await this.getTechnicalMetrics(startDate, endDate);

    return {
      // Financial
      monthlyRecurringRevenue: revenueData.mrr,
      customerAcquisitionCost: costData.cac,
      lifetimeValue: customerData.ltv,
      profitMargin: (revenueData.total - costData.total) / revenueData.total,
      revenuePerCustomer: revenueData.total / customerData.active,
      
      // Operational
      documentsProcessedPerDay: operationalData.documentsPerDay,
      averageProcessingTime: operationalData.avgProcessingTime,
      systemUptime: operationalData.uptime,
      errorRate: operationalData.errorRate,
      
      // Customer
      activeCustomers: customerData.active,
      customerRetentionRate: customerData.retentionRate,
      apiUsageGrowth: customerData.usageGrowth,
      supportTicketRate: customerData.supportTicketRate,
      
      // Technical
      systemPerformance: technicalData.performance,
      aiModelAccuracy: technicalData.accuracy,
      costPerDocument: costData.total / operationalData.totalDocuments,
      scalabilityMetric: technicalData.scalability
    };
  }

  private async getRevenueMetrics(startDate: Date, endDate: Date) {
    const { data } = await supabase.rpc('calculate_revenue_metrics', {
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString()
    });

    return {
      total: data.total_revenue,
      mrr: data.monthly_recurring_revenue,
      growth: data.revenue_growth_rate
    };
  }

  private async getOperationalMetrics(startDate: Date, endDate: Date) {
    // Documents processed
    const { count: totalDocuments } = await supabase
      .from('documents')
      .select('*', { count: 'exact' })
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .eq('processing_status', 'completed');

    // Average processing time
    const { data: processingData } = await supabase
      .from('documents')
      .select('processing_time_ms')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .eq('processing_status', 'completed');

    const avgProcessingTime = processingData?.reduce((sum, doc) => 
      sum + (doc.processing_time_ms || 0), 0) / (processingData?.length || 1);

    // Error rate
    const { count: errorCount } = await supabase
      .from('audit_logs')
      .select('*', { count: 'exact' })
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .eq('status', 'failure');

    const { count: totalRequests } = await supabase
      .from('audit_logs')
      .select('*', { count: 'exact' })
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      totalDocuments: totalDocuments || 0,
      documentsPerDay: (totalDocuments || 0) / periodDays,
      avgProcessingTime: avgProcessingTime || 0,
      errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
      uptime: await this.calculateUptime(startDate, endDate)
    };
  }

  async generateExecutiveSummary(): Promise<ExecutiveSummary> {
    const currentMetrics = await this.calculateBusinessMetrics('month');
    const previousMetrics = await this.calculateBusinessMetrics('month'); // Previous month
    
    return {
      revenue: {
        current: currentMetrics.monthlyRecurringRevenue,
        growth: this.calculateGrowthRate(
          currentMetrics.monthlyRecurringRevenue,
          previousMetrics.monthlyRecurringRevenue
        ),
        target: 50000, // $50K ARR target from PRD
        onTrack: currentMetrics.monthlyRecurringRevenue >= (50000 / 12)
      },
      profitability: {
        current: currentMetrics.profitMargin,
        target: 0.60, // 60% target from PRD
        onTrack: currentMetrics.profitMargin >= 0.60
      },
      performance: {
        processingTime: currentMetrics.averageProcessingTime,
        target: 5000, // <5s target from PRD
        onTrack: currentMetrics.averageProcessingTime < 5000,
        uptime: currentMetrics.systemUptime,
        uptimeTarget: 99.5, // 99.5% target from PRD
        uptimeOnTrack: currentMetrics.systemUptime >= 99.5
      },
      customers: {
        active: currentMetrics.activeCustomers,
        target: 25, // 25 customers target from PRD
        onTrack: currentMetrics.activeCustomers >= 25,
        retention: currentMetrics.customerRetentionRate
      },
      recommendations: this.generateRecommendations(currentMetrics)
    };
  }

  private generateRecommendations(metrics: BusinessMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.profitMargin < 0.60) {
      recommendations.push('Optimize AI model selection to improve profit margins');
    }

    if (metrics.averageProcessingTime > 5000) {
      recommendations.push('Investigate performance bottlenecks in document processing');
    }

    if (metrics.errorRate > 5) {
      recommendations.push('Focus on reducing error rates through better validation');
    }

    if (metrics.customerRetentionRate < 80) {
      recommendations.push('Improve customer onboarding and support processes');
    }

    return recommendations;
  }
}

interface ExecutiveSummary {
  revenue: {
    current: number;
    growth: number;
    target: number;
    onTrack: boolean;
  };
  profitability: {
    current: number;
    target: number;
    onTrack: boolean;
  };
  performance: {
    processingTime: number;
    target: number;
    onTrack: boolean;
    uptime: number;
    uptimeTarget: number;
    uptimeOnTrack: boolean;
  };
  customers: {
    active: number;
    target: number;
    onTrack: boolean;
    retention: number;
  };
  recommendations: string[];
}
```

---

## 🎯 Architecture Summary & Next Steps

### Key Architecture Decisions

1. **API-First Design**: Pure backend service using Supabase Edge Functions
2. **Multi-Model AI Strategy**: OpenAI → Claude → LlamaParse fallback chain
3. **Cost-Optimized Processing**: 60%+ profit margin through intelligent model selection
4. **Dual Key Architecture**: Test (skt_) vs Production (skp_) with different retention
5. **Asynchronous Processing**: Queue system for large documents and batch operations
6. **Real-time Capabilities**: WebSocket updates for job status and system monitoring
7. **Enterprise Security**: Row-Level Security, API key hashing, audit logging
8. **GDPR Compliance**: Automated data retention and deletion capabilities

### Performance Targets Achievement Strategy

| Requirement | Target | Architecture Solution |
|-------------|--------|----------------------|
| Processing Time | <5 seconds | Caching + Model selection + Queue optimization |
| Uptime | 99.5% | Circuit breakers + Multi-model fallbacks |
| Profit Margin | 60%+ | Cost-aware model selection + Usage optimization |
| API Response | <500ms | Connection pooling + Multi-tier caching |
| Extraction Accuracy | >95% | Model quality scoring + Confidence thresholds |
| Scalability | 1000+ calls/month | Edge Functions + Database optimization |

### Implementation Roadmap

**Phase 1: Foundation (Epic 1)**
- Database schema implementation with RLS
- Edge Functions infrastructure setup
- Basic API key authentication
- Health check and monitoring endpoints

**Phase 2: Core Processing (Epic 2)**
- Document processing pipeline
- Multi-model AI integration with circuit breakers
- Queue system for async processing
- Usage tracking and cost monitoring

**Phase 3: Agent System (Epic 3)**
- Default agent catalog creation
- Agent cloning and customization system
- JSON schema validation
- Version control for custom agents

**Phase 4: Enterprise Features (Epic 4)**
- Advanced admin portal capabilities
- Customer lifecycle management
- Sophisticated rate limiting
- Comprehensive analytics dashboard

**Phase 5: Production Ready (Epic 5)**
- Security hardening and compliance
- Performance optimization
- Monitoring and alerting systems
- GDPR compliance implementation

### Technical Risk Mitigation

**High Priority Risks:**
1. **Edge Function Timeout**: Async queue system for >55s operations
2. **AI Cost Overruns**: Real-time cost monitoring with circuit breakers
3. **Database Performance**: Connection pooling + Materialized views + Optimized indexes
4. **Security Vulnerabilities**: Multi-layer input sanitization + RLS + Audit logging

**Medium Priority Risks:**
1. **Rate Limit Management**: Redis-backed sliding window implementation
2. **Data Compliance**: Automated retention policies + GDPR tools
3. **Monitoring Gaps**: Comprehensive metrics collection + Alert system

### Success Criteria Validation

The architecture enables:
- ✅ **Immediate Value**: Epic 1 delivers working API platform
- ✅ **Business Viability**: Cost structure supports 60%+ margins
- ✅ **Enterprise Readiness**: Security and compliance features included
- ✅ **Developer Experience**: Clear patterns for Epic 2-5 implementation
- ✅ **Scalability**: Queue system and caching for growth
- ✅ **Reliability**: Circuit breakers and fallback systems

This architecture provides a solid foundation for building the IDP Platform as specified in the PRD, with clear implementation guidance for the development team.

---

*Architecture Document Complete*  
*Ready for Epic 1 Implementation*