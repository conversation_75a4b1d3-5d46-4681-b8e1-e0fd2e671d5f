
# IDP Platform API Documentation

This document provides a comprehensive overview of the IDP Platform's API endpoints.

## Authentication

All API requests must be authenticated using a <PERSON><PERSON> token in the `Authorization` header.

```
Authorization: Bearer YOUR_API_KEY
```

## Admin Analytics API

Base Path: `/admin-analytics`

### Get Usage Analytics

*   **Endpoint:** `/usage`
*   **Method:** `POST`
*   **Description:** Retrieves usage analytics data for a specified date range and customer.

**Request Body:**

```json
{
  "date_range": {
    "start": "2025-08-22T00:00:00Z",
    "end": "2025-09-22T23:59:59Z"
  },
  "customer_id": "cus_12345",
  "group_by": "day"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "usage_metrics": [
      {
        "date": "2025-09-22",
        "total_requests": 1500,
        "successful_requests": 1490,
        "failed_requests": 10,
        "success_rate": "99.33",
        "credits_consumed": 750,
        "documents_processed": 300,
        "avg_processing_time_ms": 1200
      }
    ]
  }
}
```

### Get Revenue Analytics

*   **Endpoint:** `/revenue`
*   **Method:** `POST`
*   **Description:** Retrieves revenue analytics data.

**Request Body:**

```json
{
  "date_range": {
    "start": "2025-08-01T00:00:00Z",
    "end": "2025-08-31T23:59:59Z"
  },
  "customer_id": "cus_12345",
  "include_cost_breakdown": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "revenue_metrics": {
      "total_revenue": 5000.00,
      "total_cost": 1500.00,
      "gross_profit": 3500.00,
      "profit_margin": 70.00
    },
    "cost_breakdown": {
      "openai_costs": 800.00,
      "claude_costs": 500.00,
      "llamaparse_costs": 200.00
    }
  }
}
```

---

## Admin API Keys API

Base Path: `/admin-api-keys`

### Create API Key

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Creates a new API key for a customer.

**Request Body:**

```json
{
  "customer_id": "cus_12345",
  "name": "My New Production Key",
  "key_type": "production",
  "expires_at": "2026-01-01T00:00:00Z",
  "scope_restrictions": {
    "allowed_endpoints": ["/document-processing"],
    "max_file_size": 10485760
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "key_67890",
    "key": "skp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "name": "My New Production Key",
    "key_type": "production",
    "expires_at": "2026-01-01T00:00:00Z"
  }
}
```

### Suspend API Key

*   **Endpoint:** `/{keyId}/suspend`
*   **Method:** `PUT`
*   **Description:** Suspends an active API key.

**Request Body:**

```json
{
  "reason": "Security concern detected."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "key_67890",
    "status": "suspended",
    "suspension_reason": "Security concern detected."
  }
}
```

---

## Admin Credits API

Base Path: `/admin-credits`

### Purchase Credits

*   **Endpoint:** `/{customerId}/purchase`
*   **Method:** `POST`
*   **Description:** Adds credits to a customer's account.

**Request Body:**

```json
{
  "amount": 10000,
  "payment_reference": "txn_12345"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "transaction_id": "cred_txn_abcde",
    "customer_id": "cus_12345",
    "amount": 10000,
    "new_balance": 25000
  }
}
```

### Get Credit History

*   **Endpoint:** `/{customerId}/history`
*   **Method:** `GET`
*   **Description:** Retrieves the credit transaction history for a customer.

**Response:**

```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "cred_txn_abcde",
        "transaction_type": "purchase",
        "amount": 10000,
        "balance_after": 25000,
        "created_at": "2025-09-22T10:00:00Z"
      }
    ]
  }
}
```

---

## Admin Customers API

Base Path: `/admin-customers`

### Create Customer

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Creates a new customer.

**Request Body:**

```json
{
  "name": "New Corp",
  "email": "<EMAIL>",
  "tier": "enterprise"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "cus_54321",
    "name": "New Corp",
    "email": "<EMAIL>",
    "tier": "enterprise",
    "status": "trial"
  }
}
```

### List Customers

*   **Endpoint:** `/`
*   **Method:** `GET`
*   **Description:** Retrieves a list of customers.

**Response:**

```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "id": "cus_54321",
        "name": "New Corp",
        "status": "trial"
      }
    ],
    "pagination": {
      "total": 1,
      "limit": 20,
      "offset": 0
    }
  }
}
```

---

## Agents API

Base Path: `/agents`

### List Agents

*   **Endpoint:** `/`
*   **Method:** `GET`
*   **Description:** Retrieves a list of available agents.

**Response:**

```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "id": "agent_invoice",
        "name": "Invoice Agent",
        "description": "Extracts data from invoices."
      }
    ]
  }
}
```

### Customize Agent

*   **Endpoint:** `/{id}`
*   **Method:** `PUT`
*   **Description:** Customizes an agent's prompt and other settings.

**Request Body:**

```json
{
  "prompt": "Extract the total amount and due date from the invoice.",
  "json_schema": {
    "type": "object",
    "properties": {
      "total_amount": { "type": "number" },
      "due_date": { "type": "string", "format": "date" }
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "agent_invoice_custom_123",
    "version": 2,
    "status": "customized"
  }
}
```

---

## Document Extraction API

Base Path: `/extract`

### Extract Data from Document

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Extracts structured data from a document.

**Request Body (multipart/form-data):**

*   `document`: The file to process.
*   `agentId`: The ID of the agent to use for extraction.

**Response:**

```json
{
  "success": true,
  "data": {
    "documentId": "doc_abcde",
    "extractedData": {
      "total_amount": 123.45,
      "due_date": "2025-10-15"
    },
    "confidence": 0.98,
    "creditsUsed": 5
  }
}
```

---

## Health Check API

Base Path: `/health`

### Get Health Status

*   **Endpoint:** `/`
*   **Method:** `GET`
*   **Description:** Retrieves the health status of the platform and its services.

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2025-09-22T12:00:00Z",
  "services": {
    "database": { "status": "healthy" },
    "openai": { "status": "healthy" },
    "claude": { "status": "healthy" }
  }
}
```

---

## Job Status API

Base Path: `/job-status`

### Get Job Status

*   **Endpoint:** `/{jobId}`
*   **Method:** `GET`
*   **Description:** Retrieves the status of an asynchronous job.

**Response:**

```json
{
  "success": true,
  "data": {
    "jobId": "job_xyz",
    "status": "completed",
    "result": {
      "extractedData": {
        "total_amount": 123.45
      }
    }
  }
}
```

---


---

## Admin Generate Key API

Base Path: `/admin-generate-key`

### Generate API Key

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Generates a new test or production API key for a customer.

**Request Body:**

```json
{
  "customerId": "cus_12345",
  "keyType": "production",
  "name": "Production Key 1",
  "credits": 10000
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "rawKey": "skp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "keyId": "key_67890",
    "customerId": "cus_12345",
    "keyType": "production",
    "credits": 10000,
    "expiresAt": null
  }
}
```

---

## Admin Manage Keys API

Base Path: `/admin-manage-keys`

### Update API Key Status

*   **Endpoint:** `/`
*   **Method:** `PUT`
*   **Description:** Updates the status of an API key (e.g., activate, suspend).

**Request Body:**

```json
{
  "keyId": "key_67890",
  "action": "suspend",
  "reason": "Security review needed."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "keyId": "key_67890",
    "status": "suspended",
    "message": "API key suspended successfully."
  }
}
```

### Revoke API Key

*   **Endpoint:** `/`
*   **Method:** `DELETE`
*   **Description:** Permanently revokes an API key.

**Request Body:**

```json
{
  "keyId": "key_67890",
  "reason": "Key compromised."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "keyId": "key_67890",
    "status": "revoked",
    "message": "API key revoked successfully."
  }
}
```

---

## Admin Rate Limits API

Base Path: `/admin-rate-limits`

### Create Rate Limit

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Creates a new rate limit configuration.

**Request Body:**

```json
{
  "customer_id": "cus_12345",
  "limit_type": "per_hour",
  "limit_value": 1000,
  "burst_allowance": 100
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "rl_abcde",
    "customer_id": "cus_12345",
    "limit_type": "per_hour",
    "limit_value": 1000,
    "burst_allowance": 100,
    "is_active": true
  }
}
```

### Check Rate Limit

*   **Endpoint:** `/check`
*   **Method:** `POST`
*   **Description:** Checks if a request is allowed under the current rate limits.

**Request Body:**

```json
{
  "api_key_id": "key_67890",
  "endpoint": "/document-processing"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "allowed": true,
    "remaining": 999
  }
}
```

---

## Admin Support API

Base Path: `/admin-support`

### Get Customer Timeline

*   **Endpoint:** `/customers/{id}/timeline`
*   **Method:** `GET`
*   **Description:** Retrieves the activity timeline for a customer.

**Response:**

```json
{
  "success": true,
  "data": {
    "timeline": [
      {
        "timestamp": "2025-09-22T10:00:00Z",
        "event_type": "api_request",
        "details": {
          "endpoint": "/extract",
          "status": 200
        }
      }
    ]
  }
}
```

### Create Impersonation Session

*   **Endpoint:** `/customers/{id}/impersonate`
*   **Method:** `POST`
*   **Description:** Creates a customer impersonation session for support purposes.

**Request Body:**

```json
{
  "reason": "Investigating reported issue #1234.",
  "duration_minutes": 60
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "impersonation_token": "imp_xxxxxxxxxxxxxxxx",
    "expires_at": "2025-09-22T11:00:00Z"
  }
}
```

---

## Agent Metrics API

Base Path: `/agent-metrics`

### Record Agent Performance

*   **Endpoint:** `/record`
*   **Method:** `POST`
*   **Description:** Records performance metrics for an agent.

**Request Body:**

```json
{
  "metrics": {
    "agent_id": "agent_invoice_custom_123",
    "accuracy": 0.95,
    "processing_time_ms": 1500
  }
}
```

**Response:**

```json
{
  "success": true,
  "message": "Performance metrics recorded successfully."
}
```

### Get Agent Summary

*   **Endpoint:** `/summary`
*   **Method:** `GET`
*   **Description:** Retrieves a summary of an agent's performance.

**Query Parameters:**

*   `agentId`: The ID of the agent.

**Response:**

```json
{
  "success": true,
  "summary": {
    "avg_accuracy": 0.92,
    "avg_processing_time_ms": 1800
  }
}
```

---

## Usage Tracking API

Base Path: `/usage-tracking`

### Record Usage

*   **Endpoint:** `/record`
*   **Method:** `POST`
*   **Description:** Records usage for a document processing operation.

**Request Body:**

```json
{
  "processingResult": {
    "documentId": "doc_abcde",
    "model": "gpt-4",
    "inputTokens": 1000,
    "outputTokens": 200
  },
  "customerContext": {
    "customerId": "cus_12345"
  }
}
```

**Response:**

```json
{
  "success": true,
  "usage_recorded": true
}
```

### Get Credit Balance

*   **Endpoint:** `/credits/balance`
*   **Method:** `GET`
*   **Description:** Retrieves the current credit balance for an API key.

**Response:**

```json
{
  "success": true,
  "data": {
    "current_balance": 24995
  }
}
```
"date_range": {
    "start": "2025-08-01T00:00:00Z",
    "end": "2025-08-31T23:59:59Z"
  },
  "customer_id": "cus_12345",
  "include_cost_breakdown": true
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "revenue_metrics": {
      "total_revenue": 5000.00,
      "total_cost": 1500.00,
      "gross_profit": 3500.00,
      "profit_margin": 70.00
    },
    "cost_breakdown": {
      "openai_costs": 800.00,
      "claude_costs": 500.00,
      "llamaparse_costs": 200.00
    }
  }
}
```

---

## Admin API Keys API

Base Path: `/admin-api-keys`

### Create API Key

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Creates a new API key for a customer.

**Request Body:**

```json
{
  "customer_id": "cus_12345",
  "name": "My New Production Key",
  "key_type": "production",
  "expires_at": "2026-01-01T00:00:00Z",
  "scope_restrictions": {
    "allowed_endpoints": ["/document-processing"],
    "max_file_size": 10485760
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "key_67890",
    "key": "skp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "name": "My New Production Key",
    "key_type": "production",
    "expires_at": "2026-01-01T00:00:00Z"
  }
}
```

### Suspend API Key

*   **Endpoint:** `/{keyId}/suspend`
*   **Method:** `PUT`
*   **Description:** Suspends an active API key.

**Request Body:**

```json
{
  "reason": "Security concern detected."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "key_67890",
    "status": "suspended",
    "suspension_reason": "Security concern detected."
  }
}
```

---

## Admin Credits API

Base Path: `/admin-credits`

### Purchase Credits

*   **Endpoint:** `/{customerId}/purchase`
*   **Method:** `POST`
*   **Description:** Adds credits to a customer's account.

**Request Body:**

```json
{
  "amount": 10000,
  "payment_reference": "txn_12345"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "transaction_id": "cred_txn_abcde",
    "customer_id": "cus_12345",
    "amount": 10000,
    "new_balance": 25000
  }
}
```

### Get Credit History

*   **Endpoint:** `/{customerId}/history`
*   **Method:** `GET`
*   **Description:** Retrieves the credit transaction history for a customer.

**Response:**

```json
{
  "success": true,
  "data": {
    "transactions": [
      {
        "id": "cred_txn_abcde",
        "transaction_type": "purchase",
        "amount": 10000,
        "balance_after": 25000,
        "created_at": "2025-09-22T10:00:00Z"
      }
    ]
  }
}
```

---

## Admin Customers API

Base Path: `/admin-customers`

### Create Customer

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Creates a new customer.

**Request Body:**

```json
{
  "name": "New Corp",
  "email": "<EMAIL>",
  "tier": "enterprise"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "cus_54321",
    "name": "New Corp",
    "email": "<EMAIL>",
    "tier": "enterprise",
    "status": "trial"
  }
}
```

### List Customers

*   **Endpoint:** `/`
*   **Method:** `GET`
*   **Description:** Retrieves a list of customers.

**Response:**

```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "id": "cus_54321",
        "name": "New Corp",
        "status": "trial"
      }
    ],
    "pagination": {
      "total": 1,
      "limit": 20,
      "offset": 0
    }
  }
}
```

---

## Agents API

Base Path: `/agents`

### List Agents

*   **Endpoint:** `/`
*   **Method:** `GET`
*   **Description:** Retrieves a list of available agents.

**Response:**

```json
{
  "success": true,
  "data": {
    "agents": [
      {
        "id": "agent_invoice",
        "name": "Invoice Agent",
        "description": "Extracts data from invoices."
      }
    ]
  }
}
```

### Customize Agent

*   **Endpoint:** `/{id}`
*   **Method:** `PUT`
*   **Description:** Customizes an agent's prompt and other settings.

**Request Body:**

```json
{
  "prompt": "Extract the total amount and due date from the invoice.",
  "json_schema": {
    "type": "object",
    "properties": {
      "total_amount": { "type": "number" },
      "due_date": { "type": "string", "format": "date" }
    }
  }
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "agent_invoice_custom_123",
    "version": 2,
    "status": "customized"
  }
}
```

---

## Document Extraction API

Base Path: `/extract`

### Extract Data from Document

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Extracts structured data from a document.

**Request Body (multipart/form-data):**

*   `document`: The file to process.
*   `agentId`: The ID of the agent to use for extraction.

**Response:**

```json
{
  "success": true,
  "data": {
    "documentId": "doc_abcde",
    "extractedData": {
      "total_amount": 123.45,
      "due_date": "2025-10-15"
    },
    "confidence": 0.98,
    "creditsUsed": 5
  }
}
```

---

## Health Check API

Base Path: `/health`

### Get Health Status

*   **Endpoint:** `/`
*   **Method:** `GET`
*   **Description:** Retrieves the health status of the platform and its services.

**Response:**

```json
{
  "status": "healthy",
  "timestamp": "2025-09-22T12:00:00Z",
  "services": {
    "database": { "status": "healthy" },
    "openai": { "status": "healthy" },
    "claude": { "status": "healthy" }
  }
}
```

---

## Job Status API

Base Path: `/job-status`

### Get Job Status

*   **Endpoint:** `/{jobId}`
*   **Method:** `GET`
*   **Description:** Retrieves the status of an asynchronous job.

**Response:**

```json
{
  "success": true,
  "data": {
    "jobId": "job_xyz",
    "status": "completed",
    "result": {
      "extractedData": {
        "total_amount": 123.45
      }
    }
  }
}
```

---


---

## Admin Generate Key API

Base Path: `/admin-generate-key`

### Generate API Key

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Generates a new test or production API key for a customer.

**Request Body:**

```json
{
  "customerId": "cus_12345",
  "keyType": "production",
  "name": "Production Key 1",
  "credits": 10000
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "rawKey": "skp_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "keyId": "key_67890",
    "customerId": "cus_12345",
    "keyType": "production",
    "credits": 10000,
    "expiresAt": null
  }
}
```

---

## Admin Manage Keys API

Base Path: `/admin-manage-keys`

### Update API Key Status

*   **Endpoint:** `/`
*   **Method:** `PUT`
*   **Description:** Updates the status of an API key (e.g., activate, suspend).

**Request Body:**

```json
{
  "keyId": "key_67890",
  "action": "suspend",
  "reason": "Security review needed."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "keyId": "key_67890",
    "status": "suspended",
    "message": "API key suspended successfully."
  }
}
```

### Revoke API Key

*   **Endpoint:** `/`
*   **Method:** `DELETE`
*   **Description:** Permanently revokes an API key.

**Request Body:**

```json
{
  "keyId": "key_67890",
  "reason": "Key compromised."
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "keyId": "key_67890",
    "status": "revoked",
    "message": "API key revoked successfully."
  }
}
```

---

## Admin Rate Limits API

Base Path: `/admin-rate-limits`

### Create Rate Limit

*   **Endpoint:** `/`
*   **Method:** `POST`
*   **Description:** Creates a new rate limit configuration.

**Request Body:**

```json
{
  "customer_id": "cus_12345",
  "limit_type": "per_hour",
  "limit_value": 1000,
  "burst_allowance": 100
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "rl_abcde",
    "customer_id": "cus_12345",
    "limit_type": "per_hour",
    "limit_value": 1000,
    "burst_allowance": 100,
    "is_active": true
  }
}
```

### Check Rate Limit

*   **Endpoint:** `/check`
*   **Method:** `POST`
*   **Description:** Checks if a request is allowed under the current rate limits.

**Request Body:**

```json
{
  "api_key_id": "key_67890",
  "endpoint": "/document-processing"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "allowed": true,
    "remaining": 999
  }
}
```

---

## Usage Tracking API

Base Path: `/usage-tracking`

### Record Usage

*   **Endpoint:** `/record`
*   **Method:** `POST`
*   **Description:** Records usage for a document processing operation.

**Request Body:**

```json
{
  "processingResult": {
    "documentId": "doc_abcde",
    "model": "gpt-4",
    "inputTokens": 1000,
    "outputTokens": 200
  },
  "customerContext": {
    "customerId": "cus_12345"
  }
}
```

**Response:**

```json
{
  "success": true,
  "usage_recorded": true
}
```

### Get Credit Balance

*   **Endpoint:** `/credits/balance`
*   **Method:** `GET`
*   **Description:** Retrieves the current credit balance for an API key.

**Response:**

```json
{
  "success": true,
  "data": {
    "current_balance": 24995
  }
}
```
