# Suggested Commands for IDP Platform

## Development Commands
```bash
# Start development environment
npm run db:start              # Start Supabase locally
npm run functions:serve       # Serve Edge Functions
npm run db:status            # Check Supabase status

# Database operations
npm run db:reset             # Reset database with fresh data
npm run db:migrate           # Apply pending migrations
npm run db:types             # Generate TypeScript types
npm run db:seed              # Insert sample data
```

## Testing Commands
```bash
npm test                     # Run all tests
npm run test:watch           # Run tests in watch mode
npm run test:coverage        # Run tests with coverage
npm run test:manual          # Interactive manual tests
```

## Code Quality Commands
```bash
npm run lint                 # Lint TypeScript code
npm run lint:fix             # Auto-fix lint issues
npm run format               # Format code with Prettier
npm run format:check         # Check code formatting
npm run type-check           # TypeScript validation
```

## Utility Commands (Darwin/macOS)
```bash
find . -name "*.ts" -type f   # Find TypeScript files
grep -r "pattern" .          # Search for patterns
ls -la                       # List files with details
cd supabase/functions        # Navigate to functions
```

## When Task is Completed
1. Run `npm run type-check` - Ensure TypeScript compiles
2. Run `npm run lint` - Check for code quality issues
3. Run `npm test` - Ensure all tests pass
4. Run `npm run format` - Format code consistently
5. Commit changes with descriptive message