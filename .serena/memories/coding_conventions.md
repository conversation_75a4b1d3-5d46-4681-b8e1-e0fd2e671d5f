# Coding Conventions and Style Guide

## TypeScript Standards
- **Strict mode enabled** - no any types allowed
- **Explicit return types** for all functions
- **Interface definitions** for all data structures
- **Zod validation** for runtime type checking

## File Organization
- `/supabase/functions/` - Edge Functions (Deno/TypeScript)
- `/tests/unit/` - Unit tests (Bun Test)
- `/types/` - TypeScript type definitions
- `/docs/` - Project documentation

## Naming Conventions
- **Files**: kebab-case (extract-document.ts)
- **Functions**: camelCase (validateApiKey)
- **Interfaces**: PascalCase (DocumentRequest)
- **Constants**: SCREAMING_SNAKE_CASE (MAX_FILE_SIZE)

## Testing Patterns
- **Test files**: *.test.ts suffix
- **Test structure**: describe/it blocks
- **Mocking**: Use Bun's built-in mocking
- **Assertions**: expect() statements

## Security Rules
- Never store raw API keys (always hash with SHA-256)
- Use environment variables for secrets
- Validate all inputs before processing
- Implement proper error handling

## Code Quality Rules
- Zero tolerance for `any` types
- All functions must have error handling
- Use specific types over generic ones
- Prefer union types over `any`