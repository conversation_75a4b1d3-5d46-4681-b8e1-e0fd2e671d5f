# Technology Stack

## Core Technologies
- **Database**: PostgreSQL 17 with Supabase
- **Backend**: Supabase Edge Functions (Deno runtime)
- **Language**: TypeScript 5.9+ (strict mode, no any types)
- **Testing**: Bun Test framework
- **Package Manager**: Bun 1.1+

## AI Integration
- OpenAI API for primary processing
- Claude API for fallback
- LlamaParse API for PDF processing

## Development Tools
- ESLint for code linting
- Prettier for code formatting
- TypeScript compiler for type checking
- <PERSON><PERSON> for git hooks

## Key Dependencies
- @supabase/supabase-js for database operations
- zod for runtime validation
- file-type for file validation
- pdf-parse, mammoth, xlsx for document processing
- sharp for image processing