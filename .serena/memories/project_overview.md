# IDP Platform Project Overview

## Purpose
The IDP Platform is an API-first document processing service that transforms unstructured documents (PDFs, images, spreadsheets) into structured JSON data using AI models. Built for developers who need reliable document processing without UI complexity.

## Key Features
- Multi-Model AI Fallbacks (OpenAI → Claude → LlamaParse) for 99.5% uptime
- Dual API Key System (test keys skt_, production keys skp_)
- Customizable extraction agents with versioning
- Enterprise security with SHA-256 API key hashing
- 60%+ profit margins through intelligent model routing

## Architecture
- Backend: Supabase (PostgreSQL + Edge Functions)
- Runtime: Deno for Edge Functions, Bun for tests
- Language: TypeScript with strict typing
- AI Integration: OpenAI, Claude, LlamaParse APIs
- Testing: Bun Test framework

## Current Status
- Basic project structure exists
- Documentation is comprehensive
- Test structure is partially set up
- Supabase configuration is minimal (only .gitignore exists)